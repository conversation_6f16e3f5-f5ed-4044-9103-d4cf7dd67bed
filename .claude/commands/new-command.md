# Create New Claude Code Command

Create a new custom Claude Code command based on the provided specifications.

**Usage**: `/project:new-command "description of command to create"`

**Note**: This is a project command, so it uses the `/project:` prefix. Personal commands in `~/.claude/commands/` would use just `/command-name`.

## Command Creation Process

1. **Analyze the request**: Understanding what the command should do and what parameters it needs
2. **Create the command file**: Generate a properly formatted Markdown file in `.claude/commands/`
3. **Follow best practices**: Ensure the command follows Claude Code conventions and project standards

## Command Template Structure

Generate a command with the following structure:
- **Clear title**: Descriptive name for the command
- **Purpose description**: What the command accomplishes
- **Step-by-step instructions**: Detailed actions to perform
- **Parameter support**: Use `$ARGUMENTS` for flexible input
- **Project-specific guidelines**: Follow our coding standards from CLAUDE.md

## Project Standards Integration

Ensure all generated commands follow our established patterns:
- Use snake_case naming conventions
- Include proper type hints and async patterns
- Follow the LayerModel pattern (DB model → DTO → Domain → API schema)
- Use repository pattern with GenericRepository inheritance
- Apply our error handling approach with domain-specific exceptions
- Follow our API router conventions with ReeAPIRouter

## Usage Instructions

Provide clear documentation on:
- How to use the command with `/project:command-name` (for project commands in `.claude/commands/`)
- Note: Personal commands in `~/.claude/commands/` use `/command-name` without prefix
- What arguments are expected
- Example usage scenarios
- Expected outputs or behaviors

## Command Request

Create a new Claude Code command for: $ARGUMENTS

Include specific details about:
- Command purpose and functionality
- Required parameters or inputs
- Step-by-step implementation guide
- Integration with existing codebase patterns
- Testing recommendations if applicable

The command should be production-ready and follow all established project conventions.
