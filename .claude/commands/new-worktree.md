# Create New Git Worktree

Create a new git worktree for a given branch name and switch <PERSON>'s working directory to the new worktree.

**Usage**: `/project:new-worktree "branch-name"`

**Note**: This is a project command that manages git worktrees and changes <PERSON>'s working directory.

## Command Process

1. **Validate current git repository**: Ensure we're in a valid git repository
2. **Parse branch name**: Extract and validate the branch name from arguments
3. **Create new worktree**: Use `git worktree add` to create a new worktree
4. **Switch working directory**: Change <PERSON>'s current working directory to the new worktree
5. **Confirm setup**: Verify the worktree was created successfully and we're in the correct location

## Implementation Steps

### Step 1: Validate Git Repository
```bash
# Check if we're in a git repository
git rev-parse --is-inside-work-tree
```

### Step 2: Create Worktree
For branch name: $ARGUMENTS

```bash
# Create worktree directory path
BRANCH_NAME="$ARGUMENTS"
WORKTREE_DIR="../${<PERSON>ANCH_NAME}"

# Create new worktree
git worktree add "${WORKTREE_DIR}" -b "${BRANCH_NAME}"
```

### Step 3: Verify Worktree Creation
```bash
# Confirm worktree was created successfully
git worktree list
ls -la "${WORKTREE_DIR}"
```

### Step 4: Instructions for User
Since Claude Code cannot change to parent/sibling directories due to security restrictions, instruct the user to:

1. Open a new terminal
2. Navigate to the worktree: `cd "${WORKTREE_DIR}"`
3. Start a new Claude Code instance: `claude`

## Error Handling

- Check if branch name already exists locally or remotely
- If branch exists and is already checked out in another worktree, inform user of existing location
- Validate that worktree directory doesn't already exist
- Ensure git worktree command succeeds
- Handle cases where git operations fail

## Usage Examples

- `/project:new-worktree "feature/user-authentication"`
- `/project:new-worktree "bugfix/api-timeout"`
- `/project:new-worktree "hotfix/security-patch"`

## Expected Behavior

1. Creates a new git worktree in a sibling directory (e.g., `../feature-user-authentication`)
2. Creates and checks out a new branch with the specified name (or uses existing branch)
3. Provides instructions for user to open a new Claude Code instance in the worktree
4. Confirms successful setup with worktree listing and directory information

## Claude Code Limitation

Due to Claude Code's security restrictions, it cannot automatically change to parent/sibling directories. After creating the worktree, you'll need to manually:

1. Open a new terminal
2. `cd` to the worktree directory
3. Run `claude` to start a new instance in that location

## Cleanup Note

Remember that worktrees can be removed later with:
```bash
git worktree remove <worktree-path>
```

## Project Integration

This command follows our established patterns:
- Uses bash commands for git operations
- Provides clear error handling and validation
- Maintains clean directory structure
- Integrates with existing git workflow
