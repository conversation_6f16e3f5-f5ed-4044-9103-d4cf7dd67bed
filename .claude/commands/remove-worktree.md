# Remove Git Worktree

Remove a specific git worktree and its associated files safely.

**Usage**: `/project:remove-worktree "branch-name-or-path"`

**Note**: This is a project command that removes git worktrees and optionally deletes the associated branch.

## Command Process

1. **List existing worktrees**: Show all current worktrees to help identify the target
2. **Validate target worktree**: Ensure the specified worktree exists
3. **Check for uncommitted changes**: Warn if there are unsaved changes
4. **Remove worktree**: Use `git worktree remove` to clean up the worktree
5. **Optional branch cleanup**: Ask if the associated branch should also be deleted

## Implementation Steps

### Step 1: List Current Worktrees
```bash
# Show all existing worktrees
echo "Current worktrees:"
git worktree list
```

### Step 2: Identify Target Worktree
For input: $ARGUMENTS

```bash
# Parse input - could be branch name or path
TARGET="$ARGUMENTS"

# Try to find matching worktree by branch name or path
WORKTREE_PATH=$(git worktree list --porcelain | grep -B1 "branch.*${TARGET}" | head -1 | sed 's/worktree //' || echo "")

# If not found by branch, try as direct path
if [ -z "$WORKTREE_PATH" ]; then
    # Check if it's a valid worktree path
    if git worktree list | grep -q "$TARGET"; then
        WORKTREE_PATH="$TARGET"
    fi
fi
```

### Step 3: Validate and Check Status
```bash
# Ensure we found a valid worktree
if [ -z "$WORKTREE_PATH" ]; then
    echo "Error: No worktree found matching '$TARGET'"
    echo "Available worktrees:"
    git worktree list
    exit 1
fi

# Check for uncommitted changes
echo "Checking for uncommitted changes in: $WORKTREE_PATH"
cd "$WORKTREE_PATH" 2>/dev/null && git status --porcelain
```

### Step 4: Remove Worktree
```bash
# Remove the worktree (use --force if needed for uncommitted changes)
echo "Removing worktree: $WORKTREE_PATH"
git worktree remove "$WORKTREE_PATH"

# If removal fails due to uncommitted changes, ask user
if [ $? -ne 0 ]; then
    echo "Worktree has uncommitted changes. Use --force to remove anyway? (y/N)"
    # Note: In practice, you'd want user confirmation here
    git worktree remove "$WORKTREE_PATH" --force
fi
```

### Step 5: Optional Branch Cleanup
```bash
# Get the branch name that was in the worktree
BRANCH_NAME=$(git worktree list --porcelain | grep -A1 "$WORKTREE_PATH" | grep "branch" | sed 's/branch //')

if [ ! -z "$BRANCH_NAME" ]; then
    echo "Worktree removed successfully."
    echo "Would you like to delete the associated branch '$BRANCH_NAME'? (y/N)"
    echo "If yes, run: git branch -d $BRANCH_NAME"
    echo "Or force delete: git branch -D $BRANCH_NAME"
fi
```

### Step 6: Verify Cleanup
```bash
# Confirm worktree was removed
echo "Updated worktree list:"
git worktree list
```

## Error Handling

- Validate that we're in a git repository
- Check if specified worktree exists
- Handle worktrees with uncommitted changes
- Provide clear error messages for invalid inputs
- Warn before destructive operations

## Safety Features

- Lists all worktrees before attempting removal
- Checks for uncommitted changes and warns user
- Provides branch cleanup options rather than automatic deletion
- Confirms successful removal with updated worktree list

## Usage Examples

- `/project:remove-worktree "feature/user-authentication"`
- `/project:remove-worktree "../bugfix-api-timeout"`
- `/project:remove-worktree "/full/path/to/worktree"`

## Expected Behavior

1. Shows current worktrees for reference
2. Identifies the target worktree by branch name or path
3. Checks for uncommitted changes and warns if found
4. Removes the worktree directory and git tracking
5. Offers guidance on cleaning up the associated branch
6. Confirms successful removal

## Branch Cleanup Options

After removing a worktree, you may want to:
- `git branch -d <branch-name>` - Safe delete (only if merged)
- `git branch -D <branch-name>` - Force delete (even if unmerged)
- Keep the branch for future use

## Project Integration

This command follows our established patterns:
- Uses bash commands for git operations
- Provides comprehensive error handling and validation
- Offers safe defaults with user guidance
- Integrates with existing git workflow
- Maintains clean repository state
