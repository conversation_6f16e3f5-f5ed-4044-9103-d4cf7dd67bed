# Update Claude Documentation

Update CLAUDE.md files throughout the project following best practices for Claude Code memory management.

**Usage**: `/project:update-claude-docs "target-folder or 'root' for main CLAUDE.md"`

**Note**: This command helps maintain proper CLAUDE.md documentation structure across the project.

## CLAUDE.md File Types and Purposes

### Root CLAUDE.md (`./CLAUDE.md`)
- **Project-wide preferences and settings**
- **Shared team instructions** (checked into git)
- **Build commands, coding standards, conventions**
- **Architecture patterns and project structure**

### Subfolder CLAUDE.md (`./subfolder/CLAUDE.md`)
- **Folder-specific documentation**
- **How to use code in that specific directory**
- **Module-specific patterns and conventions**
- **Local testing and development notes**

## Best Practices from Anthropic

### Content Structure
- Use **specific, actionable instructions**
- Structure with **clear markdown headings**
- Group related memories under **descriptive sections**
- Use **bullet points** for clarity
- Add **"IMPORTANT"** or **"YOU MUST"** for critical instructions

### Memory Management
- **Be specific** rather than generic
- **Periodically review and update** content
- **Avoid duplication** - Claude reads all CLAUDE.md files recursively
- Use **imports** with `@path/to/import` syntax when needed

## Implementation Steps

### Step 1: Determine Target
For target: $ARGUMENTS

```bash
TARGET="$ARGUMENTS"

if [ "$TARGET" = "root" ]; then
    CLAUDE_FILE="./CLAUDE.md"
    echo "Updating root CLAUDE.md for project-wide settings"
else
    CLAUDE_FILE="./${TARGET}/CLAUDE.md"
    echo "Updating CLAUDE.md for folder: $TARGET"
fi
```

### Step 2: Check Current Structure
```bash
# Show current CLAUDE.md files in project
echo "Current CLAUDE.md files:"
find . -name "CLAUDE.md" -type f | head -10

# Check if target file exists
if [ -f "$CLAUDE_FILE" ]; then
    echo "Existing file found: $CLAUDE_FILE"
    echo "Current content length: $(wc -l < "$CLAUDE_FILE") lines"
else
    echo "Creating new file: $CLAUDE_FILE"
fi
```

### Step 3: Create/Update Content Based on Type

#### For Root CLAUDE.md:
```markdown
# Project Commands & Guidelines

## Commands
- Build: [project build commands]
- Run: [project run commands]
- Lint: [linting commands]
- Tests: [testing commands]

## Code Style
- **Naming**: [naming conventions]
- **Imports**: [import organization]
- **Types**: [type requirements]
- **Structure**: [architectural patterns]
- **Error Handling**: [error patterns]

## Git Workflow
- [commit message standards]
- [branch naming]
- [PR requirements]

## Important Instructions
IMPORTANT: [critical project-specific requirements]
YOU MUST: [mandatory practices]
```

#### For Subfolder CLAUDE.md:
```markdown
# [Folder Name] Documentation

## Purpose
[What this folder/module does]

## Key Files
- [Important files and their purposes]

## Usage Patterns
[How to use code in this folder]

## Testing
[Folder-specific testing approaches]

## Important Notes
[Folder-specific requirements or gotchas]
```

### Step 4: Open for Editing
```bash
# Use Claude's memory command to edit
echo "Opening $CLAUDE_FILE for editing..."
echo "You can also use: /memory to edit in your system editor"
```

### Step 5: Validate Structure
```bash
# Check for best practices
echo "Validating CLAUDE.md structure..."

# Check for key sections
if grep -q "## Commands" "$CLAUDE_FILE" 2>/dev/null; then
    echo "✓ Commands section found"
else
    echo "⚠ Consider adding Commands section"
fi

if grep -q "IMPORTANT\|YOU MUST" "$CLAUDE_FILE" 2>/dev/null; then
    echo "✓ Emphasis keywords found"
else
    echo "⚠ Consider adding IMPORTANT/YOU MUST for critical instructions"
fi

# Check line count
LINES=$(wc -l < "$CLAUDE_FILE" 2>/dev/null || echo "0")
if [ "$LINES" -gt 5 ]; then
    echo "✓ Substantial content ($LINES lines)"
else
    echo "⚠ Consider adding more detailed content"
fi
```

## Error Handling

- Validate target directory exists (for subfolder docs)
- Check write permissions
- Backup existing content before major changes
- Validate markdown syntax
- Ensure no duplicate information across files

## Content Guidelines

### Root CLAUDE.md Should Include:
- **Build/run/test commands**
- **Code style and conventions**
- **Git workflow requirements**
- **Architecture patterns**
- **Critical project requirements**

### Subfolder CLAUDE.md Should Include:
- **Folder purpose and scope**
- **Key files and their roles**
- **Usage patterns and examples**
- **Testing approaches**
- **Folder-specific gotchas**

### Avoid in Subfolder Docs:
- **Duplicating root-level information**
- **Project-wide settings**
- **General coding standards** (already in root)

## Usage Examples

- `/project:update-claude-docs "root"` - Update main project CLAUDE.md
- `/project:update-claude-docs "salestech_be/core/auth"` - Update auth module docs
- `/project:update-claude-docs "tests"` - Update testing documentation
- `/project:update-claude-docs "scripts"` - Update scripts folder docs

## Expected Behavior

1. **Identifies target** CLAUDE.md file (root or subfolder)
2. **Shows current structure** and existing files
3. **Provides templates** based on file type (root vs subfolder)
4. **Opens for editing** using Claude's memory system
5. **Validates structure** against best practices
6. **Confirms updates** and checks for common issues

## Memory Management Tips

- **Quick addition**: Use `#` at start of input to add memories quickly
- **System editing**: Use `/memory` command for full editor access
- **Import syntax**: Use `@path/to/import` to include other files
- **Review regularly**: Periodically audit and update all CLAUDE.md files

## Project Integration

This command follows our established patterns:
- **Maintains documentation standards**
- **Follows Anthropic best practices**
- **Integrates with Claude Code memory system**
- **Supports team collaboration**
- **Prevents documentation duplication**
