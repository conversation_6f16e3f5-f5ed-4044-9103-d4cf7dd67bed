---
description: Guidance for implementing and updating any AI and LLM related functionalities
globs:
---
# AI Integration Patterns

## Prompt Management

- Use Langfuse for prompt versioning and management
- Structure data into nested variables
- Convert complex types to simple structures
- Handle optional data with conditionals
- Include metadata for tracking

## Example Prompt Management

```python
from salestech_be.common.langfuse.prompt_client import get_prompt_client
from salestech_be.core.ai.workflows.schema import PipelineContext

prompt_client = get_prompt_client()
prompt = await prompt_client.get_prompt(
    "generate_pipeline_intel_tasks",
    variables={
        "account": context.account.model_dump(),
        "pipeline": context.pipeline.model_dump(),
        "meeting_transcript": context.meeting_context.transcript,
    },
)
```

## LLM Integration

- Use ree_llm for completions
- Include proper metadata and tracing
- Handle tool calls appropriately
- Validate LLM responses
- Track metrics and costs

## Example LLM Integration

```python
from salestech_be.common.ree_llm import LLMTraceMetadata, acompletion
from salestech_be.web.api.task.schema import TaskRequestList<PERSON>romLLM
from uuid import UUID

response = await ree_llm.acompletion(
    model="anthropic/claude-3-5-sonnet",
    messages=[{"role": "user", "content": prompt}],
    temperature=0,
    tools=[{
        "type": "function",
        "function": {
            "name": "create_tasks",
            "parameters": TaskRequestListFromLLM.model_json_schema(),
        },
    }],
    metadata=LLMTraceMetadata(
        trace_name="activity.generate_tasks",
        session_id=activity_input.langfuse_session_id,
    ),
    tool_choice="any",
)
```

## Insight Services

- Organize by domain (Meeting, Email)
- Handle async data processing
- Implement caching strategies
- Track processing metrics
- Handle rate limits

## Example Insight Service

```python
from salestech_be.core.meeting.meeting_insight_service import MeetingInsightService
from uuid import UUID
from typing import Optional
from salestech_be.core.meeting.types.meeting_types_v2 import MeetingInsightV2

class MeetingInsightService:
    async def get_meeting_insights(
        self,
        meeting_id: UUID,
        organization_id: UUID,
    ) -> MeetingInsightV2:
        cache_key = f"meeting_insight:{meeting_id}"
        cached = await self.cache.get(cache_key)
        if cached:
            return MeetingInsightV2.model_validate_json(cached)

        insight = await self._generate_meeting_insight(meeting_id, organization_id)
        await self.cache.set(cache_key, insight.model_dump_json())
        return insight

    async def _generate_meeting_insight(
        self,
        meeting_id: UUID,
        organization_id: UUID,
    ) -> MeetingInsightV2:
        meeting = await self.meeting_repository.find_by_id(meeting_id)
        if not meeting:
            raise ResourceNotFoundError(f"Meeting {meeting_id} not found")

        prompt = await self.prompt_client.get_prompt(
            "generate_meeting_insights",
            variables={
                "transcript": meeting.transcript,
                "metadata": meeting.metadata,
            },
        )

        response = await self.ree_llm.acompletion(
            model="anthropic/claude-3-5-sonnet",
            messages=[{"role": "user", "content": prompt}],
            metadata=LLMTraceMetadata(
                trace_name="service.generate_meeting_insight",
                meeting_id=meeting_id,
            ),
        )

        return MeetingInsightV2.model_validate_json(response.choices[0].message.content)
```

## Error Handling & Retries

- Implement exponential backoff for rate limits
- Handle API timeouts gracefully
- Validate LLM responses against schemas
- Log errors with context
- Track error metrics

## Example Error Handling

```python
from salestech_be.common.exception.exception import LLMError
from salestech_be.ree_logging import get_logger
from tenacity import retry, stop_after_attempt, wait_exponential

logger = get_logger(__name__)

@retry(
    stop=stop_after_attempt(3),
    wait=wait_exponential(multiplier=1, min=4, max=10),
)
async def generate_with_retry(
    prompt: str,
    metadata: LLMTraceMetadata,
) -> str:
    try:
        response = await ree_llm.acompletion(
            model="anthropic/claude-3-5-sonnet",
            messages=[{"role": "user", "content": prompt}],
            metadata=metadata,
        )

        # Validate response
        if not response.choices:
            raise LLMError("Empty response from LLM")

        return response.choices[0].message.content

    except Exception as e:
        logger.error(
            "Failed to generate LLM response",
            exc_info=e,
            extra={
                "prompt": prompt,
                "metadata": metadata.model_dump(),
            },
        )
        raise
```

## Metrics & Monitoring

- Track token usage and costs
- Monitor response times
- Log prompt and completion tokens
- Track error rates
- Monitor rate limits

## Example Metrics

```python
from salestech_be.common.stats.metric import custom_metric
from salestech_be.common.stats.constants import MetricType

async def track_llm_metrics(
    response: LLMResponse,
    metadata: LLMTraceMetadata,
) -> None:
    await custom_metric(
        name="llm.tokens.prompt",
        value=response.usage.prompt_tokens,
        metric_type=MetricType.COUNT,
        tags={
            "model": response.model,
            "trace_name": metadata.trace_name,
        },
    )

    await custom_metric(
        name="llm.tokens.completion",
        value=response.usage.completion_tokens,
        metric_type=MetricType.COUNT,
        tags={
            "model": response.model,
            "trace_name": metadata.trace_name,
        },
    )

    await custom_metric(
        name="llm.latency",
        value=response.latency_ms,
        metric_type=MetricType.TIMING,
        tags={
            "model": response.model,
            "trace_name": metadata.trace_name,
        },
    )
```
