---
description: Must-follow guidance for generating any API integration guides
globs:
alwaysApply: false
---

# API Integration Guide Generation Pattern

## Overview

This rule defines the standard approach for creating accurate and comprehensive API integration guides for front-end engineers based on the backend API implementation. *YOU MUST* Follow this pattern to ensure consistency across all API documentation.

## IMPORTANT NOTES

- **Test-Driven Documentation:** EVERY use case MUST be implemented as a functional test case similar to [test_stage_criteria_integration_guide.py](mdc:tests/integration/web/api/stage_criteria/test_stage_criteria_integration_guide.py). These test cases should be arranged in a separate test file in the same directory. Each test MUST pass before using its output as documentation.

- **No Fabricated Examples:** Always use the serialized request and response payloads logged from passing tests as your documentation examples. NEVER write example payloads based on imagination or assumptions.

- **Comprehensive Test Coverage:** Ensure each test case is complex enough to demonstrate the majority of functionality provided by the API endpoint.

- **Test Execution:** Always run tests using `uv run pytest -v --capture=no` to ensure proper execution and output capture.

- **Structured Logging:**
  - Never use f-strings for logging. Follow standard structured logging patterns.
  - Example: `logger.info("example request", request_body=request_object)`
  - Always create loggers using `get_logger` from `salestech_be.ree_logging`
- **Integration guide placement**
  - If not specified otherwise, the generated integration guide need to be saved under the same folder of the view.py file

## Core Principles

1. **Documentation Accuracy**: All examples must be generated from actual API calls, not manually crafted
2. **Complete Coverage**: Document all endpoints in the API module
3. **Consistent Format**: Follow standard Markdown formatting for all guides
4. **Real-world Examples**: Examples should represent typical use cases
5. **Strict Type Validation**: Document all type restrictions and validations

## Implementation Steps

### 1. Analyze API Endpoints

- Examine the `views.py` file in the target API directory
- Identify all endpoints (POST, GET, PUT, DELETE methods)
- Note the request/response models, path parameters, and query parameters
- Review docstrings for endpoint descriptions and important notes

#### Finding Accurate URL Paths

To determine the complete URL path for each API endpoint:

1. First look at the corresponding view file (e.g., `views.py`) to find the URL path defined in each endpoint's decorator
2. Then check `router_private.py` to identify the router prefix for the API module
3. The full URL path is the combination of the router prefix and the endpoint path

Example:

- In `views.py`: `@router.post(path="/_list_stage_criteria_item_template", ...)`
- In `router_private.py`: `api_router_private.include_router(stage_criteria_v2.router, prefix="/v1/stage_criteria_v2", ...)`
- Full URL path: `/v1/stage_criteria_v2/_list_stage_criteria_item_template`

### 2. Create Test Cases for Example Generation

- Create dedicated test file named `test_*_integration_guide.py`
- Implement test functions that cover each endpoint
- Use the `CommonAPIClient` to make API calls
- Add logging statements to capture request/response data
- Use descriptive function names: `test_[endpoint_operation]_for_integration_guide`
- Always ensure the test cases are correctly and successfully passing

### 3. Generate Examples

- Run test cases to generate example requests and responses
- Log outputs as JSON for inclusion in documentation
- Include edge cases and error scenarios where appropriate
- Ensure examples cover all required and optional parameters

### 4. Document Each Endpoint

For each endpoint, document the following:

1. **URL and HTTP Method**

```curl
[HTTP_METHOD] /api/[path]
```

2. **Example Request**

```json
[Formatted request JSON]
```

3. **Example Response**

```json
[Formatted response JSON]
```

4. **Note any restrictions or special behaviors**

```markdown
> **Note**: [Important information about restrictions, validations, etc.]
```

### 5. Testing Structure Pattern

Example test file structure:

```python
import json
from faker import Faker
from salestech_be.ree_logging import get_logger
from tests.integration.web.api.util.common_api_client import CommonAPIClient

logger = get_logger(__name__)

async def test_[endpoint_name]_for_integration_guide(
    common_api_client: CommonAPIClient,
    [other_fixtures],
) -> None:
    """Test [endpoint description] and log request/response for the integration guide."""
    # Arrange
    request = [RequestModel](
        [parameters]
    )

    # Log the request for documentation
    logger.info(
        "Example request for [endpoint description]",
        request_body=json.loads(request.model_dump_json()),
    )

    # Act
    response = await common_api_client.[api_method](
        [parameters],
        request=request,
    )

    # Log the response for documentation
    logger.info(
        "Example response for [endpoint description]",
        response_body=json.loads(response.model_dump_json()),
    )

    # Assert
    assert [validation statements]

    # Clean up if necessary
    [cleanup code]
```

### 6. Integration Guide Template

Use the following template for the integration guide:

```markdown
# [API Name] Integration Guide

## [Feature Group]

### [Operation Description]

1. URL and HTTP Method

```curl
[HTTP_METHOD] /api/[path]
```

2. Example Request

```json
[Request JSON]
```

3. Example Response

```json
[Response JSON]
```

> **Note**: [Important information]

```

## Special Cases

### Error Responses

- Document common error responses and status codes
- Include examples of error responses where appropriate

### Authentication

- Document authentication requirements if applicable
- Show headers required for authentication

### Pagination

- Document pagination parameters and response format
- Show example of paginated response

### Filtering and Sorting

- Document filter and sort parameters
- Show examples of filtered/sorted requests and responses

## Best Practices

1. **Cross-validate with tests**: Ensure the documented examples exactly match test outputs
2. **Document restrictions**: Note type constraints, validation rules, and applicable criteria types
3. **Keep examples reasonable**: Use realistic and complicated enough examples to illustrate functionality, espeically important edge cases
4. **Document relationships**: Note any required relationships between objects
5. **Include field descriptions**: For complex fields, include descriptions of their purpose
6. **Document versioning**: Clearly indicate the API version in the path and documentation title

## Implementation Note

This pattern has been successfully used to document the Stage Criteria APIs, providing front-end engineers with accurate and comprehensive integration examples.
