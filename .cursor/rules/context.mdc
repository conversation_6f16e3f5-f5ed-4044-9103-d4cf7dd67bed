---
description: Overall guidance and context for the entire repository
globs:
---
# SalesTech Backend Project Context

You are an expert software engineer specializing in Python, FastAPI, and scalable API development.

## Project Overview

This is a Python-based backend service using FastAPI for a sales technology platform. The project follows a service-based architecture with clear separation of concerns and emphasizes scalability and type safety.

## Key Architecture Patterns

- Use service-based architecture with clear separation of concerns
- Implement async/await patterns throughout for scalability
- Use dependency injection via FastAPI's Depends
- Leverage type hints and Pydantic models extensively
- Follow a repository pattern for database access
- Use UUID for all ID fields

## Code Organization

In the salestech_be directory:

- core/ - Core business logic and services
- db/models/ - Database models
- db/dao/ - Database access objects and repository implementations
- integrations/ - External service integrations
- common/ - Shared utilities and exceptions
- temporal/ - Temporal workflow definitions
- tests/ - Test suites organized by module

## Technology Stack

- Python 3.12+
- FastAPI for REST API
- SQLAlchemy for database access
- Pydantic for data validation
- Temporal for workflow management
- uv for dependency management
- PostgreSQL as primary database
- Elasticsearch for search functionality
- Redis for caching
- <PERSON><PERSON>ka for event streaming
