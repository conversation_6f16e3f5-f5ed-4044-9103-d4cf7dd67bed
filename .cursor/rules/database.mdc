---
description: Guidance of implementing and updating any database query and database repository related functionalities.
globs: **/*.py
---
# Database Patterns

## Model Patterns

- Use TableModel base class for all database models
- Define table_name and ordered_primary_keys for each model
- Use Column[Type] annotations for database model fields
- Use JsonColumn for JSON data fields
- Include standard audit fields (created_at, updated_at, deleted_at)

## Repository Patterns

- Inherit from GenericRepository for basic CRUD operations
- Define repository-specific methods for complex queries
- Use async/await for all database operations
- Implement proper transaction handling:
  - Use async with self.engine.begin() for transactions
  - Handle rollbacks automatically
- Follow naming conventions:
  - find_* for single item queries
  - list_* for multiple item queries
  - update_* for updates
  - delete_* for deletions
- Use proper error handling:
  - Raise ResourceNotFoundError when items don't exist
  - Handle IntegrityError for conflicts
  - Use custom exceptions for business logic errors

## Example Repository Implementation
See details at: [account_repository.py](mdc:salestech_be/db/dao/account_repository.py)


## Domain Query Service Pattern

- Use DomainObjectQueryService for complex domain queries
- Support filtering and sorting:
  - Use FilterSpec for filtering conditions
  - Use SortingSpec for sorting options
- Handle relationships:
  - Support field references for related objects
  - Implement proper eager loading
  - Use fetch contexts for caching
- Follow patterns:
  - list_*_records for listing records
  - get_*_record for single record
  - map_*_by_* for mapping relationships

## Example Query Service
See details at: [pipeline_query_service.py](mdc:salestech_be/core/pipeline/service/pipeline_query_service.py)
