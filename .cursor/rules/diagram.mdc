---
description: When user ask to generate diagram as code, always follow this guidance.
globs:
alwaysApply: false
---

When generating any diagrams as code, such as flowchart, sequence diagram, entity relationship diagram, class diagram, state machine diagram, c4 diagram, you should output the generated diagram code in Mermaid format that is supported by lucidchart's "diagram as code" feature. You can look into following links to learn about lucidchart's "diagram as code" feature as well as Mermaid syntaxes and ensure your generated code is compatible with such:

- Lucid chart - diagram as code overview: <https://help.lucid.co/hc/en-us/articles/29549366940948-Diagram-as-code-with-Mermaid-in-Lucidchart>
- Lucid chart - diagram as code examples: <https://community.lucid.co/inspiration-5/what-syntax-can-i-use-to-diagram-as-code-with-mermaid-9665>
- Mermaid - Flowchart: <https://mermaid.js.org/syntax/flowchart.html>
- Mermaid - Sequence diagram: <https://mermaid.js.org/syntax/sequenceDiagram.html>
- Mermaid - Class diagram: <https://mermaid.js.org/syntax/classDiagram.html>
- Mermaid - Entity Relationship: <https://mermaid.js.org/syntax/entityRelationshipDiagram.html>
- Mermaid - C4: <https://mermaid.js.org/syntax/c4.html>
- Mermaid - State diagram: <https://mermaid.js.org/syntax/stateDiagram.html>

Additional layout requirements:

1. Depending on the diagram's content, ensure the width and length of the generated graph is reader friendly. Horizontally, grpah shouldn't be too wide. Vertically, graph shouldn't too long.
