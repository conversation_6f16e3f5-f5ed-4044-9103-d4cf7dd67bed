---
description: Guidance on implmenting and updating any event handling functionalities
globs: **/*.py
---
# Event-Driven Architecture Patterns

## Event Handlers

- Organize handlers in core/ai/event_handlers/
- Keep handlers focused and single-purpose
- Use pattern matching for event types
- Validate required fields early
- Use proper error handling and logging

## Example Event Handler

```python
from salestech_be.common.events import DomainEnrichedCDCEvent
from salestech_be.core.ai.event_handlers.types import PipelineIntelTriggerEvent
from salestech_be.db.models.meeting import Meeting
from salestech_be.ree_logging import get_logger
from uuid import UUID

logger = get_logger(__name__)

async def pipeline_intel_handler(event: PipelineIntelTriggerEvent) -> None:
    match event:
        case DomainEnrichedCDCEvent() if isinstance(event.after, Meeting):
            if not meeting.organization_id:
                logger.bind(meeting_id=meeting.id).error(
                    "Meeting missing required organization info"
                )
                return
            await start_workflow(...)
```

## Event Processors

- Use CDC event processors for data change events
- Implement feature flag checks
- Chain multiple handlers when needed
- Track metrics for processing time
- Handle domain model conversion

## Example Event Processor

```python
from typing import cast
from salestech_be.common.events import DomainEnrichedCDCEvent, EnrichedCDCEventProcessor
from salestech_be.common.stats.metric import custom_metric
from salestech_be.core.meeting.types.meeting_types_v2 import MeetingV2
from salestech_be.db.models.meeting import Meeting
from salestech_be.integrations.kafka.types import CDCObject, CDCObjectState
from salestech_be.ree_logging import get_logger
from salestech_be.core.ff.feature_flag_service import get_feature_flag_service
from salestech_be.core.ff.types import FeatureFlagRequest

logger = get_logger(__name__)

class MeetingCDCEventProcessor(EnrichedCDCEventProcessor[Meeting, MeetingV2]):
    async def process_domain_enriched_event(
        self,
        domain_event: DomainEnrichedCDCEvent[Meeting, MeetingV2]
    ) -> MeetingV2:
        logger.bind(event_before=domain_event.before, event_after=domain_event.after).info(
            "Processing meeting event"
        )

        if settings.enable_research_agent_on_meeting:
            await meeting_research_handler(domain_event)

        if settings.enable_pipeline_intel_on_meeting:
            await pipeline_intel_handler(domain_event)

        # Feature flag check
        if domain_event.after and domain_event.after.organization_id:
            enable_auto_close = await self.ff_service.is_enabled(
                request=FeatureFlagRequest(
                    flag_key="task-auto-completion-vfeb2025",
                    organization_id=domain_event.after.organization_id,
                )
            )
            if enable_auto_close:
                await close_tasks_handler(domain_event)

        return domain_event.current_domain_model
```

## Event Metrics

- Track event processing time
- Monitor event queue sizes
- Track error rates by event type
- Monitor handler success rates
- Track feature flag usage

## Example Event Metrics

```python
from salestech_be.common.stats.metric import custom_metric
from salestech_be.common.stats.constants import MetricType
from time import perf_counter
from typing import Any

async def track_event_metrics(
    event_type: str,
    start_time: float,
    success: bool,
    metadata: dict[str, Any],
) -> None:
    processing_time = perf_counter() - start_time

    await custom_metric(
        name="event.processing.time",
        value=processing_time,
        metric_type=MetricType.TIMING,
        tags={
            "event_type": event_type,
            "success": str(success),
            **metadata,
        },
    )

    await custom_metric(
        name="event.processed",
        value=1,
        metric_type=MetricType.COUNT,
        tags={
            "event_type": event_type,
            "success": str(success),
            **metadata,
        },
    )
```

## Error Handling

- Log errors with full context
- Track error metrics
- Implement dead letter queues
- Handle retries appropriately
- Validate event data

## Example Error Handler

```python
from salestech_be.common.exception.exception import EventProcessingError
from salestech_be.ree_logging import get_logger
from typing import Any

logger = get_logger(__name__)

async def handle_event_error(
    event: Any,
    error: Exception,
    context: dict[str, Any],
) -> None:
    logger.error(
        "Failed to process event",
        exc_info=error,
        extra={
            "event": event.model_dump() if hasattr(event, "model_dump") else str(event),
            "error_type": type(error).__name__,
            **context,
        },
    )

    await custom_metric(
        name="event.error",
        value=1,
        metric_type=MetricType.COUNT,
        tags={
            "error_type": type(error).__name__,
            "event_type": type(event).__name__,
            **context,
        },
    )

    if isinstance(error, EventProcessingError):
        # Handle known error types
        await handle_known_error(error, event)
    else:
        # Send to dead letter queue for unknown errors
        await send_to_dlq(event, error)
```

## Feature Flag Integration

- Use FeatureFlagService for granular control
- Initialize service in constructor
- Check flags at processor level
- Handle undefined flags gracefully
- Track flag usage metrics

## Example Feature Flag Check

```python
from salestech_be.core.ff.feature_flag_service import get_feature_flag_service
from salestech_be.core.ff.types import FeatureFlagRequest
from uuid import UUID

ff_service = get_feature_flag_service()

async def check_feature_enabled(
    flag_key: str,
    organization_id: UUID,
) -> bool:
    try:
        is_enabled = await ff_service.is_enabled(
            request=FeatureFlagRequest(
                flag_key=flag_key,
                organization_id=organization_id,
            )
        )

        await custom_metric(
            name="feature_flag.check",
            value=1,
            metric_type=MetricType.COUNT,
            tags={
                "flag_key": flag_key,
                "enabled": str(is_enabled),
            },
        )

        return is_enabled

    except Exception as e:
        logger.error(
            "Failed to check feature flag",
            exc_info=e,
            extra={
                "flag_key": flag_key,
                "organization_id": organization_id,
            },
        )
        return False  # Default to disabled on error
```
