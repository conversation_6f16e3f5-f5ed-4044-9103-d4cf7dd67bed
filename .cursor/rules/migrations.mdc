---
description: Must-follow guidance when generating any DB migrations
globs:
alwaysApply: false
---

# Database Migration Patterns

- Always use the Alembic CLI (`alembic revision -m ...`) to generate migration file skeletons, then implement the migration logic according to these rules.

## General Principles

- Use direct SQL execution with `op.execute()` for all schema changes
- Avoid using SQLAlchemy's DDL methods (like `op.create_table`, `op.add_column`, etc.)
- Always include both upgrade and downgrade functions
- **Never create foreign key constraints** in migrations - the application enforces relationships
- Use proper PostgreSQL types (e.g., `TEXT` instead of `VARCHAR`, `TIMESTAMP WITH TIME ZONE` for timestamps)

## Table Creation Pattern

```python
def upgrade() -> None:
    # Create table with explicit SQL
    op.execute(
        """
        CREATE TABLE example_table (
            id UUID NOT NULL PRIMARY KEY,
            name TEXT NOT NULL,
            description TEXT NOT NULL,
            organization_id UUID NOT NULL,
            created_by_user_id UUID NOT NULL,
            updated_by_user_id UUID NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE NOT NULL,
            updated_at TIMESTAMP WITH TIME ZONE NOT NULL,
            deleted_at TIMESTAMP WITH TIME ZONE,
            deleted_by_user_id UUID
        );
        """
    )

    # Create standard indexes
    op.commit() # since we want to create index concurrently, we have to do it outside of the transaction context

    op.execute(
        """
        CREATE INDEX CONCURRENTLY example_table_organization_id_idx
        ON example_table (organization_id);
        """
    )
```

## Indexing Conventions

- Create indexes using `CREATE INDEX` statements
- Standard index naming convention: `tablename_columnname_idx`
- For multi-column indexes: `tablename_column1_column2_idx`
- For unique indexes: `tablename_column1_column2_uidx`
- Always create indexes on organization_id and deleted_at
- For soft-deleted tables with unique constraints, use `WHERE deleted_at IS NULL`

```python
# Example of unique index with soft-delete condition
op.execute(
    """
    CREATE UNIQUE INDEX CONCURRENTLY example_table_name_organization_id_uidx
    ON example_table (name, organization_id)
    WHERE deleted_at IS NULL;
    """
)
```

## Nullable and Default Values

- Use explicit `NOT NULL` for required fields
- Avoid using database-level defaults, let the application handle them
- For boolean fields, always specify `NOT NULL`

## Array Types

- Use PostgreSQL array types: `TEXT[]`, `UUID[]`, etc.
- Don't specify dimensions or bounds for arrays

```python
op.execute(
    """
    CREATE TABLE tags_example (
        id UUID NOT NULL PRIMARY KEY,
        tags TEXT[],
        related_ids UUID[]
    );
    """
)
```

## Downgrade Function

- Do not create downgrade operations. Unless user explicitly ask to do so.

## Views and Functions

- Create views using `CREATE OR REPLACE VIEW` to make migrations idempotent
- For complex views, use CTEs for clarity

```python
op.execute(
    """
    CREATE OR REPLACE VIEW example_view AS
    WITH filtered_data AS (
        SELECT *
        FROM example_table
        WHERE deleted_at IS NULL
    )
    SELECT id, name, organization_id
    FROM filtered_data;
    """
)
```

## Altering Existing Schemas

- For altering tables, use `ALTER TABLE` statements
- When adding columns to existing tables, make them nullable or provide a default
- When making changes that require data migration, handle it in stages

```python
# Adding a new column
op.execute(
    """
    ALTER TABLE example_table
    ADD COLUMN new_column TEXT;
    """
)

# Updating existing data
op.execute(
    """
    UPDATE example_table
    SET new_column = 'default value'
    WHERE new_column IS NULL;
    """
)

# Making column not nullable after data is updated
op.execute(
    """
    ALTER TABLE example_table
    ALTER COLUMN new_column SET NOT NULL;
    """
)
```

## Transaction Handling

- Alembic runs migrations in a transaction by default
- For operations that can't run in a transaction, use the transactional=False flag

## Common Issues & Best Practices

- When creating index, always do "CREATE INDEX CONCURRENTLY" to avoid locking.
- Avoid long-running migrations that lock tables.
- Consider backwards compatibility when changing schemas, never create schema breaking changes unless user asked to do so.
- Use explicit UUIDs for primary keys.
- Add comments to complex SQL operations.
- Prefer multiple simple migrations over one complex migration.
