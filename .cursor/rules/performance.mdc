---
description: Guidance for performance concerns when implementing any I/O operations and comput insenstive operations.
globs:
---
# Performance & Scalability Patterns

## Core Principles

- Use async/await for all I/O operations
- Implement proper database connection pooling
- Use batch operations where possible
- Implement proper caching strategies
- Handle large datasets efficiently

## Database Performance

- Use proper indexing strategies
- Implement query optimization
- Use connection pooling
- Handle N+1 query problems
- Implement efficient pagination

## Example Connection Pool

```python
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from typing import AsyncGenerator

async def create_engine():
    return create_async_engine(
        settings.database_url,
        pool_size=20,
        max_overflow=10,
        pool_timeout=30,
        pool_recycle=1800,
        pool_pre_ping=True,
    )

async_session = sessionmaker(
    bind=create_engine(),
    class_=AsyncSession,
    expire_on_commit=False,
)

async def get_session() -> AsyncGenerator[AsyncSession, None]:
    async with async_session() as session:
        try:
            yield session
        finally:
            await session.close()
```

## Caching Strategies

- Use Redis for distributed caching
- Implement proper cache invalidation
- Cache expensive computations
- Use cache decorators
- Handle cache stampede

## Example Cache Implementation

```python
from functools import wraps
from typing import Any, Callable, TypeVar
from salestech_be.common.cache import get_redis_client
from salestech_be.ree_logging import get_logger

logger = get_logger(__name__)
T = TypeVar("T")

def cache_result(
    key_prefix: str,
    ttl: int = 3600,
) -> Callable:
    def decorator(func: Callable[..., T]) -> Callable[..., T]:
        @wraps(func)
        async def wrapper(*args: Any, **kwargs: Any) -> T:
            # Generate cache key
            cache_key = f"{key_prefix}:{hash(str(args) + str(kwargs))}"

            # Try to get from cache
            redis = await get_redis_client()
            cached = await redis.get(cache_key)
            if cached:
                return cached

            # Get fresh result
            result = await func(*args, **kwargs)

            # Cache the result
            try:
                await redis.set(
                    cache_key,
                    result.model_dump_json() if hasattr(result, "model_dump_json") else result,
                    ex=ttl,
                )
            except Exception as e:
                logger.error(f"Failed to cache result: {e}")

            return result
        return wrapper
    return decorator
```

## Batch Processing

- Use batch operations for bulk updates
- Implement proper chunking
- Handle memory efficiently
- Process in parallel when possible
- Monitor batch job progress

## Example Batch Processing

```python
from typing import Sequence
from uuid import UUID
from salestech_be.db.models.user import User
from salestech_be.common.exception.exception import BatchProcessingError

async def process_users_in_batches(
    user_ids: list[UUID],
    batch_size: int = 100,
) -> None:
    # Process in batches
    for i in range(0, len(user_ids), batch_size):
        batch = user_ids[i:i + batch_size]
        try:
            await process_user_batch(batch)
        except Exception as e:
            logger.error(f"Failed to process batch {i}: {e}")
            raise BatchProcessingError(f"Failed at batch {i}")

async def process_user_batch(user_ids: list[UUID]) -> None:
    async with session.begin():
        # Fetch users in batch
        users = await user_repository.list_by_ids(user_ids)

        # Process each user
        for user in users:
            await process_single_user(user)

        # Commit batch
        await session.commit()
```

## Load Testing

- Implement proper load testing
- Monitor system metrics
- Test scaling behavior
- Identify bottlenecks
- Set performance baselines

## Example Load Test

```python
from locust import HttpUser, task, between
from typing import Any

class UserLoadTest(HttpUser):
    wait_time = between(1, 3)

    def on_start(self) -> None:
        # Login and get token
        response = self.client.post(
            "/v1/auth/login",
            json={
                "email": "<EMAIL>",
                "password": "password123",
            },
        )
        self.token = response.json()["access_token"]
        self.headers = {"Authorization": f"Bearer {self.token}"}

    @task
    def get_user_profile(self) -> None:
        self.client.get(
            "/v1/users/me",
            headers=self.headers,
        )

    @task
    def list_users(self) -> None:
        self.client.get(
            "/v1/users",
            headers=self.headers,
            params={"limit": 10, "offset": 0},
        )
```

## Monitoring & Metrics

- Track key performance metrics
- Monitor resource usage
- Set up alerting
- Track error rates
- Monitor latency

## Example Metrics Collection

```python
from salestech_be.common.stats.metric import custom_metric
from salestech_be.common.stats.constants import MetricType
from time import perf_counter
from typing import Any

async def track_performance_metrics(
    operation: str,
    start_time: float,
    success: bool,
    metadata: dict[str, Any],
) -> None:
    processing_time = perf_counter() - start_time

    # Track operation latency
    await custom_metric(
        name="operation.latency",
        value=processing_time,
        metric_type=MetricType.TIMING,
        tags={
            "operation": operation,
            "success": str(success),
            **metadata,
        },
    )

    # Track operation count
    await custom_metric(
        name="operation.count",
        value=1,
        metric_type=MetricType.COUNT,
        tags={
            "operation": operation,
            "success": str(success),
            **metadata,
        },
    )
```
