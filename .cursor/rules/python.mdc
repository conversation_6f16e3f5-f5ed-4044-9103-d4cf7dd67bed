---
description: Overall guidance on writing and running any python code
globs:
---
# Python Coding Standards

## General Patterns

- Use async/await for all I/O operations
- Define services as classes with dependency-injected constructors
- Use Pydantic models for data validation and serialization
- Use TableModel base class for database models
- Define clear type hints for all function parameters and returns
- Use enums (StrEnum) for string-based enumerations
- Follow snake_case for all names (variables, functions, files)
- Use UUID type from uuid module for all IDs

## Type Annotations

- All Python code must be properly type annotated
- Absolutely do not use `Any` and `dict[str, Any]` in type annotations or return types.
- All function arguments need correct type annotations
- All function returns must be type annotated
- Test functions must be annotated with return type "None"
- Use concrete types instead of module-level imports
- Use uuid4() from uuid module for UUID fields

## Imports

- Import concrete types and functions directly
- Never import at module level
- Only import from defined codebase or installed dependencies
- Group imports logically (stdlib, third-party, local)
- Use absolute imports within the project

## Model & DTO Patterns

- Use Pydantic models for all data structures
- Define clear model hierarchies:
  - db/models/ - Database models inheriting from TableModel
  - db/dto/ - Data transfer objects for database layer
  - core/*/types.py - Core domain models and types
  - web/api/*/schema.py - API request/response schemas
- Use strict type validation:
  - Column[Type] for database fields
  - JsonColumn for JSON data
  - Annotated types for dependency injection
- Include standard audit fields:
  - created_at: ZoneRequiredDateTime
  - updated_at: ZoneRequiredDateTime | None
  - deleted_at: ZoneRequiredDateTime | None
- Use model_copy() for immutable updates
- Define clear conversion methods between models

## Running python code directly from terminal for debugging

- Regular python command: Always make use `uv run <python-command>` to invoke the `<python-command>`, for example `uv run ipython`
- Pytest command: Always make use `uv run -v --capture=no pytest` to run test cases, so you can see the stdout outputs clearly

## Example Patterns

```python
from uuid import UUID, uuid4
from datetime import datetime
from typing import Optional
from pydantic import BaseModel
from sqlalchemy import Column
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime

class UserBase(BaseModel):
    name: str
    email: str
    organization_id: UUID

class UserCreate(UserBase):
    id: UUID = Field(default_factory=uuid4)
    created_at: ZoneRequiredDateTime = Field(default_factory=datetime.utcnow)

class User(UserBase):
    id: UUID
    created_at: ZoneRequiredDateTime
    updated_at: Optional[ZoneRequiredDateTime] = None
    deleted_at: Optional[ZoneRequiredDateTime] = None

    class Config:
        from_attributes = True
```
