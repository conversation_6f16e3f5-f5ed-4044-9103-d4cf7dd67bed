---
description: Guidance for implementing and updating any FastAPI Router / Endpoint code.
globs: **/*.py
---
# API Router Patterns

## Router Organization

- Register all routers in router_private.py or router_public.py
- Use consistent prefix pattern: /v1/resource_name
- Group related endpoints under meaningful tags
- Follow RESTful routing conventions
- Use proper dependency injection

## Service Dependencies

- Service classes must be injected using `*_from_lifespan` dependency functions
- All `*_from_lifespan` functions should be defined in `lifespan_service.py`
- Each `*_from_lifespan` function should return a field from the `LifeSpanService` class
- Use `Annotated` with `Depends` for type-safe dependency injection

Example service dependency pattern:
```python
from typing import Annotated
from fastapi import Depends
from salestech_be.web.lifespan_service import user_service_from_lifespan
from salestech_be.core.user.service import UserService

async def my_endpoint(
    user_service: Annotated[UserService, Depends(user_service_from_lifespan)],
) -> None:
    # Use the service
    pass
```

## Example Router Implementation

```python
from typing import Annotated, Sequence
from fastapi import APIRouter, Depends, HTTPException, status
from uuid import UUID
from salestech_be.web.api.user.schema import UserResponse, UserCreate, UserUpdate
from salestech_be.core.user.service import UserService
from salestech_be.core.auth.service import get_current_user
from salestech_be.common.exception.exception import ResourceNotFoundError
from salestech_be.web.lifespan_service import user_service_from_lifespan

router = APIRouter(
    prefix="/v1/users",
    tags=["users"],
)

@router.post(
    "",
    response_model=UserResponse,
    status_code=status.HTTP_201_CREATED,
)
async def create_user(
    user_create: UserCreate,
    user_service: Annotated[UserService, Depends(user_service_from_lifespan)],
    current_user: User = Depends(get_current_user),
) -> User:
    return await user_service.create_user(user_create)

@router.get(
    "/{user_id}",
    response_model=UserResponse,
)
async def get_user(
    user_id: UUID,
    user_service: Annotated[UserService, Depends(user_service_from_lifespan)],
    current_user: User = Depends(get_current_user),
) -> User:
    try:
        return await user_service.get_user(user_id)
    except ResourceNotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e),
        )

@router.get(
    "",
    response_model=list[UserResponse],
)
async def list_users(
    organization_id: UUID,
    limit: int = 100,
    offset: int = 0,
    user_service: Annotated[UserService, Depends(user_service_from_lifespan)],
    current_user: User = Depends(get_current_user),
) -> Sequence[User]:
    return await user_service.list_users(
        organization_id=organization_id,
        limit=limit,
        offset=offset,
    )

@router.patch(
    "/{user_id}",
    response_model=UserResponse,
)
async def update_user(
    user_id: UUID,
    user_update: UserUpdate,
    user_service: Annotated[UserService, Depends(user_service_from_lifespan)],
    current_user: User = Depends(get_current_user),
) -> User:
    try:
        return await user_service.update_user(user_id, user_update)
    except ResourceNotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e),
        )

@router.delete(
    "/{user_id}",
    status_code=status.HTTP_204_NO_CONTENT,
)
async def delete_user(
    user_id: UUID,
    user_service: Annotated[UserService, Depends(user_service_from_lifespan)],
    current_user: User = Depends(get_current_user),
) -> None:
    try:
        await user_service.delete_user(user_id)
    except ResourceNotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e),
        )
```

## Error Handling

- Use proper HTTP status codes
- Convert service exceptions to HTTP exceptions
- Include detailed error messages
- Handle validation errors
- Log errors appropriately

## Example Error Handler

```python
from fastapi import Request
from fastapi.responses import JSONResponse
from salestech_be.common.exception.exception import (
    ResourceNotFoundError,
    ValidationError,
    AuthenticationError,
)
from salestech_be.ree_logging import get_logger

logger = get_logger(__name__)

async def resource_not_found_handler(
    request: Request,
    exc: ResourceNotFoundError,
) -> JSONResponse:
    logger.error(f"Resource not found: {exc}")
    return JSONResponse(
        status_code=status.HTTP_404_NOT_FOUND,
        content={"detail": str(exc)},
    )

async def validation_error_handler(
    request: Request,
    exc: ValidationError,
) -> JSONResponse:
    logger.error(f"Validation error: {exc}")
    return JSONResponse(
        status_code=status.HTTP_400_BAD_REQUEST,
        content={"detail": str(exc)},
    )

async def authentication_error_handler(
    request: Request,
    exc: AuthenticationError,
) -> JSONResponse:
    logger.error(f"Authentication error: {exc}")
    return JSONResponse(
        status_code=status.HTTP_401_UNAUTHORIZED,
        content={"detail": str(exc)},
    )
```

## Request/Response Models

- Use Pydantic models for validation
- Define clear input/output schemas
- Use proper field types
- Include field descriptions
- Handle optional fields properly

## Example Schema Models

```python
from pydantic import BaseModel, EmailStr, Field
from uuid import UUID
from typing import Optional
from datetime import datetime

class UserBase(BaseModel):
    name: str = Field(..., description="User's full name")
    email: EmailStr = Field(..., description="User's email address")
    organization_id: UUID = Field(..., description="ID of user's organization")

class UserCreate(UserBase):
    password: str = Field(
        ...,
        description="User's password",
        min_length=8,
    )

class UserUpdate(BaseModel):
    name: Optional[str] = Field(None, description="User's full name")
    email: Optional[EmailStr] = Field(None, description="User's email address")
    password: Optional[str] = Field(
        None,
        description="User's password",
        min_length=8,
    )

class UserResponse(UserBase):
    id: UUID = Field(..., description="User's unique identifier")
    created_at: datetime
    updated_at: Optional[datetime] = None
    deleted_at: Optional[datetime] = None

    class Config:
        from_attributes = True
```

## Router Registration

```python
from fastapi import FastAPI
from salestech_be.web.api.user.router import router as user_router
from salestech_be.web.api.organization.router import router as org_router
from salestech_be.web.middleware.exception.exception_handler import (
    resource_not_found_handler,
    validation_error_handler,
    authentication_error_handler,
)
from salestech_be.common.exception.exception import (
    ResourceNotFoundError,
    ValidationError,
    AuthenticationError,
)

def create_app() -> FastAPI:
    app = FastAPI(title="SalesTech API")

    # Register exception handlers
    app.add_exception_handler(
        ResourceNotFoundError,
        resource_not_found_handler,
    )
    app.add_exception_handler(
        ValidationError,
        validation_error_handler,
    )
    app.add_exception_handler(
        AuthenticationError,
        authentication_error_handler,
    )

    # Register routers
    app.include_router(user_router)
    app.include_router(org_router)

    return app
```
