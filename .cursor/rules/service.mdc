---
description: Guidance for implementing and updating any Service class code
globs: **/*.py
---
# Service Layer Patterns

## Core Patterns

- Define service classes with clear responsibilities
- Use dependency injection for repositories and other services
- Implement business logic validation
- Handle complex operations spanning multiple repositories
- Follow naming conventions:
  - get_* for retrieving items
  - create_* for creation
  - update_* for updates
  - delete_* for deletion
- Use proper error handling:
  - Validate inputs before processing
  - Handle business logic errors with custom exceptions
  - Provide clear error messages

## Singleton Pattern Implementation

- All services should follow the Singleton pattern
- Create a base service class with core functionality
- Create a singleton class that inherits from both Singleton and base service
- Use factory functions to instantiate services
- Follow naming convention:
  - BaseClass: `ServiceName`
  - Singleton Class: `SingletonServiceName`
  - Factory Function: `get_service_name_general`

## Example Service Implementation

```python
from uuid import UUID
from typing import Optional, Sequence
from salestech_be.common.singleton import Singleton
from salestech_be.db.dao.user_repository import UserRepository
from salestech_be.core.user.types import UserCreate, UserUpdate
from salestech_be.common.exception.exception import ResourceNotFoundError, ValidationError

class UserService:
    def __init__(self, repository: UserRepository) -> None:
        self.repository = repository

    async def create_user(self, user_create: UserCreate) -> User:
        # Validate business rules
        if await self.repository.find_by_email(user_create.email):
            raise ValidationError(f"User with email {user_create.email} already exists")

        # Create user
        user = User(**user_create.model_dump())
        await self.repository.create(user)
        return user

    async def get_user(self, user_id: UUID) -> User:
        user = await self.repository.find_by_id(user_id)
        if not user:
            raise ResourceNotFoundError(f"User {user_id} not found")
        return user

    async def update_user(
        self,
        user_id: UUID,
        user_update: UserUpdate,
    ) -> User:
        # Get existing user
        user = await self.get_user(user_id)

        # Validate update
        if user_update.email and user_update.email != user.email:
            if await self.repository.find_by_email(user_update.email):
                raise ValidationError(f"Email {user_update.email} already in use")

        # Apply updates
        for field, value in user_update.model_dump(exclude_unset=True).items():
            setattr(user, field, value)

        await self.repository.update(user)
        return user

    async def delete_user(self, user_id: UUID) -> None:
        user = await self.get_user(user_id)
        await self.repository.delete(user)


class SingletonUserService(Singleton, UserService):
    pass


def get_user_service_general(db_engine: DatabaseEngine) -> UserService:
    if SingletonUserService.has_instance():
        return SingletonUserService.get_singleton_instance()
    return SingletonUserService(
        repository=UserRepository(engine=db_engine),
    )


def get_user_service(request: Request) -> UserService:
    return get_user_service_general(db_engine=get_db_engine(request))
```

## Transaction Handling

- Use transaction context managers
- Handle rollbacks properly
- Maintain data consistency
- Use proper isolation levels
- Handle deadlocks gracefully

## Example Transaction Handling

```python
from sqlalchemy.ext.asyncio import AsyncSession
from salestech_be.common.singleton import Singleton
from salestech_be.db.dao.user_repository import UserRepository
from salestech_be.db.dao.organization_repository import OrganizationRepository

class UserOrganizationService:
    def __init__(
        self,
        session: AsyncSession,
        user_repository: UserRepository,
        org_repository: OrganizationRepository,
    ) -> None:
        self.session = session
        self.user_repository = user_repository
        self.org_repository = org_repository

    async def create_user_with_organization(
        self,
        user_create: UserCreate,
        org_create: OrganizationCreate,
    ) -> tuple[User, Organization]:
        async with self.session.begin():
            try:
                # Create organization
                org = Organization(**org_create.model_dump())
                await self.org_repository.create(org)

                # Create user with organization
                user = User(**user_create.model_dump(), organization_id=org.id)
                await self.user_repository.create(user)

                return user, org
            except Exception as e:
                await self.session.rollback()
                raise


class SingletonUserOrganizationService(Singleton, UserOrganizationService):
    pass


def get_user_org_service_general(db_engine: DatabaseEngine) -> UserOrganizationService:
    if UserOrganizationService.has_instance():
        return UserOrganizationService.get_singleton_instance()
    return SingletonUserOrganizationService(
        session=AsyncSession(db_engine),
        user_repository=UserRepository(engine=db_engine),
        org_repository=OrganizationRepository(engine=db_engine),
    )


def get_user_org_service(request: Request) -> UserOrganizationService:
    return get_user_org_service_general(db_engine=get_db_engine(request))
```

## Dependency Injection

- Use FastAPI's Depends for dependency injection
- Define clear dependencies
- Use proper scoping
- Handle cleanup properly
- Cache expensive operations

## Example Dependency Injection

```python
from fastapi import Depends
from starlette.requests import Request
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.common.lifespan import get_db_engine

async def get_user_service(
    request: Request,
) -> UserService:
    return get_user_org_service(db_engine=get_db_engine(request))

async def get_user_org_service(
    db_engine: DatabaseEngine
) -> UserOrganizationService:
    return UserOrganizationService(
        user_repository=UserRepository(engine=db_engine),
        org_repository=OrganizationRepository(engine=db_engine),
    )
```

## Service Composition

- Use composition over inheritance
- Inject dependent services
- Keep services focused
- Handle circular dependencies
- Use proper abstraction layers

## Example Service Composition

```python
from salestech_be.common.singleton import Singleton
from salestech_be.core.notification.service import NotificationService
from salestech_be.core.email.service import EmailService

class UserNotificationService:
    def __init__(
        self,
        user_service: UserService,
        notification_service: NotificationService,
        email_service: EmailService,
    ) -> None:
        self.user_service = user_service
        self.notification_service = notification_service
        self.email_service = email_service

    async def notify_user_update(
        self,
        user_id: UUID,
        update_type: str,
    ) -> None:
        user = await self.user_service.get_user(user_id)

        # Send notification
        await self.notification_service.send_notification(
            user_id=user_id,
            title="Profile Updated",
            message=f"Your profile has been {update_type}",
        )

        # Send email
        await self.email_service.send_email(
            to_email=user.email,
            template="profile_update",
            context={
                "user_name": user.name,
                "update_type": update_type,
            },
        )


class SingletonUserNotificationService(Singleton, UserNotificationService):
    pass


def get_user_notification_service_general(db_engine: DatabaseEngine) -> UserNotificationService:
    if UserNotificationService.has_instance():
        return UserNotificationService.get_singleton_instance()
    return SingletonUserNotificationService(
        user_service=get_user_service_general(db_engine),
        notification_service=get_notification_service_general(db_engine),
        email_service=get_email_service_general(db_engine),
    )


def get_user_notification_service(request: Request) -> UserNotificationService:
    return get_user_notification_service_general(db_engine=get_db_engine(request))
```
