---
description: Guidance for implementing and updating tests.
globs: **/*.py
---
# Testing Patterns

## Async Testing Guidelines

- DO NOT use `@pytest.mark.asyncio` decorator on test functions. The repository has a custom event loop setup in conftest.py that automatically handles async tests.
- All async test functions should be defined with `async def` but WITHOUT the `@pytest.mark.asyncio` decorator
- The event loop is managed globally through pytest configuration to ensure proper setup/teardown and avoid conflicts
- If you need custom event loop behavior, use pytest fixtures instead of `@pytest.mark.asyncio`

Example of correct async test:
```python
# ✅ Correct - just use async def without @pytest.mark.asyncio
async def test_async_function():
    result = await some_async_call()
    assert result == expected

# ❌ Incorrect - don't use @pytest.mark.asyncio
@pytest.mark.asyncio  # Don't do this!
async def test_async_function():
    result = await some_async_call()
    assert result == expected

## Test Structure

- Organize tests by module under tests/integration/ and tests/unit/
- Use cassettes for external service mocks under tests/integration/module/cassettes/
- Follow test_module_name/test_specific_feature pattern for test files
- Use appropriate fixtures from conftest.py

## API Client Pattern

- Add all API endpoints to CommonAPIClient class
- Follow consistent method naming: action_resource_type
- Use proper type hints for request/response types
- Implement both success and error case methods

## Test Implementation

- Follow Arrange-Act-Assert pattern
- Test both success and error cases
- Test permission/authorization if applicable
- Test input validation
- Test edge cases and boundary conditions
- Use VCR cassettes for external service calls
- Filter sensitive data in cassettes (headers, tokens)

## Example Test Implementation

```python
from uuid import UUID
from typing import AsyncGenerator
import pytest
from httpx import AsyncClient
from salestech_be.tests.integration.conftest import CommonAPIClient
from salestech_be.web.api.user.schema import UserResponse, UserCreate
from salestech_be.db.models.user import User

async def test_create_user(
    client: AsyncClient,
    common_api_client: CommonAPIClient,
    organization_id: UUID,
) -> None:
    # Arrange
    user_data = UserCreate(
        name="Test User",
        email="<EMAIL>",
        organization_id=organization_id,
    )

    # Act
    response = await common_api_client.create_user(user_data)

    # Assert
    assert response.status_code == 201
    user_response = UserResponse.model_validate(response.json())
    assert user_response.name == user_data.name
    assert user_response.email == user_data.email
    assert user_response.organization_id == organization_id

async def test_get_user_not_found(
    client: AsyncClient,
    common_api_client: CommonAPIClient,
) -> None:
    # Act
    response = await common_api_client.get_user(UUID("00000000-0000-0000-0000-000000000000"))

    # Assert
    assert response.status_code == 404
    error_data = response.json()
    assert error_data["detail"] == "User not found"

@pytest.fixture
async def test_user(
    session: AsyncSession,
    organization_id: UUID,
) -> AsyncGenerator[User, None]:
    user = User(
        name="Test User",
        email="<EMAIL>",
        organization_id=organization_id,
    )
    session.add(user)
    await session.commit()
    yield user
    await session.delete(user)
    await session.commit()
```

## Integration Testing

- Use CommonAPIClient for API calls
- Mock external services with VCR cassettes
- Test full request/response cycle
- Verify database state changes
- Test API contract compliance

## Example Integration Test

```python
from uuid import UUID
import pytest
from vcr import VCR
from salestech_be.tests.integration.conftest import CommonAPIClient
from salestech_be.core.user.service.user_service import UserService
from salestech_be.db.dao.user_repository import UserRepository

my_vcr = VCR(
    cassette_library_dir="tests/integration/user/cassettes",
    filter_headers=["authorization"],
)

@my_vcr.use_cassette()
async def test_user_integration_flow(
    common_api_client: CommonAPIClient,
    user_service: UserService,
    user_repository: UserRepository,
    organization_id: UUID,
) -> None:
    # Create user
    user_data = {
        "name": "Integration Test User",
        "email": "<EMAIL>",
        "organization_id": organization_id,
    }
    create_response = await common_api_client.create_user(user_data)
    user_id = create_response.json()["id"]

    # Verify in database
    user = await user_repository.find_by_id(user_id)
    assert user is not None
    assert user.name == user_data["name"]

    # Update user
    update_data = {"name": "Updated Name"}
    await common_api_client.update_user(user_id, update_data)

    # Verify update
    updated_user = await user_service.get_user(user_id)
    assert updated_user.name == update_data["name"]
```

## Unit Testing

- Test service layer logic independently
- Mock database and external dependencies
- Focus on business logic validation
- Test edge cases and error handling
- Use pytest fixtures for common setup

## Example Unit Test

```python
from uuid import UUID, uuid4
import pytest
from unittest.mock import AsyncMock, MagicMock
from salestech_be.core.user.service.user_service import UserService
from salestech_be.common.exception.exception import ResourceNotFoundError

async def test_user_service_get_user() -> None:
    # Arrange
    user_id = uuid4()
    mock_user = MagicMock(id=user_id)
    mock_repository = AsyncMock()
    mock_repository.find_by_id.return_value = mock_user

    service = UserService(repository=mock_repository)

    # Act
    result = await service.get_user(user_id)

    # Assert
    assert result == mock_user
    mock_repository.find_by_id.assert_called_once_with(user_id)

async def test_user_service_get_user_not_found() -> None:
    # Arrange
    user_id = uuid4()
    mock_repository = AsyncMock()
    mock_repository.find_by_id.return_value = None

    service = UserService(repository=mock_repository)

    # Act & Assert
    with pytest.raises(ResourceNotFoundError):
        await service.get_user(user_id)
```
