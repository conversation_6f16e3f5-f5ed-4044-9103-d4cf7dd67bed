---
description: Guidance for implementing or updating Temporal Workflow and Activity code.
globs: **/*.py
---
# Workflow & Job Processing Patterns

## Temporal Workflow System

- Use Temporal as the primary workflow and job processing system
- Implement workflows in temporal/ directory with clear activity definitions
- Available workers:
  - default_activity_worker: Handles core business logic and workflow activities
  - default_workflow_worker: Manages workflow executions and orchestration
  - meeting_worker: Processes meeting recordings, analysis, and tracking
  - calendar_worker: Handles calendar event syncing and scheduling
  - notification_worker: Manages notification delivery and task alerts
  - ai_worker: Processes AI-related tasks and pipeline intelligence
  - email_sync_worker: Handles email synchronization tasks

## Workflow Patterns

- Define workflows with @workflow.defn
- Use signals for input handling
- Implement queuing for parallel jobs
- Generate deterministic workflow IDs
- Handle timeouts and retries appropriately

## Example Workflow Implementation

```python
from temporalio import workflow
from salestech_be.core.ai.workflows.schema import IntelInput

@workflow.defn
class CloseExistingTasksWorkflow:
    def __init__(self) -> None:
        self._input_queue: list[IntelInput] = []

    @workflow.signal
    async def enqueue_input(self, workflow_input: IntelInput) -> None:
        self._input_queue.append(workflow_input)

    @workflow.run
    async def run(self) -> None:
        await workflow.wait_condition(lambda: len(self._input_queue) > 0)
        while len(self._input_queue) > 0:
            await self._process_single_input(self._input_queue.pop(0))
```

## Activity Patterns

- Define activities with @activity.defn
- Initialize services within activity scope
- Use proper error handling and retries
- Include activity timeouts
- Track metrics and logging
- Handle LLM interactions consistently

## Example Activity Implementation

```python
from temporalio import activity
from salestech_be.common.ree_llm import LLMTraceMetadata, acompletion
from salestech_be.common.langfuse.prompt_client import get_prompt_client
from salestech_be.temporal.database import get_or_init_db_engine
from salestech_be.core.ai.workflows.schema import MeetingTriggerPipelineIntelInput

@activity.defn
async def generate_pipeline_intel_tasks(
    activity_input: MeetingTriggerPipelineIntelInput,
) -> str:
    db_engine = await get_or_init_db_engine()
    context = await get_context_since_last_meeting(activity_input)

    prompt = await prompt_client.get_prompt(
        "generate_pipeline_intel_tasks",
        variables=context.model_dump(),
    )

    response = await ree_llm.acompletion(
        model="anthropic/claude-3-5-sonnet",
        messages=[{"role": "user", "content": prompt}],
        metadata=LLMTraceMetadata(
            trace_name="activity.generate_tasks",
            session_id=activity_input.langfuse_session_id,
        ),
    )
```

## Worker Configuration

- Configure task queues for specific purposes:
  - AI_TASK_QUEUE for AI/LLM operations
  - MEETING_TASK_QUEUE for meeting processing
  - DEFAULT_TASK_QUEUE for general tasks
- Set appropriate executor settings
- Handle shared state appropriately
- Configure proper resource limits
- Implement health checks

## Example Worker Configuration

```python
from concurrent.futures import ThreadPoolExecutor
from temporalio.client import Client
from temporalio.worker import Worker
from temporalio.runtime import AsyncIOExecutor
from temporalio.runtime.workflow_runner import UnsandboxedWorkflowRunner
from temporalio.runtime.shared_state_manager import SharedStateManager
from typing import Callable, Type

def create_temporal_worker(
    client: Client,
    task_queue: str,
    activities: list[Callable],
    workflows: list[Type],
) -> Worker:
    return Worker(
        client,
        task_queue=task_queue,
        workflows=workflows,
        activities=activities,
        activity_executor=AsyncIOExecutor(
            thread_pool_executor=ThreadPoolExecutor(max_workers=4)
        ),
        workflow_runner=UnsandboxedWorkflowRunner(),
        shared_state_manager=SharedStateManager(),
    )
```

## Worker Organization

- Define worker types by domain:
  - AI workers for LLM and intelligence tasks
  - Meeting workers for recording/transcription
  - Email workers for sync/processing
- Configure worker-specific settings:
  - Task queue names (AI_TASK_QUEUE, MEETING_TASK_QUEUE)
  - Resource limits
  - Retry policies

## Example Worker Implementation

```python
from salestech_be.temporal.worker import create_temporal_worker
from salestech_be.core.ai.activities import meeting_pipeline_intel_activities
from salestech_be.core.ai.workflows import trigger_pipeline_intel

async def start_ai_worker(client: Client) -> None:
    worker = await create_temporal_worker(
        client=client,
        task_queue=AI_TASK_QUEUE,
        activities=[
            meeting_pipeline_intel_activities.generate_pipeline_intel_tasks,
            company_news.get_company_news,
        ],
        workflows=[
            trigger_pipeline_intel.PipelineIntelWorkflow,
        ],
    )
    await worker.run()
```

## Some Additional Guidelines

### Use `workflow.unsafe.imports_passed_through()` to import non-Temporal packages to avoid potential memory leak. And make sure workflow code and activity code are in different files.

- Bad Example:
```python
from pydantic import BaseModel
from temporalio import activity, workflow

class SomeModel(BaseModel):
    # omitted

@activity.defn
async def do_something(url: str) -> str:
    return f"did something with {url}"```

@workflow.defn
class DemoWorkflow:
    # omitted
```

- Good Example:
```python
## activity.py file

from pydantic import BaseModel
from temporalio import activity

class SomeModel(BaseModel):
    # omitted

@activity.defn
async def do_something(url: str) -> str:
    return f"did something with {url}"```
```
```python
## workflow.py file

from temporalio import workflow
with workflow.unsafe.imports_passed_through():
    from .activity import do_something

@workflow.defn
class DemoWorkflow:
    @workflow.run
    async def run(self, data: int) -> str:
        await workflow.execute_activity(
            do_something,
            ...
        )
```
