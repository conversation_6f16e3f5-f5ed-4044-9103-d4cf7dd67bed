import glob
import json
from pathlib import Path

if __name__ == "__main__":
    durations = {}

    # Find all pytest duration split folders
    split_folders = glob.glob("pytest-durations-split-*")

    # Merge all duration data
    for folder in split_folders:
        duration_file = Path(folder) / ".test_durations"
        if duration_file.exists():
            with open(duration_file) as f:
                durations.update(json.load(f))

    # Write merged durations
    with open(".test_durations", "w") as f:
        json.dump(durations, f)
