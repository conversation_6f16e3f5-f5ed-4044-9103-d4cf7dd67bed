name: "Daily Src Backup"

on:
  schedule:
  - cron: '0 0 * * *'   # Runs at midnight every day
  workflow_dispatch: # Allows manual triggering of the workflow


run-name: Backup - Commit ${{ github.sha }}

jobs:
  backup:
    runs-on: ubuntu-latest

    steps:

    - name: "👷‍♂️ Checkout Code"
      uses: actions/checkout@v4
      with:
        fetch-depth: "0"

    - name: Configure AWS Credentials
      uses: aws-actions/configure-aws-credentials@v2
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: us-west-2

    - name: Create backup archive
      run: |
        zip -r backup.zip .

    - name: Upload to S3
      run: |
        BACKUP_DATE=$(date +'%Y-%m-%d')
        aws s3 cp backup.zip s3://reevo-backup/github/"${BACKUP_DATE}-${{ github.repository }}".zip
