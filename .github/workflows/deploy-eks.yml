name: Deploy Sales Tech BE - EKS (Deprecated)

on:
  workflow_dispatch:
    inputs:
      semver:
        description: "Which version you want to increment? Use major, minor or patch"
        required: true
        default: "patch"
        type: choice
        options:
        - "patch"
        - "minor"
        - "major"
      aws_account_id:
        description: "ZV AWS account ID"
        required: true
        default: "************"
      env:
        description: "Environment to deploy to"
        required: true
        default: "dev"
        type: choice
        options:
        - "dev"
        - "prod"

env:
  AWS_REGION: us-west-2
  ECR_REPO: reevo-ecr/salestech-be
  CLUSTER_NAME: reevo-${{ github.event.inputs.env }}-eks

concurrency: production-deploy
run-name: ${{ github.event.inputs.env || 'unknown' }} deployment - Commit ${{ github.sha }}

jobs:
  deploy-to-prototype-server:
    runs-on: ubuntu-latest
    services:
      registry:
        image: registry:2
        ports:
        - 5000:5000
    permissions:
      contents: write
    env:
      aws_account_id: ${{ github.event.inputs.aws_account_id }}
    steps:

    # Checkout sources
    - name: "👷‍♂️ Checkout Code"
      uses: actions/checkout@v4
      with:
        fetch-depth: "0"

    # Get current latest version
    - name: Get current latest version
      uses: actions-ecosystem/action-get-latest-tag@v1
      id: get-latest-tag
      # output: ${{ steps.get-latest-tag.outputs.tag }}

    # Generate potentially new versions: if no new commit, no new version
    - name: Generate new version
      id: semverGenerate
      uses: anothrNick/github-tag-action@1.69.0   # Don't use @master or @v1 unless you're happy to test the latest version
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}   # if you don't want to set write permissions use a PAT token
        WITH_V: true
        DRY_RUN: true
        VERBOSE: true
        DEFAULT_BUMP: ${{ github.event.inputs.semver }}
        BRANCH_HISTORY: last
      # output: ${{ steps.semverGenerate.outputs.new_tag }}
      # note: ${{ steps.semverGenerate.outputs.tag }} is actually latest tag after this
      # action, so, not very useful

    - name: Check if new tag is same as current tag
      id: tagCheck
      run: |
        if [ "${{ steps.semverGenerate.outputs.new_tag }}" == "${{ steps.get-latest-tag.outputs.tag }}" ]; then
          echo "skip=true" >> $GITHUB_OUTPUT
        else
          echo "skip=false" >> $GITHUB_OUTPUT
        fi

    - name: Configure AWS Credentials
      uses: aws-actions/configure-aws-credentials@v2
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ env.AWS_REGION }}

    - name: Login to Amazon ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v2
      with:
        registries: ${{ inputs.aws_account_id }}
        mask-password: "true"   # see: https://github.com/aws-actions/amazon-ecr-login#docker-credentials

    # set up the img env for build (optional) and deployment
    - name: Build image config
      id: build-config
      shell: bash
      env:
        ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        ECR_REPOSITORY: ${{ env.ECR_REPO }}
        IMAGE_EKS_TAG: salestech-be-eks-${{ steps.semverGenerate.outputs.new_tag }}
      run: |
        echo "ecr_repository=$ECR_REPOSITORY"
        echo "image_eks_tag=$IMAGE_EKS_TAG" >> $GITHUB_OUTPUT
        echo "full_eks_image=$ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_EKS_TAG" >> $GITHUB_OUTPUT

    - name: Skip steps message
      if: steps.tagCheck.outputs.skip == 'true'
      run: |
        echo "The new tag is the same as the current tag. Skipping steps..."

    - name: Echo new img tag
      if: steps.tagCheck.outputs.skip == 'false'
      run: |
        echo "The next tag version will be: ${{ steps.semverGenerate.outputs.new_tag }}"

    - name: "👷‍♂️ Setup Python"
      if: steps.tagCheck.outputs.skip == 'false'
      uses: actions/setup-python@v5
      with:
        python-version: "3.12"

    - name: Install jq
      if: steps.tagCheck.outputs.skip == 'false'
      run: sudo apt-get -y install jq

    # build a new docker img with new tag, otherwise, skip to use existing one
    - name: Build, tag, and push image to Amazon ECR
      if: steps.tagCheck.outputs.skip == 'false'
      id: build-publish
      shell: bash
      env:
        ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        ECR_REPOSITORY: ${{ env.ECR_REPO }}
        IMAGE_EKS_TAG: salestech-be-eks-${{ steps.semverGenerate.outputs.new_tag }}
      run: |
        echo "IMAGE $IMAGE_TAG is being uploaded to $ECR_REGISTRY/$ECR_REPOSITORY"
        docker build . -f "deploy/eks.Dockerfile" -t "$ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_EKS_TAG"
        docker push "$ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_EKS_TAG"

    # Push release into github
    - name: Bump version and push tag
      if: steps.tagCheck.outputs.skip == 'false'
      uses: anothrNick/github-tag-action@1.69.0   # Don't use @master or @v1 unless you're happy to test the latest version
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}   # if you don't want to set write permissions use a PAT token
        WITH_V: true
        DEFAULT_BUMP: ${{ github.event.inputs.semver }}

    - name: Run DB Migration Job
      run: |
        export JOB_MANIFEST_NAME=deploy/kubernetes/db.migration.${{ github.event.inputs.env }}.job.yaml
        export JOB_RUN_NAME=database-migration-${{steps.build-config.outputs.image_eks_tag}}

        # get eks kube creds
        aws eks update-kubeconfig --name ${{ env.CLUSTER_NAME }} --region ${{ env.AWS_REGION }}

        # interpolate k8s job fields
        yq e -i '.metadata.name = env(JOB_RUN_NAME)' $JOB_MANIFEST_NAME
        yq e -i '.spec.template.spec.containers[0].image = "${{steps.build-config.outputs.full_eks_image}}"' $JOB_MANIFEST_NAME

        # run the migration job
        kubectl apply -f $JOB_MANIFEST_NAME --validate=false

        # check exit status, wait for job until timeout
        # kubectl wait will return exit code 1 after timeout, use `|| true` so github action is not exiting rightaway
        kubectl wait --for=condition=complete job/$JOB_RUN_NAME -n salestech --timeout=10m || true

        # Fetch the job status
        JOB_STATUS=$(kubectl get job/$JOB_RUN_NAME -n salestech -o jsonpath='{.status.succeeded}')

        # If the job succeeded, it will have succeeded count == 1
        if [ "$JOB_STATUS" -eq 1 ]; then
          echo "Database migration completed successfully."
        else
          echo "Database migration failed or timed out. Printing logs..."
          # Print the logs to help diagnose the issue
          kubectl logs job/$JOB_RUN_NAME -n salestech
          exit 1
        fi

    # move down below DB migration, reduce the secret leak (prints of gha of env to logs)
    - name: Set up kubeconfig for helm deploy
      run: |
        aws eks update-kubeconfig --name ${{ env.CLUSTER_NAME }} --region ${{ env.AWS_REGION }}  --kubeconfig ./kube/config
        echo 'KUBE_CONFIG_DATA<<EOF' >> $GITHUB_ENV
        echo $(cat ./kube/config | base64) >> $GITHUB_ENV
        echo 'EOF' >> $GITHUB_ENV

    - name: Helm deploy - API
      uses: koslib/helm-eks-action@master
      env:
        KUBE_CONFIG_DATA: ${{ env.KUBE_CONFIG_DATA }}
      with:
        command: |
          helm repo add gruntwork-io https://helmcharts.gruntwork.io/
          helm upgrade salestech-be-api gruntwork-io/k8s-service -f deploy/helm/${{ github.event.inputs.env }}/api.${{ github.event.inputs.env }}.values.yaml --set containerImage.tag=${{steps.build-config.outputs.image_eks_tag}} -n salestech --create-namespace --wait --timeout=20m

    - name: Helm deploy - Webhook
      uses: koslib/helm-eks-action@master
      env:
        KUBE_CONFIG_DATA: ${{ env.KUBE_CONFIG_DATA }}
      with:
        command: |
          helm repo add gruntwork-io https://helmcharts.gruntwork.io/
          helm upgrade salestech-be-webhook gruntwork-io/k8s-service -f deploy/helm/${{ github.event.inputs.env }}/webhook.${{ github.event.inputs.env }}.values.yaml --set containerImage.tag=${{steps.build-config.outputs.image_eks_tag}} -n salestech --create-namespace --wait --timeout=10m

    - name: Helm deploy - Scheduler
      uses: koslib/helm-eks-action@master
      env:
        KUBE_CONFIG_DATA: ${{ env.KUBE_CONFIG_DATA }}
      with:
        command: |
          helm repo add gruntwork-io https://helmcharts.gruntwork.io/
          helm upgrade salestech-be-scheduler gruntwork-io/k8s-service -f deploy/helm/${{ github.event.inputs.env }}/scheduler.${{ github.event.inputs.env }}.values.yaml --set containerImage.tag=${{steps.build-config.outputs.image_eks_tag}} -n salestech --create-namespace --wait --timeout=10m

    - name: Helm deploy - Worker
      uses: koslib/helm-eks-action@master
      env:
        KUBE_CONFIG_DATA: ${{ env.KUBE_CONFIG_DATA }}
      with:
        command: |
          helm repo add gruntwork-io https://helmcharts.gruntwork.io/
          helm upgrade salestech-be-worker gruntwork-io/k8s-service -f deploy/helm/${{ github.event.inputs.env }}/worker.${{ github.event.inputs.env }}.values.yaml --set containerImage.tag=${{steps.build-config.outputs.image_eks_tag}} -n salestech --create-namespace --wait --timeout=10m

    - name: Helm deploy - Temporal Worker
      uses: koslib/helm-eks-action@master
      env:
        KUBE_CONFIG_DATA: ${{ env.KUBE_CONFIG_DATA }}
      with:
        command: |
          helm repo add gruntwork-io https://helmcharts.gruntwork.io/
          helm upgrade --install temporal-worker gruntwork-io/k8s-service -f deploy/helm/${{ github.event.inputs.env }}/temporal-worker.${{ github.event.inputs.env }}.values.yaml --set containerImage.tag=${{steps.build-config.outputs.image_eks_tag}} -n temporal --create-namespace --wait

    - name: Helm deploy - Event Processor Group A
      uses: koslib/helm-eks-action@master
      env:
        KUBE_CONFIG_DATA: ${{ env.KUBE_CONFIG_DATA }}
      with:
        command: |
          helm repo add gruntwork-io https://helmcharts.gruntwork.io/
          helm upgrade --install salestech-be-event-processor-a gruntwork-io/k8s-service -f deploy/helm/${{ github.event.inputs.env }}/event.${{ github.event.inputs.env }}.values.yaml --set containerImage.tag=${{steps.build-config.outputs.image_eks_tag}} --set envVars.SALESTECH_EVENT_GROUP_ID=event_group_a -n salestech --create-namespace --wait

  slack-notification:
    needs: deploy-to-prototype-server
    if: always()
    uses: ./.github/workflows/slack.yml
    secrets: inherit
    with:
      name: Deploy
      status: ${{ needs.deploy-to-prototype-server.result }}
