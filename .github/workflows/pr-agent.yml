on:
  pull_request:
    types: [opened, reopened, ready_for_review, edited]
  issue_comment:
jobs:
  pr_agent_job:
    if: ${{ github.event.sender.type != 'Bot' && !contains(github.event.pull_request.title, 'wip') && !contains(github.event.pull_request.title, 'WIP') }}
    runs-on: ubuntu-latest
    permissions:
      issues: write
      pull-requests: write
      contents: write
    name: Run pr agent on every pull request, respond to user comments
    steps:
    - name: PR Agent action step
      id: pragent
      uses: docker://codiumai/pr-agent:0.29-github_action
      env:
        ANTHROPIC.KEY: ${{ secrets.ANTHROPIC_KEY }}
        OPENAI_KEY: ${{ secrets.OPENAI_KEY }}
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
