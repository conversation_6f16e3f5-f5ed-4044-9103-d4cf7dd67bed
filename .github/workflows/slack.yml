name: DMs Slack user using email address

on:
  workflow_call:
    inputs:
      name:
        description: Name of the completed workflow to alert for
        type: string
        required: true
      status:
        description: Status of the completed workflow to alert for
        type: string
        required: true

jobs:
  slack-notification:
    timeout-minutes: 1
    runs-on: ubuntu-latest
    steps:
    - name: Find GitHub email
      continue-on-error: true
      id: gh-email
      uses: evvanErb/get-github-email-by-username-action@v2.0
      with:
        github-username: ${{ github.actor }}
        token: ${{ secrets.GH_TOKEN_TO_FIND_GH_USER_EMAILS }}
    - name: Find Slack user
      if: steps.gh-email.outcome == 'success'
      continue-on-error: true
      id: slack-user
      uses: scribd/find-slack-user-action@v1
      with:
        slack-token: ${{ secrets.SLACK_BOT_TOKEN }}
        email: ${{ steps.gh-email.outputs.email }}
    - name: Send Slack DM
      if: steps.gh-email.outcome == 'success' && steps.slack-user.outcome == 'success'
      continue-on-error: true
      uses: slackapi/slack-github-action@v1.26.0
      with:
        channel-id: ${{ steps.slack-user.outputs.member-id }}
        payload: >-
          {
            "text": "${{ inputs.name }} status: ${{ inputs.status }}",
            "blocks": [
              {
                "type": "section",
                "text": {
                  "type": "mrkdwn",
                  "text": "${{ inputs.name }} status: ${{ inputs.status }}"
                },
                "accessory": {
                  "type": "button",
                  "text": {
                    "type": "plain_text",
                    "text": "More Details",
                    "emoji": true
                  },
                  "url": "${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"
                }
              }
            ]
          }
      env:
        SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}
    - name: No-op
      run: echo "done"
