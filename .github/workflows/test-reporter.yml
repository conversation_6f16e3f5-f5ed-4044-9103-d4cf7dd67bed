name: Publish test report with comments

on:
  workflow_call:
    inputs:
      comment-header:
        type: string
        default: reports
      comment-message-recreate:
        type: string
        default: |
          ## 🚦Reports for run [#${{ github.run_number }}](${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }})🚦
          Reports will be posted here as they get available.
      comment-message-success:
        type: string
        default: |
          ### 🥳 {0} passed

          | Passed | Failed | Skipped |
          |--------|--------|---------|
          | ✅ {1} | ❌ {2} | ⚠️ {3}   |

          You can see the report [here]({4}).
      comment-message-failure:
        type: string
        default: |
          ### 😔 pytest failed

          | Passed | Failed | Skipped |
          |--------|--------|---------|
          | ✅ {1} | ❌ {2} | ⚠️ {3}   |

          You can see the report [here]({4}).
      download-artifact-pattern:
        type: string
      download-artifact-merge-multiple:
        type: string
        default: 'false'
      report-name:
        type: string
      report-path:
        type: string
        default: '**/*.xml'
      report-reporter:
        type: string

jobs:
  test-reporter:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout
      uses: actions/checkout@v4
    - name: Download Test Report Artifacts
      uses: actions/download-artifact@v4
      with:
        pattern: ${{ inputs.download-artifact-pattern }}
        merge-multiple: ${{ inputs.download-artifact-merge-multiple }}
    - name: Generate Test Report
      # depandabot wont have permission for certain github APIs like checkrun, for
      # security reasons. We do not need to generate a test report nescessarily for
      # dependabot opened PRs
      if: github.actor != 'dependabot[bot]'
      id: generate-report
      uses: phoenix-actions/test-reporting@v15
      with:
        fail-on-error: false
        name: ${{ inputs.report-name }} Report
        path: ${{ inputs.report-path }}
        reporter: ${{ inputs.report-reporter }}
        token: ${{ github.token }}
    - name: Update PR status comment on success
      uses: marocchino/sticky-pull-request-comment@v2
      if: ${{ steps.generate-report.outputs.conclusion == 'success' }}
      with:
        hide_and_recreate: true
        hide_classify: 'OUTDATED'
        header: ${{ inputs.comment-header }}
        message: ${{ format(inputs.comment-message-success, inputs.report-name, steps.generate-report.outputs.passed, steps.generate-report.outputs.failed, steps.generate-report.outputs.skipped, steps.generate-report.outputs.runHtmlUrl) }}
        GITHUB_TOKEN: ${{ github.token }}
    - name: Update PR status comment on failure
      uses: marocchino/sticky-pull-request-comment@v2
      if: ${{ steps.generate-report.outputs.conclusion == 'failure' }}
      with:
        hide_and_recreate: true
        hide_classify: 'OUTDATED'
        header: ${{ inputs.comment-header }}
        message: ${{ format(inputs.comment-message-failure, inputs.report-name, steps.generate-report.outputs.passed, steps.generate-report.outputs.failed, steps.generate-report.outputs.skipped, steps.generate-report.outputs.runHtmlUrl) }}
        GITHUB_TOKEN: ${{ github.token }}
