name: Testing salestech_be

on:
  pull_request:
  push:
    branches:
    - main

env:
  AWS_REGION: us-west-2
  AWS_ACCOUNT_ID: "************"

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  code-checks:
    timeout-minutes: 5
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        ref: ${{ github.event.pull_request.head.sha }}
    - name: Set up python
      id: setup-python
      uses: actions/setup-python@v5
      with:
        python-version: "3.12.3"
    - name: Install uv
      run: curl -LsSf https://astral.sh/uv/install.sh | sh
    - name: Install project
      run: uv sync --locked --group dev
      env:
        UV_SYSTEM_PYTHON: 1
    - name: Run dependency architecture check
      run: uv run tach check
    - name: Run deptry check
      run: uv run deptry .
    - name: Run ruff format check
      run: uv run ruff format --check .
    - name: Run ruff lint check
      run: uv run ruff check --no-fix
    - name: Run mypy check
      run: uv run mypy .

  pytest_unsharded:
    timeout-minutes: 20

    # NOTE: if you change the runner, remmeber to check whether those runners are within aws
    # region or not. If it's within our own VPC (like self-hosted), better to use
    # our ECR to avoid data transfer cost
    runs-on: warp-ubuntu-latest-x64-4x
    steps:
    - name: Configure AWS Credentials
      uses: aws-actions/configure-aws-credentials@v2
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ env.AWS_REGION }}
    - name: Login to Amazon ECR
      uses: aws-actions/amazon-ecr-login@v2
      with:
        registries: ${{ env.AWS_ACCOUNT_ID }}
    - uses: actions/checkout@v4
      with:
        ref: ${{ github.event.pull_request.head.sha }}
    - name: Start dependencies
      run: docker compose --project-directory . -f deploy/docker-compose.dep.yml up --quiet-pull --build --detach --wait
    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: "3.12.3"
    - name: Install uv
      run: curl -LsSf https://astral.sh/uv/install.sh | sh
    - name: Install project
      run: uv sync --locked --group dev
      env:
        UV_SYSTEM_PYTHON: 1
    - name: Run db migration
      run: uv run alembic upgrade head
    - name: Run bootstrap
      run: docker compose --project-directory . -f deploy/docker-compose.bootstrap.yml run salestech_be-bootstrap --quiet-pull --build
    - name: Refresh test-durations from s3
      continue-on-error: true   # make it non-blocking step
      run: aws s3 cp s3://reevo-salestech-be-pytest-durations/.test_durations .test_durations
      env:
        AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
        AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        AWS_DEFAULT_REGION: us-west-2   # default region for the bucket
    - name: Run pytest check
          # make sure you have splits according to the number of gha matrix groups.
          # if you want to have gh branch protection, make sure you update the blocking steps in the pipeline
      run: |
        uv run pytest -vv --record-mode=none --durations=0 \
        --junitxml=pytest-results-unsharded.xml \
        tests/integration/core/workflow/test_workflow_logic_evaluator.py \
        tests/integration/web/api/ai/test_meeting_controller.py \
        tests/integration/web/api/calendar/test_controller.py \
        tests/integration/web/api/search/test_search_endpoint.py \
        tests/integration/web/test_routers.py \
        --store-durations
      env:
        SALESTECH_BE_HOST: "0.0.0.0"
    - name: Upload results
      uses: actions/upload-artifact@v4
      if: always()
      with:
          # this is unsharded run, but gha matrix starts with 1. so you can do 0 here
        name: pytest-results-0
        path: pytest-results-0.xml
        retention-days: 1
    - name: Upload test durations
      uses: actions/upload-artifact@v4
      with:
        name: pytest-durations-split-0
        path: .test_durations
        include-hidden-files: true
        retention-days: 1
  pytest_sharded:
    timeout-minutes: 20
    strategy:
      fail-fast: false
      matrix:
        # if you change this list, remember to change the pytest run split and
        # test durations merges
        group: [1, 2, 3, 4, 5, 6, 7]
    # NOTE: if you change the runner, remmeber to check whether those runners are within aws
    # region or not. If it's within our own VPC (like self-hosted), better to use
    # our ECR to avoid data transfer cost
    runs-on: warp-ubuntu-latest-x64-8x
    steps:
    - name: Configure AWS Credentials
      uses: aws-actions/configure-aws-credentials@v2
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ env.AWS_REGION }}
    - name: Login to Amazon ECR
      uses: aws-actions/amazon-ecr-login@v2
      with:
        registries: ${{ env.AWS_ACCOUNT_ID }}
    - uses: actions/checkout@v4
      with:
        ref: ${{ github.event.pull_request.head.sha }}
    - name: Start dependencies
      run: docker compose --project-directory . -f deploy/docker-compose.dep.yml up --quiet-pull --build --detach --wait
    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: "3.12.3"
    - name: Install uv
      run: curl -LsSf https://astral.sh/uv/install.sh | sh
    - name: Install project
      run: uv sync --locked --group dev
      env:
        UV_SYSTEM_PYTHON: 1
    - name: Apply migrations
      run: uv run alembic upgrade head
    - name: Run bootstrap
      run: docker compose --project-directory . -f deploy/docker-compose.bootstrap.yml run salestech_be-bootstrap --quiet-pull --build
    - name: Refresh test-durations from s3
      continue-on-error: true   # make it non-blocking step
      run: aws s3 cp s3://reevo-salestech-be-pytest-durations/.test_durations .test_durations
      env:
        AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
        AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        AWS_DEFAULT_REGION: us-west-2   # default region for the bucket
    - name: Run pytest check
          # make sure you have splits according to the number of gha matrix groups.
          # if you want to have gh branch protection, make sure you update the blocking steps in the pipeline
      run: |
        # Run with duration-based splitting
        set +e
        uv run pytest -vv --record-mode=none --durations=0 --splits 7 --group ${{ matrix.group }} \
        --junitxml=pytest-results-${{ matrix.group }}.xml . \
        --ignore=tests/integration/core/workflow/test_workflow_logic_evaluator.py \
        --ignore=tests/integration/web/api/ai/test_meeting_controller.py \
        --ignore=tests/integration/web/api/contact/test_import_contact.py \
        --ignore=tests/integration/web/api/calendar/test_controller.py \
        --ignore=tests/integration/web/api/ai/test_meeting_chat_controller.py \
        --ignore=tests/integration/web/api/search/test_search_endpoint.py \
        --ignore=tests/integration/web/test_routers.py \
        --store-durations

        EXIT_CODE=$?
        if [ $EXIT_CODE -eq 5 ]; then
          echo "No tests found for shard ${{ matrix.group }}, marking as success"
          exit 0
        elif [ $EXIT_CODE -ne 0 ]; then
          echo "Tests failed for shard ${{ matrix.group }} with exit code $EXIT_CODE"
          exit $EXIT_CODE
        fi
        set -e
      env:
        SALESTECH_BE_HOST: "0.0.0.0"
    - name: Upload results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: pytest-results-${{ matrix.group }}
        path: pytest-results-${{ matrix.group }}.xml
        retention-days: 1
    - name: Upload test durations
      uses: actions/upload-artifact@v4
      with:
        name: pytest-durations-split-${{ matrix.group }}
        path: .test_durations
        include-hidden-files: true
        retention-days: 1

  pytest-success:
    name: All Tests Passed
    needs:
    - code-checks
    - pytest_sharded
    - pytest_unsharded
    runs-on: ubuntu-latest
    if: always()
    steps:
    - name: Check test results
      run: |
        if [ "${{ needs.pytest_sharded.result }}" != "success" ] || [ "${{ needs.pytest_unsharded.result }}" != "success" ] || [ "${{ needs.code-checks.result }}" != "success" ]; then
          echo "Some tests failed"
          exit 1
        fi
        echo "All tests passed"

  pytest-reporter:
    needs:
    - pytest-success
    if: always()
    uses: ./.github/workflows/test-reporter.yml
    with:
      download-artifact-pattern: pytest-results-*
      download-artifact-merge-multiple: true
      report-name: pytest
      report-reporter: java-junit

  upload-test-durations:
    needs:
    - pytest-success
    runs-on: ubuntu-latest
    steps:
      # upload the test durations at 1% chance
      # we do not need a frequent refresh
    - name: Sampling
      run: |
        if [[ $((RANDOM%100)) -eq 0 ]]; then
          echo "PROCEED=true" >> $GITHUB_ENV
        else
          echo "PROCEED=false" >> $GITHUB_ENV
        fi
    - if: env.PROCEED == 'true'
      uses: actions/checkout@v4
      with:
        ref: ${{ github.event.pull_request.head.sha }}
    - if: env.PROCEED == 'true'
      name: Fetch test-durations
      uses: actions/download-artifact@v4
      with:
        pattern: pytest-durations-split-*
    - if: env.PROCEED == 'true'
      name: Combine test-durations
      run: python3 .github/scripts/merge_test_durations.py
          # upload test durations file (github hosted runner has aws cli)
    - if: env.PROCEED == 'true'
      name: Upload test-durations to s3
      run: aws s3 cp .test_durations s3://reevo-salestech-be-pytest-durations/.test_durations
      env:
        AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
        AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        AWS_DEFAULT_REGION: us-west-2   # default region for the bucket

  slack-notification:
    needs:
    - pytest-reporter
    - pytest-success
    if: always()
    uses: ./.github/workflows/slack.yml
    secrets: inherit
    with:
      name: BE CI
      status: ${{ needs.pytest-success.result }}
