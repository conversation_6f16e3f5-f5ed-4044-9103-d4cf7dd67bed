FROM debian:bookworm-slim AS uv

# Install uv by copying from the official image
COPY --from=ghcr.io/astral-sh/uv:latest /uv /uvx /bin/


FROM uv AS builder

# Install build dependencies
RUN apt-get update && apt-get install \
  --no-install-recommends -y \
  gcc=4:12.2.0-3 procps=2:4.0.2-3 ffmpeg build-essential git ca-certificates \
  && rm -rf /var/lib/apt/lists/*

# Configure uv for optimal Docker usage
ENV UV_COMPILE_BYTECODE=1
ENV UV_LINK_MODE=copy

# Set up the working directory
WORKDIR /app/src

# Copy only the files needed for dependency installation first
COPY pyproject.toml .

# Install dependencies
RUN --mount=type=cache,target=/root/.cache/uv \
  uv sync --no-install-project

# Copy the rest of the application
COPY . .

# Install playwright dependencies for chromium
RUN uv run playwright install --with-deps chromium


# Create the final image with minimal dependencies
FROM uv AS base

# Install runtime dependencies
RUN apt-get update && apt-get install \
  --no-install-recommends -y \
  procps=2:4.0.2-3 ca-certificates \
  && rm -rf /var/lib/apt/lists/*

# Copy the installed packages and application from the builder
COPY --from=builder /root/.local/share/uv/python/ /root/.local/share/uv/python/
COPY --from=builder /usr/local/bin /usr/local/bin
COPY --from=builder /app/src /app/src

# Copy the playwright installation
COPY --from=builder /root/.cache/ms-playwright /root/.cache/ms-playwright

# Set the working directory
WORKDIR /app/src

FROM base AS prod
CMD ["/usr/bin/uv", "run", "python", "-m", "salestech_be"]

FROM base AS worker
CMD ["/usr/bin/uv", "run", "dramatiq", "salestech_be.task.task_definition"]

FROM base AS scheduler
CMD ["/usr/bin/uv", "run", "python", "-m", "salestech_be.task.instantiate.scheduler"]

FROM base AS temporal_worker
CMD ["/usr/bin/uv", "run", "python", "-m", "salestech_be.temporal.worker"]

FROM base AS event_processor
CMD ["/usr/bin/uv", "run", "python", "-m", "salestech_be.event.processor"]

FROM base AS dev
# No need to reinstall dev dependencies as they're already included in the base image
CMD ["/usr/bin/uv", "run", "python", "-m", "salestech_be"]
