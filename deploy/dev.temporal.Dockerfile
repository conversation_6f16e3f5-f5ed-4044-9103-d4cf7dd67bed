FROM debian:bookworm

# Install necessary tools
RUN apt-get update && apt-get install -y \
    curl \
    tar \
    && rm -rf /var/lib/apt/lists/*

# Download and install Temporal CLI
RUN curl -sSL https://temporal.download/cli/archive/latest?platform=linux\&arch=amd64 -o temporal.tar.gz \
    && tar -xzf temporal.tar.gz \
    && mv temporal /usr/local/bin/ \
    && rm temporal.tar.gz

# Verify installation
RUN temporal --version

# Set the working directory
WORKDIR /data

# Expose ports
EXPOSE 7233 8233

# Add volume for persistent data
VOLUME /data

# Set the entrypoint to run Temporal server in dev mode
ENTRYPOINT ["temporal", "server", "start-dev", "--db-filename", "/data/temporal.db", "--ip", "0.0.0.0"]
