include:
- path: deploy/docker-compose.dep.yml
  project_directory: .

services:
  salestech_be-bootstrap:
    depends_on:
      salestech_be-db:
        condition: service_healthy
      salestech_be-materialize:
        condition: service_healthy
      salestech_be-kafka:
        condition: service_healthy
      salestech_be-debezium:
        condition: service_healthy
    build:
      context: .
      dockerfile: deploy/dev.bootstrap.Dockerfile
    restart: no
