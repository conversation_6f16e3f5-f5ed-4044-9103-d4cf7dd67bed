services:
  migrator:
    environment:
      SALESTECH_BE_ENVIRONMENT: local

  api:
    ports:
      # Exposes application port.
    - "8000:8000"
    build:
      target: dev
    volumes:
      # Adds current directory as volume.
    - .:/app/src/
    environment:
      # Enables autoreload.
      SALESTECH_BE_RELOAD: "True"

  worker:
    build:
      target: worker
    volumes:
      # Adds current directory as volume.
    - .:/app/src/
    environment:
      # Enables autoreload.
      SALESTECH_BE_RELOAD: "True"

  scheduler:
    build:
      target: scheduler
    volumes:
      # Adds current directory as volume.
    - .:/app/src/
    environment:
      # Enables autoreload.
      SALESTECH_BE_RELOAD: "True"

  event_processor:
    build:
      target: event_processor
    volumes:
      # Adds current directory as volume.
    - .:/app/src/
    environment:
      # Enables autoreload.
      SALESTECH_BE_RELOAD: "True"
