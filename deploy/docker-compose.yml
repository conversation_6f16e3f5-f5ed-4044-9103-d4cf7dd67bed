include:
- path: deploy/docker-compose.bootstrap.yml
  project_directory: .

services:
  api:
    build:
      context: .
      dockerfile: ./deploy/Dockerfile
      target: prod
    image: salestech_be_api:${SALESTECH_BE_VERSION:-latest}
    restart: always
    env_file:
    - .env
    labels:
      # Enables traefik for this container.
    - traefik.enable=true
    - traefik.http.routers.salestech_be.rule=Host(`${SALESTECH_BE_TRAEFIK_HOST:-salestech_be.localhost}`)
    - traefik.http.routers.salestech_be.entrypoints=http
    - traefik.http.routers.salestech_be.service=salestech_be
    - traefik.http.services.salestech_be.loadbalancer.server.port=${SALESTECH_BE_PORT:-8000}
    networks:
    - default
    - traefik-shared
    depends_on:
      salestech_be-db:
        condition: service_healthy
      salestech_be-redis:
        condition: service_healthy
      salestech_be-temporal:
        condition: service_healthy
      salestech_be-kafka:
        condition: service_healthy
      salestech_be-es:
        condition: service_healthy
      salestech_be-falkordb:
        condition: service_healthy
      salestech_be-bootstrap:
        condition: service_completed_successfully
    ports:
    - "8080:8000"
    environment:
      SALESTECH_BE_HOST: 0.0.0.0
      SALESTECH_BE_DB_HOST: salestech_be-db
      SALESTECH_BE_REDIS_HOST: salestech_be-redis
      SALESTECH_BE_TEMPORAL_HOST_URL: salestech_be-temporal:7233
      SALESTECH_BE_KAFKA_KAFKA_BOOTSTRAP_SERVERS: '["salestech_be-kafka:9092"]'
      SALESTECH_BE_SENDGRID_API_KEY: placeholder

  worker:
    build:
      context: .
      dockerfile: ./deploy/Dockerfile
      target: worker
    image: salestech_be_worker:${SALESTECH_BE_VERSION:-latest}
    restart: always
    env_file:
    - .env
    depends_on:
    - api
    environment:
      SALESTECH_BE_DB_HOST: salestech_be-db
      SALESTECH_BE_REDIS_HOST: salestech_be-redis
      SALESTECH_BE_TEMPORAL_HOST_URL: salestech_be-temporal:7233
      SALESTECH_BE_KAFKA_KAFKA_BOOTSTRAP_SERVERS: '["salestech_be-kafka:9092"]'
      SALESTECH_BE_SENDGRID_API_KEY: placeholder

  scheduler:
    build:
      context: .
      dockerfile: ./deploy/Dockerfile
      target: scheduler
    image: salestech_be_scheduler:${SALESTECH_BE_VERSION:-latest}
    restart: always
    env_file:
    - .env
    depends_on:
    - api
    environment:
      SALESTECH_BE_DB_HOST: salestech_be-db
      SALESTECH_BE_REDIS_HOST: salestech_be-redis
      SALESTECH_BE_TEMPORAL_HOST_URL: salestech_be-temporal:7233
      SALESTECH_BE_KAFKA_KAFKA_BOOTSTRAP_SERVERS: '["salestech_be-kafka:9092"]'
      SALESTECH_BE_SENDGRID_API_KEY: placeholder

  temporal_worker:
    build:
      context: .
      dockerfile: ./deploy/Dockerfile
      target: temporal_worker
    image: salestech_be_temporal_worker:${SALESTECH_BE_VERSION:-latest}
    restart: always
    env_file:
    - .env
    depends_on:
    - api
    environment:
      SALESTECH_BE_DB_HOST: salestech_be-db
      SALESTECH_BE_REDIS_HOST: salestech_be-redis
      SALESTECH_BE_TEMPORAL_HOST_URL: salestech_be-temporal:7233
      SALESTECH_BE_KAFKA_KAFKA_BOOTSTRAP_SERVERS: '["salestech_be-kafka:9092"]'
      SALESTECH_BE_SENDGRID_API_KEY: placeholder

  event_processor:
    build:
      context: .
      dockerfile: ./deploy/Dockerfile
      target: event_processor
    image: salestech_be_event_processor:${SALESTECH_BE_VERSION:-latest}
    restart: always
    env_file:
    - .env
    depends_on:
    - api
    environment:
      SALESTECH_BE_DB_HOST: salestech_be-db
      SALESTECH_BE_REDIS_HOST: salestech_be-redis
      SALESTECH_BE_TEMPORAL_HOST_URL: salestech_be-temporal:7233
      SALESTECH_BE_KAFKA_KAFKA_BOOTSTRAP_SERVERS: '["salestech_be-kafka:9092"]'
      SALESTECH_BE_SENDGRID_API_KEY: placeholder

networks:
  # Network for traefik.
  traefik-shared:
    name: traefik-shared
