applicationName: falkor-cdc-index

replicaCount: 1

serviceAccount:
  name: falkor-cdc
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/reevo-dev-eks-reevo-be-falkor-cdc-iam-role

containerImage:
  repository: ************.dkr.ecr.us-west-2.amazonaws.com/reevo-ecr/be
  tag: be-eks-v4.1.3
  pullPolicy: IfNotPresent

envVars:
  # sensitive info should go into parameter store, and use chamber to load

additionalContainerEnv:
- name: SALESTECH_BE_DD_AGENT_HOST
  valueFrom:
    fieldRef:
      fieldPath: status.hostIP

containerResources:
  requests:
    cpu: 100m
    memory: 400Mi
  limits:
    cpu: 500m
    memory: 1000Mi

containerCommand:
- "/bin/chamber"
- "exec"
- "reevo-be-dev"
- "--"
- "/usr/bin/uv"
- "run"
- "python"
- "-m"
- "salestech_be.falkordb.cdc_events.index_event_consumer"

ingress:
  enabled: false

# this secret is managed by terraform
# provides auth for temporal cloud <> api
secrets:
  ca-cert:
    as: volume
    mountPath: /etc/temporal/certs/
    readOnly: true

deploymentStrategy:
  enabled: true
  type: RollingUpdate
  rollingUpdate:
    maxSurge: 100%
    maxUnavailable: 0%
