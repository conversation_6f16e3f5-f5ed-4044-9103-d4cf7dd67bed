applicationName: temporal-email-activity-worker

replicaCount: 1

serviceAccount:
  name: default
  create: false
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/reevo-dev-eks-temporal-worker-default-iam-role

containerImage:
  repository: ************.dkr.ecr.us-west-2.amazonaws.com/reevo-ecr/be
  tag: be-eks-v4.1.3
  pullPolicy: IfNotPresent

envVars:
  SALESTECH_BE_HOST: 0.0.0.0
  SALESTECH_BE_PORT: 8080
  SALESTECH_BE_DB_BASE: reevo_main
  SALESTECH_BE_DB_PORT: 5432
  TEMPORAL_HOST_URL: reevo-dev.ubca3.tmprl.cloud:7233
  TEMPORAL_NAMESPACE: default
  TEMPORAL_MTLS_TLS_CERT: /etc/temporal/certs/ca.crt
  TEMPORAL_MTLS_TLS_KEY: /etc/temporal/certs/ca.key
  # sensitive info should go into parameter store, and use chamber to load

service:
  enabled: true

additionalContainerEnv:
- name: SALESTECH_BE_DD_AGENT_HOST
  valueFrom:
    fieldRef:
      fieldPath: status.hostIP

containerResources:
  requests:
    cpu: 200m
    memory: 2000Mi
  limits:
    cpu: 500m
    memory: 2000Mi

containerCommand:
- "/bin/chamber"
- "exec"
- "reevo-be-dev"
- "--"
- "/usr/bin/uv"
- "run"
- "python"
- "-m"
- "salestech_be.temporal.worker"
- "-t"
- "email_activity_all"

# this secret is managed by terraform
# provides auth for temporal cloud <> workers
secrets:
  ca-cert:
    as: volume
    mountPath: /etc/temporal/certs/
    readOnly: true

deploymentStrategy:
  enabled: true
  type: RollingUpdate
  rollingUpdate:
    maxSurge: 50%
    maxUnavailable: 25%
