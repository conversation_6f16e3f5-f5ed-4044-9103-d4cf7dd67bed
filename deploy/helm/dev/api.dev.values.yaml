applicationName: api

replicaCount: 2

serviceAccount:
  name: default
  create: false
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/reevo-dev-eks-salestech-service-default-iam-role

containerImage:
  repository: ************.dkr.ecr.us-west-2.amazonaws.com/reevo-ecr/salestech-be
  tag: salestech-be-eks-v4.1.3
  pullPolicy: IfNotPresent

envVars:
  SALESTECH_BE_HOST: 0.0.0.0
  SALESTECH_BE_PORT: 8080
  SALESTECH_BE_DB_BASE: salestech_be
  SALESTECH_BE_DB_PORT: 5432
  # sensitive info should go into parameter store, and use chamber to load

service:
  enabled: true
  ports:
    app:
      port: 80
      targetPort: 8080
      protocol: TCP

additionalContainerEnv:
- name: SALESTECH_BE_DD_AGENT_HOST
  valueFrom:
    fieldRef:
      fieldPath: status.hostIP

containerResources:
  requests:
    cpu: 200m
    memory: 1000Mi
  limits:
    cpu: 1000m
    memory: 10000Mi

containerCommand:
- "/bin/chamber"
- "exec"
- "salestech-dev"
- "--"
- "/usr/bin/uv"
- "run"
- "python"
- "-m"
- "salestech_be"

# the app has 15*30 seconds max time to startup
# once it starts, it turns over to liveness/readiness probes
# if failed, it's subject to k8s pod restart
# preferred way for slower starting pod before liveness kills it too early
startupProbe:
  httpGet:
    path: /api/v1/monitoring/health
    port: 8080
  failureThreshold: 30
  periodSeconds: 15

livenessProbe:
  httpGet:
    path: /api/v1/monitoring/health
    port: 8080
  initialDelaySeconds: 15
  periodSeconds: 15
  timeoutSeconds: 3

readinessProbe:
  httpGet:
    path: /api/v1/monitoring/health
    port: 8080
  initialDelaySeconds: 15
  periodSeconds: 15
  timeoutSeconds: 3

# improve availability during deployment
terminationGracePeriodSeconds: 60
lifecycleHooks:
  enabled: true
  preStop:
    exec:
      command:
      - sleep
      - "30"

ingress:
  enabled: true
  annotations:
    kubernetes.io/ingress.class: alb
    #Share a single ALB with all Ingress rules with a specific group name
    alb.ingress.kubernetes.io/group.name: salestech-ingress-private-dev
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/healthcheck-path: /api/v1/monitoring/health
    alb.ingress.kubernetes.io/healthcheck-interval-seconds: "10"
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:us-west-2:************:certificate/2bafceb4-1f70-42ac-9ca2-708ea47c7313
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS":443}]'
    alb.ingress.kubernetes.io/ssl-redirect: "443"
    alb.ingress.kubernetes.io/target-group-attributes: deregistration_delay.timeout_seconds=30
    # specify FE security group (use terraform to manage/create)
    alb.ingress.kubernetes.io/security-groups: sg-05bf08edc74190e40
    # use auto-created BE security group
    alb.ingress.kubernetes.io/manage-backend-security-group-rules: "true"
    external-dns.alpha.kubernetes.io/hostname: api-private-dev.reevo.ai
  path: /
  pathType: Prefix
  servicePort: 80
  hosts:
  - api-private-dev.reevo.ai

# this secret is managed by terraform
# provides auth for temporal cloud <> api
secrets:
  ca-cert:
    as: volume
    mountPath: /etc/temporal/certs/
    readOnly: true
