applicationName: event-processor

containerImage:
  repository: 058264279112.dkr.ecr.us-west-2.amazonaws.com/reevo-ecr/salestech-be
  tag: salestech-be-eks-v4.1.3
  pullPolicy: IfNotPresent

envVars:
  SALESTECH_BE_HOST: 0.0.0.0
  SALESTECH_BE_PORT: 80
  SALESTECH_BE_DB_BASE: salestech_be
  SALESTECH_BE_DB_PORT: 5432
  SALESTECH_EVENT_GROUP_ID: default
  # sensitive info should go into parameter store, and use chamber to load

additionalContainerEnv:
- name: SALESTECH_BE_DD_AGENT_HOST
  valueFrom:
    fieldRef:
      fieldPath: status.hostIP

containerResources:
  requests:
    cpu: 100m
    memory: 400Mi
  limits:
    cpu: 200m
    memory: 1000Mi

containerCommand:
- "/bin/chamber"
- "exec"
- "salestech-dev"
- "--"
- "/usr/bin/uv"
- "run"
- "python"
- "-m"
- "salestech_be.event.processor"

ingress:
  enabled: false

# this secret is managed by terraform
# provides auth for temporal cloud <> api
secrets:
  ca-cert:
    as: volume
    mountPath: /etc/temporal/certs/
    readOnly: true
