applicationName: event-processor

containerImage:
  repository: 058264279112.dkr.ecr.us-west-2.amazonaws.com/reevo-ecr/be
  tag: be-eks-v4.1.3
  pullPolicy: IfNotPresent

envVars:
  SALESTECH_BE_HOST: 0.0.0.0
  SALESTECH_BE_PORT: 80
  SALESTECH_BE_DB_BASE: reevo_main
  SALESTECH_BE_DB_PORT: 5432
  SALESTECH_DIALER_EVENT_GROUP_ID: dialer_event_group
  # sensitive info should go into parameter store, and use chamber to load

additionalContainerEnv:
- name: SALESTECH_BE_DD_AGENT_HOST
  valueFrom:
    fieldRef:
      fieldPath: status.hostIP

containerResources:
  requests:
    cpu: 1000m
    memory: 2000Mi
  limits:
    cpu: 1000m
    memory: 2000Mi

containerCommand:
- "/bin/chamber"
- "exec"
- "reevo-be-prod"
- "--"
- "/usr/bin/uv"
- "run"
- "python"
- "-m"
- "salestech_be.event.processor"

ingress:
  enabled: false

# this secret is managed by terraform
# provides auth for temporal cloud <> api
secrets:
  ca-cert:
    as: volume
    mountPath: /etc/temporal/certs/
    readOnly: true

deploymentStrategy:
  enabled: true
  type: RollingUpdate
  rollingUpdate:
    maxSurge: 25%
    maxUnavailable: 25%
