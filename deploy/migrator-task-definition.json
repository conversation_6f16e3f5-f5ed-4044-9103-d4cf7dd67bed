{"containerDefinitions": [{"name": "ecs-salestech-migrator-dev", "image": "058264279112.dkr.ecr.us-east-2.amazonaws.com/salestech-be:salestech-be-v0.2.2", "cpu": 200, "memory": 200, "portMappings": [], "essential": true, "entryPoint": ["/usr/bin/uv", "run", "python", "-m", "alembic", "upgrade", "head"], "environment": [{"name": "SALESTECH_BE_PORT", "value": "80"}, {"name": "SALESTECH_BE_HOST", "value": "0.0.0.0"}, {"name": "SALESTECH_BE_DB_HOST", "value": "salestech-postgres-dev.cd0wag224vz3.us-east-2.rds.amazonaws.com"}, {"name": "SALESTECH_BE_DB_PORT", "value": "5432"}, {"name": "SALESTECH_BE_DB_BASE", "value": "salestech_be"}], "secrets": [{"name": "SALESTECH_BE_DB_USER", "valueFrom": "arn:aws:secretsmanager:us-east-2:058264279112:secret:rds!db-f0acf768-0b18-4bd9-957d-f19776caf52c-BuLA8t:username::"}, {"name": "SALESTECH_BE_DB_PASS", "valueFrom": "arn:aws:secretsmanager:us-east-2:058264279112:secret:rds!db-f0acf768-0b18-4bd9-957d-f19776caf52c-BuLA8t:password::"}], "mountPoints": [{"sourceVolume": "my-vol", "containerPath": "/var/www/my-vol"}], "volumesFrom": [], "linuxParameters": {"initProcessEnabled": false}, "startTimeout": 30, "stopTimeout": 120, "user": "0", "privileged": false, "readonlyRootFilesystem": false, "interactive": false, "pseudoTerminal": false, "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/aws/ecs/salestech-be-dev/ecs-salestech-migrator-dev", "awslogs-region": "us-east-2", "awslogs-stream-prefix": "ecs"}}}], "family": "salestech-migrator-dev", "taskRoleArn": "arn:aws:iam::058264279112:role/salestech-be-service-dev-20240126210801783600000003", "executionRoleArn": "arn:aws:iam::058264279112:role/salestech-be-service-dev-20240126210801777600000001", "networkMode": "bridge", "volumes": [{"name": "my-vol", "host": {}}], "status": "ACTIVE", "requiresAttributes": [{"name": "com.amazonaws.ecs.capability.logging-driver.awslogs"}, {"name": "ecs.capability.execution-role-awslogs"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.19"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.17"}, {"name": "com.amazonaws.ecs.capability.task-iam-role"}, {"name": "ecs.capability.container-ordering"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.18"}], "placementConstraints": [], "compatibilities": ["EC2"], "requiresCompatibilities": ["EC2"], "cpu": "200", "memory": "200", "runtimePlatform": {"cpuArchitecture": "X86_64", "operatingSystemFamily": "LINUX"}, "registeredAt": "2024-01-22T17:17:32.707Z", "registeredBy": "arn:aws:iam::058264279112:user/machine-terraform", "tags": [{"key": "Terraform", "value": "true"}, {"key": "Environment", "value": "dev"}]}