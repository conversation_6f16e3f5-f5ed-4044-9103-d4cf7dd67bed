# Receives all info with OpenTelemetry protocol.
receivers:
  otlp:
    protocols:
      grpc:
      http:

# Batch all spans.
processors:
  batch:

exporters:
  # Exports spans to log.
  logging:
    logLevel: info

  # Exports spans to jaeger.
  jaeger:
    endpoint: "jaeger:14250"
    tls:
      insecure: true

extensions:
  health_check:
  pprof:

service:
  extensions: [health_check, pprof]
  pipelines:
    traces:
      receivers: [otlp]
      processors: [batch]
      exporters: [logging, jaeger]
