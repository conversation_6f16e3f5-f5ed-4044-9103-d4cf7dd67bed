set -e

BUILDER_NAME=reevo-multi-platform-builder
BUILDER_INSTANCE_NAME=reevo-multi-platform-builder-instance
BUILDER_STATUS=$(docker buildx ls | grep $BUILDER_INSTANCE_NAME | awk '{print $3}')
if [[ "$BUILDER_STATUS" == "running" ]]; then
  echo "Builder $BUILDER_NAME is already running."
elif [[ -n "$BUILDER_STATUS" ]]; then
  echo "Builder $BUILDER_NAME exists but is not running."
  docker buildx use $BUILDER_NAME
  docker buildx inspect --bootstrap
else
  echo "Builder $BUILDER_NAME does not exist. Creating and starting it..."
  docker buildx create --name $BUILDER_NAME --node $BUILDER_INSTANCE_NAME --use
  docker buildx inspect --bootstrap
fi
