{"containerDefinitions": [{"name": "ecs-salestech-be-dev", "image": "058264279112.dkr.ecr.us-east-2.amazonaws.com/salestech-be:salestech-be-v0.2.1", "cpu": 1000, "memory": 1600, "portMappings": [{"name": "ecs-salestech-be-dev", "containerPort": 80, "hostPort": 0, "protocol": "tcp"}], "essential": true, "entryPoint": ["/usr/bin/uv", "run", "python", "-m", "salestech_be"], "environment": [{"name": "SALESTECH_BE_PORT", "value": "80"}, {"name": "SALESTECH_BE_HOST", "value": "0.0.0.0"}, {"name": "SALESTECH_BE_DB_HOST", "value": "salestech-postgres-dev.cd0wag224vz3.us-east-2.rds.amazonaws.com"}, {"name": "SALESTECH_BE_DB_PORT", "value": "5432"}, {"name": "SALESTECH_BE_DB_BASE", "value": "salestech_be"}], "secrets": [{"name": "SALESTECH_BE_DB_USER", "valueFrom": "arn:aws:secretsmanager:us-east-2:058264279112:secret:rds!db-f0acf768-0b18-4bd9-957d-f19776caf52c-BuLA8t:username::"}, {"name": "SALESTECH_BE_DB_PASS", "valueFrom": "arn:aws:secretsmanager:us-east-2:058264279112:secret:rds!db-f0acf768-0b18-4bd9-957d-f19776caf52c-BuLA8t:password::"}, {"name": "SALESTECH_BE_SENDGRID_API_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-2:058264279112:secret:SendGridAPIKey-9I6Bxs:sendgrid_api_key::"}, {"name": "SALESTECH_BE_OPENAI_API_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-2:058264279112:secret:prod/salestech_be/openai/api_key-ytEPhu:open_ai_api_key::"}, {"name": "SALESTECH_BE_NYLAS_V3_API_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-2:058264279112:secret:dev/salestech_be/nylas_v3-xz5xUb:v3-api-key::"}, {"name": "SALESTECH_BE_SENTRY_DSN", "valueFrom": "arn:aws:secretsmanager:us-east-2:058264279112:secret:prod/salestech_be/sentry-yxeI1e:dsn::"}, {"name": "SALESTECH_BE_AUTH0_APP_CLIENT_ID", "valueFrom": "arn:aws:secretsmanager:us-east-2:058264279112:secret:dev/salestech_be/auth0:client_id::"}, {"name": "SALESTECH_BE_AUTH0_APP_CLIENT_SECRET", "valueFrom": "arn:aws:secretsmanager:us-east-2:058264279112:secret:dev/salestech_be/auth0:client_secret::"}, {"name": "SALESTECH_BE_AUTH0_AUTH_REDIRECT_URI", "valueFrom": "arn:aws:secretsmanager:us-east-2:058264279112:secret:dev/salestech_be/auth0:redirect_uri::"}, {"name": "SALESTECH_BE_SALESFORCE_CLIENT_SECRET", "valueFrom": "arn:aws:secretsmanager:us-east-2:058264279112:secret:dev/salestech_be/salesforce:client_secret::"}, {"name": "SALESTECH_BE_SALESFORCE_CLIENT_ID", "valueFrom": "arn:aws:secretsmanager:us-east-2:058264279112:secret:dev/salestech_be/salesforce:client_id::"}], "mountPoints": [{"sourceVolume": "my-vol", "containerPath": "/var/www/my-vol"}], "volumesFrom": [], "linuxParameters": {"initProcessEnabled": false}, "startTimeout": 30, "stopTimeout": 120, "user": "0", "privileged": false, "readonlyRootFilesystem": false, "interactive": false, "pseudoTerminal": false, "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/aws/ecs/salestech-be-dev/ecs-salestech-be-dev", "awslogs-region": "us-east-2", "awslogs-stream-prefix": "ecs"}}}], "family": "salestech-be-service-dev", "taskRoleArn": "arn:aws:iam::058264279112:role/salestech-be-service-dev-20240126210801783600000003", "executionRoleArn": "arn:aws:iam::058264279112:role/salestech-be-service-dev-20240126210801777600000001", "networkMode": "bridge", "volumes": [{"name": "my-vol", "host": {}}], "status": "ACTIVE", "requiresAttributes": [{"name": "com.amazonaws.ecs.capability.logging-driver.awslogs"}, {"name": "ecs.capability.execution-role-awslogs"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.19"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.17"}, {"name": "com.amazonaws.ecs.capability.task-iam-role"}, {"name": "ecs.capability.container-ordering"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.18"}, {"name": "ecs.capability.task-eni"}], "placementConstraints": [], "compatibilities": ["EC2"], "requiresCompatibilities": ["EC2"], "cpu": "1000", "memory": "1600", "runtimePlatform": {"cpuArchitecture": "X86_64", "operatingSystemFamily": "LINUX"}, "registeredAt": "2024-01-22T17:17:32.707Z", "registeredBy": "arn:aws:iam::058264279112:user/machine-terraform", "tags": [{"key": "Terraform", "value": "true"}, {"key": "Environment", "value": "dev"}]}