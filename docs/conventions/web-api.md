# Web API Conventions

Use REST API best practices.

## Request and Response Format

See `salestech_be/web/api/common/container.py` for base schema classes used for request and responses.

## Endpoints

### Paths

Paths follow the pattern of `/api/v{version}/{resource}/{id}`.

With a resource or entity endpoint prefix `/api/v{version}/{resource}`, conventions for common requests are as follows:

- `POST {prefix}/_list` - Fetch a paginated list of the resource
- `POST {prefix}` - Create a resource record
- `GET {prefix}/{id}` - Get a specific resource
- `PATCH {prefix}/{id}` - Update a specific resource
- `DELETE {prefix}/{id}` - Delete a specific resource
- `POST {prefix}/{id}/_{action}` - Perform a custom action on a specific resource
- `POST {prefix}/_{action}` - Perform a general custom action on the resource collection

#### Custom Actions

Use a custom action for any non-standard CRUD operation.

Common custom action cases:
- Updating a field with side effects
- Creating or removing a relationship

For example, if updating the status field of a resource entails side effects, then the endpoint should be implemented as a custom action rather than a field update.

#### Sub-resources

For a sub-resource, the convention is to append the sub-resource name to the parent resource path, then the sub-resource path will follow the same conventions as the parent resource.

For example:

```http
### Fetch a list of emails associated with a contact
POST /api/v1/contacts/7f47d660-3a98-4ced-a0e8-a833b419db1/emails/_list
```

### Pagination

Use page-based pagination (`page` and `page_size`).
