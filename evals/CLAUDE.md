# Evals Commands & Guidelines

## Commands

### Running Evaluations
-   All evals: `uv run python -m evals.task_eval`
-   Specific eval module: `uv run python -m evals.tasks.{module_name}`
-   Chatbot redteam tests: `uv run python -m evals.chatbot.redteam`
-   Stage criteria eval: `uv run python -m evals.stage_criteria.criteria_1`

### Development
-   Install dependencies: `uv sync`
-   Lint evals code: `uv run ruff check evals/`
-   Type check evals: `uv run mypy evals/`
-   Run specific test: `uv run pytest -v evals/tests/test_{name}.py`

## Evaluation Guidelines

### Code Style
-   Follow parent project code style from root CLAUDE.md
-   **Test Functions**: Use descriptive names like `test_task_ownership_simple_assignment()`
-   **Eval Data**: Store test cases in structured format (JSON/YAML preferred)
-   **Mock Data**: Use realistic mock data that reflects production scenarios
-   **Assertions**: Include detailed failure messages with expected vs actual values

### Evaluation Structure
-   **Test Cases**: Each eval should test multiple scenarios (happy path, edge cases, error conditions)
-   **Scoring**: Use 0.0-1.0 scoring with clear success criteria
-   **Documentation**: Include clear descriptions of what each test validates
-   **Reproducibility**: Evals should be deterministic and repeatable

### AI Model Testing
-   **Prompt Testing**: Test various prompt formulations and structures
-   **Edge Cases**: Include boundary conditions and malformed inputs
-   **Performance**: Measure response time and token usage where applicable
-   **Safety**: Include red team tests for inappropriate or harmful outputs
-   **Consistency**: Test model consistency across multiple runs with same input

### Data Requirements
-   **Privacy**: Never include real customer data in eval datasets
-   **Diversity**: Include diverse test cases covering different user scenarios
-   **Realism**: Mock data should be realistic enough to catch real issues
-   **Maintenance**: Keep eval data up-to-date with product changes

## Integration with Main Project

### Dependencies
-   Evals inherit project dependencies from parent pyproject.toml
-   Add eval-specific dependencies to main project requirements
-   Use `autoevals` library for standardized evaluation patterns

### CI/CD Integration
-   Evals should be runnable in CI environment
-   Include eval results in PR status checks when applicable
-   Set up automated regression testing for model performance

## Git commits

-   Ensure commit messages are clear, concise, and descriptive of the changes made
-   Use conventional commits format when applicable (feat:, fix:, chore:, etc.)
-   Eval commits should specify which functionality is being tested