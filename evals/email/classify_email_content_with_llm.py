import asyncio

from deepeval import evaluate
from deepeval.metrics import AnswerRelevancyMetric, GEval
from deepeval.test_case import LLMTestCase, LLMTestCaseParams
from langfuse import Lang<PERSON>

from salestech_be.core.ai.email.llm_calls.classify_email_content_with_llm import (
    classify_email_content_with_llm,
)
from salestech_be.settings import settings

langfuse = Langfuse(
    public_key=settings.langfuse_public_key.get_secret_value(),
    secret_key=settings.langfuse_secret_key.get_secret_value(),
    host=settings.langfuse_host,
)

correctness_metric = GEval(
    name="Correctness",
    evaluation_steps=[
        "Check whether the email content is classified correctly",
        "You should also heavily penalize omission of detail",
        "Vague language, or contradicting OPINIONS, are OK",
    ],
    evaluation_params=[
        LLMTestCaseParams.INPUT,
        LLMTestCaseParams.ACTUAL_OUTPUT,
        LLMTestCaseParams.EXPECTED_OUTPUT,
    ],
)
metrics = [correctness_metric, AnswerRelevancyMetric()]


async def run_evals() -> None:
    eval_metrics = metrics

    dataset = langfuse.get_dataset("email.classify-v0.1-golden")

    test_cases = []

    # Process each golden in your dataset
    for item in dataset.items:
        actual_output = await classify_email_content_with_llm(
            email_content=item.input["email"],
            organization_id=item.input["organization_id"],
            langfuse_session_id=item.input["langfuse_session_id"],
            attempt_num=item.input["attempt_num"],
            last_error=item.input["last_error"],
            all_errors=item.input["all_errors"],
            sender_type=item.input["sender_type"],
            recipient_type=item.input["recipient_type"],
            is_part_of_sales_thread=item.input["is_part_of_sales_thread"],
        )
        test_case = LLMTestCase(
            input=str(item.input),
            actual_output=str(actual_output),
            expected_output=str(item.expected_output),
        )
        test_cases.append(test_case)

    # Run an evaluation
    evaluate(
        test_cases=test_cases,
        metrics=eval_metrics,
        hyperparameters={
            # model and prompt template are required for logging for deepeval
            "feature": "email_classify",
            "model": "claude-3.7",
            "agent_type": "zeroshot",
            "version": 1,
            "prompt template": "",
        },
    )


if __name__ == "__main__":
    asyncio.run(run_evals())
