import asyncio
import uuid

from deepeval import evaluate
from deepeval.metrics import AnswerRelevancyMetric, GEval
from deepeval.test_case import LLMTest<PERSON>ase, LLMTestCaseParams
from langfuse import Lang<PERSON>

from salestech_be.core.ai.tasks.llm_calls.generate_task_ownership_llm_call import (
    generate_task_ownership_llm_call,
)
from salestech_be.settings import settings

langfuse = Langfuse(
    public_key=settings.langfuse_public_key.get_secret_value(),
    secret_key=settings.langfuse_secret_key.get_secret_value(),
    host=settings.langfuse_host,
)


# There's prebuilt ones like Summarization, Prompt Alignment, etc
# What we really want for tomorrow is probably to have a good set of custom GEval metrics
# TODO Ahmed: Talk to <PERSON> about his list of eval criteria but probably this format is superior
correctness_metric = GEval(
    name="Correctness",
    # criteria="Determine whether the actual output is factually correct based on the expected output.",
    # NOTE: you can only provide either criteria or evaluation_steps, and not both
    evaluation_steps=[
        "Check whether the facts in 'actual output' contradicts any facts in 'expected output'",
        "You should also heavily penalize omission of detail",
        "Vague language, or contradicting OPINIONS, are OK",
    ],
    evaluation_params=[
        LLMTestCaseParams.INPUT,
        LLMTestCaseParams.ACTUAL_OUTPUT,
        LLMTestCaseParams.EXPECTED_OUTPUT,
    ],
)
metrics = [correctness_metric, AnswerRelevancyMetric()]


async def run_evals() -> None:
    eval_metrics = metrics

    dataset = langfuse.get_dataset("intel.generate_task_ownership")

    test_cases = []

    # Process each golden in your dataset - limit to 10 for now
    for i, item in enumerate(dataset.items):
        if i >= 10:  # Only process first 10 items  # noqa: PLR2004
            break
        input_data = item.input

        # Call the LLM function with mapped parameters
        llm_response = await generate_task_ownership_llm_call(
            task_title=input_data["task_title"],
            task_note=input_data["task_note"],
            object_content=input_data["object_content"],
            users=input_data["users"],
            account_owner=input_data["account_owner_user"],
            organization_id=str(uuid.uuid4()),  # Default UUID since not in dataset
            langfuse_session_id="eval-session",  # Default session ID
            workflow_id="eval-workflow",  # Default workflow ID
        )

        # Extract the message content from the LLM response
        actual_output = llm_response.raw_response.choices[0].message.content  # type: ignore
        test_case = LLMTestCase(
            input=str(item.input),
            actual_output=str(actual_output),
            expected_output=str(item.expected_output),
        )
        test_cases.append(test_case)

    # Run an evaluation
    evaluate(
        test_cases=test_cases,
        metrics=eval_metrics,
        hyperparameters={
            # model and prompt template are required for logging for deepeval
            "feature": "task_ownership",
            "model": "claude-3.7",
            "agent_type": "zeroshot",
            "version": 1,
            "prompt template": "",
        },
    )


if __name__ == "__main__":
    asyncio.run(run_evals())
