{"metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat_minor": 4, "nbformat": 4, "cells": [{"cell_type": "code", "source": "import asyncio\nimport os\nfrom uuid import UUID\n\nimport litellm\nfrom salestech_be.db.dao.thread_repository import ThreadRepository\nfrom salestech_be.db.dbengine.core import DatabaseEngine\nfrom salestech_be.db.models.message import Message\nfrom salestech_be.settings import settings\n\n# Setup the Langfuse environment variables\nos.environ[\"LANGFUSE_PUBLIC_KEY\"] = settings.langfuse_public_key.get_secret_value()\nos.environ[\"LANGFUSE_SECRET_KEY\"] = settings.langfuse_secret_key.get_secret_value()\n\n# Langfuse host\nos.environ[\"LANGFUSE_HOST\"] = \"https://us.cloud.langfuse.com\"  # 🇺🇸 US region\n\n# set callbacks\nlitellm.success_callback = [\"langfuse\"]\nlitellm.failure_callback = [\"langfuse\"]\n\n\nasync def parse_email_thread() -> None:\n    print(\"parsing email messages\")\n    db_engine = DatabaseEngine(url=str(settings.db_url))\n\n    separator = \"====================================\"\n    thread_repo = ThreadRepository(engine=db_engine)\n    messages = await thread_repo._find_by_column_values(\n        Message,\n        thread_id=UUID(\"f8055a94-5bac-4cfb-950e-3bfb097e14ba\")\n    )\n    message_list = []\n    for message in messages:\n        message_list.append(f\"[{message.id}]: {message.body_html}\")\n    message_list = separator.join(message_list)\n\n    prompt = f\"\"\"\nHere's a email message list with chronnoical order in the thread with pattern like [$message_id]: $html_body, and splitted by separator {separator}.\nEmail message list:\n{message_list}\n\nPlease extract the latest message text from the html body of each email message provided in the list above with the requirements below:\n    - The parsed message text should be extracted from the html body of the email message.\n    - The output format should be like: $senders [$message_id]: parsed_email_text\n    - The output should be one message per line with chronological order.\n    - The output should only include formatted parsed email text without any other information.\n            \"\"\"\n\n    response = litellm.completion(\n        model=\"claude-3-5-sonnet-20240620\",\n        messages=[{\"role\": \"user\", \"content\": prompt}],\n        api_key=settings.anthropic_api_key.get_secret_value(),\n    )\n    content = response.choices[0].message.content\n    print(content)\n", "metadata": {"trusted": true}, "outputs": [], "execution_count": null}]}