{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/salestech-be/.venv/lib/python3.12/site-packages/pydantic/_internal/_config.py:341: UserWarning: Valid config keys have changed in V2:\n", "* 'fields' has been removed\n", "  warnings.warn(message, UserWarning)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[32m2025-01-31 13:24:40.457\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[1m\n", "LiteLLM completion() model= gemini-1.5-pro; provider = gemini\u001b[0m | \u001b[33mserialized: {\"message\":\"\\nLiteLLM completion() model= gemini-1.5-pro; provider = gemini\",\"name\":\"<PERSON><PERSON><PERSON><PERSON>\",\"lineno\":2825,\"function_name\":\"_check_valid_arg\",\"level\":\"INFO\",\"timestamp\":1738358680457.4202,\"time\":\"2025-01-31T13:24:40.457420-08:00\",\"thread_id\":13455405056,\"thread_name\":\"asyncio_0\",\"process_id\":16349,\"extra\":{\"span_id\":\"815704053451576042\",\"trace_id\":\"68724910906035006763447285095052798023\"}}\u001b[0m| \u001b[36mLiteLLM\u001b[0m:\u001b[36m_check_valid_arg\u001b[0m:\u001b[36m2825\u001b[0m | \u001b[35mtrace_id=68724910906035006763447285095052798023\u001b[0m | \u001b[34mspan_id=815704053451576042\u001b[0m\n", "['The term \"Reevo\" or \"reevo.ai\" refers to several different entities:\\n\\n**1. Reevo (Sales Platform):** This is an AI-powered sales platform designed to streamline sales operations.  It offers features like smart meeting management, pipeline tracking, integrations with other sales tools (like Zoom), and AI-driven pricing optimization.  It aims to consolidate various sales functions into a single platform, boosting productivity and accelerating revenue growth.  This platform is likely the one referenced by \"reevo.ai\".\\n\\n**2. <PERSON><PERSON> (Fleet Management):**  This refers to an AI-powered fleet management software platform specifically focused on tire monitoring and management. It uses sensors to collect tire data, providing insights and analytics through a live fleet view and APIs.\\n\\n**3. Reevo (E-bike):** This is a brand of hubless e-bikes known for their unique design and integrated security features like automatic lights and built-in turn signals.\\n\\n**4. Reevo Money:** This is a UK-based financial services provider offering fast, fair, and affordable loans. They emphasize transparency, with no hidden fees, and utilize technology to provide quick responses and competitive APRs.\\n\\n**5. <PERSON>eVo (Cloud Storage Partner):** This \"ReeVo\" (note the capitalization) seems to be a partner of Iperius, an Italian software company specializing in data backup and recovery solutions.  They collaborate on cloud storage services.\\n\\nIt\\'s important to distinguish between these different entities when searching for information, as they operate in distinct industries and offer different products and services.  If you are looking for a specific \"Reevo,\" be sure to clarify which one you mean (e.g., the e-bike, the sales platform, etc.).\\n']\n", "\u001b[32m2025-01-31 13:24:48.679\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[1m\n", "LiteLLM completion() model= gemini-1.5-pro; provider = gemini\u001b[0m | \u001b[33mserialized: {\"message\":\"\\nLiteLLM completion() model= gemini-1.5-pro; provider = gemini\",\"name\":\"<PERSON><PERSON><PERSON><PERSON>\",\"lineno\":2825,\"function_name\":\"_check_valid_arg\",\"level\":\"INFO\",\"timestamp\":1738358688679.034,\"time\":\"2025-01-31T13:24:48.679034-08:00\",\"thread_id\":13455405056,\"thread_name\":\"asyncio_0\",\"process_id\":16349,\"extra\":{\"span_id\":\"2265759860883340433\",\"trace_id\":\"46680745571557327650152724123191494957\"}}\u001b[0m| \u001b[36mLiteLLM\u001b[0m:\u001b[36m_check_valid_arg\u001b[0m:\u001b[36m2825\u001b[0m | \u001b[35mtrace_id=46680745571557327650152724123191494957\u001b[0m | \u001b[34mspan_id=2265759860883340433\u001b[0m\n", "\u001b[32m2025-01-31 13:24:48.684\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[1mLangfuse Layer Logging - logging success\u001b[0m | \u001b[33mserialized: {\"message\":\"Langfuse Layer Logging - logging success\",\"name\":\"Li<PERSON>LLM\",\"lineno\":297,\"function_name\":\"_old_log_event\",\"level\":\"INFO\",\"timestamp\":1738358688684.993,\"time\":\"2025-01-31T13:24:48.684993-08:00\",\"thread_id\":**********,\"thread_name\":\"MainThread\",\"process_id\":16349,\"extra\":{\"span_id\":\"18194948324446830425\",\"trace_id\":\"165972835484281226621221812088440593475\"}}\u001b[0m| \u001b[36mLiteLLM\u001b[0m:\u001b[36m_old_log_event\u001b[0m:\u001b[36m297\u001b[0m | \u001b[35mtrace_id=165972835484281226621221812088440593475\u001b[0m | \u001b[34mspan_id=18194948324446830425\u001b[0m\n", "The term \"Reevo\" or \"reevo.ai\" refers to several different entities:\n", "\n", "**1. <PERSON><PERSON> (Sales Platform):** This is an AI-powered sales platform designed to streamline sales operations.  It offers features like smart meeting management, pipeline tracking, integrations with other sales tools (like Zoom), and AI-driven pricing optimization.  It aims to consolidate various sales functions into a single platform, boosting productivity and accelerating revenue growth.  This platform is likely the one referenced by \"reevo.ai\".\n", "\n", "**2. <PERSON><PERSON> (Fleet Management):**  This refers to an AI-powered fleet management software platform specifically focused on tire monitoring and management. It uses sensors to collect tire data, providing insights and analytics through a live fleet view and APIs.\n", "\n", "**3. <PERSON><PERSON> (E-bike):** This is a brand of hubless e-bikes known for their unique design and integrated security features like automatic lights and built-in turn signals.\n", "\n", "**4. Reevo Money:** This is a UK-based financial services provider offering fast, fair, and affordable loans. They emphasize transparency, with no hidden fees, and utilize technology to provide quick responses and competitive APRs.\n", "\n", "**5. <PERSON><PERSON><PERSON><PERSON> (Cloud Storage Partner):** This \"ReeVo\" (note the capitalization) seems to be a partner of Iperius, an Italian software company specializing in data backup and recovery solutions.  They collaborate on cloud storage services.\n", "\n", "It's important to distinguish between these different entities when searching for information, as they operate in distinct industries and offer different products and services.  If you are looking for a specific \"Reevo,\" be sure to clarify which one you mean (e.g., the e-bike, the sales platform, etc.).\n", "\n", "\n", "Reevo.ai primarily focuses on sales pipeline management, meeting management, research and insights, email copywriting, and reporting, rather than customer success stories in the traditional sense.  The platform is currently in limited beta release and is actively developing upcoming features like email delivery automation, prospecting, and automated engagement.  Therefore, traditional case studies or customer success stories are not yet readily available.\n", "\n", "However, the website highlights the potential benefits for sales representatives, emphasizing how Reevo.ai can act as a \"GTM Chief of Staff\" by streamlining various sales processes.  Here's a summary of how Reevo.ai aims to help sales teams succeed:\n", "\n", "* **Pipeline Management:**  Visualizes the sales pipeline, tracks progress in real-time, and helps sales reps stay organized and prioritize deals effectively.\n", "* **Smart Meeting Management:**  Captures meeting notes, summarizes key takeaways, generates action items, and ensures follow-up, allowing sales reps to focus on building relationships.\n", "* **Research & Insights:** Provides personalized insights into companies and contacts, enabling sales reps to tailor their messaging and address customer needs effectively.\n", "* **Email Copywriting & Intelligence:**  Helps craft compelling emails with AI-generated copy and customizable templates, improving outreach efficiency and personalization.\n", "* **Reporting:** Offers actionable reports on sales funnel performance and individual rep activity, enabling data-driven decision-making.\n", "\n", "While concrete customer success stories are not available at this stage, the features offered by Reevo.ai suggest its potential to drive sales success by automating tasks, providing valuable insights, and improving overall sales process efficiency.  It's worth noting that this information is current as of today, January 31, 2025, and may change as Reevo.ai evolves and gathers more user data.\n", "\n", "\u001b[32m2025-01-31 13:24:57.005\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[1m\n", "LiteLLM completion() model= gpt-4o-mini; provider = openai\u001b[0m | \u001b[33mserialized: {\"message\":\"\\nLiteLLM completion() model= gpt-4o-mini; provider = openai\",\"name\":\"<PERSON>te<PERSON><PERSON>\",\"lineno\":2825,\"function_name\":\"_check_valid_arg\",\"level\":\"INFO\",\"timestamp\":1738358697005.728,\"time\":\"2025-01-31T13:24:57.005728-08:00\",\"thread_id\":13455405056,\"thread_name\":\"asyncio_0\",\"process_id\":16349,\"extra\":{\"span_id\":\"10142263020607399971\",\"trace_id\":\"123737601080410786379851388397753923051\"}}\u001b[0m| \u001b[36mLiteLLM\u001b[0m:\u001b[36m_check_valid_arg\u001b[0m:\u001b[36m2825\u001b[0m | \u001b[35mtrace_id=123737601080410786379851388397753923051\u001b[0m | \u001b[34mspan_id=10142263020607399971\u001b[0m\n", "\u001b[32m2025-01-31 13:24:57.009\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[1mLangfuse Layer Logging - logging success\u001b[0m | \u001b[33mserialized: {\"message\":\"Langfuse Layer Logging - logging success\",\"name\":\"LiteLLM\",\"lineno\":297,\"function_name\":\"_old_log_event\",\"level\":\"INFO\",\"timestamp\":1738358697009.98,\"time\":\"2025-01-31T13:24:57.009980-08:00\",\"thread_id\":**********,\"thread_name\":\"MainThread\",\"process_id\":16349,\"extra\":{\"span_id\":\"15180299173373807957\",\"trace_id\":\"124604029941571559952964929422172195592\"}}\u001b[0m| \u001b[36mLiteLLM\u001b[0m:\u001b[36m_old_log_event\u001b[0m:\u001b[36m297\u001b[0m | \u001b[35mtrace_id=124604029941571559952964929422172195592\u001b[0m | \u001b[34mspan_id=15180299173373807957\u001b[0m\n", "Products & Services: Reevo offers several products and services across different sectors: 1. **<PERSON><PERSON> (Sales Platform)**: An AI-powered sales platform for smart meeting management, pipeline tracking, integrations with sales tools, and AI-driven pricing optimization. 2. **<PERSON><PERSON> (Fleet Management)**: AI-powered fleet management software focused on tire monitoring and management. 3. **<PERSON>evo (E-bike)**: Hubless e-bikes with integrated security features. 4. **Reevo Money**: Financial services provider offering fast and affordable loans with a focus on transparency. 5. **<PERSON>e<PERSON>o (Cloud Storage Partner)**: Partner of Iperius for cloud storage services.\n", "Ideal Customer Profile: The ideal customer profile for Reevo.ai appears to be sales teams and representatives looking for tools to enhance their sales processes, improve productivity, and streamline operations. This includes organizations that prioritize data-driven decision-making, effective pipeline management, and personalized customer engagement.\n", "Case Studies:\n", "\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[32m2025-01-31 13:24:59.235\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[1mLangfuse Layer Logging - logging success\u001b[0m | \u001b[33mserialized: {\"message\":\"Langfuse Layer Logging - logging success\",\"name\":\"LiteLL<PERSON>\",\"lineno\":297,\"function_name\":\"_old_log_event\",\"level\":\"INFO\",\"timestamp\":1738358699235.6492,\"time\":\"2025-01-31T13:24:59.235649-08:00\",\"thread_id\":**********,\"thread_name\":\"MainThread\",\"process_id\":16349,\"extra\":{\"span_id\":\"6293962082206829688\",\"trace_id\":\"53551173278972229023865105716729948294\"}}\u001b[0m| \u001b[36mLiteLLM\u001b[0m:\u001b[36m_old_log_event\u001b[0m:\u001b[36m297\u001b[0m | \u001b[35mtrace_id=53551173278972229023865105716729948294\u001b[0m | \u001b[34mspan_id=6293962082206829688\u001b[0m\n"]}], "source": ["from salestech_be.temporal.activities.research_agent.company_site import get_company_site_content_with_grounding\n", "\n", "company_name = \"reevo\"\n", "company_domain = \"reevo.ai\"\n", "\n", "company_site_content = await get_company_site_content_with_grounding(company_name, company_domain)\n", "\n", "print(company_site_content)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.8"}}, "nbformat": 4, "nbformat_minor": 2}