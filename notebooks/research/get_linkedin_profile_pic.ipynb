{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[32m2025-02-05 18:37:08.672\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[1mHTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json \"HTTP/1.1 200 OK\"\u001b[0m | \u001b[33mserialized: {\"message\":\"HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json \\\"HTTP/1.1 200 OK\\\"\",\"name\":\"httpx\",\"lineno\":1026,\"function_name\":\"_send_single_request\",\"level\":\"INFO\",\"timestamp\":1738809428672.581,\"time\":\"2025-02-05T18:37:08.672581-08:00\",\"thread_id\":8272231104,\"thread_name\":\"MainThread\",\"process_id\":14996,\"extra\":{\"span_id\":\"3372556331216506665\",\"trace_id\":\"214902026387507209907722378673503963724\"}}\u001b[0m| \u001b[36mhttpx\u001b[0m:\u001b[36m_send_single_request\u001b[0m:\u001b[36m1026\u001b[0m | \u001b[35mtrace_id=214902026387507209907722378673503963724\u001b[0m | \u001b[34mspan_id=3372556331216506665\u001b[0m\n", "\u001b[32m2025-02-05 18:37:08.692\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[33m\u001b[1m/Users/<USER>/salestech-be/.venv/lib/python3.12/site-packages/pydantic/_internal/_config.py:341: UserWarning: Valid config keys have changed in V2:\n", "* 'fields' has been removed\n", "  warnings.warn(message, UserWarning)\n", "\u001b[0m | \u001b[33mserialized: {\"message\":\"/Users/<USER>/salestech-be/.venv/lib/python3.12/site-packages/pydantic/_internal/_config.py:341: UserWarning: Valid config keys have changed in V2:\\n* 'fields' has been removed\\n  warnings.warn(message, UserWarning)\\n\",\"name\":\"py.warnings\",\"lineno\":112,\"function_name\":\"_showwarnmsg\",\"level\":\"WARNING\",\"timestamp\":1738809428692.991,\"time\":\"2025-02-05T18:37:08.692991-08:00\",\"thread_id\":8272231104,\"thread_name\":\"MainThread\",\"process_id\":14996,\"extra\":{\"span_id\":\"13111074663571243212\",\"trace_id\":\"50333131436609080680752788536655759902\"}}\u001b[0m| \u001b[36mpy.warnings\u001b[0m:\u001b[36m_showwarnmsg\u001b[0m:\u001b[36m112\u001b[0m | \u001b[35mtrace_id=50333131436609080680752788536655759902\u001b[0m | \u001b[34mspan_id=13111074663571243212\u001b[0m\n", "\u001b[32m2025-02-05 18:37:09.273\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[1mStatsd client <salestech_be.common.stats.noop_statsd.NoopStatsd object at 0x12c5b3950>\u001b[0m | \u001b[33mserialized: {\"message\":\"Statsd client <salestech_be.common.stats.noop_statsd.NoopStatsd object at 0x12c5b3950>\",\"name\":\"common.stats.metric\",\"lineno\":134,\"function_name\":\"__init__\",\"level\":\"INFO\",\"timestamp\":1738809429273.77,\"time\":\"2025-02-05T18:37:09.273770-08:00\",\"thread_id\":8272231104,\"thread_name\":\"MainThread\",\"process_id\":14996,\"extra\":{\"span_id\":\"12857929029787853856\",\"trace_id\":\"197209362082163527640743207783945710152\"}}\u001b[0m| \u001b[36mcommon.stats.metric\u001b[0m:\u001b[36m__init__\u001b[0m:\u001b[36m134\u001b[0m | \u001b[35mtrace_id=197209362082163527640743207783945710152\u001b[0m | \u001b[34mspan_id=12857929029787853856\u001b[0m\n", "\u001b[32m2025-02-05 18:37:09.274\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[1mStatsd client <salestech_be.common.stats.noop_statsd.NoopStatsd object at 0x12c5b3bc0>\u001b[0m | \u001b[33mserialized: {\"message\":\"Statsd client <salestech_be.common.stats.noop_statsd.NoopStatsd object at 0x12c5b3bc0>\",\"name\":\"common.stats.metric\",\"lineno\":134,\"function_name\":\"__init__\",\"level\":\"INFO\",\"timestamp\":1738809429274.021,\"time\":\"2025-02-05T18:37:09.274021-08:00\",\"thread_id\":8272231104,\"thread_name\":\"MainThread\",\"process_id\":14996,\"extra\":{\"span_id\":\"4835256791976138035\",\"trace_id\":\"122360730616003787874545976179853428232\"}}\u001b[0m| \u001b[36mcommon.stats.metric\u001b[0m:\u001b[36m__init__\u001b[0m:\u001b[36m134\u001b[0m | \u001b[35mtrace_id=122360730616003787874545976179853428232\u001b[0m | \u001b[34mspan_id=4835256791976138035\u001b[0m\n", "\u001b[32m2025-02-05 18:37:09.274\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[1mStatsd client <salestech_be.common.stats.noop_statsd.NoopStatsd object at 0x11e428230>\u001b[0m | \u001b[33mserialized: {\"message\":\"Statsd client <salestech_be.common.stats.noop_statsd.NoopStatsd object at 0x11e428230>\",\"name\":\"common.stats.metric\",\"lineno\":134,\"function_name\":\"__init__\",\"level\":\"INFO\",\"timestamp\":1738809429274.4429,\"time\":\"2025-02-05T18:37:09.274443-08:00\",\"thread_id\":8272231104,\"thread_name\":\"MainThread\",\"process_id\":14996,\"extra\":{\"span_id\":\"3436954308660794896\",\"trace_id\":\"171578232270583739918565049847013044507\"}}\u001b[0m| \u001b[36mcommon.stats.metric\u001b[0m:\u001b[36m__init__\u001b[0m:\u001b[36m134\u001b[0m | \u001b[35mtrace_id=171578232270583739918565049847013044507\u001b[0m | \u001b[34mspan_id=3436954308660794896\u001b[0m\n", "\u001b[32m2025-02-05 18:37:09.274\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[1mStatsd client <salestech_be.common.stats.noop_statsd.NoopStatsd object at 0x11e3df440>\u001b[0m | \u001b[33mserialized: {\"message\":\"Statsd client <salestech_be.common.stats.noop_statsd.NoopStatsd object at 0x11e3df440>\",\"name\":\"common.stats.metric\",\"lineno\":134,\"function_name\":\"__init__\",\"level\":\"INFO\",\"timestamp\":1738809429274.707,\"time\":\"2025-02-05T18:37:09.274707-08:00\",\"thread_id\":8272231104,\"thread_name\":\"MainThread\",\"process_id\":14996,\"extra\":{\"span_id\":\"459763144582250870\",\"trace_id\":\"227868759688616233839857801253686594728\"}}\u001b[0m| \u001b[36mcommon.stats.metric\u001b[0m:\u001b[36m__init__\u001b[0m:\u001b[36m134\u001b[0m | \u001b[35mtrace_id=227868759688616233839857801253686594728\u001b[0m | \u001b[34mspan_id=459763144582250870\u001b[0m\n", "\u001b[32m2025-02-05 18:37:09.782\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[1mrequesting person info for ['https://www.linkedin.com/in/yann-lecun/']\u001b[0m | \u001b[33mserialized: {\"message\":\"requesting person info for ['https://www.linkedin.com/in/yann-lecun/']\",\"name\":\"CrustdataClient\",\"lineno\":211,\"function_name\":\"get_person_info\",\"level\":\"INFO\",\"timestamp\":1738809429782.074,\"time\":\"2025-02-05T18:37:09.782074-08:00\",\"thread_id\":8272231104,\"thread_name\":\"MainThread\",\"process_id\":14996,\"extra\":{\"span_id\":\"15511590242171702096\",\"trace_id\":\"179585644230766866094772985273587734292\"}}\u001b[0m| \u001b[36mCrustdataClient\u001b[0m:\u001b[36mget_person_info\u001b[0m:\u001b[36m211\u001b[0m | \u001b[35mtrace_id=179585644230766866094772985273587734292\u001b[0m | \u001b[34mspan_id=15511590242171702096\u001b[0m\n", "\u001b[32m2025-02-05 18:37:10.289\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[1mHTTP Request: GET https://api.crustdata.com/screener/person/enrich?linkedin_profile_url=https%3A%2F%2Fwww.linkedin.com%2Fin%2Fyann-lecun%2F \"HTTP/1.1 200 OK\"\u001b[0m | \u001b[33mserialized: {\"message\":\"HTTP Request: GET https://api.crustdata.com/screener/person/enrich?linkedin_profile_url=https%3A%2F%2Fwww.linkedin.com%2Fin%2Fyann-lecun%2F \\\"HTTP/1.1 200 OK\\\"\",\"name\":\"httpx\",\"lineno\":1773,\"function_name\":\"_send_single_request\",\"level\":\"INFO\",\"timestamp\":1738809430289.978,\"time\":\"2025-02-05T18:37:10.289978-08:00\",\"thread_id\":8272231104,\"thread_name\":\"MainThread\",\"process_id\":14996,\"extra\":{\"span_id\":\"16091849929417328844\",\"trace_id\":\"280614197725666622973313876847458372377\"}}\u001b[0m| \u001b[36mhttpx\u001b[0m:\u001b[36m_send_single_request\u001b[0m:\u001b[36m1773\u001b[0m | \u001b[35mtrace_id=280614197725666622973313876847458372377\u001b[0m | \u001b[34mspan_id=16091849929417328844\u001b[0m\n", "[CrustdataPersonInfo(linkedin_profile_url='https://www.linkedin.com/in/ACwAAAADFk0BpwUBhc14vvI5ncYN5k8TxJEWBdE', linkedin_flagship_url='https://www.linkedin.com/in/yann-lecun', name='<PERSON><PERSON>', first_name=None, last_name=None, location='New York, New York, United States', email=None, title=None, last_updated=datetime.datetime(2025, 2, 5, 4, 35, 57, 156625, tzinfo=TzInfo(UTC)), headline='VP & Chief AI Scientist at Meta', summary='Professor, researcher, and R&D manager with academic and industry experience in AI, machine learning, deep learning, computer vision, intelligent data analysis, data mining, data compression,  digital library systems, and robotics.\\n\\nSpecialties: research, technical consulting, scientific advising.', num_of_connections=1383, skills=['Research', 'machine learning', 'computer vision', 'robotics', 'Statistical Data Analysis', 'Data Mining', 'C', 'C++', 'Image Processing', 'Research Management', 'Computational Neuroscience', 'Image Compression', 'Artificial Intelligence', 'Pattern Recognition', 'Machine Learning', 'Algorithms', 'Computer Vision', 'Perl', 'Computer Science', 'Robotics', 'Statistics', 'LaTeX', 'Statistical Modeling', 'Science', 'Mathematical Modeling', 'Signal Processing', 'Optimization', 'Data Science', 'Data Analysis', 'Neuroscience', 'Neural Networks', 'Data mining'], profile_picture_url='https://media.licdn.com/dms/image/v2/C4D03AQHBLoV7GRUP2w/profile-displayphoto-shrink_400_400/profile-displayphoto-shrink_400_400/0/1642552032280?e=1744243200&v=beta&t=xnrPgRKgweBP2RYq3ufroZ0LD-QSJqyoPXQ58AgiIhE', twitter_handle='ylecun', languages=[], all_employers=['AT&T', 'Bell Laboratories', 'Facebook', 'NEC Corporation', 'New York University Stern School of Business', 'University of Toronto'], all_titles=['Department Head', 'Director of AI Research', 'Fellow', 'Professor', 'Research Associate (postdoc)', 'research staff member', 'Research staff member', 'Research Staff Member', 'VP & Chief AI Scientist', 'VP & Chief AI Scientist at Meta'], all_schools=['ESIEE PARIS', 'Pierre and Marie Curie University'], all_degrees=['Engineering Diploma', 'PhD'], past_employers=[EmployerInfo(employer_name='AT&T', employer_linkedin_profile_url=None, employer_linkedin_id='1052', employer_linkedin_description='We understand that our customers want an easier, less complicated life. , , We’re using our network, labs, products, services, and people to create a world where everything works together seamlessly, and life is better as a result. How will we continue to drive for this excellence in innovation?, , With you., , Our people, and their passion to succeed, are at the heart of what we do. Today, we’re poised to connect millions of people with their world, delivering the human benefits of technology in ways that defy the imaginable., , What are you dreaming of doing with your career?, , Find stories about our talent, career advice, opportunities, company news, and innovations here on LinkedIn., , To learn more about joining AT&T, visit: http://www.att.jobs, , We provide in some of our posts links to articles or posts from third-party websites unaffiliated with AT&T. In doing so, AT&T is not adopting, endorsing or otherwise approving the content of those articles or posts. AT&T is providing this content for your information only.', employer_company_id=[681950], employee_position_id=22825565, employee_title='Department Head', employee_description='Head of the Image Processing Research Department.\\n\\nDirected research in machine learning, pattern recognition, image compression, video compression, animated characters. Department members included Yoshua Bengio, Leon Bottou, Eric Cosatto, Hans-Peter Graf, Patrick Haffner, Fu Jie Huang, Jörn Ostermann, Patrice Simard, Vladimir Vapnik,', employee_location='Middletown, NJ', start_date=datetime.datetime(1996, 1, 1, 0, 0, tzinfo=TzInfo(UTC)), end_date=datetime.datetime(2002, 1, 1, 0, 0, tzinfo=TzInfo(UTC))), EmployerInfo(employer_name='NEC Corporation', employer_linkedin_profile_url=None, employer_linkedin_id='1696', employer_linkedin_description='NEC Corporation has established itself as a leader in the integration of IT and network technologies while promoting the brand statement of “Orchestrating a brighter world.” NEC enables businesses and communities to adapt to rapid changes taking place in both society and the market as it provides for the social values of safety, security, fairness and efficiency to promote a more sustainable world where everyone has the chance to reach their full potential. For more information, visit NEC at http://www.nec.com, , NEC\\'s Strengths and Competencies, ・Named \"2017 Global 100 Most Sustainable Corporations in the World\"\\u200b an Index Listing the World’s 100 Most Sustainable Corporations, ・Named one of the \"World\\'s Top 100 Most Innovative Organizations for 2016\"\\u200b by Thomson Reuters , ・Face recognition technology achieved No. 1 in the recent Face in Video Evaluation (FIVE, *1) testing performed by the U.S. National Institute of Standards and Technology (NIST) (*2), , (*1) https://www.nist.gov/programs-projects/face-video-evaluation-five, (*2) Results shown from the Face In Video Evaluation (FIVE), the Multiple Biometric Grand Challenge (MBGC), the Multiple Biometric Evaluation (MBE) and the Face Recognition Vendor Test (FRVT) do not constitute endorsement of any particular product by the U.S. Government., , ** Please note that we reserve the right to delete any comments posted on NEC Corporation’s LinkedIn page that are deemed extremely defamatory or libelous to any company or individual, or that may be offensive to others.', employer_company_id=[1043400], employee_position_id=22825492, employee_title='Fellow', employee_description='Research in machine learning and computer vision.', employee_location='Princeton, NJ', start_date=datetime.datetime(2002, 1, 1, 0, 0, tzinfo=TzInfo(UTC)), end_date=datetime.datetime(2003, 8, 1, 0, 0, tzinfo=TzInfo(UTC))), EmployerInfo(employer_name='University of Toronto', employer_linkedin_profile_url=None, employer_linkedin_id='3660', employer_linkedin_description='Founded in 1827, the University of Toronto is Canada’s top university with a long history of challenging the impossible and transforming society through the ingenuity and resolve of our faculty, students, alumni, and supporters. , , We are proud to be one of the world’s top research-intensive universities, bringing together top minds from every conceivable background and discipline to collaborate on the world’s most pressing challenges. As a catalyst for discovery, innovation, and progress, we prepare our students for success through an outstanding global education and commitment to inclusive excellence. , , The ideas, innovations, and actions of more than 660,000 graduates advance U of T’s impact on communities across the globe.', employer_company_id=[1239279], employee_position_id=40493141, employee_title='Research Associate (postdoc)', employee_description=\"postdoc in the Geoff Hinton's lab in the Department of Computer Science.\", employee_location=None, start_date=datetime.datetime(1987, 1, 1, 0, 0, tzinfo=TzInfo(UTC)), end_date=datetime.datetime(1988, 1, 1, 0, 0, tzinfo=TzInfo(UTC))), EmployerInfo(employer_name='Bell Laboratories', employer_linkedin_profile_url=None, employer_linkedin_id='7545', employer_linkedin_description='Throughout our 100-year history, Nokia Bell Labs has overcome some of the world’s biggest scientific challenges by bringing together the brightest minds in mathematics, physics, computing and engineering. We identify a problem, and then commit our best people to find a solution. As Nokia’s industrial research lab, we innovate with purpose, pursuing responsible, sustainable technologies that will have a demonstrable impact on society. And we believe the best research is done in an inclusive, collaborative manner, taking multiple diverse points of view into account.', employer_company_id=[9434], employee_position_id=********, employee_title='research staff member', employee_description='Carried out research in machine learning, neural nets, optical character recognition, handwriting recognition, signature verification, integrated circuits for pattern recogntiion, and other topics.', employee_location='Holmdel, NJ', start_date=datetime.datetime(1988, 10, 1, 0, 0, tzinfo=TzInfo(UTC)), end_date=datetime.datetime(1996, 1, 1, 0, 0, tzinfo=TzInfo(UTC))), EmployerInfo(employer_name='Bell Laboratories', employer_linkedin_profile_url=None, employer_linkedin_id='7545', employer_linkedin_description='Throughout our 100-year history, Nokia Bell Labs has overcome some of the world’s biggest scientific challenges by bringing together the brightest minds in mathematics, physics, computing and engineering. We identify a problem, and then commit our best people to find a solution. As Nokia’s industrial research lab, we innovate with purpose, pursuing responsible, sustainable technologies that will have a demonstrable impact on society. And we believe the best research is done in an inclusive, collaborative manner, taking multiple diverse points of view into account.', employer_company_id=[9434], employee_position_id=********, employee_title='Research staff member', employee_description=None, employee_location=None, start_date=datetime.datetime(1988, 1, 1, 0, 0, tzinfo=TzInfo(UTC)), end_date=datetime.datetime(1996, 1, 1, 0, 0, tzinfo=TzInfo(UTC))), EmployerInfo(employer_name='Bell Laboratories', employer_linkedin_profile_url=None, employer_linkedin_id='7545', employer_linkedin_description='Throughout our 100-year history, Nokia Bell Labs has overcome some of the world’s biggest scientific challenges by bringing together the brightest minds in mathematics, physics, computing and engineering. We identify a problem, and then commit our best people to find a solution. As Nokia’s industrial research lab, we innovate with purpose, pursuing responsible, sustainable technologies that will have a demonstrable impact on society. And we believe the best research is done in an inclusive, collaborative manner, taking multiple diverse points of view into account.', employer_company_id=[9434], employee_position_id=********, employee_title='Research Staff Member', employee_description=None, employee_location=None, start_date=datetime.datetime(1988, 1, 1, 0, 0, tzinfo=TzInfo(UTC)), end_date=datetime.datetime(1996, 1, 1, 0, 0, tzinfo=TzInfo(UTC))), EmployerInfo(employer_name='Facebook', employer_linkedin_profile_url=None, employer_linkedin_id='********', employer_linkedin_description='The Facebook company is now Meta. Meta builds technologies that help people connect, find communities, and grow businesses. When Facebook launched in 2004, it changed the way people connect. Apps like Messenger, Instagram and WhatsApp further empowered billions around the world. Now, Meta is moving beyond 2D screens toward immersive experiences like augmented and virtual reality to help build the next evolution in social technology. , , We want to give people the power to build community and bring the world closer together. To do that, we ask that you help create a safe and respectful online space. These community values encourage constructive conversations on this page:, , • Start with an open mind. Whether you agree or disagree, engage with empathy., • Comments violating our Community Standards will be removed or hidden. So please treat everybody with respect. , • Keep it constructive. Use your interactions here to learn about and grow your understanding of others., • Our moderators are here to uphold these guidelines for the benefit of everyone, every day. , • If you are seeking support for issues related to your Facebook account, please reference our Help Center (https://www.facebook.com/help) or Help Community (https://www.facebook.com/help/community)., , For a full listing of our jobs, visit http://www.facebookcareers.com', employer_company_id=[764486], employee_position_id=*********, employee_title='Director of AI Research', employee_description='Director of Facebook AI Research (FAIR), with research locations in Menlo Park, New York City, and Paris, focused on bringing about major advances in AI.\\n\\nProfessor at NYU on a part-time basis (since January 2014).\\n\\nCofounder and advisor of Elements Inc. (biometrics), and Museami (music technology)', employee_location='Greater New York City Area', start_date=datetime.datetime(2013, 12, 1, 0, 0, tzinfo=TzInfo(UTC)), end_date=datetime.datetime(2018, 2, 1, 0, 0, tzinfo=TzInfo(UTC))), EmployerInfo(employer_name='Facebook', employer_linkedin_profile_url=None, employer_linkedin_id='********', employer_linkedin_description='The Facebook company is now Meta. Meta builds technologies that help people connect, find communities, and grow businesses. When Facebook launched in 2004, it changed the way people connect. Apps like Messenger, Instagram and WhatsApp further empowered billions around the world. Now, Meta is moving beyond 2D screens toward immersive experiences like augmented and virtual reality to help build the next evolution in social technology. , , We want to give people the power to build community and bring the world closer together. To do that, we ask that you help create a safe and respectful online space. These community values encourage constructive conversations on this page:, , • Start with an open mind. Whether you agree or disagree, engage with empathy., • Comments violating our Community Standards will be removed or hidden. So please treat everybody with respect. , • Keep it constructive. Use your interactions here to learn about and grow your understanding of others., • Our moderators are here to uphold these guidelines for the benefit of everyone, every day. , • If you are seeking support for issues related to your Facebook account, please reference our Help Center (https://www.facebook.com/help) or Help Community (https://www.facebook.com/help/community)., , For a full listing of our jobs, visit http://www.facebookcareers.com', employer_company_id=[764486], employee_position_id=**********, employee_title='VP & Chief AI Scientist', employee_description='Scientific and technical leadership at Facebook AI Research, AI R&D strategy.', employee_location='Greater New York City Area', start_date=datetime.datetime(2018, 3, 1, 0, 0, tzinfo=TzInfo(UTC)), end_date=datetime.datetime(2021, 10, 1, 0, 0, tzinfo=TzInfo(UTC)))], current_employers=[EmployerInfo(employer_name='New York University Stern School of Business', employer_linkedin_profile_url=None, employer_linkedin_id='3159', employer_linkedin_description=None, employer_company_id=[], employee_position_id=239324, employee_title='Professor', employee_description='Silver Professor of Computer Science, Courant Institute of Mathematical Sciences (since 2006)\\nProfessor of Neural Science, NYU Center for Neural Science (since 2007)\\nProfessor of Electrical and Computer Engineering, NYU School of Engineering (since 2011)\\nProfessor of Data Science, NYU Center for Data Science (since 2013)\\nFounding Director of the NYU Center for Data Science (since 2013)\\n\\nResearch and teaching in machine learning, AI, data science, computer vision, robotics, computational neuroscience, and knowledge extraction from data,', employee_location='Greater New York City Area', start_date=datetime.datetime(2003, 9, 1, 0, 0, tzinfo=TzInfo(UTC)), end_date=None), EmployerInfo(employer_name='Facebook', employer_linkedin_profile_url=None, employer_linkedin_id='********', employer_linkedin_description='The Facebook company is now Meta. Meta builds technologies that help people connect, find communities, and grow businesses. When Facebook launched in 2004, it changed the way people connect. Apps like Messenger, Instagram and WhatsApp further empowered billions around the world. Now, Meta is moving beyond 2D screens toward immersive experiences like augmented and virtual reality to help build the next evolution in social technology. , , We want to give people the power to build community and bring the world closer together. To do that, we ask that you help create a safe and respectful online space. These community values encourage constructive conversations on this page:, , • Start with an open mind. Whether you agree or disagree, engage with empathy., • Comments violating our Community Standards will be removed or hidden. So please treat everybody with respect. , • Keep it constructive. Use your interactions here to learn about and grow your understanding of others., • Our moderators are here to uphold these guidelines for the benefit of everyone, every day. , • If you are seeking support for issues related to your Facebook account, please reference our Help Center (https://www.facebook.com/help) or Help Community (https://www.facebook.com/help/community)., , For a full listing of our jobs, visit http://www.facebookcareers.com', employer_company_id=[764486], employee_position_id=**********, employee_title='VP & Chief AI Scientist at Meta', employee_description=None, employee_location='New York, New York, United States', start_date=datetime.datetime(2021, 10, 1, 0, 0, tzinfo=TzInfo(UTC)), end_date=None)], education_background=[{'degree_name': 'Engineering Diploma', 'institute_name': 'ESIEE PARIS', 'institute_linkedin_id': '********', 'institute_linkedin_url': 'https://www.linkedin.com/school/********/', 'institute_logo_url': 'https://media.licdn.com/dms/image/v2/D4E0BAQGnsKmdPlPdCg/company-logo_400_400/company-logo_400_400/0/*************/esiee_paris_logo?e=**********&v=beta&t=xrYBKhpdd0Rs0RrFgP0uzuugAlOUvxt80i6FhcqfUx4', 'field_of_study': 'Electrical and Electronics Engineering', 'start_date': '1978-01-01T00:00:00+00:00', 'end_date': '1983-01-01T00:00:00+00:00'}, {'degree_name': 'PhD', 'institute_name': 'Pierre and Marie Curie University', 'institute_linkedin_id': '288600', 'institute_linkedin_url': 'https://www.linkedin.com/school/288600/', 'institute_logo_url': 'https://media.licdn.com/dms/image/v2/C4D0BAQFB2Andpcm4zw/company-logo_400_400/company-logo_400_400/0/1631313764672?e=**********&v=beta&t=sbfbwq87Ap66VUtN3Qsfx1L2mGKZMLbaPYbpMXmLeQk', 'field_of_study': 'Computer Science', 'start_date': '1983-01-01T00:00:00+00:00', 'end_date': '1987-01-01T00:00:00+00:00'}], all_employers_company_id=[681950, 1043400, 1239279, 9434, 9434, 9434, 764486, 764486, 764486], enriched_realtime=False, query_linkedin_profile_urn_or_slug=['yann-lecun'])]\n"]}], "source": ["\n", "from salestech_be.temporal.activities.research_agent.schema import GetPersonInfosInput\n", "from salestech_be.temporal.activities.research_agent.linkedin_profile import (\n", "    get_person_infos,\n", ")\n", "from salestech_be.temporal.activities.research_agent.user_intel_profile_pic import (\n", "    update_user_profile_pic_from_linkedin,\n", ")\n", "\n", "person_input = GetPersonInfosInput(\n", "    person_linkedin_urls=[\"https://www.linkedin.com/in/yann-lecun/\"],\n", "    business_emails=[\"<EMAIL>\"],\n", ")\n", "\n", "#get person data\n", "try:\n", "    person_data = await get_person_infos(person_input)\n", "    print(person_data)\n", "except Exception as e:\n", "    print(e)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["CrustdataPersonInfo(linkedin_profile_url='https://www.linkedin.com/in/ACwAAAADFk0BpwUBhc14vvI5ncYN5k8TxJEWBdE', linkedin_flagship_url='https://www.linkedin.com/in/yann-lecun', name='<PERSON><PERSON>', first_name=None, last_name=None, location='New York, New York, United States', email=None, title=None, last_updated=datetime.datetime(2025, 2, 5, 4, 35, 57, 156625, tzinfo=TzInfo(UTC)), headline='VP & Chief AI Scientist at Meta', summary='Professor, researcher, and R&D manager with academic and industry experience in AI, machine learning, deep learning, computer vision, intelligent data analysis, data mining, data compression,  digital library systems, and robotics.\\n\\nSpecialties: research, technical consulting, scientific advising.', num_of_connections=1383, skills=['Research', 'machine learning', 'computer vision', 'robotics', 'Statistical Data Analysis', 'Data Mining', 'C', 'C++', 'Image Processing', 'Research Management', 'Computational Neuroscience', 'Image Compression', 'Artificial Intelligence', 'Pattern Recognition', 'Machine Learning', 'Algorithms', 'Computer Vision', 'Perl', 'Computer Science', 'Robotics', 'Statistics', 'LaTeX', 'Statistical Modeling', 'Science', 'Mathematical Modeling', 'Signal Processing', 'Optimization', 'Data Science', 'Data Analysis', 'Neuroscience', 'Neural Networks', 'Data mining'], profile_picture_url='https://media.licdn.com/dms/image/v2/C4D03AQHBLoV7GRUP2w/profile-displayphoto-shrink_400_400/profile-displayphoto-shrink_400_400/0/1642552032280?e=1744243200&v=beta&t=xnrPgRKgweBP2RYq3ufroZ0LD-QSJqyoPXQ58AgiIhE', twitter_handle='ylecun', languages=[], all_employers=['AT&T', 'Bell Laboratories', 'Facebook', 'NEC Corporation', 'New York University Stern School of Business', 'University of Toronto'], all_titles=['Department Head', 'Director of AI Research', 'Fellow', 'Professor', 'Research Associate (postdoc)', 'research staff member', 'Research staff member', 'Research Staff Member', 'VP & Chief AI Scientist', 'VP & Chief AI Scientist at Meta'], all_schools=['ESIEE PARIS', 'Pierre and Marie Curie University'], all_degrees=['Engineering Diploma', 'PhD'], past_employers=[EmployerInfo(employer_name='AT&T', employer_linkedin_profile_url=None, employer_linkedin_id='1052', employer_linkedin_description='We understand that our customers want an easier, less complicated life. , , We’re using our network, labs, products, services, and people to create a world where everything works together seamlessly, and life is better as a result. How will we continue to drive for this excellence in innovation?, , With you., , Our people, and their passion to succeed, are at the heart of what we do. Today, we’re poised to connect millions of people with their world, delivering the human benefits of technology in ways that defy the imaginable., , What are you dreaming of doing with your career?, , Find stories about our talent, career advice, opportunities, company news, and innovations here on LinkedIn., , To learn more about joining AT&T, visit: http://www.att.jobs, , We provide in some of our posts links to articles or posts from third-party websites unaffiliated with AT&T. In doing so, AT&T is not adopting, endorsing or otherwise approving the content of those articles or posts. AT&T is providing this content for your information only.', employer_company_id=[681950], employee_position_id=22825565, employee_title='Department Head', employee_description='Head of the Image Processing Research Department.\\n\\nDirected research in machine learning, pattern recognition, image compression, video compression, animated characters. Department members included Yoshua Bengio, Leon Bottou, Eric Cosatto, Hans-Peter Graf, Patrick Haffner, Fu Jie Huang, Jörn Ostermann, Patrice Simard, Vladimir Vapnik,', employee_location='Middletown, NJ', start_date=datetime.datetime(1996, 1, 1, 0, 0, tzinfo=TzInfo(UTC)), end_date=datetime.datetime(2002, 1, 1, 0, 0, tzinfo=TzInfo(UTC))), EmployerInfo(employer_name='NEC Corporation', employer_linkedin_profile_url=None, employer_linkedin_id='1696', employer_linkedin_description='NEC Corporation has established itself as a leader in the integration of IT and network technologies while promoting the brand statement of “Orchestrating a brighter world.” NEC enables businesses and communities to adapt to rapid changes taking place in both society and the market as it provides for the social values of safety, security, fairness and efficiency to promote a more sustainable world where everyone has the chance to reach their full potential. For more information, visit NEC at http://www.nec.com, , NEC\\'s Strengths and Competencies, ・Named \"2017 Global 100 Most Sustainable Corporations in the World\"\\u200b an Index Listing the World’s 100 Most Sustainable Corporations, ・Named one of the \"World\\'s Top 100 Most Innovative Organizations for 2016\"\\u200b by Thomson Reuters , ・Face recognition technology achieved No. 1 in the recent Face in Video Evaluation (FIVE, *1) testing performed by the U.S. National Institute of Standards and Technology (NIST) (*2), , (*1) https://www.nist.gov/programs-projects/face-video-evaluation-five, (*2) Results shown from the Face In Video Evaluation (FIVE), the Multiple Biometric Grand Challenge (MBGC), the Multiple Biometric Evaluation (MBE) and the Face Recognition Vendor Test (FRVT) do not constitute endorsement of any particular product by the U.S. Government., , ** Please note that we reserve the right to delete any comments posted on NEC Corporation’s LinkedIn page that are deemed extremely defamatory or libelous to any company or individual, or that may be offensive to others.', employer_company_id=[1043400], employee_position_id=22825492, employee_title='Fellow', employee_description='Research in machine learning and computer vision.', employee_location='Princeton, NJ', start_date=datetime.datetime(2002, 1, 1, 0, 0, tzinfo=TzInfo(UTC)), end_date=datetime.datetime(2003, 8, 1, 0, 0, tzinfo=TzInfo(UTC))), EmployerInfo(employer_name='University of Toronto', employer_linkedin_profile_url=None, employer_linkedin_id='3660', employer_linkedin_description='Founded in 1827, the University of Toronto is Canada’s top university with a long history of challenging the impossible and transforming society through the ingenuity and resolve of our faculty, students, alumni, and supporters. , , We are proud to be one of the world’s top research-intensive universities, bringing together top minds from every conceivable background and discipline to collaborate on the world’s most pressing challenges. As a catalyst for discovery, innovation, and progress, we prepare our students for success through an outstanding global education and commitment to inclusive excellence. , , The ideas, innovations, and actions of more than 660,000 graduates advance U of T’s impact on communities across the globe.', employer_company_id=[1239279], employee_position_id=40493141, employee_title='Research Associate (postdoc)', employee_description=\"postdoc in the Geoff Hinton's lab in the Department of Computer Science.\", employee_location=None, start_date=datetime.datetime(1987, 1, 1, 0, 0, tzinfo=TzInfo(UTC)), end_date=datetime.datetime(1988, 1, 1, 0, 0, tzinfo=TzInfo(UTC))), EmployerInfo(employer_name='Bell Laboratories', employer_linkedin_profile_url=None, employer_linkedin_id='7545', employer_linkedin_description='Throughout our 100-year history, Nokia Bell Labs has overcome some of the world’s biggest scientific challenges by bringing together the brightest minds in mathematics, physics, computing and engineering. We identify a problem, and then commit our best people to find a solution. As Nokia’s industrial research lab, we innovate with purpose, pursuing responsible, sustainable technologies that will have a demonstrable impact on society. And we believe the best research is done in an inclusive, collaborative manner, taking multiple diverse points of view into account.', employer_company_id=[9434], employee_position_id=********, employee_title='research staff member', employee_description='Carried out research in machine learning, neural nets, optical character recognition, handwriting recognition, signature verification, integrated circuits for pattern recogntiion, and other topics.', employee_location='Holmdel, NJ', start_date=datetime.datetime(1988, 10, 1, 0, 0, tzinfo=TzInfo(UTC)), end_date=datetime.datetime(1996, 1, 1, 0, 0, tzinfo=TzInfo(UTC))), EmployerInfo(employer_name='Bell Laboratories', employer_linkedin_profile_url=None, employer_linkedin_id='7545', employer_linkedin_description='Throughout our 100-year history, Nokia Bell Labs has overcome some of the world’s biggest scientific challenges by bringing together the brightest minds in mathematics, physics, computing and engineering. We identify a problem, and then commit our best people to find a solution. As Nokia’s industrial research lab, we innovate with purpose, pursuing responsible, sustainable technologies that will have a demonstrable impact on society. And we believe the best research is done in an inclusive, collaborative manner, taking multiple diverse points of view into account.', employer_company_id=[9434], employee_position_id=********, employee_title='Research staff member', employee_description=None, employee_location=None, start_date=datetime.datetime(1988, 1, 1, 0, 0, tzinfo=TzInfo(UTC)), end_date=datetime.datetime(1996, 1, 1, 0, 0, tzinfo=TzInfo(UTC))), EmployerInfo(employer_name='Bell Laboratories', employer_linkedin_profile_url=None, employer_linkedin_id='7545', employer_linkedin_description='Throughout our 100-year history, Nokia Bell Labs has overcome some of the world’s biggest scientific challenges by bringing together the brightest minds in mathematics, physics, computing and engineering. We identify a problem, and then commit our best people to find a solution. As Nokia’s industrial research lab, we innovate with purpose, pursuing responsible, sustainable technologies that will have a demonstrable impact on society. And we believe the best research is done in an inclusive, collaborative manner, taking multiple diverse points of view into account.', employer_company_id=[9434], employee_position_id=********, employee_title='Research Staff Member', employee_description=None, employee_location=None, start_date=datetime.datetime(1988, 1, 1, 0, 0, tzinfo=TzInfo(UTC)), end_date=datetime.datetime(1996, 1, 1, 0, 0, tzinfo=TzInfo(UTC))), EmployerInfo(employer_name='Facebook', employer_linkedin_profile_url=None, employer_linkedin_id='********', employer_linkedin_description='The Facebook company is now Meta. Meta builds technologies that help people connect, find communities, and grow businesses. When Facebook launched in 2004, it changed the way people connect. Apps like Messenger, Instagram and WhatsApp further empowered billions around the world. Now, Meta is moving beyond 2D screens toward immersive experiences like augmented and virtual reality to help build the next evolution in social technology. , , We want to give people the power to build community and bring the world closer together. To do that, we ask that you help create a safe and respectful online space. These community values encourage constructive conversations on this page:, , • Start with an open mind. Whether you agree or disagree, engage with empathy., • Comments violating our Community Standards will be removed or hidden. So please treat everybody with respect. , • Keep it constructive. Use your interactions here to learn about and grow your understanding of others., • Our moderators are here to uphold these guidelines for the benefit of everyone, every day. , • If you are seeking support for issues related to your Facebook account, please reference our Help Center (https://www.facebook.com/help) or Help Community (https://www.facebook.com/help/community)., , For a full listing of our jobs, visit http://www.facebookcareers.com', employer_company_id=[764486], employee_position_id=*********, employee_title='Director of AI Research', employee_description='Director of Facebook AI Research (FAIR), with research locations in Menlo Park, New York City, and Paris, focused on bringing about major advances in AI.\\n\\nProfessor at NYU on a part-time basis (since January 2014).\\n\\nCofounder and advisor of Elements Inc. (biometrics), and Museami (music technology)', employee_location='Greater New York City Area', start_date=datetime.datetime(2013, 12, 1, 0, 0, tzinfo=TzInfo(UTC)), end_date=datetime.datetime(2018, 2, 1, 0, 0, tzinfo=TzInfo(UTC))), EmployerInfo(employer_name='Facebook', employer_linkedin_profile_url=None, employer_linkedin_id='********', employer_linkedin_description='The Facebook company is now Meta. Meta builds technologies that help people connect, find communities, and grow businesses. When Facebook launched in 2004, it changed the way people connect. Apps like Messenger, Instagram and WhatsApp further empowered billions around the world. Now, Meta is moving beyond 2D screens toward immersive experiences like augmented and virtual reality to help build the next evolution in social technology. , , We want to give people the power to build community and bring the world closer together. To do that, we ask that you help create a safe and respectful online space. These community values encourage constructive conversations on this page:, , • Start with an open mind. Whether you agree or disagree, engage with empathy., • Comments violating our Community Standards will be removed or hidden. So please treat everybody with respect. , • Keep it constructive. Use your interactions here to learn about and grow your understanding of others., • Our moderators are here to uphold these guidelines for the benefit of everyone, every day. , • If you are seeking support for issues related to your Facebook account, please reference our Help Center (https://www.facebook.com/help) or Help Community (https://www.facebook.com/help/community)., , For a full listing of our jobs, visit http://www.facebookcareers.com', employer_company_id=[764486], employee_position_id=**********, employee_title='VP & Chief AI Scientist', employee_description='Scientific and technical leadership at Facebook AI Research, AI R&D strategy.', employee_location='Greater New York City Area', start_date=datetime.datetime(2018, 3, 1, 0, 0, tzinfo=TzInfo(UTC)), end_date=datetime.datetime(2021, 10, 1, 0, 0, tzinfo=TzInfo(UTC)))], current_employers=[EmployerInfo(employer_name='New York University Stern School of Business', employer_linkedin_profile_url=None, employer_linkedin_id='3159', employer_linkedin_description=None, employer_company_id=[], employee_position_id=239324, employee_title='Professor', employee_description='Silver Professor of Computer Science, Courant Institute of Mathematical Sciences (since 2006)\\nProfessor of Neural Science, NYU Center for Neural Science (since 2007)\\nProfessor of Electrical and Computer Engineering, NYU School of Engineering (since 2011)\\nProfessor of Data Science, NYU Center for Data Science (since 2013)\\nFounding Director of the NYU Center for Data Science (since 2013)\\n\\nResearch and teaching in machine learning, AI, data science, computer vision, robotics, computational neuroscience, and knowledge extraction from data,', employee_location='Greater New York City Area', start_date=datetime.datetime(2003, 9, 1, 0, 0, tzinfo=TzInfo(UTC)), end_date=None), EmployerInfo(employer_name='Facebook', employer_linkedin_profile_url=None, employer_linkedin_id='********', employer_linkedin_description='The Facebook company is now Meta. Meta builds technologies that help people connect, find communities, and grow businesses. When Facebook launched in 2004, it changed the way people connect. Apps like Messenger, Instagram and WhatsApp further empowered billions around the world. Now, Meta is moving beyond 2D screens toward immersive experiences like augmented and virtual reality to help build the next evolution in social technology. , , We want to give people the power to build community and bring the world closer together. To do that, we ask that you help create a safe and respectful online space. These community values encourage constructive conversations on this page:, , • Start with an open mind. Whether you agree or disagree, engage with empathy., • Comments violating our Community Standards will be removed or hidden. So please treat everybody with respect. , • Keep it constructive. Use your interactions here to learn about and grow your understanding of others., • Our moderators are here to uphold these guidelines for the benefit of everyone, every day. , • If you are seeking support for issues related to your Facebook account, please reference our Help Center (https://www.facebook.com/help) or Help Community (https://www.facebook.com/help/community)., , For a full listing of our jobs, visit http://www.facebookcareers.com', employer_company_id=[764486], employee_position_id=**********, employee_title='VP & Chief AI Scientist at Meta', employee_description=None, employee_location='New York, New York, United States', start_date=datetime.datetime(2021, 10, 1, 0, 0, tzinfo=TzInfo(UTC)), end_date=None)], education_background=[{'degree_name': 'Engineering Diploma', 'institute_name': 'ESIEE PARIS', 'institute_linkedin_id': '********', 'institute_linkedin_url': 'https://www.linkedin.com/school/********/', 'institute_logo_url': 'https://media.licdn.com/dms/image/v2/D4E0BAQGnsKmdPlPdCg/company-logo_400_400/company-logo_400_400/0/*************/esiee_paris_logo?e=**********&v=beta&t=xrYBKhpdd0Rs0RrFgP0uzuugAlOUvxt80i6FhcqfUx4', 'field_of_study': 'Electrical and Electronics Engineering', 'start_date': '1978-01-01T00:00:00+00:00', 'end_date': '1983-01-01T00:00:00+00:00'}, {'degree_name': 'PhD', 'institute_name': 'Pierre and Marie Curie University', 'institute_linkedin_id': '288600', 'institute_linkedin_url': 'https://www.linkedin.com/school/288600/', 'institute_logo_url': 'https://media.licdn.com/dms/image/v2/C4D0BAQFB2Andpcm4zw/company-logo_400_400/company-logo_400_400/0/1631313764672?e=**********&v=beta&t=sbfbwq87Ap66VUtN3Qsfx1L2mGKZMLbaPYbpMXmLeQk', 'field_of_study': 'Computer Science', 'start_date': '1983-01-01T00:00:00+00:00', 'end_date': '1987-01-01T00:00:00+00:00'}], all_employers_company_id=[681950, 1043400, 1239279, 9434, 9434, 9434, 764486, 764486, 764486], enriched_realtime=False, query_linkedin_profile_urn_or_slug=['yann-lecun'])"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["person_data[0]"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'id': ColumnFieldInfo(mapped_column=MappedColumn(is_json=False, is_array=False), field_info=FieldInfo(annotation=UUID, required=True, metadata=[MappedColumn(is_json=False, is_array=False)])), 'sys_updated_at': ColumnFieldInfo(mapped_column=MappedColumn(is_json=False, is_array=False), field_info=FieldInfo(annotation=AwareDatetime, required=False, default_factory=zoned_utc_now, metadata=[MappedColumn(is_json=False, is_array=False)])), 'email': ColumnFieldInfo(mapped_column=MappedColumn(is_json=False, is_array=False), field_info=FieldInfo(annotation=str, required=True, metadata=[MappedColumn(is_json=False, is_array=False)])), 'created_at': ColumnFieldInfo(mapped_column=MappedColumn(is_json=False, is_array=False), field_info=FieldInfo(annotation=datetime, required=True, metadata=[MappedColumn(is_json=False, is_array=False)])), 'first_name': ColumnFieldInfo(mapped_column=MappedColumn(is_json=False, is_array=False), field_info=FieldInfo(annotation=Union[str, NoneType], required=False, default=None, metadata=[MappedColumn(is_json=False, is_array=False)])), 'last_name': ColumnFieldInfo(mapped_column=MappedColumn(is_json=False, is_array=False), field_info=FieldInfo(annotation=Union[str, NoneType], required=False, default=None, metadata=[MappedColumn(is_json=False, is_array=False)])), 'phone_number': ColumnFieldInfo(mapped_column=MappedColumn(is_json=False, is_array=False), field_info=FieldInfo(annotation=Union[str, NoneType], required=False, default=None, metadata=[MappedColumn(is_json=False, is_array=False)])), 'linkedin_url': ColumnFieldInfo(mapped_column=MappedColumn(is_json=False, is_array=False), field_info=FieldInfo(annotation=Union[str, NoneType], required=False, default=None, metadata=[MappedColumn(is_json=False, is_array=False)])), 'avatar_s3_key': ColumnFieldInfo(mapped_column=MappedColumn(is_json=False, is_array=False), field_info=FieldInfo(annotation=Union[str, NoneType], required=False, default=None, metadata=[MappedColumn(is_json=False, is_array=False)])), 'created_by_user_id': ColumnFieldInfo(mapped_column=MappedColumn(is_json=False, is_array=False), field_info=FieldInfo(annotation=Union[UUID, NoneType], required=False, default=None, metadata=[MappedColumn(is_json=False, is_array=False)])), 'updated_at': ColumnFieldInfo(mapped_column=MappedColumn(is_json=False, is_array=False), field_info=FieldInfo(annotation=Union[datetime, NoneType], required=False, default=None, metadata=[MappedColumn(is_json=False, is_array=False)])), 'updated_by_user_id': ColumnFieldInfo(mapped_column=MappedColumn(is_json=False, is_array=False), field_info=FieldInfo(annotation=Union[UUID, NoneType], required=False, default=None, metadata=[MappedColumn(is_json=False, is_array=False)])), 'deactivated_at': ColumnFieldInfo(mapped_column=MappedColumn(is_json=False, is_array=False), field_info=FieldInfo(annotation=Union[datetime, NoneType], required=False, default=None, metadata=[MappedColumn(is_json=False, is_array=False)])), 'deactivated_by_user_id': ColumnFieldInfo(mapped_column=MappedColumn(is_json=False, is_array=False), field_info=FieldInfo(annotation=Union[UUID, NoneType], required=False, default=None, metadata=[MappedColumn(is_json=False, is_array=False)]))}\n", "{'id': ColumnFieldInfo(mapped_column=MappedColumn(is_json=False, is_array=False), field_info=FieldInfo(annotation=UUID, required=True, metadata=[MappedColumn(is_json=False, is_array=False)])), 'sys_updated_at': ColumnFieldInfo(mapped_column=MappedColumn(is_json=False, is_array=False), field_info=FieldInfo(annotation=AwareDatetime, required=False, default_factory=zoned_utc_now, metadata=[MappedColumn(is_json=False, is_array=False)])), 'email': ColumnFieldInfo(mapped_column=MappedColumn(is_json=False, is_array=False), field_info=FieldInfo(annotation=str, required=True, metadata=[MappedColumn(is_json=False, is_array=False)])), 'created_at': ColumnFieldInfo(mapped_column=MappedColumn(is_json=False, is_array=False), field_info=FieldInfo(annotation=datetime, required=True, metadata=[MappedColumn(is_json=False, is_array=False)])), 'first_name': ColumnFieldInfo(mapped_column=MappedColumn(is_json=False, is_array=False), field_info=FieldInfo(annotation=Union[str, NoneType], required=False, default=None, metadata=[MappedColumn(is_json=False, is_array=False)])), 'last_name': ColumnFieldInfo(mapped_column=MappedColumn(is_json=False, is_array=False), field_info=FieldInfo(annotation=Union[str, NoneType], required=False, default=None, metadata=[MappedColumn(is_json=False, is_array=False)])), 'phone_number': ColumnFieldInfo(mapped_column=MappedColumn(is_json=False, is_array=False), field_info=FieldInfo(annotation=Union[str, NoneType], required=False, default=None, metadata=[MappedColumn(is_json=False, is_array=False)])), 'linkedin_url': ColumnFieldInfo(mapped_column=MappedColumn(is_json=False, is_array=False), field_info=FieldInfo(annotation=Union[str, NoneType], required=False, default=None, metadata=[MappedColumn(is_json=False, is_array=False)])), 'avatar_s3_key': ColumnFieldInfo(mapped_column=MappedColumn(is_json=False, is_array=False), field_info=FieldInfo(annotation=Union[str, NoneType], required=False, default=None, metadata=[MappedColumn(is_json=False, is_array=False)])), 'created_by_user_id': ColumnFieldInfo(mapped_column=MappedColumn(is_json=False, is_array=False), field_info=FieldInfo(annotation=Union[UUID, NoneType], required=False, default=None, metadata=[MappedColumn(is_json=False, is_array=False)])), 'updated_at': ColumnFieldInfo(mapped_column=MappedColumn(is_json=False, is_array=False), field_info=FieldInfo(annotation=Union[datetime, NoneType], required=False, default=None, metadata=[MappedColumn(is_json=False, is_array=False)])), 'updated_by_user_id': ColumnFieldInfo(mapped_column=MappedColumn(is_json=False, is_array=False), field_info=FieldInfo(annotation=Union[UUID, NoneType], required=False, default=None, metadata=[MappedColumn(is_json=False, is_array=False)])), 'deactivated_at': ColumnFieldInfo(mapped_column=MappedColumn(is_json=False, is_array=False), field_info=FieldInfo(annotation=Union[datetime, NoneType], required=False, default=None, metadata=[MappedColumn(is_json=False, is_array=False)])), 'deactivated_by_user_id': ColumnFieldInfo(mapped_column=MappedColumn(is_json=False, is_array=False), field_info=FieldInfo(annotation=Union[UUID, NoneType], required=False, default=None, metadata=[MappedColumn(is_json=False, is_array=False)]))}\n"]}], "source": ["#pass this person data to update_user_profile_pic_from_linkedin\n", "await update_user_profile_pic_from_linkedin(person_data[0])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.8"}}, "nbformat": 4, "nbformat_minor": 2}