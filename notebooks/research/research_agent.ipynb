{"cells": [{"cell_type": "markdown", "id": "2e9d707723c0b11", "metadata": {}, "source": "# Web Search Tool"}, {"cell_type": "code", "execution_count": 261, "id": "initial_id", "metadata": {"ExecuteTime": {"end_time": "2024-08-15T22:05:38.816134Z", "start_time": "2024-08-15T22:05:38.143807Z"}, "collapsed": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Welcome to WaveCX - WaveCX\n", "Embrace dynamic in-app contentproduct demoscontent-rich pagesformsgenerative search · Trusted by · Deploy immersive & portable product demos to employees, ...\n", "https://wavecx.com/\n", "---\n", "WaveCX\n", "WaveCX is a customer experience training, feedback and marketing platform for financial institutions. WaveCX provides the high fidelity customer tutorials, ...\n", "https://www.linkedin.com/company/wavecx\n", "---\n", "WaveCX - Crunchbase Company Profile & Funding\n", "WaveCX is a product engagement platform that specializes in training employees and market innovation solutions.\n", "https://www.crunchbase.com/organization/wavecx\n", "---\n", "WaveCX Partners With William Mills Agency for Fintech ...\n", "WaveCX is a cutting-edge platform designed to streamline operations and boost customer and employee engagement for financial institutions. With ...\n", "https://www.morningstar.com/news/business-wire/20240814330352/wavecx-partners-with-william-mills-agency-for-fintech-public-relations\n", "---\n", "WaveCX Partners With William Mills Agency for Fintech ...\n", "WaveCX is a cutting-edge platform designed to streamline operations and boost customer and employee engagement for financial institutions. With ...\n", "https://finance.yahoo.com/news/wavecx-partners-william-mills-agency-*********.html\n", "---\n", "Best WaveCX Alternatives 2024\n", "Alternatives to WaveCX · Top WaveCX Alternatives · Rocketlane · OnScreen · GUIDEcx · VisualSP · Produktly · Gyde · Bytes Route. by Caphyon. 4.7 (3).\n", "https://www.capterra.com/p/236954/WaveCX/alternatives/\n", "---\n", "WaveCX Reviews & Ratings 2024\n", "WaveCX is a product engagement platform designed to provide contact-free updates and tutorials for both internal and external audiences.\n", "https://www.trustradius.com/products/wavecx/reviews\n"]}], "source": ["from salestech_be.settings import settings\n", "from serpapi import GoogleSearch\n", "\n", "serpapi_params = {\n", "    \"engine\": \"google\",\n", "    \"api_key\": settings.serp_api_key.get_secret_value(),\n", "}\n", "search = GoogleSearch({**serpapi_params, \"q\": \"wavecx\"})\n", "\n", "results = search.get_dict()[\"organic_results\"]\n", "contexts = \"\\n---\\n\".join(\n", "    [\"\\n\".join([x[\"title\"], x[\"snippet\"], x[\"link\"]]) for x in results]\n", ")\n", "print(contexts)"]}, {"cell_type": "code", "execution_count": 262, "id": "bec853009f98a586", "metadata": {"ExecuteTime": {"end_time": "2024-08-15T22:05:38.839410Z", "start_time": "2024-08-15T22:05:38.835578Z"}}, "outputs": [], "source": ["from langchain_core.tools import tool\n", "\n", "\n", "@tool(\"web_search\")\n", "def web_search(query: str):\n", "    \"\"\"Finds general knowledge information using Google search. Can also be used\n", "    to augment more 'general' knowledge to a previous specialist query.\"\"\"\n", "    search = GoogleSearch({**serpapi_params, \"q\": query, \"num\": 5})\n", "    results = search.get_dict()[\"organic_results\"]\n", "    contexts = \"\\n---\\n\".join(\n", "        [\"\\n\".join([x[\"title\"], x[\"snippet\"], x[\"link\"]]) for x in results]\n", "    )\n", "    return contexts"]}, {"cell_type": "markdown", "id": "f72ec74ca420698f", "metadata": {}, "source": "# <PERSON><PERSON><PERSON>l"}, {"cell_type": "code", "execution_count": 263, "id": "8c6feaca01fffd84", "metadata": {"ExecuteTime": {"end_time": "2024-08-15T22:05:38.849438Z", "start_time": "2024-08-15T22:05:38.845782Z"}}, "outputs": [], "source": ["import httpx\n", "from bs4 import BeautifulSoup\n", "\n", "\n", "class Scraper:\n", "    def __init__(self, root_url):\n", "        self.root_url = root_url\n", "        self.headers = {\n", "            \"User-Agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36\"\n", "        }\n", "\n", "    def scrape(self):\n", "        with httpx.Client(headers=self.headers) as client:\n", "            response = client.get(self.root_url)\n", "            soup = BeautifulSoup(response.text, \"html.parser\")\n", "\n", "            content = self.extract_content(soup)\n", "            urls = self.extract_urls(soup)\n", "\n", "            return {\"content\": content, \"linked_urls\": urls}\n", "\n", "    def extract_content(self, soup):\n", "        content = []\n", "        for element in soup.find_all(\n", "            [\"h1\", \"h2\", \"h3\", \"h4\", \"h5\", \"h6\", \"p\", \"ul\", \"ol\"]\n", "        ):\n", "            if element.name.startswith(\"h\"):\n", "                content.append(\n", "                    {\n", "                        \"type\": \"header\",\n", "                        \"level\": int(element.name[1]),\n", "                        \"text\": element.get_text(strip=True),\n", "                    }\n", "                )\n", "            elif element.name == \"p\":\n", "                content.append(\n", "                    {\"type\": \"paragraph\", \"text\": element.get_text(strip=True)}\n", "                )\n", "            elif element.name in [\"ul\", \"ol\"]:\n", "                items = [li.get_text(strip=True) for li in element.find_all(\"li\")]\n", "                content.append({\"type\": \"list\", \"items\": items})\n", "        return content\n", "\n", "    def extract_urls(self, soup):\n", "        urls = []\n", "        for a in soup.find_all(\"a\", href=True):\n", "            href = a[\"href\"]\n", "            if href.startswith(\"http\"):\n", "                urls.append(href)\n", "            elif href.startswith(\"/\"):\n", "                urls.append(self.root_url + href)\n", "        return list(set(urls))  # Remove duplicates\n", "\n", "\n", "# import json\n", "#\n", "# root_url = input(\"Enter the URL: \")\n", "# scraper = Scraper(root_url)\n", "# data = scraper.scrape()\n", "#\n", "# print(data)\n", "# with open('company_data.json', 'w') as f:\n", "#     json.dump(data, f, indent=2)"]}, {"cell_type": "code", "execution_count": 264, "id": "b9416427626a4c44", "metadata": {"ExecuteTime": {"end_time": "2024-08-15T22:05:38.864010Z", "start_time": "2024-08-15T22:05:38.861009Z"}}, "outputs": [], "source": ["import json\n", "\n", "from langchain_core.tools import tool\n", "\n", "\n", "@tool(\"scrape_url\")\n", "def scrape_url(url: str):\n", "    \"\"\"Scrapes the provided URL for its HTML content and linked URLs. It is recommended to drill down into linked URLs and scrape those as well if they show promise in providing more relevant information.\"\"\"\n", "    return json.dumps(<PERSON><PERSON><PERSON>(url).scrape())"]}, {"cell_type": "markdown", "id": "4148e378ea5cf109", "metadata": {}, "source": "# Final Answer Tool"}, {"cell_type": "code", "execution_count": 265, "id": "e2c153bbd9598756", "metadata": {"ExecuteTime": {"end_time": "2024-08-15T22:05:38.879Z", "start_time": "2024-08-15T22:05:38.875437Z"}}, "outputs": [], "source": ["@tool(\"final_answer\")\n", "def final_answer(\n", "    executive_summary: str,\n", "    research_steps: list[str],\n", "    main_body: str,\n", "    sources: list[str],\n", "):\n", "    \"\"\"Returns a natural language response to the user in the form of a research\n", "    report. There are several sections to this report, those are:\n", "    - `executive_summary`: a short paragraph (1 minute read) providing a TLDR to the reader.\n", "    - `research_steps`: the steps that were taken\n", "    to research your report.\n", "    - `main_body`: this is where the bulk of high quality and concise\n", "    information that answers the user's question belongs. It is 3-4 paragraphs\n", "    long in length.\n", "    - `sources`: the URLs scraped during the research process. Only include URLs if they were useful in creating your report.\n", "    \"\"\"\n", "    return \"\""]}, {"cell_type": "markdown", "id": "fa4521c3cf3daab5", "metadata": {}, "source": "# Graph State"}, {"cell_type": "code", "execution_count": 266, "id": "813907816389c462", "metadata": {"ExecuteTime": {"end_time": "2024-08-15T22:05:38.892117Z", "start_time": "2024-08-15T22:05:38.890186Z"}}, "outputs": [], "source": ["import operator\n", "from typing import Annotated, TypedDict\n", "\n", "from langchain_core.agents import AgentAction\n", "from langchain_core.messages import BaseMessage\n", "\n", "\n", "class AgentState(TypedDict):\n", "    input: str\n", "    chat_history: list[BaseMessage]\n", "    intermediate_steps: Annotated[list[tuple[AgentAction, str]], operator.add]"]}, {"cell_type": "markdown", "id": "2e14534ae86e8162", "metadata": {}, "source": "# Oracle"}, {"cell_type": "code", "execution_count": 267, "id": "215bac83cd9d47", "metadata": {"ExecuteTime": {"end_time": "2024-08-15T22:05:38.904873Z", "start_time": "2024-08-15T22:05:38.902928Z"}}, "outputs": [], "source": ["from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder\n", "\n", "system_prompt = \"\"\"You are the oracle, the great AI decision maker.\n", "Given the user's query you must decide what to do with it based on the\n", "list of tools provided to you.\n", "\n", "If you see that a tool has been used (in the scratchpad) with a particular\n", "input, do NOT use that same tool with the same input again. \n", "\n", "You should aim to collect information from a diverse range of sources before\n", "providing the answer to the user. Be encouraged to follow linked URLs to dive as deep as you need to. Once you have collected sufficient information\n", "to answer the user's question (stored in the scratchpad) use the final_answer\n", "tool.\"\"\"\n", "\n", "prompt = ChatPromptTemplate.from_messages(\n", "    [\n", "        (\"system\", system_prompt),\n", "        MessagesPlaceholder(variable_name=\"chat_history\"),\n", "        (\"user\", \"{input}\"),\n", "        (\"assistant\", \"scratchpad: {scratchpad}\"),\n", "    ]\n", ")"]}, {"cell_type": "code", "execution_count": 268, "id": "4fdc934a562286f8", "metadata": {"ExecuteTime": {"end_time": "2024-08-15T22:05:38.938077Z", "start_time": "2024-08-15T22:05:38.924896Z"}}, "outputs": [], "source": ["from langchain_openai import ChatOpenAI\n", "from salestech_be.settings import settings\n", "\n", "llm = ChatOpenAI(\n", "    model=\"gpt-4o\",\n", "    openai_api_key=settings.openai_api_key.get_secret_value(),\n", ")\n", "\n", "# llm = ChatAnthropic(\n", "#     api_key=settings.anthropic_api_key.get_secret_value(),\n", "#     model=\"claude-3-5-sonnet-20240620\"\n", "# )\n", "\n", "tools = [web_search, scrape_url, final_answer]\n", "\n", "\n", "# define a function to transform intermediate_steps from list\n", "# of AgentAction to scratchpad string\n", "def create_scratchpad(intermediate_steps: list[AgentAction]):\n", "    research_steps = []\n", "    for i, action in enumerate(intermediate_steps):\n", "        if action.log != \"TBD\":\n", "            # this was the ToolExecution\n", "            research_steps.append(\n", "                f\"Tool: {action.tool}, input: {action.tool_input}\\n\"\n", "                f\"Output: {action.log}\"\n", "            )\n", "    return \"\\n---\\n\".join(research_steps)\n", "\n", "\n", "oracle = (\n", "    {\n", "        \"input\": lambda x: x[\"input\"],\n", "        \"chat_history\": lambda x: x[\"chat_history\"],\n", "        \"scratchpad\": lambda x: create_scratchpad(\n", "            intermediate_steps=x[\"intermediate_steps\"]\n", "        ),\n", "    }\n", "    | prompt\n", "    | llm.bind_tools(tools, tool_choice=\"any\")\n", ")"]}, {"cell_type": "code", "execution_count": 269, "id": "af7617098acde18d", "metadata": {"ExecuteTime": {"end_time": "2024-08-15T22:05:38.942573Z", "start_time": "2024-08-15T22:05:38.940490Z"}}, "outputs": [], "source": ["def run_oracle(state: list):\n", "    out = oracle.invoke(state)\n", "    tool_name = out.tool_calls[0][\"name\"]\n", "    tool_args = out.tool_calls[0][\"args\"]\n", "    action_out = AgentAction(tool=tool_name, tool_input=tool_args, log=\"TBD\")\n", "    return {\"intermediate_steps\": [action_out]}\n", "\n", "\n", "def router(state: list):\n", "    # return the tool name to use\n", "    if isinstance(state[\"intermediate_steps\"], list):\n", "        return state[\"intermediate_steps\"][-1].tool\n", "    else:\n", "        # if we output bad format go to final answer\n", "        print(\"Router invalid format\")\n", "        return \"final_answer\""]}, {"cell_type": "code", "execution_count": 270, "id": "36b9e2e25ebfdcde", "metadata": {"ExecuteTime": {"end_time": "2024-08-15T22:05:38.955135Z", "start_time": "2024-08-15T22:05:38.953030Z"}}, "outputs": [], "source": ["tool_str_to_func = {\n", "    \"web_search\": web_search,\n", "    \"scrape_url\": scrape_url,\n", "    \"final_answer\": final_answer,\n", "}\n", "\n", "\n", "def run_tool(state: list):\n", "    # use this as helper function so we repeat less code\n", "    tool_name = state[\"intermediate_steps\"][-1].tool\n", "    tool_args = state[\"intermediate_steps\"][-1].tool_input\n", "    print(f\"{tool_name}.invoke(input={tool_args})\")\n", "    # run tool\n", "    out = tool_str_to_func[tool_name].invoke(input=tool_args)\n", "    action_out = AgentAction(tool=tool_name, tool_input=tool_args, log=str(out))\n", "    return {\"intermediate_steps\": [action_out]}"]}, {"cell_type": "code", "execution_count": 271, "id": "96b58eab00c35242", "metadata": {"ExecuteTime": {"end_time": "2024-08-15T22:05:38.968895Z", "start_time": "2024-08-15T22:05:38.966092Z"}}, "outputs": [], "source": ["from langgraph.graph import END, StateGraph\n", "\n", "graph = StateGraph(AgentState)\n", "\n", "graph.add_node(\"oracle\", run_oracle)\n", "graph.add_node(\"web_search\", run_tool)\n", "graph.add_node(\"scrape_url\", run_tool)\n", "graph.add_node(\"final_answer\", run_tool)\n", "\n", "graph.set_entry_point(\"oracle\")\n", "\n", "graph.add_conditional_edges(\n", "    source=\"oracle\",  # where in graph to start\n", "    path=router,  # function to determine which node is called\n", ")\n", "\n", "# create edges from each tool back to the oracle\n", "for tool_obj in tools:\n", "    if tool_obj.name != \"final_answer\":\n", "        graph.add_edge(tool_obj.name, \"oracle\")\n", "\n", "# if anything goes to final answer, it must then move to END\n", "graph.add_edge(\"final_answer\", END)\n", "\n", "runnable = graph.compile()"]}, {"cell_type": "code", "execution_count": 272, "id": "912f0f531a1b2cd3", "metadata": {"ExecuteTime": {"end_time": "2024-08-15T22:05:38.981198Z", "start_time": "2024-08-15T22:05:38.979119Z"}}, "outputs": [], "source": ["def build_report(output: dict):\n", "    research_steps = output[\"research_steps\"]\n", "    if type(research_steps) is list:\n", "        research_steps = \"\\n\".join([f\"- {r}\" for r in research_steps])\n", "    sources = output[\"sources\"]\n", "    if type(sources) is list:\n", "        sources = \"\\n\".join([f\"- {s}\" for s in sources])\n", "    return f\"\"\"\n", "EXECUTIVE SUMMARY\n", "------------\n", "{output[\"executive_summary\"]}\n", "\n", "RESEARCH STEPS\n", "--------------\n", "{research_steps}\n", "\n", "REPORT\n", "------\n", "{output[\"main_body\"]}\n", "\n", "SOURCES\n", "-------\n", "{sources}\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 273, "id": "252bb527e342564c", "metadata": {"ExecuteTime": {"end_time": "2024-08-15T22:06:27.678987Z", "start_time": "2024-08-15T22:05:38.992470Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["web_search.invoke(input={'query': 'WaveCX company overview'})\n", "scrape_url.invoke(input={'url': 'https://wavecx.com/'})\n", "scrape_url.invoke(input={'url': 'https://www.linkedin.com/company/wavecx'})\n", "web_search.invoke(input={'query': 'WaveCX customer testimonials'})\n", "scrape_url.invoke(input={'url': 'https://www.trustradius.com/products/wavecx/reviews'})\n", "web_search.invoke(input={'query': 'WaveCX case studies'})\n", "scrape_url.invoke(input={'url': 'https://wavecx.com/pages/platform-overview/'})\n", "web_search.invoke(input={'query': 'WaveCX competitors'})\n", "final_answer.invoke(input={'executive_summary': 'WaveCX is a digital product engagement platform specialized in providing customer experience training, feedback, and marketing solutions primarily for financial institutions. Their services focus on creating high-fidelity product demos, internal training, and voice of customer (VoC) solicitation to support digital transformation and enhance customer interactions.', 'research_steps': ['Searched for an overview of WaveCX and their business model', 'Scraped the WaveCX official website for details on services and platform features', 'Scraped LinkedIn for company profile and employee details', 'Searched for customer testimonials and reviews on external platforms', 'Searched for case studies related to WaveCX', 'Searched for information on competitors and industry context'], 'main_body': \"WaveCX operates primarily within the fintech sector, offering a comprehensive platform designed to improve customer engagement and training for financial institutions. Their core services include creating and managing product demos, generating in-app content, and providing tools for feedback and analytics. The platform supports both desktop and mobile demos, ensuring accessibility across various devices. Key features include demo creation and management tools, promo pages, feature tours, self-service pages, and content pages. WaveCX also offers robust admin controls, user management, and white labeling options to maintain brand consistency.\\n\\nOne of the primary problems WaveCX addresses is the need for effective digital transformation within financial institutions. By providing high-fidelity customer tutorials and internal training, WaveCX helps these institutions educate their employees and customers about new digital products and features. Another significant issue is the need for continuous customer feedback and engagement. WaveCX's feedback and analytics tools allow businesses to capture user feedback and monitor the effectiveness of their demos, leading to improved customer satisfaction and product usage.\\n\\nWaveCX's main competitors include Seismic, Showpad, Rocketlane, and OnScreen, all of which offer similar digital adoption and customer engagement solutions. The industry faces common challenges such as the need for seamless digital adoption, constant innovation to keep up with technological advancements, and maintaining high levels of customer satisfaction. WaveCX differentiates itself by focusing on the financial sector and providing a tailored platform that meets the unique needs of financial institutions.\\n\\nOverall, WaveCX solves key problems related to digital transformation, customer engagement, and feedback management for financial institutions, enabling them to improve their customer experience and operational efficiency.\", 'sources': ['https://wavecx.com/', 'https://www.linkedin.com/company/wavecx', 'https://www.trustradius.com/products/wavecx/reviews', 'https://www.capterra.com/p/236954/WaveCX/', 'https://tracxn.com/d/companies/wavecx/__r4yYtQpl2qS6rkAwCPp-s30xAha9ZBbtKxDGWkyoz_M/competitors']})\n"]}], "source": ["out = runnable.invoke(\n", "    {\n", "        \"input\": \"Help me understand the company WaveCX. Analyze their business model, core services or products, target audience, and market positioning. I'm aiming to understand the basic structure and objectives of their business. Dive deep into the company's services or products. Look at customer testimonials, case studies, and service descriptions on their website to uncover insights. I want to identify the key problems they are solving for their customers. Investigate the industry in which the company operates and their main competitors. Study industry reports, competitor websites, and market trends to identify common challenges in the industry. I need to understand the broader context of the problems the company addresses. With the information gathered, pinpoint 1-3 key problems that the prospect's company is solving in the market. Focus on the most significant issues they address, as indicated by their services, customer feedback, and industry positioning. Refine the identified problems to ensure they are clearly articulated and validate them against industry standards or benchmarks. I'm looking for a concise and impactful description of each problem to ensure we have a clear understanding of what the prospect's company aims to solve.\",\n", "        \"chat_history\": [],\n", "    },\n", "    {\"recursion_limit\": 100},\n", ")"]}, {"cell_type": "code", "execution_count": 274, "id": "6eea17b126ba52f1", "metadata": {"ExecuteTime": {"end_time": "2024-08-15T22:06:48.513117Z", "start_time": "2024-08-15T22:06:48.510404Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "EXECUTIVE SUMMARY\n", "------------\n", "WaveCX is a digital product engagement platform specialized in providing customer experience training, feedback, and marketing solutions primarily for financial institutions. Their services focus on creating high-fidelity product demos, internal training, and voice of customer (VoC) solicitation to support digital transformation and enhance customer interactions.\n", "\n", "RESEARCH STEPS\n", "--------------\n", "- Searched for an overview of WaveCX and their business model\n", "- Scraped the WaveCX official website for details on services and platform features\n", "- Scraped LinkedIn for company profile and employee details\n", "- Searched for customer testimonials and reviews on external platforms\n", "- Searched for case studies related to WaveCX\n", "- Searched for information on competitors and industry context\n", "\n", "REPORT\n", "------\n", "WaveCX operates primarily within the fintech sector, offering a comprehensive platform designed to improve customer engagement and training for financial institutions. Their core services include creating and managing product demos, generating in-app content, and providing tools for feedback and analytics. The platform supports both desktop and mobile demos, ensuring accessibility across various devices. Key features include demo creation and management tools, promo pages, feature tours, self-service pages, and content pages. WaveCX also offers robust admin controls, user management, and white labeling options to maintain brand consistency.\n", "\n", "One of the primary problems WaveCX addresses is the need for effective digital transformation within financial institutions. By providing high-fidelity customer tutorials and internal training, WaveCX helps these institutions educate their employees and customers about new digital products and features. Another significant issue is the need for continuous customer feedback and engagement. WaveCX's feedback and analytics tools allow businesses to capture user feedback and monitor the effectiveness of their demos, leading to improved customer satisfaction and product usage.\n", "\n", "WaveCX's main competitors include Seismic, Showpad, Rocketlane, and OnScreen, all of which offer similar digital adoption and customer engagement solutions. The industry faces common challenges such as the need for seamless digital adoption, constant innovation to keep up with technological advancements, and maintaining high levels of customer satisfaction. WaveCX differentiates itself by focusing on the financial sector and providing a tailored platform that meets the unique needs of financial institutions.\n", "\n", "Overall, WaveCX solves key problems related to digital transformation, customer engagement, and feedback management for financial institutions, enabling them to improve their customer experience and operational efficiency.\n", "\n", "SOURCES\n", "-------\n", "- https://wavecx.com/\n", "- https://www.linkedin.com/company/wavecx\n", "- https://www.trustradius.com/products/wavecx/reviews\n", "- https://www.capterra.com/p/236954/WaveCX/\n", "- https://tracxn.com/d/companies/wavecx/__r4yYtQpl2qS6rkAwCPp-s30xAha9ZBbtKxDGWkyoz_M/competitors\n", "\n"]}], "source": ["print(build_report(output=out[\"intermediate_steps\"][-1].tool_input))"]}, {"cell_type": "code", "execution_count": null, "id": "10d6ff60093eb2ef", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}