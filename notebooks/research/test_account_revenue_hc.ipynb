{"cells": [{"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<salestech_be.db.dbengine.core.DatabaseEngine object at 0x10740e000>\n"]}, {"ename": "TypeError", "evalue": "CrustdataClient.get_company_info() got an unexpected keyword argument 'company_name'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mTypeError\u001b[0m                                 Traceback (most recent call last)", "Cell \u001b[0;32mIn[4], line 12\u001b[0m\n\u001b[1;32m      9\u001b[0m intel_repository \u001b[38;5;241m=\u001b[39m IntelRepository(engine\u001b[38;5;241m=\u001b[39mdb_engine)\n\u001b[1;32m     10\u001b[0m crustdata_client \u001b[38;5;241m=\u001b[39m CrustdataClient()\n\u001b[0;32m---> 12\u001b[0m company_info \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mawait\u001b[39;00m \u001b[43mcrustdata_client\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget_company_info\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcompany_name\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mamazon.com\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     14\u001b[0m \u001b[38;5;28;01mawait\u001b[39;00m update_account_revenue_headcount(company_info[\u001b[38;5;241m0\u001b[39m])\n", "\u001b[0;31mTypeError\u001b[0m: CrustdataClient.get_company_info() got an unexpected keyword argument 'company_name'"]}], "source": ["from salestech_be.temporal.activities.research_agent.account_revenue_headcount import update_account_revenue_headcount\n", "from salestech_be.db.dao.intel_repository import IntelRepository\n", "from salestech_be.integrations.crustdata.client import CrustdataClient\n", "from salestech_be.temporal.database import get_or_init_db_engine\n", "\n", "\n", "db_engine = await get_or_init_db_engine()\n", "print(db_engine)\n", "intel_repository = IntelRepository(engine=db_engine)\n", "crustdata_client = CrustdataClient()\n", "\n", "company_info = await crustdata_client.get_company_info(company_domains=[\"amazon.com\"])\n", "\n", "await update_account_revenue_headcount(company_info[0])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.8"}}, "nbformat": 4, "nbformat_minor": 2}