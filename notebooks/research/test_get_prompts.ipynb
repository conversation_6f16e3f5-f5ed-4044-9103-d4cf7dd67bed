{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[32m2025-02-10 14:30:44.308\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[1mStatsd client <salestech_be.common.stats.noop_statsd.NoopStatsd object at 0x10a7b2b10>\u001b[0m | \u001b[33mserialized: {\"message\":\"Statsd client <salestech_be.common.stats.noop_statsd.NoopStatsd object at 0x10a7b2b10>\",\"name\":\"common.stats.metric\",\"lineno\":134,\"function_name\":\"__init__\",\"level\":\"INFO\",\"timestamp\":1739226644308.378,\"time\":\"2025-02-10T14:30:44.308378-08:00\",\"thread_id\":**********,\"thread_name\":\"MainThread\",\"process_id\":93639,\"extra\":{\"span_id\":\"17281517611159049171\",\"trace_id\":\"311545918544175495307164407366852631616\"}}\u001b[0m| \u001b[36mcommon.stats.metric\u001b[0m:\u001b[36m__init__\u001b[0m:\u001b[36m134\u001b[0m | \u001b[35mtrace_id=311545918544175495307164407366852631616\u001b[0m | \u001b[34mspan_id=17281517611159049171\u001b[0m\n", "\u001b[32m2025-02-10 14:30:44.308\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[1mStatsd client <salestech_be.common.stats.noop_statsd.NoopStatsd object at 0x10a72de20>\u001b[0m | \u001b[33mserialized: {\"message\":\"Statsd client <salestech_be.common.stats.noop_statsd.NoopStatsd object at 0x10a72de20>\",\"name\":\"common.stats.metric\",\"lineno\":134,\"function_name\":\"__init__\",\"level\":\"INFO\",\"timestamp\":1739226644308.701,\"time\":\"2025-02-10T14:30:44.308701-08:00\",\"thread_id\":**********,\"thread_name\":\"MainThread\",\"process_id\":93639,\"extra\":{\"span_id\":\"7467968421150782182\",\"trace_id\":\"28284255482121436568871332220916524293\"}}\u001b[0m| \u001b[36mcommon.stats.metric\u001b[0m:\u001b[36m__init__\u001b[0m:\u001b[36m134\u001b[0m | \u001b[35mtrace_id=28284255482121436568871332220916524293\u001b[0m | \u001b[34mspan_id=7467968421150782182\u001b[0m\n", "\u001b[32m2025-02-10 14:30:44.309\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[1mStatsd client <salestech_be.common.stats.noop_statsd.NoopStatsd object at 0x10a3e7020>\u001b[0m | \u001b[33mserialized: {\"message\":\"Statsd client <salestech_be.common.stats.noop_statsd.NoopStatsd object at 0x10a3e7020>\",\"name\":\"common.stats.metric\",\"lineno\":134,\"function_name\":\"__init__\",\"level\":\"INFO\",\"timestamp\":1739226644309.339,\"time\":\"2025-02-10T14:30:44.309339-08:00\",\"thread_id\":**********,\"thread_name\":\"MainThread\",\"process_id\":93639,\"extra\":{\"span_id\":\"10195256887376040036\",\"trace_id\":\"105442495258366109497982593652025081007\"}}\u001b[0m| \u001b[36mcommon.stats.metric\u001b[0m:\u001b[36m__init__\u001b[0m:\u001b[36m134\u001b[0m | \u001b[35mtrace_id=105442495258366109497982593652025081007\u001b[0m | \u001b[34mspan_id=10195256887376040036\u001b[0m\n", "\u001b[32m2025-02-10 14:30:44.309\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[1mStatsd client <salestech_be.common.stats.noop_statsd.NoopStatsd object at 0x10a23f5f0>\u001b[0m | \u001b[33mserialized: {\"message\":\"Statsd client <salestech_be.common.stats.noop_statsd.NoopStatsd object at 0x10a23f5f0>\",\"name\":\"common.stats.metric\",\"lineno\":134,\"function_name\":\"__init__\",\"level\":\"INFO\",\"timestamp\":1739226644309.545,\"time\":\"2025-02-10T14:30:44.309545-08:00\",\"thread_id\":**********,\"thread_name\":\"MainThread\",\"process_id\":93639,\"extra\":{\"span_id\":\"2961452405714698844\",\"trace_id\":\"12773490072102925634877240840932247411\"}}\u001b[0m| \u001b[36mcommon.stats.metric\u001b[0m:\u001b[36m__init__\u001b[0m:\u001b[36m134\u001b[0m | \u001b[35mtrace_id=12773490072102925634877240840932247411\u001b[0m | \u001b[34mspan_id=2961452405714698844\u001b[0m\n"]}], "source": ["from salestech_be.core.ai.prompt.langfuse_prompt_service import (\n", "    get_langfuse_prompt_service,\n", ")\n", "from salestech_be.core.ai.prompt.schema import PromptEnum, PromptRequest\n", "\n", "langfuse_prompt_service = get_langfuse_prompt_service()\n", "\n", "prompt_obj = await langfuse_prompt_service.get_prompt(\n", "        request=PromptRequest(\n", "            prompt_name=PromptEnum.RESEARCH_COMPANY_NEWS,\n", "            variables={\n", "                \"company_description_base\": \"hello\",\n", "                \"company_name\": \"reevoAI\",\n", "                \"company_domain\": \"reevo.ai\",\n", "            },\n", "        )\n", "    )"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["\"hello Find related articles related to 'reevoAI' and 'reevo.ai' from {{start_date}} to now. Pay more attention to the top items of the list. Give me the latest information or news about the company:'{company_name}'. Based on the description of the Company in 'reevo.ai', preserve only relevant news and exclude irrelevant news. All valid news articles should contain the full company name: **reevoAI** in the title or summary. Format news articles into a flat json with title as a property, summary of the article as a property, published date, and url as a property which is the source link from grounding without category separation and remove the markdown tags. Remove quotes and format published date in YYYY-MM-DD, for example 2025-01-10. If published date cannot adhere to this format, leave it empty. \""]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["prompt_obj.messages[0][\"content\"]"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["'gemini/gemini-2.0-flash'"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["prompt_obj.config[\"model\"]"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[32m2025-02-10 14:39:11.518\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[33m\u001b[1m/Users/<USER>/salestech-be/.venv/lib/python3.12/site-packages/pydantic/_internal/_config.py:341: UserWarning: Valid config keys have changed in V2:\n", "* 'fields' has been removed\n", "  warnings.warn(message, UserWarning)\n", "\u001b[0m | \u001b[33mserialized: {\"message\":\"/Users/<USER>/salestech-be/.venv/lib/python3.12/site-packages/pydantic/_internal/_config.py:341: UserWarning: Valid config keys have changed in V2:\\n* 'fields' has been removed\\n  warnings.warn(message, UserWarning)\\n\",\"name\":\"py.warnings\",\"lineno\":112,\"function_name\":\"_showwarnmsg\",\"level\":\"WARNING\",\"timestamp\":1739227151518.2122,\"time\":\"2025-02-10T14:39:11.518212-08:00\",\"thread_id\":**********,\"thread_name\":\"MainThread\",\"process_id\":93639,\"extra\":{\"span_id\":\"2657552103119692564\",\"trace_id\":\"196877811048362783578407033645315867938\"}}\u001b[0m| \u001b[36mpy.warnings\u001b[0m:\u001b[36m_showwarnmsg\u001b[0m:\u001b[36m112\u001b[0m | \u001b[35mtrace_id=196877811048362783578407033645315867938\u001b[0m | \u001b[34mspan_id=2657552103119692564\u001b[0m\n", "\u001b[32m2025-02-10 14:39:11.862\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[1m\n", "LiteLLM completion() model= gemini-2.0-flash; provider = gemini\u001b[0m | \u001b[33mserialized: {\"message\":\"\\nLiteLLM completion() model= gemini-2.0-flash; provider = gemini\",\"name\":\"<PERSON><PERSON><PERSON><PERSON>\",\"lineno\":2825,\"function_name\":\"_check_valid_arg\",\"level\":\"INFO\",\"timestamp\":1739227151862.375,\"time\":\"2025-02-10T14:39:11.862375-08:00\",\"thread_id\":13690580992,\"thread_name\":\"asyncio_0\",\"process_id\":93639,\"extra\":{\"span_id\":\"7959509856093804992\",\"trace_id\":\"113754643871499095450511669385530366123\"}}\u001b[0m| \u001b[36mLiteLLM\u001b[0m:\u001b[36m_check_valid_arg\u001b[0m:\u001b[36m2825\u001b[0m | \u001b[35mtrace_id=113754643871499095450511669385530366123\u001b[0m | \u001b[34mspan_id=7959509856093804992\u001b[0m\n", "\u001b[32m2025-02-10 14:39:23.779\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[1mLangfuse Layer Logging - logging success\u001b[0m | \u001b[33mserialized: {\"message\":\"Langfuse Layer Logging - logging success\",\"name\":\"Li<PERSON>LLM\",\"lineno\":297,\"function_name\":\"_old_log_event\",\"level\":\"INFO\",\"timestamp\":1739227163779.584,\"time\":\"2025-02-10T14:39:23.779584-08:00\",\"thread_id\":**********,\"thread_name\":\"MainThread\",\"process_id\":93639,\"extra\":{\"span_id\":\"6417826438253418083\",\"trace_id\":\"282004719890030433310887592508171979170\"}}\u001b[0m| \u001b[36mLiteLLM\u001b[0m:\u001b[36m_old_log_event\u001b[0m:\u001b[36m297\u001b[0m | \u001b[35mtrace_id=282004719890030433310887592508171979170\u001b[0m | \u001b[34mspan_id=6417826438253418083\u001b[0m\n", "\u001b[32m2025-02-10 14:54:44.125\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[1m\n", "LiteLLM completion() model= gemini-2.0-flash; provider = gemini\u001b[0m | \u001b[33mserialized: {\"message\":\"\\nLiteLLM completion() model= gemini-2.0-flash; provider = gemini\",\"name\":\"<PERSON><PERSON><PERSON><PERSON>\",\"lineno\":2825,\"function_name\":\"_check_valid_arg\",\"level\":\"INFO\",\"timestamp\":1739228084125.553,\"time\":\"2025-02-10T14:54:44.125553-08:00\",\"thread_id\":13690580992,\"thread_name\":\"asyncio_0\",\"process_id\":93639,\"extra\":{\"span_id\":\"2379318398259116428\",\"trace_id\":\"153311563099926702109494194941680329543\"}}\u001b[0m| \u001b[36mLiteLLM\u001b[0m:\u001b[36m_check_valid_arg\u001b[0m:\u001b[36m2825\u001b[0m | \u001b[35mtrace_id=153311563099926702109494194941680329543\u001b[0m | \u001b[34mspan_id=2379318398259116428\u001b[0m\n", "\u001b[32m2025-02-10 14:54:48.938\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[1mLangfuse Layer Logging - logging success\u001b[0m | \u001b[33mserialized: {\"message\":\"Langfuse Layer Logging - logging success\",\"name\":\"LiteLLM\",\"lineno\":297,\"function_name\":\"_old_log_event\",\"level\":\"INFO\",\"timestamp\":1739228088938.145,\"time\":\"2025-02-10T14:54:48.938145-08:00\",\"thread_id\":**********,\"thread_name\":\"MainThread\",\"process_id\":93639,\"extra\":{\"span_id\":\"3309982031623649563\",\"trace_id\":\"311590498308301693445407014693645790697\"}}\u001b[0m| \u001b[36mLiteLLM\u001b[0m:\u001b[36m_old_log_event\u001b[0m:\u001b[36m297\u001b[0m | \u001b[35mtrace_id=311590498308301693445407014693645790697\u001b[0m | \u001b[34mspan_id=3309982031623649563\u001b[0m\n", "\u001b[32m2025-02-10 14:54:48.939\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[1m\n", "LiteLLM completion() model= gemini-2.0-flash; provider = gemini\u001b[0m | \u001b[33mserialized: {\"message\":\"\\nLiteLLM completion() model= gemini-2.0-flash; provider = gemini\",\"name\":\"<PERSON><PERSON><PERSON><PERSON>\",\"lineno\":2825,\"function_name\":\"_check_valid_arg\",\"level\":\"INFO\",\"timestamp\":1739228088939.7048,\"time\":\"2025-02-10T14:54:48.939705-08:00\",\"thread_id\":13690580992,\"thread_name\":\"asyncio_0\",\"process_id\":93639,\"extra\":{\"span_id\":\"6962313234187293050\",\"trace_id\":\"9504372308618915999214131967566451406\"}}\u001b[0m| \u001b[36mLiteLLM\u001b[0m:\u001b[36m_check_valid_arg\u001b[0m:\u001b[36m2825\u001b[0m | \u001b[35mtrace_id=9504372308618915999214131967566451406\u001b[0m | \u001b[34mspan_id=6962313234187293050\u001b[0m\n", "\u001b[32m2025-02-10 14:54:53.392\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[1mLangfuse Layer Logging - logging success\u001b[0m | \u001b[33mserialized: {\"message\":\"Langfuse Layer Logging - logging success\",\"name\":\"LiteLLM\",\"lineno\":297,\"function_name\":\"_old_log_event\",\"level\":\"INFO\",\"timestamp\":1739228093392.862,\"time\":\"2025-02-10T14:54:53.392862-08:00\",\"thread_id\":**********,\"thread_name\":\"MainThread\",\"process_id\":93639,\"extra\":{\"span_id\":\"5151189319267787614\",\"trace_id\":\"39737124131611677182771438088998385777\"}}\u001b[0m| \u001b[36mLiteLLM\u001b[0m:\u001b[36m_old_log_event\u001b[0m:\u001b[36m297\u001b[0m | \u001b[35mtrace_id=39737124131611677182771438088998385777\u001b[0m | \u001b[34mspan_id=5151189319267787614\u001b[0m\n", "\u001b[32m2025-02-10 14:54:53.394\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[1m\n", "LiteLLM completion() model= gemini-2.0-flash; provider = gemini\u001b[0m | \u001b[33mserialized: {\"message\":\"\\nLiteLLM completion() model= gemini-2.0-flash; provider = gemini\",\"name\":\"<PERSON><PERSON><PERSON><PERSON>\",\"lineno\":2825,\"function_name\":\"_check_valid_arg\",\"level\":\"INFO\",\"timestamp\":1739228093394.809,\"time\":\"2025-02-10T14:54:53.394809-08:00\",\"thread_id\":13690580992,\"thread_name\":\"asyncio_0\",\"process_id\":93639,\"extra\":{\"span_id\":\"6535308447463988179\",\"trace_id\":\"320894017213333120037041066376559516007\"}}\u001b[0m| \u001b[36mLiteLLM\u001b[0m:\u001b[36m_check_valid_arg\u001b[0m:\u001b[36m2825\u001b[0m | \u001b[35mtrace_id=320894017213333120037041066376559516007\u001b[0m | \u001b[34mspan_id=6535308447463988179\u001b[0m\n", "\u001b[32m2025-02-10 14:55:00.907\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[1mLangfuse Layer Logging - logging success\u001b[0m | \u001b[33mserialized: {\"message\":\"Langfuse Layer Logging - logging success\",\"name\":\"Li<PERSON>LLM\",\"lineno\":297,\"function_name\":\"_old_log_event\",\"level\":\"INFO\",\"timestamp\":1739228100907.8728,\"time\":\"2025-02-10T14:55:00.907873-08:00\",\"thread_id\":**********,\"thread_name\":\"MainThread\",\"process_id\":93639,\"extra\":{\"span_id\":\"13129899484612111787\",\"trace_id\":\"177121271990497948168747595219555732744\"}}\u001b[0m| \u001b[36mLiteLLM\u001b[0m:\u001b[36m_old_log_event\u001b[0m:\u001b[36m297\u001b[0m | \u001b[35mtrace_id=177121271990497948168747595219555732744\u001b[0m | \u001b[34mspan_id=13129899484612111787\u001b[0m\n", "\u001b[32m2025-02-10 14:55:00.910\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[1m\n", "LiteLLM completion() model= gemini-2.0-flash; provider = gemini\u001b[0m | \u001b[33mserialized: {\"message\":\"\\nLiteLLM completion() model= gemini-2.0-flash; provider = gemini\",\"name\":\"<PERSON><PERSON><PERSON><PERSON>\",\"lineno\":2825,\"function_name\":\"_check_valid_arg\",\"level\":\"INFO\",\"timestamp\":1739228100910.0781,\"time\":\"2025-02-10T14:55:00.910078-08:00\",\"thread_id\":13690580992,\"thread_name\":\"asyncio_0\",\"process_id\":93639,\"extra\":{\"span_id\":\"8574346637759693242\",\"trace_id\":\"102953358693520879392420865755087010320\"}}\u001b[0m| \u001b[36mLiteLLM\u001b[0m:\u001b[36m_check_valid_arg\u001b[0m:\u001b[36m2825\u001b[0m | \u001b[35mtrace_id=102953358693520879392420865755087010320\u001b[0m | \u001b[34mspan_id=8574346637759693242\u001b[0m\n", "\u001b[32m2025-02-10 14:55:10.554\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[1mLangfuse Layer Logging - logging success\u001b[0m | \u001b[33mserialized: {\"message\":\"Langfuse Layer Logging - logging success\",\"name\":\"Li<PERSON>LLM\",\"lineno\":297,\"function_name\":\"_old_log_event\",\"level\":\"INFO\",\"timestamp\":1739228110554.038,\"time\":\"2025-02-10T14:55:10.554038-08:00\",\"thread_id\":**********,\"thread_name\":\"MainThread\",\"process_id\":93639,\"extra\":{\"span_id\":\"16739001416733145341\",\"trace_id\":\"151018495286166543019185006838337435443\"}}\u001b[0m| \u001b[36mLiteLLM\u001b[0m:\u001b[36m_old_log_event\u001b[0m:\u001b[36m297\u001b[0m | \u001b[35mtrace_id=151018495286166543019185006838337435443\u001b[0m | \u001b[34mspan_id=16739001416733145341\u001b[0m\n", "\u001b[32m2025-02-10 14:55:14.801\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[1m\n", "LiteLLM completion() model= gemini-2.0-flash; provider = gemini\u001b[0m | \u001b[33mserialized: {\"message\":\"\\nLiteLLM completion() model= gemini-2.0-flash; provider = gemini\",\"name\":\"<PERSON><PERSON><PERSON><PERSON>\",\"lineno\":2825,\"function_name\":\"_check_valid_arg\",\"level\":\"INFO\",\"timestamp\":1739228114801.7048,\"time\":\"2025-02-10T14:55:14.801705-08:00\",\"thread_id\":13690580992,\"thread_name\":\"asyncio_0\",\"process_id\":93639,\"extra\":{\"span_id\":\"16560946328829984228\",\"trace_id\":\"141567454112974586054439296553496690245\"}}\u001b[0m| \u001b[36mLiteLLM\u001b[0m:\u001b[36m_check_valid_arg\u001b[0m:\u001b[36m2825\u001b[0m | \u001b[35mtrace_id=141567454112974586054439296553496690245\u001b[0m | \u001b[34mspan_id=16560946328829984228\u001b[0m\n", "\u001b[32m2025-02-10 14:55:20.382\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[1mLangfuse Layer Logging - logging success\u001b[0m | \u001b[33mserialized: {\"message\":\"Langfuse Layer Logging - logging success\",\"name\":\"LiteLLM\",\"lineno\":297,\"function_name\":\"_old_log_event\",\"level\":\"INFO\",\"timestamp\":1739228120382.149,\"time\":\"2025-02-10T14:55:20.382149-08:00\",\"thread_id\":**********,\"thread_name\":\"MainThread\",\"process_id\":93639,\"extra\":{\"span_id\":\"17044733416315259624\",\"trace_id\":\"340010914279205811076001501407161020306\"}}\u001b[0m| \u001b[36mLiteLLM\u001b[0m:\u001b[36m_old_log_event\u001b[0m:\u001b[36m297\u001b[0m | \u001b[35mtrace_id=340010914279205811076001501407161020306\u001b[0m | \u001b[34mspan_id=17044733416315259624\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["obtained: [NewsResult(title=\"Teamsters strike at Amazon facilities. Amazon says picketers mostly aren't employees.\", url='https://vertexaisearch.cloud.google.com/grounding-api-redirect/AUBnsYu2JfejVbzZ7qldeONLuOVJt0arrhduV3s_2fhWQXXz8wNs08EgP-3dMHByx7sA3jUK9hcjO28iHP34esFGBFzDJmYxRGuoNNys3yZs1aneNM6dpwuF9qYXRkUNlVc=', published_date='', summary=\"Teamsters strike at Amazon facilities. Amazon says picketers mostly aren't employees. Will Amazon strikes impact holiday shipping?\"), NewsResult(title='Amazon could release AI-enhanced <PERSON>a assistant on Feb 26: What to expect', url='https://vertexaisearch.cloud.google.com/grounding-api-redirect/AUBnsYsMHIBy5KCx4SBr4kCEYiqkFa_3yv0ALCnZxYDUAsZNs56iilEptWXSnzVtu2-29snxirc7G9DdS9a83dvLudR_E9-ay4xUHh8rDEmEBcqhksCt1YQ6p424Ir1MgxIv2tFePwl8IEnyBQ==', published_date='', summary='Amazon could release AI-enhanced Alexa assistant on Feb 26: What to expect'), NewsResult(title='Amazon Web Services to invest Rs 60,000 cr for expansion in Telangana', url='https://vertexaisearch.cloud.google.com/grounding-api-redirect/AUBnsYsMHIBy5KCx4SBr4kCEYiqkFa_3yv0ALCnZxYDUAsZNs56iilEptWXSnzVtu2-29snxirc7G9DdS9a83dvLudR_E9-ay4xUHh8rDEmEBcqhksCt1YQ6p424Ir1MgxIv2tFePwl8IEnyBQ==', published_date='', summary='Amazon Web Services to invest Rs 60,000 cr for expansion in Telangana.'), NewsResult(title='Amazon to close 7 warehouses in Quebec, end 1,700 jobs in next 2 months', url='https://vertexaisearch.cloud.google.com/grounding-api-redirect/AUBnsYsMHIBy5KCx4SBr4kCEYiqkFa_3yv0ALCnZxYDUAsZNs56iilEptWXSnzVtu2-29snxirc7G9DdS9a83dvLudR_E9-ay4xUHh8rDEmEBcqhksCt1YQ6p424Ir1MgxIv2tFePwl8IEnyBQ==', published_date='', summary='Amazon to close 7 warehouses in Quebec, end 1,700 jobs in next 2 months.'), NewsResult(title=\"Amazon CEO Andy Jassy shares 2 keys to success: have a great attitude, and 'be an incredibly ravenous learner'\", url='https://vertexaisearch.cloud.google.com/grounding-api-redirect/AUBnsYsfprNntncddAVGqf1sxoXjZ8fAK05qp8yIoXjRUIdwS6O1KLmx_62bdS27FyveFv5cGXNu1L0n9dy7ZDwWs9YmDr1asxTSp_78VsXD7S09c052FOIOPclGbNgdo4L5AGkiQTgxy0WC', published_date='', summary=\"Company news: Amazon CEO Andy Jassy shares 2 keys to success: have a great attitude, and 'be an incredibly ravenous learner'\"), NewsResult(title='Amazon.com, Inc. (NASDAQ:AMZN) today announced financial results for its fourth quarter ended December 31, 2024.', url='https://vertexaisearch.cloud.google.com/grounding-api-redirect/AUBnsYvUxohttQg0msop17_tEgQgYG9GE-N_31ZL5G610bAF2tH-iIMMMR0RtKIe4Ba5QTQTRq9N7EyQKl8YyT-U4ZtpIDny_nUYFFTF4wf0JvSP5UE2V4X95aYtTaXSUgUOyj7K3Hdfa4F--qWIzV648Rj0FU14pIoxChxnKaV_dyJ9LOIWH4fqfVsz08WphIGSW6QW549lXX66vxSwJ9F5tg==', published_date='2025-02-06', summary='Net sales increased 10% to $187.8 billion in the fourth quarter, compared with $170.0 billion in fourth quarter 2023. Excluding the $0.9 billion unfavorable impact from year-over-year changes in foreign exchange rates throughout the quarter, net sales increased 11% compared with fourth quarter 2023.'), NewsResult(title='Amazon exits Quebec', url='https://vertexaisearch.cloud.google.com/grounding-api-redirect/AUBnsYsRQTfrDaTW__N3SFj_LcUYpfCQoEl09o78wxlPtd_SsNqBzXUFXR7eG74CuTYE1HMiQCnezargcoGamY19KLeTpDPkakCwvW8YP0b1WIUKNm8Cnnk6f4yNL0FSwIwVgNnHNsNVLg==', published_date='2025-01-23', summary='Amazon is shutting down warehouses across Quebec and laying off nearly 2,000 workers. some of them are crying what What are we going to do. now. the retail giant suddenly pulling out of the province. why some say the timing. and location is no coincidence. this is a very powerful u message and it will send a chill.'), NewsResult(title='Amazon sellers, what happens when Walmart grows faster, safety recalls hit Amazon, and influencer marketing goes tech? You rethink your strategy.', url='https://vertexaisearch.cloud.google.com/grounding-api-redirect/AUBnsYtLReI0YVPO-uQ3SW793JsWMn9V89VHI9PUyz3xyxqby4i7F58T8AM1aFXzdmk-DM9zQ4DxVWat7ENk9v6fqZKdCD1haWIt28CGGVRJL1-66EJd5XVyRZYCuAD4KPyNKSby-S9_Bg==', published_date='2025-01-24', summary=\"Amazon sellers, what happens when Walmart grows faster, safety recalls hit Amazon, and influencer marketing goes tech? You rethink your strategy. This week, we'll explore Walmart's growth, the reseller vs. direct selling debate, and how product seeding platforms are redefining influencer campaigns. Plus, a closer look at Amazon's new compliance pressures following a major recall.\"), NewsResult(title='Amazon products courting controversy', url='https://vertexaisearch.cloud.google.com/grounding-api-redirect/AUBnsYsrMXlGan1IKjOgUMGKJXivpW8djwSRyVOX69MS1YYP2eK4CWxlXUFDMDszTjl7VPtYPH1wBYLE05ePc3zPDRqlI9Te7y8c4lZHLWhUB03DsMJ3RvwxeVZggjh_HVKTjL-CxXhwtbJSD8_BbY65WCQe8qGSCKI-fgZmDg3tneFiM-SPW4vPvpHbPlBlJ1I8Few86K2XOLKpWlc=', published_date='2025-02-06', summary=\"Amazon is selling merchandise referencing Donald Trump's taunts about Canada becoming the 51st state. Adrian Ghobrial explores the controversial products.\")]\n"]}], "source": ["from salestech_be.temporal.activities.research_agent.company_news import (\n", "    get_company_news,\n", ")\n", "\n", "company_name = \"amazon\"\n", "company_domain = \"amazon.com\"\n", "enforce_company_name = True\n", "\n", "\n", "results =await get_company_news(company_name, company_domain, enforce_company_name)\n", "print(f\"obtained: {results}\")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["9"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["len(results)"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/salestech-be/.venv/lib/python3.12/site-packages/pydantic/_internal/_config.py:341: UserWarning: Valid config keys have changed in V2:\n", "* 'fields' has been removed\n", "  warnings.warn(message, UserWarning)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[32m2025-02-10 14:56:15.636\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[1mStatsd client <salestech_be.common.stats.noop_statsd.NoopStatsd object at 0x1374494c0>\u001b[0m | \u001b[33mserialized: {\"message\":\"Statsd client <salestech_be.common.stats.noop_statsd.NoopStatsd object at 0x1374494c0>\",\"name\":\"common.stats.metric\",\"lineno\":134,\"function_name\":\"__init__\",\"level\":\"INFO\",\"timestamp\":1739228175636.43,\"time\":\"2025-02-10T14:56:15.636430-08:00\",\"thread_id\":**********,\"thread_name\":\"MainThread\",\"process_id\":95336,\"extra\":{\"span_id\":\"13751263686122117812\",\"trace_id\":\"241285330224131776146669390697654705891\"}}\u001b[0m| \u001b[36mcommon.stats.metric\u001b[0m:\u001b[36m__init__\u001b[0m:\u001b[36m134\u001b[0m | \u001b[35mtrace_id=241285330224131776146669390697654705891\u001b[0m | \u001b[34mspan_id=13751263686122117812\u001b[0m\n", "\u001b[32m2025-02-10 14:56:15.636\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[1mStatsd client <salestech_be.common.stats.noop_statsd.NoopStatsd object at 0x1370e78c0>\u001b[0m | \u001b[33mserialized: {\"message\":\"Statsd client <salestech_be.common.stats.noop_statsd.NoopStatsd object at 0x1370e78c0>\",\"name\":\"common.stats.metric\",\"lineno\":134,\"function_name\":\"__init__\",\"level\":\"INFO\",\"timestamp\":1739228175636.7668,\"time\":\"2025-02-10T14:56:15.636767-08:00\",\"thread_id\":**********,\"thread_name\":\"MainThread\",\"process_id\":95336,\"extra\":{\"span_id\":\"7379330238797151905\",\"trace_id\":\"160889378393051068744555306997502061070\"}}\u001b[0m| \u001b[36mcommon.stats.metric\u001b[0m:\u001b[36m__init__\u001b[0m:\u001b[36m134\u001b[0m | \u001b[35mtrace_id=160889378393051068744555306997502061070\u001b[0m | \u001b[34mspan_id=7379330238797151905\u001b[0m\n", "\u001b[32m2025-02-10 14:56:15.636\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[1mStatsd client <salestech_be.common.stats.noop_statsd.NoopStatsd object at 0x137449be0>\u001b[0m | \u001b[33mserialized: {\"message\":\"Statsd client <salestech_be.common.stats.noop_statsd.NoopStatsd object at 0x137449be0>\",\"name\":\"common.stats.metric\",\"lineno\":134,\"function_name\":\"__init__\",\"level\":\"INFO\",\"timestamp\":1739228175636.892,\"time\":\"2025-02-10T14:56:15.636892-08:00\",\"thread_id\":**********,\"thread_name\":\"MainThread\",\"process_id\":95336,\"extra\":{\"span_id\":\"726520787571891997\",\"trace_id\":\"270396575820602167556728216549143047889\"}}\u001b[0m| \u001b[36mcommon.stats.metric\u001b[0m:\u001b[36m__init__\u001b[0m:\u001b[36m134\u001b[0m | \u001b[35mtrace_id=270396575820602167556728216549143047889\u001b[0m | \u001b[34mspan_id=726520787571891997\u001b[0m\n", "\u001b[32m2025-02-10 14:56:15.637\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[1mStatsd client <salestech_be.common.stats.noop_statsd.NoopStatsd object at 0x136e8fe00>\u001b[0m | \u001b[33mserialized: {\"message\":\"Statsd client <salestech_be.common.stats.noop_statsd.NoopStatsd object at 0x136e8fe00>\",\"name\":\"common.stats.metric\",\"lineno\":134,\"function_name\":\"__init__\",\"level\":\"INFO\",\"timestamp\":1739228175637.0781,\"time\":\"2025-02-10T14:56:15.637078-08:00\",\"thread_id\":**********,\"thread_name\":\"MainThread\",\"process_id\":95336,\"extra\":{\"span_id\":\"9941422963055450901\",\"trace_id\":\"1875778642768263232389356889711155548\"}}\u001b[0m| \u001b[36mcommon.stats.metric\u001b[0m:\u001b[36m__init__\u001b[0m:\u001b[36m134\u001b[0m | \u001b[35mtrace_id=1875778642768263232389356889711155548\u001b[0m | \u001b[34mspan_id=9941422963055450901\u001b[0m\n", "\u001b[32m2025-02-10 14:56:15.815\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[1m\n", "LiteLLM completion() model= gemini-2.0-flash; provider = gemini\u001b[0m | \u001b[33mserialized: {\"message\":\"\\nLiteLLM completion() model= gemini-2.0-flash; provider = gemini\",\"name\":\"<PERSON><PERSON><PERSON><PERSON>\",\"lineno\":2825,\"function_name\":\"_check_valid_arg\",\"level\":\"INFO\",\"timestamp\":1739228175815.4048,\"time\":\"2025-02-10T14:56:15.815405-08:00\",\"thread_id\":13573337088,\"thread_name\":\"asyncio_0\",\"process_id\":95336,\"extra\":{\"span_id\":\"1053856190648672877\",\"trace_id\":\"207106040861588585282725696249137414342\"}}\u001b[0m| \u001b[36mLiteLLM\u001b[0m:\u001b[36m_check_valid_arg\u001b[0m:\u001b[36m2825\u001b[0m | \u001b[35mtrace_id=207106040861588585282725696249137414342\u001b[0m | \u001b[34mspan_id=1053856190648672877\u001b[0m\n", "\u001b[32m2025-02-10 14:56:22.826\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[1mLangfuse Layer Logging - logging success\u001b[0m | \u001b[33mserialized: {\"message\":\"Langfuse Layer Logging - logging success\",\"name\":\"LiteLLM\",\"lineno\":297,\"function_name\":\"_old_log_event\",\"level\":\"INFO\",\"timestamp\":1739228182826.669,\"time\":\"2025-02-10T14:56:22.826669-08:00\",\"thread_id\":**********,\"thread_name\":\"MainThread\",\"process_id\":95336,\"extra\":{\"span_id\":\"4395726900204520901\",\"trace_id\":\"248450200407036773885844089571375566742\"}}\u001b[0m| \u001b[36mLiteLLM\u001b[0m:\u001b[36m_old_log_event\u001b[0m:\u001b[36m297\u001b[0m | \u001b[35mtrace_id=248450200407036773885844089571375566742\u001b[0m | \u001b[34mspan_id=4395726900204520901\u001b[0m\n", "\u001b[32m2025-02-10 14:56:22.828\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[1m\n", "LiteLLM completion() model= gemini-2.0-flash; provider = gemini\u001b[0m | \u001b[33mserialized: {\"message\":\"\\nLiteLLM completion() model= gemini-2.0-flash; provider = gemini\",\"name\":\"<PERSON><PERSON><PERSON><PERSON>\",\"lineno\":2825,\"function_name\":\"_check_valid_arg\",\"level\":\"INFO\",\"timestamp\":1739228182828.948,\"time\":\"2025-02-10T14:56:22.828948-08:00\",\"thread_id\":13573337088,\"thread_name\":\"asyncio_0\",\"process_id\":95336,\"extra\":{\"span_id\":\"630194580551402820\",\"trace_id\":\"181466939919225812732445974127469165577\"}}\u001b[0m| \u001b[36mLiteLLM\u001b[0m:\u001b[36m_check_valid_arg\u001b[0m:\u001b[36m2825\u001b[0m | \u001b[35mtrace_id=181466939919225812732445974127469165577\u001b[0m | \u001b[34mspan_id=630194580551402820\u001b[0m\n", "\u001b[32m2025-02-10 14:56:28.259\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[1mLangfuse Layer Logging - logging success\u001b[0m | \u001b[33mserialized: {\"message\":\"Langfuse Layer Logging - logging success\",\"name\":\"Li<PERSON>LLM\",\"lineno\":297,\"function_name\":\"_old_log_event\",\"level\":\"INFO\",\"timestamp\":1739228188259.9648,\"time\":\"2025-02-10T14:56:28.259965-08:00\",\"thread_id\":**********,\"thread_name\":\"MainThread\",\"process_id\":95336,\"extra\":{\"span_id\":\"11206756098232224404\",\"trace_id\":\"276428546870605335784396291332483370883\"}}\u001b[0m| \u001b[36mLiteLLM\u001b[0m:\u001b[36m_old_log_event\u001b[0m:\u001b[36m297\u001b[0m | \u001b[35mtrace_id=276428546870605335784396291332483370883\u001b[0m | \u001b[34mspan_id=11206756098232224404\u001b[0m\n", "\u001b[32m2025-02-10 14:56:28.262\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[1m\n", "LiteLLM completion() model= gemini-2.0-flash; provider = gemini\u001b[0m | \u001b[33mserialized: {\"message\":\"\\nLiteLLM completion() model= gemini-2.0-flash; provider = gemini\",\"name\":\"<PERSON><PERSON><PERSON><PERSON>\",\"lineno\":2825,\"function_name\":\"_check_valid_arg\",\"level\":\"INFO\",\"timestamp\":1739228188262.842,\"time\":\"2025-02-10T14:56:28.262842-08:00\",\"thread_id\":13573337088,\"thread_name\":\"asyncio_0\",\"process_id\":95336,\"extra\":{\"span_id\":\"11408004995516013986\",\"trace_id\":\"146043103715972604763538297511522736000\"}}\u001b[0m| \u001b[36mLiteLLM\u001b[0m:\u001b[36m_check_valid_arg\u001b[0m:\u001b[36m2825\u001b[0m | \u001b[35mtrace_id=146043103715972604763538297511522736000\u001b[0m | \u001b[34mspan_id=11408004995516013986\u001b[0m\n", "obtained: [NewsResult(title='Teamsters strike at Amazon facilities', url='https://vertexaisearch.cloud.google.com/grounding-api-redirect/AUBnsYvigM2Fmw8RDpCUVI6MDyw6WfCW1RIUCtU9p9bDDigVXnbT3INDTLPB7OTnjK_HG0WApAWUYOWhNnLaaXpdxckRd42uoTIcqTLyjysX-ospsYk8lIqBEVvWKBkB52E=', published_date='', summary=\"Amazon says picketers mostly aren't employees. Will Amazon strikes impact holiday shipping?\"), NewsResult(title='Amazon could release AI-enhanced Alexa assistant on Feb 26: What to expect', url='https://vertexaisearch.cloud.google.com/grounding-api-redirect/AUBnsYvqS7qjM2aIJeHccZqeFJvn1JJUuMZBuOUfHO6MwoO1uvp8WF_RCFcFyqWf9HEhPRIFWq3gkA6LM_Xk0fhlPaKhJxDQVtEEcEnnSZSlSoGbRUKvpxZBZJx1r2H9xHC3UDDQ0f7IMNSbGw==', published_date='', summary='Amazon could release AI-enhanced Alexa assistant on Feb 26: What to expect'), NewsResult(title=\"Amazon's cloud business faces test after Microsoft, Google struggles\", url='https://vertexaisearch.cloud.google.com/grounding-api-redirect/AUBnsYvqS7qjM2aIJeHccZqeFJvn1JJUuMZBuOUfHO6MwoO1uvp8WF_RCFcFyqWf9HEhPRIFWq3gkA6LM_Xk0fhlPaKhJxDQVtEEcEnnSZSlSoGbRUKvpxZBZJx1r2H9xHC3UDDQ0f7IMNSbGw==', published_date='', summary=\"Amazon's cloud business faces test after Microsoft, Google struggles\"), NewsResult(title='Amazon Web Services to invest Rs 60,000 cr for expansion in Telangana', url='https://vertexaisearch.cloud.google.com/grounding-api-redirect/AUBnsYvqS7qjM2aIJeHccZqeFJvn1JJUuMZBuOUfHO6MwoO1uvp8WF_RCFcFyqWf9HEhPRIFWq3gkA6LM_Xk0fhlPaKhJxDQVtEEcEnnSZSlSoGbRUKvpxZBZJx1r2H9xHC3UDDQ0f7IMNSbGw==', published_date='', summary='Amazon Web Services to invest Rs 60,000 cr for expansion in Telangana.'), NewsResult(title=\"Podcast: Amazon, AI, and the cloud — a reality check, with Corey Quinn of 'Last Week in AWS'\", url='https://vertexaisearch.cloud.google.com/grounding-api-redirect/AUBnsYvRsL4jQ1lb5jZ1N22OPuqy4PeWiEUaTwtXzgDXKvTbPf_7cHkEQjSm3J_Z92tSdjV0LWKqKcjVelSPvlAHy9XmY9YriNnwCQxNxq3BDO-HphiyZC5HYm2Oz2I=', published_date='', summary=\"Amazon CEO Andy Jassy sounded more bullish than ever about the potential for artificial intelligence on the company's earnings call this week. “We think virtually every application that we know…\"), NewsResult(title='Amazon cuts workforce diversity reference from annual filing amid Trump-led backlash against DEI', url='https://vertexaisearch.cloud.google.com/grounding-api-redirect/AUBnsYvRsL4jQ1lb5jZ1N22OPuqy4PeWiEUaTwtXzgDXKvTbPf_7cHkEQjSm3J_Z92tSdjV0LWKqKcjVelSPvlAHy9XmY9YriNnwCQxNxq3BDO-HphiyZC5HYm2Oz2I=', published_date='', summary='Amazon scrubbed a reference to workforce diversity from its annual 10-K report this morning, reflecting a broader corporate retreat from diversity and inclusion practices amid changing political winds. Under a…'), NewsResult(title='Amazon on pace for $100B+ in yearly capex; Jassy expects cost efficiencies to drive AI demand', url='https://vertexaisearch.cloud.google.com/grounding-api-redirect/AUBnsYvRsL4jQ1lb5jZ1N22OPuqy4PeWiEUaTwtXzgDXKvTbPf_7cHkEQjSm3J_Z92tSdjV0LWKqKcjVelSPvlAHy9XmY9YriNnwCQxNxq3BDO-HphiyZC5HYm2Oz2I=', published_date='', summary='Amazon reported $26.3 billion in capital expenditures in the fourth quarter, and the company plans to keep spending at that pace in 2025, which would put the total at $100B+.'), NewsResult(title=\"Amazon's quarterly profits soar to a record $20 billion, but cloud growth comes up short\", url='https://vertexaisearch.cloud.google.com/grounding-api-redirect/AUBnsYvRsL4jQ1lb5jZ1N22OPuqy4PeWiEUaTwtXzgDXKvTbPf_7cHkEQjSm3J_Z92tSdjV0LWKqKcjVelSPvlAHy9XmY9YriNnwCQxNxq3BDO-HphiyZC5HYm2Oz2I=', published_date='', summary=\"Amazon beat Wall Street's overall expectations with $187.8 billion in net sales, up 10%, and came in well ahead on the bottom line, with earnings of $1.86 per share, vs.…\"), NewsResult(title='Amazon earnings preview: AWS cloud growth, AI demand, trade wars among key issues to watch', url='https://vertexaisearch.cloud.google.com/grounding-api-redirect/AUBnsYvRsL4jQ1lb5jZ1N22OPuqy4PeWiEUaTwtXzgDXKvTbPf_7cHkEQjSm3J_Z92tSdjV0LWKqKcjVelSPvlAHy9XmY9YriNnwCQxNxq3BDO-HphiyZC5HYm2Oz2I=', published_date='', summary=\"Amazon's cloud growth rate has been increasing steadily in recent quarters, and investors are looking for the trend to continue when the company reports earnings Thursday for the December quarter…\"), NewsResult(title='Podcast: DeepSeek reality check; Amazon, Bezos, and the Post; lost in the Microsoft garage', url='https://vertexaisearch.cloud.google.com/grounding-api-redirect/AUBnsYvRsL4jQ1lb5jZ1N22OPuqy4PeWiEUaTwtXzgDXKvTbPf_7cHkEQjSm3J_Z92tSdjV0LWKqKcjVelSPvlAHy9XmY9YriNnwCQxNxq3BDO-HphiyZC5HYm2Oz2I=', published_date='', summary='This week on the GeekWire Podcast, we dive deep into DeepSeek, the AI project shaking up the tech world, to better understand the underlying technical advances and the long-term implications…'), NewsResult(title='As big retailers pull back on DEI, what happens to Black', url='https://vertexaisearch.cloud.google.com/grounding-api-redirect/AUBnsYup2nCMJmqTXMTm9Gq_pXUhPafNAR044zEF6M-lWTSb2yqkr4OInNmfyZiXTSz-kFBb137-9mXltmhX_3MvVdaG0hXor49YQmRnGylck1k6c4y5p7ytaGnBRqIR-Npe0ALZmuVF-LWJ', published_date='2025-02-09', summary=\"Find the latest news about Amazon, the nation's largest online retailer, from reporter Ángel González and The Seattle Times business staff ... Topics include Amazon Prime and Amazon Fresh; Amazon Original /Amazon Studios streaming TV; Amazon Web Services; its South Lake Union headquarters and other Seattle offices; Kindle readers, Fire tablets, Fire TV, Echo and Alexa; its e-commerce competitors; and Bezos' Washington Post ownership and Blue Origin space venture.\")]\n"]}], "source": ["from salestech_be.temporal.activities.research_agent.company_news import (\n", "    get_company_news,\n", ")\n", "\n", "company_name = \"amazon\"\n", "company_domain = \"amazon.com\"\n", "enforce_company_name = True\n", "\n", "\n", "results =await get_company_news(company_name, company_domain, enforce_company_name)\n", "print(f\"obtained: {results}\")\n", "assert len(results) > 0"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[32m2025-02-10 14:56:40.768\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[1mLangfuse Layer Logging - logging success\u001b[0m | \u001b[33mserialized: {\"message\":\"Langfuse Layer Logging - logging success\",\"name\":\"LiteLLM\",\"lineno\":297,\"function_name\":\"_old_log_event\",\"level\":\"INFO\",\"timestamp\":1739228200768.24,\"time\":\"2025-02-10T14:56:40.768240-08:00\",\"thread_id\":**********,\"thread_name\":\"MainThread\",\"process_id\":95336,\"extra\":{\"span_id\":\"13189836886352450776\",\"trace_id\":\"335857417674379373875882926437096263344\"}}\u001b[0m| \u001b[36mLiteLLM\u001b[0m:\u001b[36m_old_log_event\u001b[0m:\u001b[36m297\u001b[0m | \u001b[35mtrace_id=335857417674379373875882926437096263344\u001b[0m | \u001b[34mspan_id=13189836886352450776\u001b[0m\n", "\u001b[32m2025-02-10 14:56:40.933\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[1m\n", "LiteLLM completion() model= gemini-2.0-flash; provider = gemini\u001b[0m | \u001b[33mserialized: {\"message\":\"\\nLiteLLM completion() model= gemini-2.0-flash; provider = gemini\",\"name\":\"<PERSON><PERSON><PERSON><PERSON>\",\"lineno\":2825,\"function_name\":\"_check_valid_arg\",\"level\":\"INFO\",\"timestamp\":1739228200933.384,\"time\":\"2025-02-10T14:56:40.933384-08:00\",\"thread_id\":13573337088,\"thread_name\":\"asyncio_0\",\"process_id\":95336,\"extra\":{\"span_id\":\"4709704724395483915\",\"trace_id\":\"100439502818642942196301365659563317986\"}}\u001b[0m| \u001b[36mLiteLLM\u001b[0m:\u001b[36m_check_valid_arg\u001b[0m:\u001b[36m2825\u001b[0m | \u001b[35mtrace_id=100439502818642942196301365659563317986\u001b[0m | \u001b[34mspan_id=4709704724395483915\u001b[0m\n", "\u001b[32m2025-02-10 14:56:45.526\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[1mLangfuse Layer Logging - logging success\u001b[0m | \u001b[33mserialized: {\"message\":\"Langfuse Layer Logging - logging success\",\"name\":\"LiteLLM\",\"lineno\":297,\"function_name\":\"_old_log_event\",\"level\":\"INFO\",\"timestamp\":1739228205526.8481,\"time\":\"2025-02-10T14:56:45.526848-08:00\",\"thread_id\":**********,\"thread_name\":\"MainThread\",\"process_id\":95336,\"extra\":{\"span_id\":\"14997222717901577751\",\"trace_id\":\"62387109195229540746057262020455550983\"}}\u001b[0m| \u001b[36mLiteLLM\u001b[0m:\u001b[36m_old_log_event\u001b[0m:\u001b[36m297\u001b[0m | \u001b[35mtrace_id=62387109195229540746057262020455550983\u001b[0m | \u001b[34mspan_id=14997222717901577751\u001b[0m\n", "\u001b[32m2025-02-10 14:56:45.583\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[1m\n", "LiteLLM completion() model= gemini-2.0-flash; provider = gemini\u001b[0m | \u001b[33mserialized: {\"message\":\"\\nLiteLLM completion() model= gemini-2.0-flash; provider = gemini\",\"name\":\"<PERSON><PERSON><PERSON><PERSON>\",\"lineno\":2825,\"function_name\":\"_check_valid_arg\",\"level\":\"INFO\",\"timestamp\":1739228205583.666,\"time\":\"2025-02-10T14:56:45.583666-08:00\",\"thread_id\":13573337088,\"thread_name\":\"asyncio_0\",\"process_id\":95336,\"extra\":{\"span_id\":\"1788074662154872625\",\"trace_id\":\"42970919266427847159358292841394247679\"}}\u001b[0m| \u001b[36mLiteLLM\u001b[0m:\u001b[36m_check_valid_arg\u001b[0m:\u001b[36m2825\u001b[0m | \u001b[35mtrace_id=42970919266427847159358292841394247679\u001b[0m | \u001b[34mspan_id=1788074662154872625\u001b[0m\n", "\u001b[32m2025-02-10 14:56:50.156\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[1m\n", "LiteLLM completion() model= gpt-4o-mini; provider = openai\u001b[0m | \u001b[33mserialized: {\"message\":\"\\nLiteLLM completion() model= gpt-4o-mini; provider = openai\",\"name\":\"LiteLL<PERSON>\",\"lineno\":2825,\"function_name\":\"_check_valid_arg\",\"level\":\"INFO\",\"timestamp\":1739228210156.1438,\"time\":\"2025-02-10T14:56:50.156144-08:00\",\"thread_id\":13573337088,\"thread_name\":\"asyncio_0\",\"process_id\":95336,\"extra\":{\"span_id\":\"4268206662042791404\",\"trace_id\":\"230318462002480894788816884996327848605\"}}\u001b[0m| \u001b[36mLiteLLM\u001b[0m:\u001b[36m_check_valid_arg\u001b[0m:\u001b[36m2825\u001b[0m | \u001b[35mtrace_id=230318462002480894788816884996327848605\u001b[0m | \u001b[34mspan_id=4268206662042791404\u001b[0m\n", "\u001b[32m2025-02-10 14:56:50.158\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[1mLangfuse Layer Logging - logging success\u001b[0m | \u001b[33mserialized: {\"message\":\"Langfuse Layer Logging - logging success\",\"name\":\"LiteLLM\",\"lineno\":297,\"function_name\":\"_old_log_event\",\"level\":\"INFO\",\"timestamp\":1739228210158.866,\"time\":\"2025-02-10T14:56:50.158866-08:00\",\"thread_id\":**********,\"thread_name\":\"MainThread\",\"process_id\":95336,\"extra\":{\"span_id\":\"17591465053113773192\",\"trace_id\":\"15931870521435999654012445672677490190\"}}\u001b[0m| \u001b[36mLiteLLM\u001b[0m:\u001b[36m_old_log_event\u001b[0m:\u001b[36m297\u001b[0m | \u001b[35mtrace_id=15931870521435999654012445672677490190\u001b[0m | \u001b[34mspan_id=17591465053113773192\u001b[0m\n", "\u001b[32m2025-02-10 14:56:56.621\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[1mLangfuse Layer Logging - logging success\u001b[0m | \u001b[33mserialized: {\"message\":\"Langfuse Layer Logging - logging success\",\"name\":\"Li<PERSON>LLM\",\"lineno\":297,\"function_name\":\"_old_log_event\",\"level\":\"INFO\",\"timestamp\":1739228216621.7,\"time\":\"2025-02-10T14:56:56.621700-08:00\",\"thread_id\":**********,\"thread_name\":\"MainThread\",\"process_id\":95336,\"extra\":{\"span_id\":\"6951759257468469783\",\"trace_id\":\"218356328048213332312741609532431552943\"}}\u001b[0m| \u001b[36mLiteLLM\u001b[0m:\u001b[36m_old_log_event\u001b[0m:\u001b[36m297\u001b[0m | \u001b[35mtrace_id=218356328048213332312741609532431552943\u001b[0m | \u001b[34mspan_id=6951759257468469783\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["obtained: Products & Services: Amazon offers a wide range of products and services including e-commerce as the world's largest online retailer, cloud computing through Amazon Web Services (AWS), digital streaming, artificial intelligence services, and consumer electronics such as Kindle e-readers, Echo devices, Fire tablets, and Fire TVs. Additionally, they provide a subscription service called Amazon Prime.\n", "Ideal Customer Profile: Amazon's ideal customer profile includes a diverse range of consumers and businesses looking for convenience, a vast selection of products, and innovative technology solutions. This includes individual consumers seeking a wide variety of products, businesses looking for cloud computing services, and sellers wanting to leverage Amazon's marketplace for growth.\n", "Case Studies:\n", "- company_name='HexClad' content='<PERSON><PERSON><PERSON><PERSON>, a cookware innovator, boosted sales using brand-building products on Amazon.'\n", "- company_name='Sony' content=\"Sony's home electronics team uses Premium A+ Content to engage and educate customers.\"\n", "- company_name='thefitguy' content='Thefitguy, a fitness accessory brand, uses Amazon Brand Analytics to understand customer needs and grow.'\n", "- company_name='Onkata' content='Onkata, a hardware retailer, uses Manage Your Experiments to boost sales.'\n", "- company_name='Honest Paws' content='Honest Paws is a wellness brand specializing in CBD products for pets.'\n", "- company_name='Spigen' content='Spigen is a tech hardware company known for mobile phone cases.'\n", "- company_name='Lumineux' content='Lumineux provides natural oral health solutions.'\n", "- company_name='Freshly Picked' content='Freshly Picked sells high-end shoes and accessories for babies and children.'\n", "- company_name='Hope & Henry' content='Hope & Henry is a clothing brand using organic cotton and sharing profits with workers.'\n", "- company_name='New Republic' content=\"New Republic sells men's footwear and accessories.\"\n", "- company_name='Taco vs Burrito' content='Taco vs Burrito uses Buy with Prime to reduce operating costs and increase revenue.'\n", "- company_name='bareMinerals' content='bareMinerals uses Buy with Prime to offer a bespoke online shopping experience and boost conversions.'\n", "- company_name='Wealthfront' content='Wealthfront upgraded infrastructure to improve automation and lower business costs with AWS.'\n", "- company_name='Finch Computing' content='Finch Computing trains models to understand nuances in human language using AWS.'\n", "- company_name='Telstra Purple & Museum of Performing Arts' content='Telstra Purple used AWS to help the Museum of Performing Arts in Western Australia add thousands of artifact photos and improve search results.'\n", "- company_name='Biarri & AEIOU' content='AEIOU, an educational service provider for children with autism, used Biarri to leverage AI and data with AWS.'\n", "- company_name='Bay City Public Schools' content='Bay City Public Schools partnered with Amazon Business to streamline operations and deliver resources to teachers faster.'\n"]}], "source": ["from salestech_be.temporal.activities.research_agent.company_site import (\n", "    get_company_site_content,\n", ")\n", "\n", "company_name = \"amazon\"\n", "company_domain = \"amazon.com\"\n", "\n", "results = await get_company_site_content(company_name, company_domain)\n", "print(f\"obtained: {results}\")\n", "assert results is not None"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.8"}}, "nbformat": 4, "nbformat_minor": 2}