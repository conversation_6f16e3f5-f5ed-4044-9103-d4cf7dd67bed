"""
Vercel AI SDK data stream protocol implementation.

This package provides Pydantic models and utilities for implementing the Vercel AI SDK's
data stream protocol without requiring manual serialization.
"""

from salestech_be.common.ai_stream.protocol import (
    BaseDataStreamEvent,
    DataEvent,
    DataStreamEventType,
    ErrorEvent,
    FinishMessageEvent,
    FinishStepEvent,
    MessageAnnotationEvent,
    ReasoningEvent,
    ReasoningSignatureEvent,
    RedactedReasoningEvent,
    SourceEvent,
    StartStepEvent,
    TextDeltaEvent,
    ToolCallDeltaEvent,
    ToolCallEvent,
    ToolCallStreamingStartEvent,
    ToolResultEvent,
    create_streaming_response,
)

__all__ = [
    "BaseDataStreamEvent",
    "DataEvent",
    "DataStreamEventType",
    "ErrorEvent",
    "FinishMessageEvent",
    "FinishStepEvent",
    "MessageAnnotationEvent",
    "ReasoningEvent",
    "ReasoningSignatureEvent",
    "RedactedReasoningEvent",
    "SourceEvent",
    "StartStepEvent",
    "TextDeltaEvent",
    "ToolCallDeltaEvent",
    "ToolCallEvent",
    "ToolCallStreamingStartEvent",
    "ToolResultEvent",
    # Utility functions
    "create_streaming_response",
]
