"""
Implementation of Vercel AI SDK data stream protocol.

This module provides Pydantic models for the Vercel AI SDK's data stream protocol
and utilities for easy serialization without requiring manual formatting.

Protocol spec: https://sdk.vercel.ai/docs/ai-sdk-ui/stream-protocol#data-stream-protocol

General format: TYPE_ID:CONTENT\n
"""

from collections.abc import AsyncGenerator
from enum import StrEnum
from typing import Any, Literal, TypedDict

from fastapi.responses import StreamingResponse
from pydantic import BaseModel, ConfigDict
from pydantic.alias_generators import to_camel

from salestech_be.ree_logging import get_logger

logger = get_logger(__name__)


class DataStreamEventType(StrEnum):
    """
    Event types for AI data streaming protocol.

    Values match the type IDs specified in the Vercel AI SDK documentation.
    """

    # Main types
    TEXT_DELTA = "0"
    DATA = "2"
    ERROR = "3"
    FINISH_MESSAGE = "d"
    FINISH_STEP = "e"
    START_STEP = "f"
    REASONING = "g"
    SOURCE = "h"
    REDACTED_REASONING = "i"
    REASONING_SIGNATURE = "j"
    MESSAGE_ANNOTATION = "8"
    TOOL_CALL = "9"
    TOOL_RESULT = "a"
    TOOL_CALL_STREAMING_START = "b"
    TOOL_CALL_DELTA = "c"


class BaseDataStreamEvent(BaseModel):
    """Base model for all data stream events."""

    model_config = ConfigDict(
        alias_generator=to_camel,
        populate_by_name=True,  # Allows mypy to properly check __init__ with aliases
    )

    @property
    def event_type(self) -> DataStreamEventType:
        """Return the event type for this model."""
        raise NotImplementedError("Subclasses must implement event_type")

    def format(self) -> str:
        """Format the event according to the data stream protocol."""
        return f"{self.event_type.value}:{self._format_content()}\n"

    def _format_content(self) -> str:
        """Format the content according to the specific event type requirements."""
        # Default implementation uses model_dump_json
        return self.model_dump_json(by_alias=True)


class TextDeltaEvent(BaseDataStreamEvent):
    """Text delta event for streaming text content.

    Format: `0:string\n`
    Example: `0:"example"\n`
    """

    text: str

    @property
    def event_type(self) -> DataStreamEventType:
        return DataStreamEventType.TEXT_DELTA

    def _format_content(self) -> str:
        """Format the text content with quotes."""
        # For text parts, content should be a JSON string (quoted)
        import json

        return json.dumps(self.text)


class ToolCallEvent(BaseDataStreamEvent):  # type: ignore[explicit-any] # TODO: fix-any-annotation
    """Tool call event for AI assistant tools.

    Format: `9:{toolCallId:string; toolName:string; args:object}\n`
    Example: `9:{"toolCallId":"call-123","toolName":"my-tool","args":{"some":"argument"}}\n`
    """

    tool_call_id: str
    tool_name: str
    args: dict[str, Any]  # type: ignore[explicit-any] # TODO: fix-any-annotation

    @property
    def event_type(self) -> DataStreamEventType:
        return DataStreamEventType.TOOL_CALL


class ToolCallDeltaEvent(BaseDataStreamEvent):
    """Incremental updates to a tool call event.

    Format: `c:{toolCallId:string; argsTextDelta:string}\n`
    Example: `c:{"toolCallId":"call-456","argsTextDelta":"partial arg"}\n`
    """

    tool_call_id: str
    args_text_delta: str

    @property
    def event_type(self) -> DataStreamEventType:
        return DataStreamEventType.TOOL_CALL_DELTA


class ToolCallStreamingStartEvent(BaseDataStreamEvent):
    """Event indicating the start of a streaming tool call.

    Format: `b:{toolCallId:string; toolName:string}\n`
    Example: `b:{"toolCallId":"call-456","toolName":"streaming-tool"}\n`
    """

    tool_call_id: str
    tool_name: str

    @property
    def event_type(self) -> DataStreamEventType:
        return DataStreamEventType.TOOL_CALL_STREAMING_START


class ToolResultEvent(BaseDataStreamEvent):  # type: ignore[explicit-any] # TODO: fix-any-annotation
    """Event representing a tool result.

    Format: `a:{toolCallId:string; result:object}\n`
    Example: `a:{"toolCallId":"call-123","result":"tool output"}\n`
    """

    tool_call_id: str
    result: Any  # type: ignore[explicit-any] # TODO: fix-any-annotation

    @property
    def event_type(self) -> DataStreamEventType:
        return DataStreamEventType.TOOL_RESULT


class CustomDataEventPayload(TypedDict):
    """Custom DataEvent payload. We need to agree upon structure of this data with FE
    So they can properly process it.

    Current events intended to be similar to OpenAI Assistant events that do not have a
    direct mapping to the Vercel AI SDK data stream protocol.

    See https://platform.openai.com/docs/api-reference/assistants-streaming/events
    """

    event: Literal["chat.created"]
    data: Any  # type: ignore[explicit-any]


class DataEvent(BaseDataStreamEvent):
    """Custom data event for sending arbitrary data.

    Format: `2:Array<JSONValue>\n`
    Example: `2:[{"key":"object1"},{"anotherKey":"object2"}]\n`
    """

    data: list[CustomDataEventPayload]

    @property
    def event_type(self) -> DataStreamEventType:
        return DataStreamEventType.DATA

    def _format_content(self) -> str:
        """Format the data content as a JSON array.

        According to the protocol, the data portion should be an array directly,
        not wrapped in a data object.
        """
        import json

        return json.dumps(self.model_dump(mode="json")["data"])


class ErrorEvent(BaseDataStreamEvent):
    """Error event for communicating errors.

    Format: `3:string\n`
    Example: `3:"error message"\n`
    """

    error: str

    @property
    def event_type(self) -> DataStreamEventType:
        return DataStreamEventType.ERROR

    def _format_content(self) -> str:
        """Format the error content with quotes."""
        import json

        return json.dumps(self.error)


class MessageAnnotationEvent(BaseDataStreamEvent):  # type: ignore[explicit-any] # TODO: fix-any-annotation
    """Message annotation event.

    Format: `8:Array<JSONValue>\n`
    Example: `8:[{"id":"message-123","other":"annotation"}]\n`
    """

    annotations: list[dict[str, Any]]  # type: ignore[explicit-any] # TODO: fix-any-annotation

    @property
    def event_type(self) -> DataStreamEventType:
        return DataStreamEventType.MESSAGE_ANNOTATION

    def _format_content(self) -> str:
        """Format the annotations as a JSON array."""
        import json

        return json.dumps(self.annotations)


class ReasoningEvent(BaseDataStreamEvent):
    """Reasoning event for chain-of-thought.

    Format: `g:string\n`
    Example: `g:"I will open the conversation with witty banter."\n`
    """

    reasoning: str

    @property
    def event_type(self) -> DataStreamEventType:
        return DataStreamEventType.REASONING

    def _format_content(self) -> str:
        """Format the reasoning content with quotes."""
        import json

        return json.dumps(self.reasoning)


class RedactedReasoningEvent(BaseDataStreamEvent):
    """Redacted reasoning event.

    Format: `i:{"data": string}\n`
    Example: `i:{"data": "This reasoning has been redacted for security purposes."}\n`
    """

    data: str

    @property
    def event_type(self) -> DataStreamEventType:
        return DataStreamEventType.REDACTED_REASONING


class ReasoningSignatureEvent(BaseDataStreamEvent):
    """Reasoning signature event.

    Format: `j:{"signature": string}\n`
    Example: `j:{"signature": "abc123xyz"}\n`
    """

    signature: str

    @property
    def event_type(self) -> DataStreamEventType:
        return DataStreamEventType.REASONING_SIGNATURE


class SourceEvent(BaseDataStreamEvent):
    """Source event.

    Format: `h:Source\n`
    Example: `h:{"sourceType":"url","id":"source-id","url":"https://example.com","title":"Example"}\n`
    """

    source_type: Literal["url", "citation"]

    @property
    def event_type(self) -> DataStreamEventType:
        return DataStreamEventType.SOURCE


class UrlSourceEvent(SourceEvent):
    """URL source event, same format as SourceEvent"""

    id: str
    url: str
    title: str


class CitationSourceText(TypedDict):
    speaker: str
    content: str
    start_timestamp_ms: int
    line: int


class CitingSpan(TypedDict):
    start_index: int
    end_index: int


class CitationSourceEvent(SourceEvent):
    """Transcript source event, same format as SourceEvent"""

    source_entity: Literal["transcript"] = "transcript"
    meeting_id: str
    meeting_title: str
    source_text: list[CitationSourceText]
    citing_span: CitingSpan


class FinishStepEvent(BaseDataStreamEvent):
    """Finish step event.

    Format: `e:{finishReason:'stop' | 'length' | 'content-filter' | 'tool-calls' | 'error' | 'other' | 'unknown';usage:{promptTokens:number; completionTokens:number;},isContinued:boolean}\n`
    Example: `e:{"finishReason":"stop","usage":{"promptTokens":10,"completionTokens":20},"isContinued":false}\n`
    """

    finish_reason: Literal[
        "stop", "length", "content-filter", "tool-calls", "error", "other", "unknown"
    ]
    usage: dict[str, int]
    is_continued: bool

    @property
    def event_type(self) -> DataStreamEventType:
        return DataStreamEventType.FINISH_STEP


class FinishMessageEvent(BaseDataStreamEvent):
    """Finish message event.

    Format: `d:{finishReason:'stop' | 'length' | 'content-filter' | 'tool-calls' | 'error' | 'other' | 'unknown';usage:{promptTokens:number; completionTokens:number;}}\n`
    Example: `d:{"finishReason":"stop","usage":{"promptTokens":10,"completionTokens":20}}\n`
    """

    finish_reason: Literal[
        "stop", "length", "content-filter", "tool-calls", "error", "other", "unknown"
    ]
    usage: dict[str, int]

    @property
    def event_type(self) -> DataStreamEventType:
        return DataStreamEventType.FINISH_MESSAGE


class StartStepEvent(BaseDataStreamEvent):
    """Start step event.

    Format: `f:{messageId:string}\n`
    Example: `f:{"messageId":"step_123"}\n`
    """

    message_id: str

    @property
    def event_type(self) -> DataStreamEventType:
        return DataStreamEventType.START_STEP


# Helper for creating streams
def create_streaming_response(
    stream_generator: AsyncGenerator[BaseDataStreamEvent, None],
    media_type: str = "text/plain",
) -> StreamingResponse:
    """
    Creates a FastAPI StreamingResponse with the proper headers for the Vercel AI data stream protocol.

    Args:
        stream_generator: An async generator that yields formatted protocol messages
        media_type: The media type for the response

    Returns:
        A properly configured StreamingResponse
    """

    async def create_formatted_stream(
        generator: AsyncGenerator[BaseDataStreamEvent, None],
    ) -> AsyncGenerator[str, None]:
        try:
            async for event in generator:
                yield event.format()
                logger.bind(event=event).debug(
                    f"Emitted AI data stream event: {event.event_type.name}"
                )
        except Exception:
            logger.exception("Error while generating AI data stream events")
            yield ErrorEvent(error="Unexpected error occurred.").format()
            yield FinishMessageEvent(
                finish_reason="error",
                usage={},
            ).format()

    return StreamingResponse(
        create_formatted_stream(stream_generator),
        media_type=media_type,
        headers={"x-vercel-ai-data-stream": "v1"},
    )
