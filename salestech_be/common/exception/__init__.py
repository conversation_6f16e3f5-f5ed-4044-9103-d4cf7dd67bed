from salestech_be.common.exception.exception import (
    ApplicationError,
    ClientError,
    ConcurrentModificationError,
    ConflictResourceError,
    CRMSyncConflictError,
    CRMSyncStatemachineDriftError,
    DatabaseError,
    ErrorDetails,
    ExternalServiceError,
    ForbiddenError,
    IllegalStateError,
    InvalidArgumentError,
    RequestEntityTooLargeError,
    ResourceNotFoundError,
    ServiceError,
    UnauthorizedError,
    UnprocessableEntity,
)

# export here so I do not need to update every file for this refactoring
__all__ = [
    "ApplicationError",
    "CRMSyncConflictError",
    "CRMSyncStatemachineDriftError",
    "ClientError",
    "ConcurrentModificationError",
    "ConflictResourceError",
    "DatabaseError",
    "ErrorDetails",
    "ExternalServiceError",
    "ForbiddenError",
    "IllegalStateError",
    "InvalidArgumentError",
    "RequestEntityTooLargeError",
    "ResourceNotFoundError",
    "ServiceError",
    "UnauthorizedError",
    "UnprocessableEntity",
]
