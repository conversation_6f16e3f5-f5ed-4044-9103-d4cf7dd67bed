from httpx import AsyncClient, AsyncHTTPTransport

__all__ = [
    "get_http_aclient",
]

CONNECTION_RETRIES = 2

# A singleton http async client.
#
# From httpx docs: In order to get the most benefit from connection pooling,
# make sure you're not instantiating multiple client instances - for example
# by using async with inside a "hot loop". This can be achieved either by having
# a single scoped client that's passed throughout wherever it's needed, or by
# having a single global client instance.
#
# Another option is to add this to app_context, but it's not as flexible as this global
# instance, because some callsites do not have request objects unless you pass it down
# through various layers. But make sure you explicitly close it when app exits.
_http_aclient = AsyncClient(
    transport=AsyncHTTPTransport(
        # set connection/transport layer retries
        retries=CONNECTION_RETRIES
    )
)


def get_http_aclient() -> AsyncClient:
    # NOTE: if you need some very specially customized http_aclient,
    #       maybe convert this to a map of clients
    return _http_aclient
