from typing import Any

from pydantic import BaseModel, model_validator
from pydantic._internal._utils import lenient_issubclass


# when dehydrating, load nested fields from flat data by exact field name match
# https://github.com/pydantic/pydantic/discussions/4989
class UnflatParseModel(BaseModel):
    @model_validator(mode="before")
    @classmethod
    def __root_validator__(cls, values: dict[str, Any]) -> dict[str, Any]:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        if not isinstance(values, dict):
            return values

        complex_fields = [
            name
            for name, field in cls.model_fields.items()
            if lenient_issubclass(field.annotation, BaseModel)
        ]

        for name in complex_fields:
            values.setdefault(name, values)

        return values
