from abc import ABC, abstractmethod

from salestech_be.common.type.metadata.schema import FieldReference, QualifiedField


class FieldWalker[T: QualifiedField | FieldReference](ABC):
    @abstractmethod
    def __call__(self, field: T) -> QualifiedField | FieldReference:
        raise NotImplementedError


class QualifiedFieldWalker(FieldWalker[QualifiedField]):
    @abstractmethod
    def __call__(self, field: QualifiedField) -> QualifiedField | FieldReference:
        raise NotImplementedError


class FieldReferenceWalker(FieldWalker[FieldReference]):
    @abstractmethod
    def __call__(self, field: FieldReference) -> QualifiedField | FieldReference:
        raise NotImplementedError


class DefaultFieldReferenceWalker(FieldReferenceWalker):
    def __init__(self, qualified_field_walker: QualifiedFieldWalker):
        self.qualified_field_walker = qualified_field_walker

    def _walk_field(
        self, field: QualifiedField | FieldReference
    ) -> QualifiedField | FieldReference:
        if isinstance(field, FieldReference):
            return self(field)
        else:
            return self.qualified_field_walker(field)

    def __call__(self, field: FieldReference) -> QualifiedField | FieldReference:
        return field.model_copy(update={"field": self._walk_field(field.field)})
