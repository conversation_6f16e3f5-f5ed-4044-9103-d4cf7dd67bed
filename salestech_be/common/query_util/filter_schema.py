import contextlib
import re
from collections.abc import Callable, Sequence
from datetime import date, datetime, timedelta, tzinfo
from enum import auto
from types import NoneType
from typing import (
    Annotated,
    Any,
    ClassVar,
    Final,
    Literal,
    Self,
    assert_never,
)
from uuid import UUID

from pydantic import (
    BaseModel,
    ConfigDict,
    Discriminator,
    Field,
    PositiveInt,
    WrapValidator,
    field_validator,
    model_validator,
)

from salestech_be.common.query_util.operator import MatchOperator
from salestech_be.common.type.metadata.common import ObjectIdentifier
from salestech_be.common.type.metadata.schema import (
    FieldReference,
    QualifiedField,
)
from salestech_be.util.enum_util import NameValueStrEnum
from salestech_be.util.pydantic_types.copy_util import strict_model_copy
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime


class ComplexFilterValue(BaseModel):
    model_config = ConfigDict(frozen=True)


class RelativeTime(ComplexFilterValue):
    """
    RelativeTimeFilterValue is a model that represents a filter value based on relative time.

    """

    filter_value_type: Annotated[
        Literal["relative_time"],
        Field(
            description="A future proof discriminator to indicate the type of filter value."
        ),
    ]

    days: Annotated[
        int,
        Field(description="Number of days for the relative time filter. Default is 0."),
    ] = 0

    hours: Annotated[
        int,
        Field(
            description="Number of hours for the relative time filter. Default is 0."
        ),
    ] = 0

    minutes: Annotated[
        int,
        Field(
            description="Number of minutes for the relative time filter. Default is 0."
        ),
    ] = 0

    seconds: Annotated[
        int,
        Field(
            description="Number of seconds for the relative time filter. Default is 0."
        ),
    ] = 0

    negative: Annotated[
        bool,
        Field(
            description="Indicates if the relative time is in the past (True) or future (False). Default is False."
        ),
    ] = False

    @property
    def abs_delta_seconds(self) -> PositiveInt:
        return (
            self.days * 24 * 3600 + self.hours * 3600 + self.minutes * 60 + self.seconds
        )

    def to_zoned_datetime(self, tz: tzinfo) -> ZoneRequiredDateTime:
        delta_seconds = self.abs_delta_seconds
        return (
            datetime.now(tz) - timedelta(seconds=delta_seconds)
            if self.negative
            else datetime.now(tz) + timedelta(seconds=delta_seconds)
        )

    # todo(xw): need to have org level timezone info settings to make this completely correct
    def to_local_datetime(self) -> datetime:
        delta_seconds = self.abs_delta_seconds
        return (
            datetime.now() - timedelta(seconds=delta_seconds)  # noqa: DTZ005
            if self.negative
            else datetime.now() + timedelta(seconds=delta_seconds)  # noqa: DTZ005
        )

    # todo(xw): need to have org level timezone info settings to make this completely correct
    def to_local_date(self) -> date:
        delta_seconds = self.abs_delta_seconds
        return (
            datetime.now().date() - timedelta(days=delta_seconds)  # noqa: DTZ005
            if self.negative
            else datetime.now().date() + timedelta(days=delta_seconds)  # noqa: DTZ005
        )


_iso_date_regex = re.compile(r"^\d{4}-\d{2}-\d{2}$")
_strict_iso_datetime_format = "YYYY-MM-DDTHH:MM:SS[.ffffff][Z|±HH:MM]"
_strict_iso_datetime_regex = re.compile(
    r"^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d+)?([Zz]|[+-]\d{2}:\d{2})?$"
)
_uuid_regex = re.compile(
    "(?i)^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$"
)
SingularFilterValue = datetime | date | UUID | int | float | bool | str
SetFilterValue = (
    set[datetime] | set[date] | set[UUID] | set[str] | set[int] | set[float] | set[bool]
)
ModelFilterValue = RelativeTime


def _validate_singular_filter_value(
    v: SingularFilterValue,
) -> SingularFilterValue:
    if isinstance(v, float | int):
        return v
    if not isinstance(v, str):
        return v
    if _iso_date_regex.match(v):
        with contextlib.suppress(Exception):
            return date.fromisoformat(v)
    if _strict_iso_datetime_regex.match(v):
        with contextlib.suppress(Exception):
            return datetime.fromisoformat(v)
    if _uuid_regex.match(v):
        with contextlib.suppress(Exception):
            return UUID(v)
    return v


def _validate_filter_value(v: Any, handler: Callable[[Any], Any], info: Any) -> Any:  # type: ignore[explicit-any] # TODO: fix-any-annotation
    if v is None:
        return v
    if isinstance(v, SingularFilterValue):
        return _validate_singular_filter_value(v)
    if (
        isinstance(v, list | tuple | set)
        and v
        and isinstance(next(iter(v)), SingularFilterValue)
    ):
        _results: SetFilterValue = set()
        _type: type | None = None
        for _item in v:
            _converted_item = _validate_singular_filter_value(_item)
            if _type and (_type is not type(_converted_item)):
                raise ValueError(f"Cannot mix different types in set filter value: {v}")
            _type = type(_converted_item)
            _results.add(_converted_item)  # type: ignore[arg-type]
        return _results
    return handler(v)


FilterValue = Annotated[
    SingularFilterValue | SetFilterValue | ModelFilterValue | None,
    WrapValidator(_validate_filter_value),
    Field(
        description=f"""
    The value to filter against. This can be a singular value, a set of values, or a complex value.
    - When the value is passed in as datetime, it need to follow an ISO 8601 format of '{_strict_iso_datetime_format}'.
    - When the value is passed in as date, it need to follow an ISO 8601 format of 'YYYY-MM-DD'.
    """
    ),
]


class FilterType(NameValueStrEnum):
    VALUE = auto()
    COMPOSITE = auto()


class BaseFilter(BaseModel):
    pass


class ValueFilter(BaseFilter):
    _nullable_value_operators: ClassVar[set[MatchOperator]] = {
        MatchOperator.EQ,
        MatchOperator.NE,
        MatchOperator.BLANK,
        MatchOperator.NBLANK,
    }

    filter_type: Literal[FilterType.VALUE] = FilterType.VALUE
    field: QualifiedField | FieldReference
    value: FilterValue = None
    operator: MatchOperator

    @model_validator(mode="after")
    def validate_value(self) -> Self:
        if self.value is None and self.operator not in self._nullable_value_operators:
            raise ValueError(
                f"Value cannot be None when operator is {self.operator}. "
                f"Acceptable operators are {self._nullable_value_operators}"
            )
        return self

    def is_for_field(self, field_path: tuple[str | UUID, ...]) -> bool:
        if isinstance(self.field, QualifiedField):
            return self.field.path == field_path
        return False

    def fanout_by_value(self, value: FilterValue) -> list[Self]:
        """
        This method is used to fanout a filter by a value.
        The fanout is done by creating a new filter for each value in the set or singular value.
        The new filters will have the same field and operator as the current filter,
        but will have a different value.
        """
        if isinstance(value, set):
            if isinstance(self.value, set):
                return [strict_model_copy(self, value=value)]
            else:
                return [strict_model_copy(self, value=v) for v in value]
        elif isinstance(value, SingularFilterValue | NoneType):
            if isinstance(self.value, set):
                return [strict_model_copy(self, value={value})]
            else:
                return [strict_model_copy(self, value=value)]
        elif isinstance(value, ModelFilterValue):
            if isinstance(self.value, ModelFilterValue):
                return [strict_model_copy(self, value=value)]
            else:
                raise ValueError(
                    f"Cannot fanout by model filter value. Current filter is {self.model_dump_json()}, "
                    f"and the value to fanout is {value}"
                )
        else:
            assert_never(value)


Filter = Annotated["CompositeFilter | ValueFilter", Discriminator("filter_type")]


class CompositeFilter(BaseFilter):
    """
    A composite filter that can contain multiple filters arranged in
    boolean logical groups:
    - all_of: if present, all filters must evaluate to true
    - any_of: if present, at least one filter must evaluate to true
    - none_of: if present, all filters must evaluate to false
    """

    filter_type: Literal[FilterType.COMPOSITE] = FilterType.COMPOSITE

    all_of: list[Filter] = Field(default_factory=list)
    any_of: list[Filter] = Field(default_factory=list)
    none_of: list[Filter] = Field(default_factory=list)


# Internal filters are used for internal representation of filters for
# query execution and optimization
class NonRelationalValueFilter(BaseFilter):
    field_path: tuple[str | UUID, ...]
    filter_value: FilterValue
    operator: MatchOperator

    def evaluate_as_match_when_target_absent(self) -> bool:
        """
        When the target value is not present, due to the value itself being null,
        or the parent object itself is not present, whether the filter should evaluate
        to match or not.
        i.e. When this method returns True, the filter will evaluate to match
        - when the target value is not present.
        - when the parent object is not present.
        """
        if self.operator == MatchOperator.BLANK:
            return True
        if self.operator == MatchOperator.EQ and self.filter_value is None:
            return True
        if (
            self.operator
            in (
                MatchOperator.NE,
                MatchOperator.NIN,
            )
            and self.filter_value is not None
        ):
            return True
        return False


class NestedFilter(BaseFilter):
    path: tuple[str | UUID, ...]
    all_of: Sequence[NonRelationalValueFilter | Self] | None = None
    any_of: Sequence[NonRelationalValueFilter | Self] | None = None

    def evaluate_as_match_when_target_absent(self) -> bool:
        """
        When the target value is not present, due to the value itself being null,
        or the parent object itself is not present, whether the filter should evaluate
        to match or not.
        i.e. When this method returns True, the filter will evaluate to match
        - when the target value is not present.
        - when the parent object is not present.
        """
        if self.all_of and any(
            not f.evaluate_as_match_when_target_absent() for f in self.all_of
        ):
            return False
        if self.any_of:
            return any(f.evaluate_as_match_when_target_absent() for f in self.any_of)
        return True


class RelationalFilter(BaseFilter):
    relationship_chain: tuple[str | UUID, ...]
    all_of: Sequence[NonRelationalValueFilter | Self | NestedFilter] | None = None
    any_of: Sequence[NonRelationalValueFilter | Self | NestedFilter] | None = None

    def evaluate_as_match_when_target_absent(self) -> bool:
        """
        When the target value is not present, due to the value itself being null,
        or the parent object itself is not present, whether the filter should evaluate
        to match or not.
        i.e. When this method returns True, the filter will evaluate to match
        - when the target value is not present.
        - when the parent object is not present.
        """
        if self.all_of and any(
            not f.evaluate_as_match_when_target_absent() for f in self.all_of
        ):
            return False
        if self.any_of:
            return any(f.evaluate_as_match_when_target_absent() for f in self.any_of)
        return True


class InternalCompositeFilter(BaseFilter):
    all_of: (
        Sequence[NonRelationalValueFilter | NestedFilter | RelationalFilter | Self]
        | None
    ) = None
    any_of: (
        Sequence[NonRelationalValueFilter | NestedFilter | RelationalFilter | Self]
        | None
    ) = None
    none_of: (
        Sequence[NonRelationalValueFilter | NestedFilter | RelationalFilter | Self]
        | None
    ) = None


InternalFilter = (
    NonRelationalValueFilter | NestedFilter | RelationalFilter | InternalCompositeFilter
)


def filter_depth(_filter: BaseFilter) -> int:
    if isinstance(_filter, CompositeFilter):
        child_depths = [
            filter_depth(f)
            for f in (_filter.all_of or [])
            + (_filter.any_of or [])
            + (_filter.none_of or [])
        ]
        return 1 + max(child_depths, default=0)
    return 1


_MAX_FILTER_DEPTH: Final[int] = 4


class FilterSpec(BaseModel):
    model_config = ConfigDict(frozen=True)
    primary_object_identifier: Annotated[
        ObjectIdentifier,
        Field(
            description="The identifier of the primary object,"
            "Sorters contained in this SortSpec will use this "
            "identifier with their provided 'field_identifier' "
            "to locate field for sorting"
        ),
    ]
    filter: Filter

    @field_validator("filter", mode="after")
    @classmethod
    def validate_filter_depth(cls, value: Filter) -> Filter:
        if (depth := filter_depth(value)) > _MAX_FILTER_DEPTH:
            raise ValueError(
                f"Filter depth cannot exceed {_MAX_FILTER_DEPTH}, but found {depth} "
                f"[provided filter: {value.model_dump_json()}]"
            )
        return value

    def copy_on_update_or_self(self, filter_walker: Callable[[Filter], Filter]) -> Self:
        """
        This method is used to copy the filter spec on update or return self if the filter is not changed.
        """
        transformed_filter = filter_walker(self.filter)
        if transformed_filter is self.filter:
            return self
        return self.model_copy(update={"filter": transformed_filter})


CompositeFilter.model_rebuild()
