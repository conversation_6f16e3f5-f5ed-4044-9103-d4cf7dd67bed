from collections.abc import Iterable
from enum import Enum, EnumMeta, StrEnum, auto
from typing import Annotated, Any, <PERSON>ric, TypeVar, cast

from pydantic import BaseModel, Field

from salestech_be.ree_logging import get_logger

FieldType = TypeVar("FieldType")
ValueType = TypeVar("ValueType")
logger = get_logger()


def union_list(lst1: list[Any], lst2: list[Any]) -> list[Any]:  # type: ignore[explicit-any] # TODO: fix-any-annotation
    return list(set(lst1) | set(lst2))


class LogicalOperator(StrEnum):
    AND = auto()
    OR = auto()


def get_enum_options_as_string(enum: EnumMeta) -> str:
    enum_vals: list[str] = [v.value for v in cast(Iterable[Enum], enum)]

    return f"Acceptable operations {', '.join(enum_vals)}"


class FilterSchema(BaseModel, Generic[FieldType, ValueType]):
    field: Annotated[
        FieldType,
        Field(description="A supported filterable API attribute, e.g. id"),
    ]
    value: Annotated[ValueType, Field(description="The value to filter against")]
    operator: Annotated[
        LogicalOperator | None,
        Field(
            examples=[
                (
                    f"Acceptable operations are "
                    f"{get_enum_options_as_string(LogicalOperator)}"
                ),
            ],
            description="Default to AND if missing value",
        ),
    ] = LogicalOperator.AND
    predicate: Annotated[
        StrEnum,
        Field(description="Type of filtering operation supported by this filter"),
    ]


class ApiFieldFilter(FilterSchema[str, ValueType], Generic[ValueType]):
    pass


class StandardComparator(StrEnum):
    EQ = auto()  # Equal
    NE = auto()  # Not Equal
    LT = auto()  # Less Than
    GT = auto()  # Greater Than
    LTE = auto()  # Less Than or Equal To
    GTE = auto()  # Greater Than or Equal To
    IN = auto()  # In
    NIN = auto()  # Not In
    CONTAINS = auto()  # Contains
    NCONTAINS = auto()  # Not Contains
    NULL = auto()  # Is Null
    NNULL = auto()  # Not Null


class StandardValueFilter(ApiFieldFilter[Any]):  # type: ignore[explicit-any] # TODO: fix-any-annotation
    predicate: Annotated[
        StandardComparator,
        Field(
            description=(
                f"Acceptable operations are "
                f"{get_enum_options_as_string(StandardComparator)}"
            ),
            examples=[
                (
                    f"Acceptable operations are "
                    f"{get_enum_options_as_string(StandardComparator)}"
                ),
            ],
        ),
    ] = StandardComparator.EQ
