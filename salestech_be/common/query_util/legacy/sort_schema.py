from enum import StrEnum, auto
from typing import Annota<PERSON>, <PERSON><PERSON>, TypeVar

from pydantic import BaseModel, Field

from salestech_be.common.query_util.legacy.filter_schema import (
    get_enum_options_as_string,
)

SortFieldType = TypeVar("SortFieldType")


class OrderEnum(StrEnum):
    ASC = auto()
    DESC = auto()


class SortSchema(BaseModel, Generic[SortFieldType]):
    field: Annotated[
        SortFieldType,
        Field(description="A supported sortable API attribute, e.g. id"),
    ]
    order: Annotated[
        OrderEnum,
        Field(
            description=(
                f"Acceptable operations are {get_enum_options_as_string(OrderEnum)}"
            ),
            examples=[
                f"Acceptable operations are {get_enum_options_as_string(OrderEnum)}",
            ],
        ),
    ]
