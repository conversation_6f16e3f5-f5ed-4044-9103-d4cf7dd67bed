from enum import auto
from typing import Literal, assert_never

from salestech_be.common.query_util.legacy.filter_schema import StandardComparator
from salestech_be.util.enum_util import NameValueStrEnum


class MatchOperator(NameValueStrEnum):
    EQ = auto()
    NE = auto()
    LT = auto()
    GT = auto()
    LTE = auto()
    GTE = auto()
    IN = auto()  # In
    NIN = auto()  # Not In
    CONTAINS = auto()  # Contains
    NCONTAINS = auto()  # Not Contains
    STARTS_WITH = auto()  # Starts With
    ENDS_WITH = auto()  # Ends With
    BLANK = auto()  # Presence
    NBLANK = auto()  # Absence

    def as_legacy_comparator_or_none(self) -> StandardComparator | None:  # noqa: C901, PLR0912
        _result: StandardComparator | None = None
        match self:
            case MatchOperator.EQ:
                _result = StandardComparator.EQ
            case MatchOperator.NE:
                _result = StandardComparator.NE
            case MatchOperator.LT:
                _result = StandardComparator.LT
            case MatchOperator.GT:
                _result = StandardComparator.GT
            case MatchOperator.LTE:
                _result = StandardComparator.LTE
            case MatchOperator.GTE:
                _result = StandardComparator.GTE
            case MatchOperator.IN:
                _result = StandardComparator.IN
            case MatchOperator.NIN:
                _result = StandardComparator.NIN
            case MatchOperator.CONTAINS:
                _result = StandardComparator.CONTAINS
            case MatchOperator.NCONTAINS:
                _result = StandardComparator.NCONTAINS
            case MatchOperator.BLANK:
                _result = StandardComparator.NULL
            case MatchOperator.NBLANK:
                _result = StandardComparator.NNULL
            case MatchOperator.STARTS_WITH | MatchOperator.ENDS_WITH:
                _result = None
            case _ as never:
                assert_never(never)
        return _result


EqualityOperator = Literal[MatchOperator.EQ, MatchOperator.NE]
CompareOperator = Literal[
    MatchOperator.LT, MatchOperator.GT, MatchOperator.LTE, MatchOperator.GTE
]
WithinOperator = Literal[MatchOperator.IN, MatchOperator.NIN]
ContainsOperator = Literal[MatchOperator.CONTAINS, MatchOperator.NCONTAINS]
StringPartialMatchOperator = Literal[MatchOperator.STARTS_WITH, MatchOperator.ENDS_WITH]
PresenceOperator = Literal[MatchOperator.BLANK, MatchOperator.NBLANK]
