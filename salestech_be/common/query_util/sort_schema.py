import base64
from collections.abc import Mapping
from typing import Annotated, Literal, Self
from uuid import UUID

from pydantic import BaseModel, ConfigDict, Field

from salestech_be.common.query_util.legacy.filter_schema import (
    get_enum_options_as_string,
)
from salestech_be.common.query_util.legacy.sort_schema import OrderEnum
from salestech_be.common.type.metadata.common import ObjectIdentifier
from salestech_be.common.type.metadata.schema import FieldReference, QualifiedField
from salestech_be.ree_logging import get_logger

logger = get_logger(__name__)


class Sorter(BaseModel):
    model_config = ConfigDict(frozen=True)
    field: QualifiedField | FieldReference
    order: Annotated[
        OrderEnum,
        Field(
            description=(
                f"Acceptable operations are {get_enum_options_as_string(OrderEnum)}"
            ),
            examples=[
                (f"Acceptable operations are {get_enum_options_as_string(OrderEnum)}"),
            ],
        ),
    ]
    null_first: bool = False


class SortingSpec(BaseModel):
    model_config = ConfigDict(frozen=True)
    primary_object_identifier: Annotated[
        ObjectIdentifier,
        Field(
            description="The identifier of the primary object,"
            "Sorters contained in this SortSpec will use this "
            "identifier with their provided 'field_identifier' "
            "to locate field for sorting"
        ),
    ]
    ordered_sorters: Annotated[
        tuple[Sorter, ...],
        Field(
            description="The ordered sorters. Sorting will be"
            " applied in the order they are provided."
        ),
    ]


class NonRelationalSorter(BaseModel):
    model_config = ConfigDict(frozen=True)
    field: QualifiedField
    order: Annotated[
        OrderEnum,
        Field(
            description=(
                f"Acceptable operations are {get_enum_options_as_string(OrderEnum)}"
            ),
            examples=[
                (f"Acceptable operations are {get_enum_options_as_string(OrderEnum)}"),
            ],
        ),
    ]
    null_first: bool = False

    def is_for_field(self, field_path: tuple[str | UUID, ...]) -> bool:
        return self.field.path == field_path


class NonRelationalSortingSpec(BaseModel):
    model_config = ConfigDict(frozen=True)
    primary_object_identifier: Annotated[
        ObjectIdentifier,
        Field(
            description="The identifier of the primary object,"
            "Sorters contained in this SortSpec will use this "
            "identifier with their provided 'field_identifier' "
            "to locate field for sorting"
        ),
    ]
    ordered_sorters: Annotated[
        tuple[NonRelationalSorter, ...],
        Field(
            description="The ordered sorters. Sorting will be"
            " applied in the order they are provided."
        ),
    ]


def as_non_relational_sorter_or_none(*, sorter: Sorter) -> NonRelationalSorter | None:
    """
    Convert a Sorter into a NonRelationalSorter if possible.

    This function examines a Sorter and determines if it can be converted to a NonRelationalSorter.
    The conversion is only possible if the sorter's field is a QualifiedField (direct field reference)
    rather than a FieldReference (reference to a related object's field).

    Args:
        sorter: The Sorter to potentially convert

    Returns:
        A NonRelationalSorter if the input Sorter references a direct field,
        None if the Sorter references a related object's field

    Example:
        Input Sorter with QualifiedField("name") -> Returns NonRelationalSorter
        Input Sorter with FieldReference(relationship="contacts", field="email") -> Returns None
    """
    if isinstance(sorter.field, QualifiedField):
        return NonRelationalSorter(
            field=sorter.field,
            order=sorter.order,
            null_first=sorter.null_first,
        )
    else:
        return None


def as_non_relational_sorting_spec_or_default(
    *, sorting_spec: SortingSpec, fallback: NonRelationalSortingSpec
) -> NonRelationalSortingSpec:
    """
    Convert a SortingSpec into a NonRelationalSortingSpec by filtering out any relational sorters.

    This function takes a SortingSpec which may contain both relational and non-relational sorters,
    and converts it to a NonRelationalSortingSpec by:
    1. Filtering out any sorters that reference related objects (FieldReference)
    2. Keeping only sorters that reference direct fields (QualifiedField)
    3. If no non-relational sorters remain, returns the provided fallback spec
    4. Otherwise returns a new NonRelationalSortingSpec with the filtered sorters

    Args:
        sorting_spec: The original SortingSpec containing mixed sorters
        fallback: A NonRelationalSortingSpec to use if no non-relational sorters are found

    Returns:
        A NonRelationalSortingSpec containing only the non-relational sorters from the input,
        or the fallback if no non-relational sorters were found.

    Example:
        Input SortingSpec with:
        - QualifiedField("name")
        - FieldReference(relationship="contacts", field="email")
        - QualifiedField("created_date")

        Will return NonRelationalSortingSpec with only:
        - QualifiedField("name")
        - QualifiedField("created_date")
    """
    non_relational_sorters = tuple(
        sorter
        for sorter in (
            as_non_relational_sorter_or_none(sorter=sorter)
            for sorter in sorting_spec.ordered_sorters
        )
        if sorter
    )
    if not non_relational_sorters:
        logger.warning(
            "No non-relational sorters found in the sorting spec, using fallback",
            original_sorting_spec=sorting_spec,
            fallback=fallback,
        )
        return fallback
    return NonRelationalSortingSpec(
        primary_object_identifier=sorting_spec.primary_object_identifier,
        ordered_sorters=non_relational_sorters,
    )


_UTF8_ENCODING = "utf-8"


def _b64_encode(v: str) -> str:
    return base64.b64encode(v.encode(_UTF8_ENCODING)).decode(_UTF8_ENCODING)


def _b64_decode(v: str) -> str:
    return base64.b64decode(v.encode(_UTF8_ENCODING)).decode(_UTF8_ENCODING)


class SinglePageTokenV1(BaseModel):
    model_config = ConfigDict(frozen=True)
    version: Literal["spt_v1"] = "spt_v1"
    record_id: UUID
    page_number: int

    def to_b64str(self) -> str:
        return _b64_encode(self.model_dump_json())

    @classmethod
    def from_b64str(cls, v: str) -> Self | None:
        try:
            return cls.model_validate_json(_b64_decode(v))
        except Exception:
            logger.exception("Failed to parse single page token", token=v)
            return None


class MultiPageTokenV1(BaseModel):
    model_config = ConfigDict(frozen=True)
    version: Literal["mpt_v1"] = "mpt_v1"
    single_page_tokens: list[SinglePageTokenV1]

    def to_generic_token(self) -> Mapping[int, str]:
        return {
            single_page_token.page_number: single_page_token.to_b64str()
            for single_page_token in self.single_page_tokens
        }
