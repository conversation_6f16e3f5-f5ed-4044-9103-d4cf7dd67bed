from voyageai import AsyncClient as AsyncVoyageClient
from voyageai import Client as VoyageClient

from salestech_be.settings import settings

_sync_voyage_client: VoyageClient | None = None


def get_sync_voyage_client() -> VoyageClient:
    global _sync_voyage_client  # noqa: PLW0603
    if _sync_voyage_client is None:
        _sync_voyage_client = VoyageClient(
            api_key=settings.voyage_api_key.get_secret_value()
        )
    return _sync_voyage_client


_async_voyage_client: AsyncVoyageClient | None = None


def get_async_voyage_client() -> AsyncVoyageClient:
    global _async_voyage_client  # noqa: PLW0603
    if _async_voyage_client is None:
        _async_voyage_client = AsyncVoyageClient(
            api_key=settings.voyage_api_key.get_secret_value()
        )
    return _async_voyage_client
