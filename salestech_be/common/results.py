from __future__ import annotations

from collections.abc import Mapping
from typing import Self

from pydantic import BaseModel


class Cursor(BaseModel):
    total_number: int | None = None
    total_page_number: int | None = None
    page_size: int = 100  # default page size
    page_index: int = 1  # default from page 1
    page_tokens: Mapping[int, str] | None = None
    requested_page_token: str | None = None
    has_more: bool | None = None

    @classmethod
    def single_page(cls, *, total_number: int) -> Self:
        return cls(total_number=total_number, total_page_number=1, page_size=1)
