# Standard Object Field Identifiers Documentation

## Overview
This module defines the core data model and field identifiers for the CRM system. It provides a type-safe way to reference fields across different entities while maintaining consistency and preventing typos.

## Key Components

### 1. Field Identifiers
Define available fields for each object type. Examples include:
- AccountField
- ContactField
- DealField
- LeadField
- PipelineField
- TaskField
- MeetingField
- CommentField

### 2. Object Identifiers
Define available object types in the system:
- account
- contact
- deal
- lead
- pipeline
- task
- meeting
- comment

### 3. Select List Identifiers
Define available select lists for dropdown/enum values.

## Select Lists

### Special Select Lists
Lists with additional metadata in domain-specific models:
- **deal_stage**: Manages deal pipeline stages
- **lead_stage**: Manages lead statuses
- **account_stage**: Reserved for future account stage management

### Direct Select Lists
Lists implemented directly with GenericSelectList:
- **pipeline_source**: Source of pipeline entries
- **pipeline_type**: Type classification for pipelines
- **contact_stage**: Stage/status for contacts
- **pipeline_closed_won_reasons**: Reasons for won deals
- **pipeline_closed_lost_reasons**: Reasons for lost deals

## Core Object Fields

### Account Fields
```python
class AccountField(StdFieldIdentifierEnum):
id = "id"
display_name = "display_name"
status = "status"
official_website = "official_website"
domain_name = "domain_name"
description = "description"
estimated_annual_revenue = "estimated_annual_revenue"
estimated_employee_count = "estimated_employee_count"
# ... and more
```
### Contact Fields
```python
class ContactField(StdFieldIdentifierEnum):
id = "id"
display_name = "display_name"
status = "status"
email = "email"
phone = "phone"
# ... and more
```

### Deal Fields
```python
class DealField(StdFieldIdentifierEnum):
id = "id"
display_name = "display_name"
status = "status"
amount = "amount"
close_date = "close_date"
# ... and more
```

## Usage Examples

### Field Access
```python
# Accessing a field from an object
field = AccountField.display_name
assert field.value == "display_name"
```
```python
# Access select list identifiers
stage = StdSelectListIdentifier.deal_stage
assert stage.value == "deal_stage"
# Check if a select list is direct
is_direct = is_direct_std_select_list_identifier(StdSelectListIdentifier.pipeline_source)
assert is_direct == True
```

## Type Hierarchies

### Core Type Inheritance
```mermaid
graph TD
    StrEnum --> NameValueStrEnum
    NameValueStrEnum --> StdFieldIdentifierEnum
    StdFieldIdentifierEnum --> AccountField
    StdFieldIdentifierEnum --> ContactField
    StdFieldIdentifierEnum --> DealField
    StdFieldIdentifierEnum --> LeadField
    StdFieldIdentifierEnum --> PipelineField
    StdFieldIdentifierEnum --> TaskField
    StdFieldIdentifierEnum --> MeetingField
    StdFieldIdentifierEnum --> CommentField
```

### Field Relationships
```mermaid
graph TD
    subgraph Common_Fields
        id[id]
        display_name[display_name]
        status[status]
        created_at[created_at]
        updated_at[updated_at]
    end

    subgraph Account_Fields
        account[AccountField]
        website[official_website]
        domain[domain_name]
        revenue[estimated_annual_revenue]
    end

    subgraph Contact_Fields
        contact[ContactField]
        email[primary_email]
        first_name[first_name]
        last_name[last_name]
        title[title_]
    end

    subgraph Deal_Fields
        deal[DealField]
        amount[deal_amount]
        type[deal_type]
        source[deal_source]
        close_date[anticipated_close_at]
    end

    account --> id
    account --> display_name
    account --> status
    contact --> id
    contact --> display_name
    contact --> status
    deal --> id
    deal --> display_name
    deal --> status
```

### Select List Hierarchy
```mermaid
graph TD
    subgraph Select_Lists
        StdSelectListIdentifier --> Special_Lists
        StdSelectListIdentifier --> Direct_Lists
    end

    subgraph Special_Lists
        deal_stage[deal_stage]
        lead_stage[lead_stage]
        account_stage[account_stage]
    end

    subgraph Direct_Lists
        pipeline_source[pipeline_source]
        pipeline_type[pipeline_type]
        contact_stage[contact_stage]
        won_reasons[pipeline_closed_won_reasons]
        lost_reasons[pipeline_closed_lost_reasons]
    end

    style Special_Lists fill:#f9f,stroke:#333
    style Direct_Lists fill:#bbf,stroke:#333
```

### Data Flow
```mermaid
graph LR
    subgraph Input_Validation
        Raw_Input --> Field_Identifier
        Field_Identifier --> Validation
        Validation --> Typed_Value
    end

    subgraph Storage
        Typed_Value --> Database
        Database --> Serialization
        Serialization --> API_Response
    end

    style Input_Validation fill:#f9f,stroke:#333
    style Storage fill:#bbf,stroke:#333
```


## Important Notes

1. All identifiers are immutable after definition
2. Field names must be unique within their object type
3. Object names must be unique across the system
4. Select list identifiers are used for dropdown/enum values
5. Direct select lists are implemented with GenericSelectList
6. Special select lists may have additional metadata

## Validation
- Field names follow snake_case convention
- No duplicate field names allowed within an object type
- Field identifiers are case-sensitive
- Reserved words (like 'title') use trailing underscore (title_)
