# Object Relationships Documentation

## Overview
This document outlines the standardized relationship definitions between different entities in our CRM system. All relationships follow a strict naming convention and are enforced through type-safe enums.

## Naming Convention
Relationships follow the pattern:
`object_name__direction__related_object`

Example: `account__to__owner_user` indicates that an account has a relationship TO a user who owns it.

## Core Entities and Their Relationships

### Account
- **Users**
  - → owner_user
  - → created_by_user
  - → updated_by_user
- **Contacts**
  - ← contact (contacts belonging to account)
- **Deals**
  - ← deal (deals associated with account)

### Contact
- **Account**
  - → account (primary account)
- **Users**
  - → owner_user
  - → created_by_user
  - → updated_by_user
- **Deals**
  - ← deal (deals associated with contact)

### Deal
- **Account**
  - → account
- **Contact**
  - → contact
- **Users**
  - → owner_user
  - → created_by_user
  - → updated_by_user

### Task
- **Users**
  - → owner_user
  - → created_by_user
  - → updated_by_user
- **Pipeline**
  - → pipeline
- **Account**
  - → account
- **Contact**
  - → contact

### Meeting
- **Users**
  - → owner_user
  - → created_by_user
  - → updated_by_user
- **Contacts**
  - → contact
- **Account**
  - → account

## Visual Relationship Diagram

```mermaid
graph TD
    Account --> |owner/created_by/updated_by| User
    Contact --> |owner/created_by/updated_by| User
    Contact --> |belongs_to| Account
    Deal --> |owner/created_by/updated_by| User
    Deal --> |associated_with| Account
    Deal --> |associated_with| Contact
    Task --> |owner/created_by/updated_by| User
    Task --> |associated_with| Pipeline
    Task --> |associated_with| Account
    Task --> |associated_with| Contact
    Meeting --> |owner/created_by/updated_by| User
    Meeting --> |associated_with| Contact
    Meeting --> |associated_with| Account
    Comment --> |created_by| User
    Comment --> |attached_to| Account
    Comment --> |attached_to| Contact
    Comment --> |attached_to| Deal
    Comment --> |attached_to| Task
```

## Code Example
```python
from salestech_be.common.schema_manager.std_object_relationship import AccountRelationship

# Get the relationship ID for account to owner
relationship = AccountRelationship.account__to__owner_user
assert relationship.value == "account__to__owner_user"
```

## Common Patterns

1. **Ownership Pattern**
   - Most entities have an owner_user relationship
   - Tracks who is responsible for the entity

2. **Audit Pattern**
   - created_by_user and updated_by_user relationships
   - Tracks who created and last modified the entity

3. **Association Pattern**
   - Entities like deals and tasks can be associated with multiple other entities
   - Enables complex relationship tracking

## Validation
All relationship IDs are validated against the pattern:

```python
^(?!._)[a-z]+(?:[a-z]+)(from|to)[a-z]+(?:[a-z]+)$
```


This ensures:
- Only lowercase letters and underscores
- No triple underscores
- Proper direction specification (from/to)
- Valid object names
