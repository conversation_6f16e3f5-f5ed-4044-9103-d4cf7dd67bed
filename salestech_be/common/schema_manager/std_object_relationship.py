import re

from salestech_be.util.enum_util import NameValueStrEnum

_RELATIONSHIP_ID_REGEX = r"^(?!.*___)[a-z]+(?:_[a-z]+)*__(from|to)__[a-z]+(?:_[a-z]+)*$"


class StdObjectRelationship(NameValueStrEnum):
    def __init__(self, value: str):
        super().__init__(value)
        # ensure subclass don't default duplicate object names
        if not re.match(_RELATIONSHIP_ID_REGEX, value):
            raise ValueError(
                f"Invalid RelationshipId: '{value}'. "
                f"RelationshipId must match the pattern '{_RELATIONSHIP_ID_REGEX}' e.g:"
                f"'account__from__contact', 'contact__to__account'"
            )
        self._ensure_no_duplicates(new_value=value, new_name=self.name)

    @classmethod
    def _ensure_no_duplicates(cls, new_value: str, new_name: str) -> None:
        for existing_name, member in cls.__members__.items():
            if new_value.lower() == member.lower():
                raise ValueError(
                    f"case-insensitive duplicates found. value: '{new_value}'"
                    f"conflicting member names: '{new_name}' and '{existing_name}' "
                    f"in Enum: {cls.__name__}"
                )


class AccountRelationship(StdObjectRelationship):
    account__to__created_by_user = "account__to__created_by_user"
    account__to__updated_by_user = "account__to__updated_by_user"
    account__to__owner_user = "account__to__owner_user"
    account__to__archived_by_user = "account__to__archived_by_user"
    account__to__participant_users = "account__to__participant_users"
    account__from__contact_account_role = "account__from__contact_account_role"
    account__from__pipeline = "account__from__pipeline"
    domain_object_lists__from__account = "domain_object_lists__from__account"


class ContactRelationship(StdObjectRelationship):
    contact__to__primary_account = "contact__to__primary_account"
    contact__to__created_by_user = "contact__to__created_by_user"
    contact__to__updated_by_user = "contact__to__updated_by_user"
    contact__to__owner_user = "contact__to__owner_user"
    contact__to__archived_by_user = "contact__to__archived_by_user"
    contact__to__participant_users = "contact__to__participant_users"
    contact__from__contact_account_role = "contact__from__contact_account_role"
    domain_object_lists__from__contact = "domain_object_lists__from__contact"
    contact__from__contact_pipeline_role = "contact__from__contact_pipeline_role"


class ContactAccountRoleRelationship(StdObjectRelationship):
    contact_account_role__to__account = "contact_account_role__to__account"
    contact_account_role__to__contact = "contact_account_role__to__contact"


class ContactPipelineRoleRelationship(StdObjectRelationship):
    contact_pipeline_role__to__contact = "contact_pipeline_role__to__contact"
    contact_pipeline_role__to__pipeline = "contact_pipeline_role__to__pipeline"


class PipelineRelationship(StdObjectRelationship):
    pipeline__to__account = "pipeline__to__account"
    pipeline__to__primary_contact = "pipeline__to__primary_contact"
    pipeline__to__created_by_user = "pipeline__to__created_by_user"
    pipeline__to__updated_by_user = "pipeline__to__updated_by_user"
    pipeline__to__owner_user = "pipeline__to__owner_user"
    pipeline__to__archived_by_user = "pipeline__to__archived_by_user"
    pipeline__to__closed_by_user = "pipeline__to__closed_by_user"
    pipeline__to__participant_users = "pipeline__to__participant_users"
    pipeline__from__next_due_task = "pipeline__from__next_due_task"
    pipeline__from__contact_pipeline_role = "pipeline__from__contact_pipeline_role"


class ConversationRelationship(StdObjectRelationship):
    conversation__to__created_by_user = "conversation__to__created_by_user"
    conversation__to__participant_user = "conversation__to__participant_user"
    conversation__to__participant_contact = "conversation__to__participant_contact"
    conversation__to__attendee_contact = "conversation__to__attendee_contact"
    conversation__to__attendee_user = "conversation__to__attendee_user"
    conversation__to__account = "conversation__to__account"
    conversation__to__pipeline = "conversation__to__pipeline"


class MeetingRelationship(StdObjectRelationship):
    meeting__to__created_by_user = "meeting__to__created_by_user"
    meeting__to__organizer_user = "meeting__to__organizer_user"
    meeting__to__invitee_contact = "meeting__to__invitee_contact"
    meeting__to__invitee_user = "meeting__to__invitee_user"
    meeting__to__attendee_contact = "meeting__to__attendee_contact"
    meeting__to__attendee_user = "meeting__to__attendee_user"
    meeting__to__account = "meeting__to__account"
    meeting__to__pipeline = "meeting__to__pipeline"
    meeting__to__organizer_contact = "meeting__to__organizer_contact"


class VoiceCallRelationship(StdObjectRelationship):
    voice_call__to__caller_user = "voice_call__to__caller_user"
    voice_call__to__callee_user = "voice_call__to__callee_user"
    voice_call__to__caller_contact = "voice_call__to__caller_contact"
    voice_call__to__callee_contact = "voice_call__to__callee_contact"
    voice_call__to__account = "voice_call__to__account"
    voice_call__to__pipeline = "voice_call__to__pipeline"


class CalendarEventRelationship(StdObjectRelationship):
    calendar_event__to__organizer_user = "calendar_event__to__organizer_user"
    calendar_event__to__participant_user = "calendar_event__to__participant_user"
    calendar_event__to__contact = "calendar_event__to__contact"
    calendar_event__to__account = "calendar_event__to__account"
    calendar_event__to__pipeline = "calendar_event__to__pipeline"


class GiantTaskRelationship(StdObjectRelationship):
    pass


class TaskRelationship(StdObjectRelationship):
    task__to__created_by_user = "task__to__created_by_user"
    task__to__updated_by_user = "task__to__updated_by_user"
    task__to__owner_user = "task__to__owner_user"
    task__to__archived_by_user = "task__to__archived_by_user"
    task__to__participant_users = "task__to__participant_users"
    task__to__pipeline = "task__to__pipeline"
    task__to__account = "task__to__account"
    task__to__contacts = "task__to__contacts"
    task__to__comment = "task__to__comment"
    task__to__email_threads = "task__to__email_threads"
    task__to__meeting = "task__to__meeting"
    task__to__result_meetings = "task__to__result_meetings"
    task__to__citation = "task__to__citation"
    task__to__sequence = "task__to__sequence"
    task__to__sequence_enrollment = "task__to__sequence_enrollment"
    task__to__sequence_step = "task__to__sequence_step"
    task__to__sequence_step_execution = "task__to__sequence_step_execution"
    task__to__sequence_step_variant = "task__to__sequence_step_variant"


class NoteRelationship(StdObjectRelationship):
    note__to__owner_user = "note__to__owner_user"
    note__to__participant_user = "note__to__participant_user"
    note__to__created_by_user = "note__to__created_by_user"
    note__to__updated_by_user = "note__to__updated_by_user"
    note__to__deleted_by_user = "note__to__deleted_by_user"
    note__to__account = "note__to__account"
    note__to__contact = "note__to__contact"
    note__to__pipeline = "note__to__pipeline"
    note__to__task = "note__to__task"
    note__to__meeting = "note__to__meeting"


class CommentRelationship(StdObjectRelationship):
    comment__to__created_by_user = "comment__to__created_by_user"
    comment__to__task = "comment__to__task"
    comment__to__meeting = "comment__to__meeting"
    comment__to__parent_comment = "comment__to__parent_comment"


class UserGoalRelationship(StdObjectRelationship):
    user_goal__to__created_by_user = "user_goal__to__created_by_user"
    user_goal__to__updated_by_user = "user_goal__to__updated_by_user"
    user_goal__to__assigned_to_user = "user_goal__to__assigned_to_user"
    user_goal__to__archived_by_user = "user_goal__to__archived_by_user"


class GlobalThreadRelationship(StdObjectRelationship):
    global_message__to__participant_contact = "global_message__to__participant_contact"
    global_message__to__account = "global_message__to__account"
    global_message__to__pipeline = "global_message__to__pipeline"


class EventScheduleRelationship(StdObjectRelationship):
    pass


class EmailTemplateRelationship(StdObjectRelationship):
    email_template__to__created_by_user = "email_template__to__created_by_user"
    email_template__to__updated_by_user = "email_template__to__updated_by_user"
    email_template__to__attachment_details = "email_template__to__attachment_details"


class DomainObjectListRelationship(StdObjectRelationship):
    pass


class DomainObjectListItemRelationship(StdObjectRelationship):
    domain_object_list_item__to__account = "domain_object_list_item__to__account"
    domain_object_list_item__to__contact = "domain_object_list_item__to__contact"
    domain_object_list_item__to__pipeline = "domain_object_list_item__to__pipeline"


class EmailAccountPoolRelationship(StdObjectRelationship):
    email_account_pool__to__created_by_user = "email_account_pool__to__created_by_user"
    email_account_pool__to__updated_by_user = "email_account_pool__to__updated_by_user"
    email_account_pool__to__deleted_by_user = "email_account_pool__to__deleted_by_user"


class EmailAccountRelationship(StdObjectRelationship):
    email_account__to__created_by_user = "email_account__to__created_by_user"
    email_account__to__updated_by_user = "email_account__to__updated_by_user"
    email_account__to__deleted_by_user = "email_account__to__deleted_by_user"
    email_account__to__owner_user = "email_account__to__owner_user"
    email_account__to__sequence = "email_account__to__sequence"
    email_account__to__signature = "email_account__to__signature"


class OutboundDomainRelationship(StdObjectRelationship):
    outbound_domain__to__sequence = "outbound_domain__to__sequence"
    outbound_domain__to__created_by_user = "outbound_domain__to__created_by_user"


class CustomRecordRelationship(StdObjectRelationship):
    custom_record__to__created_by_user = "custom_record__to__created_by_user"
    custom_record__to__updated_by_user = "custom_record__to__updated_by_user"
    custom_record__to__deleted_by_user = "custom_record__to__deleted_by_user"


class SequenceRelationship(StdObjectRelationship):
    sequence__to__owner_user = "sequence__to__owner_user"
    sequence__to__created_by_user = "sequence__to__created_by_user"


class SequenceEnrollmentRelationship(StdObjectRelationship):
    sequence_enrollment__to__enrolled_by_user = (
        "sequence_enrollment__to__enrolled_by_user"
    )
    sequence_enrollment__to__contact = "sequence_enrollment__to__contact"
    sequence_enrollment__to__account = "sequence_enrollment__to__account"
    sequence_enrollment__to__sequence_step = "sequence_enrollment__to__sequence_step"
    sequence_enrollment__to__sequence_step_execution = (
        "sequence_enrollment__to__sequence_step_execution"
    )


class SequenceEnrollmentContactRelationship(StdObjectRelationship):
    sequence_enrollment_contact__to__contact = (
        "sequence_enrollment_contact__to__contact"
    )
    sequence_enrollment_contact__to__account = (
        "sequence_enrollment_contact__to__account"
    )


class SequenceEnrollmentRunRelationship(StdObjectRelationship):
    sequence_enrollment_run__to__user = "sequence_enrollment_run__to__user"


class SequenceStepExecutionRelationship(StdObjectRelationship):
    sequence_step_execution__to__global_message = (
        "sequence_step_execution__to__global_message"
    )
    sequence_step_execution__to__contact = "sequence_step_execution__to__contact"
    sequence_step_execution__to__account = "sequence_step_execution__to__account"
    sequence_step_execution__to__pipeline = "sequence_step_execution__to__pipeline"


class SequenceStepVariantRelationship(StdObjectRelationship):
    sequence_step_variant__to__contact = "sequence_step_variant__to__contact"
    sequence_step_variant__to__account = "sequence_step_variant__to__account"
    sequence_step_variant__to__pipeline = "sequence_step_variant__to__pipeline"


class ProductRelationship(StdObjectRelationship):
    product__to__created_by_user = "product__to__created_by_user"
    product__to__updated_by_user = "product__to__updated_by_user"
    product__to__deleted_by_user = "product__to__deleted_by_user"
    product__to__product_component = "product__to__product_component"


class ProspectingPersonRelationship(StdObjectRelationship):
    prospecting_person__to__contact = "prospecting_person__to__contact"
    prospecting_person__to__prospecting_company = (
        "prospecting_person__to__prospecting_company"
    )


class SignatureRelationship(StdObjectRelationship):
    signature__to__created_by_user = "signature__to__created_by_user"
    signature__to__updated_by_user = "signature__to__updated_by_user"
    signature__to__deleted_by_user = "signature__to__deleted_by_user"
    signature__to__email_account = "signature__to__email_account"


class ProspectingRunRelationship(StdObjectRelationship):
    prospecting_run__to__user = "prospecting_run__to__user"


class ProspectingRunResultRelationship(StdObjectRelationship):
    prospecting_run_result__to__contact = "prospecting_run_result__to__contact"
    prospecting_run_result__to__account = "prospecting_run_result__to__account"


class UserCalendarEventRelationship(StdObjectRelationship):
    user_calendar_event__to__meeting = "user_calendar_event__to__meeting"


class CustomerFilesRelationship(StdObjectRelationship):
    customer_files__to__created_by_user = "customer_files__to__created_by_user"
    customer_files__to__attached_by_user = "customer_files__to__attached_by_user"
    customer_files__to__account = "customer_files__to__account"
    customer_files__to__pipeline = "customer_files__to__pipeline"
    customer_files__to__contact = "customer_files__to__contact"
