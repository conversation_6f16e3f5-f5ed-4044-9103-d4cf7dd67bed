import asyncio
import time
from collections.abc import Callable
from enum import StrEnum, auto
from functools import wraps
from typing import (
    Any,
    Union,
)

from datadog import initialize
from datadog.dogstatsd.base import DogStatsd
from pydantic import BaseModel

from salestech_be.common.stats.noop_statsd import NoopStatsd
from salestech_be.settings import settings


class DDStatsOptions(BaseModel):
    statsd_host: str
    statsd_port: int


def init_dog_statsd() -> None:
    options = DDStatsOptions(
        statsd_host=settings.dd_agent_host,
        statsd_port=settings.dd_agent_statsd_port,
    )
    options_map = options.model_dump()
    initialize(**options_map)


class MetricTagServiceType(StrEnum):
    API = auto()
    WORKER = auto()
    SCHEDULER = auto()
    CUSTOM = auto()
    WORKFLOW = auto()
    LLM = auto()
    CLIENT = auto()
    RESEARCH = auto()

    def get_tag(self) -> str:
        return f"service:{self.name.lower()}"


class TimerDecoratorContextManager:
    def __init__(
        self,
        statsd_client: DogStatsd,
        metric_name: str,
        tags: list[str] | None = None,
    ) -> None:
        self.statsd_client: DogStatsd = statsd_client
        self.metric_name: str = metric_name
        self.tags: list[str] = tags if tags is not None else []

    def __call__(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self, func: Callable[..., Any] | None = None
    ) -> Union[Callable[..., Any], "TimerDecoratorContextManager"]:
        if func is None:
            return self
        if asyncio.iscoroutinefunction(func):
            return self._wrap_async(func)
        return self._wrap_sync(func)

    def _wrap_sync(self, func: Callable[..., Any]) -> Callable[..., Any]:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        @wraps(func)
        def wrapped(*args: Any, **kwargs: Any) -> Any:  # type: ignore[explicit-any] # TODO: fix-any-annotation
            with self:
                return func(*args, **kwargs)

        return wrapped

    def _wrap_async(self, func: Callable[..., Any]) -> Callable[..., Any]:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        @wraps(func)
        async def wrapped(*args: Any, **kwargs: Any) -> Any:  # type: ignore[explicit-any] # TODO: fix-any-annotation
            async with self:
                return await func(*args, **kwargs)

        return wrapped

    def __enter__(self) -> "TimerDecoratorContextManager":
        self.start_time: float = time.perf_counter()
        return self

    def __exit__(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self,
        exc_type: type | None,
        exc_val: Exception | None,
        exc_tb: Any | None,
    ) -> None:
        elapsed_time: float = (
            time.perf_counter() - self.start_time
        ) * 1000  # milliseconds
        self.statsd_client.timing(self.metric_name, elapsed_time, tags=self.tags)

    async def __aenter__(self) -> "TimerDecoratorContextManager":
        self.start_time = time.perf_counter()
        return self

    async def __aexit__(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self,
        exc_type: type | None,
        exc_val: Exception | None,
        exc_tb: Any | None,
    ) -> None:
        elapsed_time: float = (
            time.perf_counter() - self.start_time
        ) * 1000  # milliseconds
        self.statsd_client.timing(self.metric_name, elapsed_time, tags=self.tags)


class ReeMetrics:
    def __init__(
        self,
        service_type: MetricTagServiceType,
        custom_statsd: DogStatsd | None = None,
        custom_prefix: str | None = None,
    ) -> None:
        """
        Initialize a DogStatsD client. Override if needed
        """
        self.singleton_statsd = (
            custom_statsd
            if custom_statsd
            else DogStatsd(
                host=settings.dd_agent_host, port=settings.dd_agent_statsd_port
            )
            if settings.datadog_on
            else NoopStatsd()
        )
        self.prefix = custom_prefix if custom_prefix else "salestechbe"
        self.default_tags = [f"env:{settings.environment}", service_type.get_tag()]

    def _merge_prefix(self, metric_name: str) -> str:
        return ".".join([self.prefix, metric_name])

    def _add_default_tags(self, tags: list[str] | None) -> list[str] | None:
        if not tags:
            return self.default_tags
        return tags + self.default_tags

    def increment(
        self,
        metric_name: str,
        tags: list[str] | None = None,
        value: int = 1,
    ) -> None:
        """
        Increment a metric, optionally adding a service tag.
        """
        tags = self._add_default_tags(tags)
        metric_name = self._merge_prefix(metric_name)
        self.singleton_statsd.increment(metric_name, value, tags=tags)

    def decrement(
        self,
        metric_name: str,
        tags: list[str] | None = None,
        value: int = 1,
    ) -> None:
        """
        Decrement a metric, optionally adding a service tag.
        """
        tags = self._add_default_tags(tags)
        metric_name = self._merge_prefix(metric_name)
        self.singleton_statsd.decrement(metric_name, value, tags=tags)

    def gauge(
        self,
        metric_name: str,
        value: float,
        tags: list[str] | None = None,
    ) -> None:
        """
        Set a gauge metric, optionally adding a service tag.
        """
        tags = self._add_default_tags(tags)
        metric_name = self._merge_prefix(metric_name)
        self.singleton_statsd.gauge(metric_name, value, tags=tags)

    def timing(
        self,
        metric_name: str,
        value: float,
        tags: list[str] | None = None,
    ) -> None:
        """
        Record a timing metric, optionally adding a service tag.
        """
        tags = self._add_default_tags(tags)
        metric_name = self._merge_prefix(metric_name)
        self.singleton_statsd.timing(metric_name, value, tags=tags)

    def timer(
        self,
        metric_name: str,
        tags: list[str] | None = None,
    ) -> TimerDecoratorContextManager:
        """
        Create and return a TimerDecoratorContextManager for the given metric.
        """
        full_metric_name = self._merge_prefix(metric_name)
        combined_tags = self._add_default_tags(tags)
        return TimerDecoratorContextManager(
            self.singleton_statsd, full_metric_name, combined_tags
        )

    def histogram(
        self,
        metric_name: str,
        value: float,
        tags: list[str] | None = None,
    ) -> None:
        """
        Record a histogram metric, optionally adding a service tag.
        """
        tags = self._add_default_tags(tags)
        metric_name = self._merge_prefix(metric_name)
        self.singleton_statsd.histogram(metric_name, value, tags=tags)


api_metric = ReeMetrics(MetricTagServiceType.API)
worker_metric = ReeMetrics(MetricTagServiceType.WORKER)
scheduler_metric = ReeMetrics(MetricTagServiceType.SCHEDULER)
custom_metric = ReeMetrics(MetricTagServiceType.CUSTOM)
workflow_metric = ReeMetrics(MetricTagServiceType.WORKFLOW)
llm_metric = ReeMetrics(MetricTagServiceType.LLM)
client_metric = ReeMetrics(MetricTagServiceType.CLIENT)
