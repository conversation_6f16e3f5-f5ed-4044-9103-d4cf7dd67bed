from __future__ import annotations

from typing import Any
from uuid import UUID

from pydantic import BaseModel


class IdBaseModel(BaseModel):
    id: UUID

    def __eq__(self, other: Any) -> bool:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        return self is other or (hasattr(other, "id") and self.id == other.id)

    def __hash__(self) -> int:
        return hash(self.id)


class FieldStatus(BaseModel):
    missing: list[str]
    present: list[str]

    def to_tags(self) -> list[str]:
        return [f"missing:{field}" for field in self.missing] + [
            f"present:{field}" for field in self.present
        ]


class BaseModelWithFieldStatus(BaseModel):
    def get_field_status(self) -> FieldStatus:
        """
        Returns a dictionary with two keys:
        - 'missing': list of field names that are None or empty
        - 'present': list of field names that have values
        """
        missing_fields = []
        present_fields = []

        for field_name, field_value in self.model_dump().items():
            # Skip internal fields
            if field_name.startswith("_"):
                continue

            # Check if field is empty (None, empty string, empty list, etc.)
            is_empty = field_value is None or (
                isinstance(field_value, str | list | dict) and not field_value
            )

            if is_empty:
                missing_fields.append(field_name)
            else:
                present_fields.append(field_name)

        return FieldStatus(missing=missing_fields, present=present_fields)
