from enum import StrEnum

from pydantic import BaseModel


class CRMSyncStatus(StrEnum):
    DISABLED = "DISABLED"
    SYNCING_DATA = "SYNCING_DATA"
    PENDING_NEXT_SYNC = "PENDING_NEXT_SYNC"
    PENDING_CONFIGURATION = "PENDING_CONFIGURATION"
    SYNCING_METADATA = "SYNCING_METADATA"
    PENDING_DATA_SYNC = "PENDING_DATA_SYNC"
    PAUSED = "PAUSED"
    ERROR = "ERROR"


class CRMConnectionStatus(StrEnum):
    CONNECTED = "CONNECTED"
    BROKEN = "BROKEN"


class CRMSyncDirection(StrEnum):
    INBOUND = "INBOUND"
    OUTBOUND = "OUTBOUND"
    TWO_WAYS = "TWO_WAYS"
    UNDEFINED = "UNDEFINED"


class CRMSyncDataErrorInfo(BaseModel):
    error_message: str
    error_code: str
