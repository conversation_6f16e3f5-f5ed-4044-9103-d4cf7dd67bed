from typing import Annotated, Any

from email_validator.exceptions_types import EmailSyntaxError
from email_validator.syntax import split_email
from pydantic import BeforeValidator, EmailStr
from pydantic.annotated_handlers import GetCoreSchemaHandler
from pydantic_core import core_schema


def strip_and_lower(v: object | None) -> str | None:
    if not v:
        return None
    after_trim = str(v).strip().lower()
    if after_trim == "":
        return None
    return after_trim


EmailStrLower = Annotated[
    EmailStr,
    BeforeValidator(strip_and_lower),
]


class SystemEmailStr:
    """Similar to EmailStr (from Pydantic), but with a very basic email address check.

    This is to support some system triggered email address (like from google calendar,
    etc). Their email address may fail the `email-validator` validation check. For
    example, the length of the local part may exceed the limit of 64 characters.
    """

    @classmethod
    def __get_pydantic_core_schema__(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        cls,
        _source: type[Any],
        _handler: GetCoreSchemaHandler,
    ) -> core_schema.CoreSchema:
        return core_schema.no_info_after_validator_function(
            cls._validate, core_schema.str_schema()
        )

    @classmethod
    def _validate(cls, input_value: str, /) -> str:
        local_part, domain_part, _ = split_email(input_value)  # type: ignore

        if len(local_part) == 0:
            raise EmailSyntaxError("There must be something before the @-sign.")
        if len(domain_part) == 0:
            raise EmailSyntaxError("There must be something after the @-sign.")

        return input_value
