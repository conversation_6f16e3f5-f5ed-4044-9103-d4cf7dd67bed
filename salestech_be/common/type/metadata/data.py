from collections.abc import Sequence
from typing import Generic, TypeVar

from pydantic import BaseModel

from salestech_be.common.type.metadata.common import ObjectIdentifier
from salestech_be.common.type.metadata.schema import Relationship

CRMRecordT = TypeVar("CRMRecordT", bound=BaseModel)
CRMRelatedRecordT_co = TypeVar("CRMRelatedRecordT_co", bound=BaseModel, covariant=True)


class RelatedRecord(BaseModel, Generic[CRMRelatedRecordT_co]):
    relationship: Relationship
    object_identifier: ObjectIdentifier
    records: Sequence[CRMRelatedRecordT_co]


class CRMRecord(BaseModel, Generic[CRMRecordT]):
    object_identifier: ObjectIdentifier
    record: CRMRecordT
    related_records: Sequence[RelatedRecord[BaseModel]]
