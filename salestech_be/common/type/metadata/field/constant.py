from enum import IntEnum


class LengthLimits(IntEnum):
    MAX_NUMERIC_TOTAL_PRECISION = 18
    MAX_SHORT_TEXT_LENGTH = 255
    MAX_URL_LENGTH = 255
    MAX_LONG_TEXT_LENGTH = 131072
    MAX_LONG_TEXT_VISIBLE_LINES = 50
    MAX_EMAIL_LENGTH = 80
    MAX_ENUM_VALUE_DISPLAY_NAME_LENGTH = 255
    MAX_NUMBER_OF_SINGLE_SELECT_ENUM_VALUES = 1000
    MAX_NUMBER_OF_MULTI_SELECT_ENUM_VALUES = 500
    MAX_NUMBER_OF_SELECTED_MULTI_SELECT_ENUM_VALUES = 100
    MAX_FIELD_DISPLAY_NAME_LENGTH = 40
    MAX_PHONE_NUMBER_LENGTH = 40
