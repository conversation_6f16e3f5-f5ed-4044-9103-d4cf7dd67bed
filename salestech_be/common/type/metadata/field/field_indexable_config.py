from typing import Any, Generic, TypeVar

from pydantic import BaseModel, ConfigDict


class BaseIndexableConfig(BaseModel):
    model_config = ConfigDict(frozen=True)
    is_indexed: bool = False


class CaseAware(BaseModel):
    is_case_sensitive: bool = True


class NonUniqueIndexableConfig(BaseIndexableConfig):
    pass


class CaseAwareNonUniqueIndexableConfig(NonUniqueIndexableConfig, CaseAware):
    pass


class UniqueIndexableConfig(BaseIndexableConfig):
    is_unique: bool = False

    def model_post_init(self, __context: Any) -> None:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        super().model_post_init(__context)
        if self.is_unique and not self.is_indexed:
            raise ValueError(
                "is_indexed must be True if is_unique is True, "
                f"but found is_indexed={self.is_indexed} and is_unique={self.is_unique}",
            )


class CaseAwareUniqueIndexableConfig(UniqueIndexableConfig, CaseAware):
    pass


IndexableConfigT = TypeVar(
    "IndexableConfigT",
    NonUniqueIndexableConfig,
    CaseAwareNonUniqueIndexableConfig,
    UniqueIndexableConfig,
    CaseAwareUniqueIndexableConfig,
)


class IndexableConfigMixin(BaseModel, Generic[IndexableConfigT]):
    model_config = ConfigDict(frozen=True)
    index_config: IndexableConfigT | None = None

    @property
    def is_indexed(self) -> bool:
        if not self.index_config:
            return False
        return self.index_config.is_indexed or self.is_unique

    @property
    def is_unique(self) -> bool:
        if not self.index_config:
            return False
        return (
            isinstance(self.index_config, UniqueIndexableConfig)
            and self.index_config.is_unique
        )

    @property
    def is_index_case_sensitive(self) -> bool:
        if not self.index_config:
            return False
        return (
            isinstance(self.index_config, CaseAware)
            and self.index_config.is_case_sensitive
        )


IndexableConfigMixinT = TypeVar(  # type: ignore[explicit-any] # TODO: fix-any-annotation
    "IndexableConfigMixinT", bound=IndexableConfigMixin[Any]
)
