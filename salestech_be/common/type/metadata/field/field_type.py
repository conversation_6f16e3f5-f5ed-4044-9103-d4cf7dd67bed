import typing
from enum import auto
from typing import Annotated, Any, Literal, TypeVar

from frozendict import frozendict
from pydantic import BaseModel, ConfigDict, Field, computed_field
from typing_extensions import TypeIs

from salestech_be.common.type.metadata.field.constant import LengthLimits
from salestech_be.common.type.metadata.value import (
    MatchOperatorName,
    NativeValueType,
    match_operator_names_by_native_type,
    sortable_native_field_types,
)
from salestech_be.util.enum_util import NameValueStrEnum


class FieldDisplayMode(NameValueStrEnum):
    default = auto()
    due_date = auto()


class FieldType(NameValueStrEnum):
    LIST = "LIST"
    DICT = "DICT"
    UUID = "UUID"
    NUMERIC = "NUMERIC"  # SFDC, number
    CURRENCY = "CURRENCY"  # SFDC, currency with rounding
    PERCENT = "PERCENT"  # SFDC, percent
    TEXT = "TEXT"  # 255
    TEXT_AREA = "TEXT_AREA"  # SFDC, textarea, # 255
    LONG_TEXT_AREA = "LONG_TEXT_AREA"  # SFDC, long textarea # 131,072
    RICH_TEXT_AREA = "RICH_TEXT_AREA"  # SFDC, rich textarea # 131,072
    TIMESTAMP = "TIMESTAMP"
    LOCAL_TIME_OF_DAY = "LOCAL_TIME_OF_DAY"  # SFDC, time
    TIME_OF_DAY = "TIME_OF_DAY"  # SFDC, time
    LOCAL_DATE = "LOCAL_DATE"  # SFDC, date | hubspot, date (date picker)
    # ADDRESS = "ADDRESS" don't actually see it from SFDC
    BOOLEAN_CHECKBOX = (
        "BOOLEAN_CHECKBOX"  # SFDC, boolean checkbox | hubspot, booleancheckbox
    )
    # enumerations:
    # replace with different option, replace with blank, deactivate
    # for now, we only gonna support local enumerations,
    # global enumerations similarly to SFDC are not supported unless super necessary.
    SINGLE_SELECT = "SINGLE_SELECT"  # SFDC, picklist, single select | hubspot select
    MULTI_SELECT = "MULTI_SELECT"  # SFDC, picklist, multi select | hubspot checkbox
    EMAIL = "EMAIL"  # SFDC, email
    PHONE_NUMBER = "PHONE_NUMBER"  # SFDC, phone
    URL = "URL"  # SFDC, url
    GEO_LOCATION = "GEO_LOCATION"  # SFDC, geolocation
    NESTED_OBJECT = "NESTED_OBJECT"
    DEFAULT_ENUM = "DEFAULT_ENUM"  # static enum, not from SFDC

    # HIERARCHICAL_RELATIONSHIP = "HIERARCHICAL_RELATIONSHIP"  # SFDC, hierarchical
    # INDIRECT_LOOKUP_RELATIONSHIP = "INDIRECT_LOOKUP_RELATIONSHIP"  # SFDC, indirect lookup relationship
    # LOOKUP_RELATIONSHIP = "LOOKUP_RELATIONSHIP"  # SFDC, lookup relationship
    # MASTER_DETAIL_RELATIONSHIP = "MASTER_DETAIL_RELATIONSHIP"  # SFDC, master detail relationship
    # ROLLUP_SUMMARY = "ROLLUP_SUMMARY"  # SFDC, rollup summary
    # ENCRYPTED_TEXT = "ENCRYPTED_TEXT"  # SFDC, encrypted text
    # AUTO_NUMBER = "AUTO_NUMBER" SFDC, unique number generator needed
    # FILE = "FILE"  # hubspot, actually upload a file and store as accessible file url
    # EXTERNAL_LOOKUP_RELASHIONSHIP = "EXTERNAL_LOOKUP_RELASHIONSHIP" # SFDC, external lookup relationship
    # FORMULA = "FORMULA"  # SFDC, formula

    @classmethod
    def custom_field_types(cls) -> set["FieldType"]:
        return {ft for ft in cls if is_custom_field_type(ft)}


CustomFieldType = Literal[
    FieldType.UUID,
    FieldType.NUMERIC,
    FieldType.CURRENCY,
    FieldType.PERCENT,
    FieldType.TEXT,
    FieldType.TEXT_AREA,
    FieldType.LONG_TEXT_AREA,
    FieldType.RICH_TEXT_AREA,
    FieldType.TIMESTAMP,
    FieldType.LOCAL_TIME_OF_DAY,
    FieldType.TIME_OF_DAY,
    FieldType.LOCAL_DATE,
    FieldType.BOOLEAN_CHECKBOX,
    FieldType.SINGLE_SELECT,
    FieldType.MULTI_SELECT,
    FieldType.EMAIL,
    FieldType.PHONE_NUMBER,
    FieldType.URL,
    FieldType.GEO_LOCATION,
]

_custom_field_types: set[FieldType] = {
    ft for ft in typing.get_args(CustomFieldType) if isinstance(ft, FieldType)
}


def is_custom_field_type(field_type: FieldType) -> TypeIs[CustomFieldType]:
    return field_type in _custom_field_types


StandardFieldType = Literal[
    FieldType.LIST,
    FieldType.DICT,
    FieldType.UUID,
    FieldType.NUMERIC,
    FieldType.CURRENCY,
    FieldType.PERCENT,
    FieldType.TEXT,
    FieldType.TEXT_AREA,
    FieldType.LONG_TEXT_AREA,
    FieldType.RICH_TEXT_AREA,
    FieldType.TIMESTAMP,
    FieldType.LOCAL_TIME_OF_DAY,
    FieldType.TIME_OF_DAY,
    FieldType.LOCAL_DATE,
    FieldType.BOOLEAN_CHECKBOX,
    FieldType.EMAIL,
    FieldType.PHONE_NUMBER,
    FieldType.URL,
    FieldType.NESTED_OBJECT,
    FieldType.DEFAULT_ENUM,
]

_standard_field_types: set[FieldType] = {
    ft for ft in typing.get_args(StandardFieldType) if isinstance(ft, FieldType)
}


def is_standard_field_type(field_type: FieldType) -> TypeIs[StandardFieldType]:
    return field_type in _standard_field_types


class CommonConstraint(BaseModel):
    model_config = ConfigDict(frozen=True)
    max_field_display_name_length: Literal[
        LengthLimits.MAX_FIELD_DISPLAY_NAME_LENGTH
    ] = LengthLimits.MAX_FIELD_DISPLAY_NAME_LENGTH


ConstraintT = TypeVar("ConstraintT", bound=CommonConstraint)


class UUIDConstraint(CommonConstraint):
    constrained_field_type: Literal[FieldType.UUID] = FieldType.UUID


class NumberConstraint(CommonConstraint):
    max_total_precision: Literal[LengthLimits.MAX_NUMERIC_TOTAL_PRECISION] = (
        LengthLimits.MAX_NUMERIC_TOTAL_PRECISION
    )
    min_total_precision: Literal[1] = 1
    max_decimal_precision: Literal[LengthLimits.MAX_NUMERIC_TOTAL_PRECISION] = (
        LengthLimits.MAX_NUMERIC_TOTAL_PRECISION
    )
    min_decimal_precision: Literal[0] = 0


class NumericConstraint(NumberConstraint):
    constrained_field_type: Literal[FieldType.NUMERIC] = FieldType.NUMERIC


class PercentConstraint(NumberConstraint):
    constrained_field_type: Literal[FieldType.PERCENT] = FieldType.PERCENT


class CurrencyConstraint(NumberConstraint):
    constrained_field_type: Literal[FieldType.CURRENCY] = FieldType.CURRENCY


class TextConstraint(CommonConstraint):
    constrained_field_type: Literal[FieldType.TEXT] = FieldType.TEXT
    max_str_length: Literal[LengthLimits.MAX_SHORT_TEXT_LENGTH] = (
        LengthLimits.MAX_SHORT_TEXT_LENGTH
    )


class TextAreaConstraint(CommonConstraint):
    constrained_field_type: Literal[FieldType.TEXT_AREA] = FieldType.TEXT_AREA
    max_str_length: Literal[LengthLimits.MAX_SHORT_TEXT_LENGTH] = (
        LengthLimits.MAX_SHORT_TEXT_LENGTH
    )


class LongTextAreaConstraint(CommonConstraint):
    constrained_field_type: Literal[FieldType.LONG_TEXT_AREA] = FieldType.LONG_TEXT_AREA
    max_str_length: Literal[LengthLimits.MAX_LONG_TEXT_LENGTH] = (
        LengthLimits.MAX_LONG_TEXT_LENGTH
    )
    max_visible_lines: Literal[LengthLimits.MAX_LONG_TEXT_VISIBLE_LINES] = (
        LengthLimits.MAX_LONG_TEXT_VISIBLE_LINES
    )


class RichTextAreaConstraint(CommonConstraint):
    constrained_field_type: Literal[FieldType.RICH_TEXT_AREA] = FieldType.RICH_TEXT_AREA
    max_str_length: Literal[LengthLimits.MAX_LONG_TEXT_LENGTH] = (
        LengthLimits.MAX_LONG_TEXT_LENGTH
    )
    max_visible_lines: Literal[LengthLimits.MAX_LONG_TEXT_VISIBLE_LINES] = (
        LengthLimits.MAX_LONG_TEXT_VISIBLE_LINES
    )


class TemporalConstraint(CommonConstraint):
    has_timezone: bool


class NoZoneTemporalConstraint(TemporalConstraint):
    has_timezone: Literal[False] = False


class ZonedTemporalConstraint(TemporalConstraint):
    has_timezone: Literal[True] = True
    allowed_timezone: set[Literal["UTC"]] = {"UTC"}


class TimestampConstraint(ZonedTemporalConstraint):
    constrained_field_type: Literal[FieldType.TIMESTAMP] = FieldType.TIMESTAMP


class TimeOfDayConstraint(ZonedTemporalConstraint):
    constrained_field_type: Literal[FieldType.TIME_OF_DAY] = FieldType.TIME_OF_DAY


class LocalDateConstraint(NoZoneTemporalConstraint):
    constrained_field_type: Literal[FieldType.LOCAL_DATE] = FieldType.LOCAL_DATE


class LocalTimeOfDayConstraint(NoZoneTemporalConstraint):
    constrained_field_type: Literal[FieldType.LOCAL_TIME_OF_DAY] = (
        FieldType.LOCAL_TIME_OF_DAY
    )


class UrlConstraint(CommonConstraint):
    constrained_field_type: Literal[FieldType.URL] = FieldType.URL
    max_url_length: Literal[LengthLimits.MAX_URL_LENGTH] = LengthLimits.MAX_URL_LENGTH


class EmailConstraint(CommonConstraint):
    constrained_field_type: Literal[FieldType.EMAIL] = FieldType.EMAIL
    max_email_length: Literal[LengthLimits.MAX_EMAIL_LENGTH] = (
        LengthLimits.MAX_EMAIL_LENGTH
    )


class EnumDefinitionConstraint(CommonConstraint):
    max_enum_value_length: Literal[LengthLimits.MAX_SHORT_TEXT_LENGTH] = (
        LengthLimits.MAX_SHORT_TEXT_LENGTH
    )
    max_number_of_enum_values: int


class SingleSelectConstraint(EnumDefinitionConstraint):
    constrained_field_type: Literal[FieldType.SINGLE_SELECT] = FieldType.SINGLE_SELECT
    max_number_of_enum_values: Literal[
        LengthLimits.MAX_NUMBER_OF_SINGLE_SELECT_ENUM_VALUES
    ] = LengthLimits.MAX_NUMBER_OF_SINGLE_SELECT_ENUM_VALUES


class MultiSelectConstraint(EnumDefinitionConstraint):
    constrained_field_type: Literal[FieldType.MULTI_SELECT] = FieldType.MULTI_SELECT
    max_number_of_enum_values: Literal[
        LengthLimits.MAX_NUMBER_OF_MULTI_SELECT_ENUM_VALUES
    ] = LengthLimits.MAX_NUMBER_OF_MULTI_SELECT_ENUM_VALUES
    max_number_of_selected_enum_values: Literal[
        LengthLimits.MAX_NUMBER_OF_SELECTED_MULTI_SELECT_ENUM_VALUES
    ] = LengthLimits.MAX_NUMBER_OF_SELECTED_MULTI_SELECT_ENUM_VALUES


class BooleanCheckboxConstraint(CommonConstraint):
    constrained_field_type: Literal[FieldType.BOOLEAN_CHECKBOX] = (
        FieldType.BOOLEAN_CHECKBOX
    )


class PhoneConstraint(CommonConstraint):
    constrained_field_type: Literal[FieldType.PHONE_NUMBER] = FieldType.PHONE_NUMBER
    max_phone_number_length: Literal[LengthLimits.MAX_PHONE_NUMBER_LENGTH] = (
        LengthLimits.MAX_PHONE_NUMBER_LENGTH
    )
    standard_format: Literal["E.164"] = "E.164"


class GeoLocationConstraint(CommonConstraint):
    constrained_field_type: Literal[FieldType.GEO_LOCATION] = FieldType.GEO_LOCATION


class NestedObjectConstraint(CommonConstraint):
    constrained_field_type: Literal[FieldType.NESTED_OBJECT] = FieldType.NESTED_OBJECT


class DefaultEnumConstraint(CommonConstraint):
    constrained_field_type: Literal[FieldType.DEFAULT_ENUM] = FieldType.DEFAULT_ENUM


class ListConstraint(CommonConstraint):
    constrained_field_type: Literal[FieldType.LIST] = FieldType.LIST


class DictConstraint(CommonConstraint):
    constrained_field_type: Literal[FieldType.DICT] = FieldType.DICT


FieldConstraint = Annotated[
    UUIDConstraint
    | NumericConstraint
    | PercentConstraint
    | CurrencyConstraint
    | TextConstraint
    | TextAreaConstraint
    | LongTextAreaConstraint
    | RichTextAreaConstraint
    | TimestampConstraint
    | TimeOfDayConstraint
    | LocalDateConstraint
    | LocalTimeOfDayConstraint
    | UrlConstraint
    | EmailConstraint
    | SingleSelectConstraint
    | MultiSelectConstraint
    | BooleanCheckboxConstraint
    | PhoneConstraint
    | GeoLocationConstraint
    | NestedObjectConstraint
    | DefaultEnumConstraint
    | ListConstraint
    | DictConstraint,
    Field(discriminator="constrained_field_type"),
]


class FieldTypeDescription(BaseModel):
    model_config = ConfigDict(frozen=True)
    field_type: FieldType
    native_value_type: NativeValueType | None = None
    description: str
    constraint: FieldConstraint
    indexable: bool = False
    lossless_convertable_types: set[FieldType] = Field(
        default_factory=set, exclude=True
    )

    @computed_field  # type: ignore[prop-decorator]
    @property
    def is_sortable(self) -> bool:
        return bool(self.native_value_type) and (
            self.native_value_type in sortable_native_field_types
        )

    @computed_field  # type: ignore[prop-decorator]
    @property
    def is_filterable(self) -> bool:
        return (
            self.supported_filter_match_operators is not None
            and len(self.supported_filter_match_operators) > 0
        )

    @computed_field  # type: ignore[prop-decorator]
    @property
    def supported_filter_match_operators(self) -> tuple[MatchOperatorName, ...] | None:
        return (
            match_operator_names_by_native_type.get(self.native_value_type)
            if self.native_value_type
            else None
        )

    def model_post_init(self, __context: Any) -> None:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        if self.field_type != self.constraint.constrained_field_type:
            raise ValueError(
                f"field_type {self.field_type} does not match "
                f"constrained_field_type {self.constraint.constrained_field_type}"
            )

    def cast_constraint(self, constraint_type: type[ConstraintT]) -> ConstraintT:
        if isinstance(self.constraint, constraint_type):
            return self.constraint
        raise ValueError(
            f"constraint {self.constraint} is not an instance of {constraint_type}"
        )


field_descriptions: list[FieldTypeDescription] = [
    ##################
    # Number types
    ##################
    FieldTypeDescription(
        field_type=FieldType.NUMERIC,
        native_value_type=NativeValueType.DECIMAL,
        description="Represent a real number. Precision is up to "
        "18 digits including decimal point.",
        lossless_convertable_types={
            FieldType.TEXT,
            FieldType.TEXT_AREA,
            FieldType.LONG_TEXT_AREA,
            FieldType.PERCENT,
        },
        constraint=NumericConstraint(),
        indexable=True,
    ),
    FieldTypeDescription(
        field_type=FieldType.PERCENT,
        native_value_type=NativeValueType.DECIMAL,
        description="Represent a percentage. Precision is up to 18 digits. For example:"
        " user input 0.5 will be displayed as 0.5% or input 10 will"
        " be displayed as 10%.",
        constraint=PercentConstraint(),
        indexable=True,
    ),
    FieldTypeDescription(
        field_type=FieldType.CURRENCY,
        native_value_type=NativeValueType.DECIMAL,
        description="Represent a currency. Precision is up to 18 digits "
        "including decimal point. Displayed as currency format. For example: 1000.50"
        "will be displayed as $1,000.50.",
        lossless_convertable_types={FieldType.NUMERIC},
        constraint=CurrencyConstraint(),
        indexable=True,
    ),
    ##################
    # Text types
    ##################
    FieldTypeDescription(
        field_type=FieldType.TEXT,
        native_value_type=NativeValueType.STRING,
        description="Short text field. Maximum length is 255 characters.",
        lossless_convertable_types={
            FieldType.TEXT_AREA,
            FieldType.LONG_TEXT_AREA,
        },
        constraint=TextConstraint(),
        indexable=True,
    ),
    FieldTypeDescription(
        field_type=FieldType.TEXT_AREA,
        native_value_type=NativeValueType.STRING,
        description="Short text area field that honors line breaks when displayed. "
        "Maximum length is 255 characters.",
        lossless_convertable_types={
            FieldType.TEXT,
            FieldType.LONG_TEXT_AREA,
        },
        constraint=TextAreaConstraint(),
        indexable=True,
    ),
    FieldTypeDescription(
        field_type=FieldType.LONG_TEXT_AREA,
        native_value_type=NativeValueType.STRING,
        description="Long Text Area field, maximum length is 131,072 characters "
        "and honor line breaks when displayed.",
        constraint=LongTextAreaConstraint(),
    ),
    FieldTypeDescription(
        field_type=FieldType.RICH_TEXT_AREA,
        native_value_type=NativeValueType.STRING,
        description="Rich Text Area field. Supports rich text editing, maximum length "
        "is 131,072 characters including formatting html tags.",
        constraint=RichTextAreaConstraint(),
    ),
    ##################
    # Temporal types
    ##################
    FieldTypeDescription(
        field_type=FieldType.TIMESTAMP,
        native_value_type=NativeValueType.ZONED_TIMESTAMP,
        description="Timestamp field, always require timezone.",
        constraint=TimestampConstraint(),
        indexable=True,
    ),
    FieldTypeDescription(
        field_type=FieldType.LOCAL_DATE,
        native_value_type=NativeValueType.LOCAL_DATE,
        description="A local date without timezone. For example: 2022-01-01.",
        constraint=LocalDateConstraint(),
        indexable=True,
    ),
    FieldTypeDescription(
        field_type=FieldType.LOCAL_TIME_OF_DAY,
        native_value_type=NativeValueType.LOCAL_TIME_OF_DAY,
        description="A local time of day without timezone. For example: 12:00:00.",
        constraint=LocalTimeOfDayConstraint(),
        indexable=True,
    ),
    FieldTypeDescription(
        field_type=FieldType.TIME_OF_DAY,
        native_value_type=NativeValueType.ZONED_TIME_OF_DAY,
        description="A time of day with timezone specified. For example: 12:00:00 UTC.",
        constraint=TimeOfDayConstraint(),
        indexable=True,
    ),
    ##################
    # Enum types
    ##################
    FieldTypeDescription(
        field_type=FieldType.BOOLEAN_CHECKBOX,
        native_value_type=NativeValueType.BOOLEAN,
        description="Boolean Checkbox field, represent a boolean value to indicate "
        "whether the 'field display name' is truthy",
        constraint=BooleanCheckboxConstraint(),
        indexable=True,
    ),
    FieldTypeDescription(
        field_type=FieldType.SINGLE_SELECT,
        native_value_type=NativeValueType.UUID,
        description="Single Select field, represent a single selection from a list of "
        "predefined values",
        constraint=SingleSelectConstraint(),
        indexable=True,
    ),
    FieldTypeDescription(
        field_type=FieldType.MULTI_SELECT,
        native_value_type=NativeValueType.LIST,
        description="Multi Select field, represent multiple selections from a list of "
        "predefined values",
        constraint=MultiSelectConstraint(),
    ),
    FieldTypeDescription(
        field_type=FieldType.DEFAULT_ENUM,
        native_value_type=NativeValueType.STRING,
        description="Static Enum field, represent default enumerations defined by Reevo",
        constraint=DefaultEnumConstraint(),
    ),
    ##################
    # Other types
    ##################
    FieldTypeDescription(
        field_type=FieldType.EMAIL,
        native_value_type=NativeValueType.STRING,
        description="Represent an email address. Maximum length is 80 characters. ",
        constraint=EmailConstraint(),
        indexable=True,
    ),
    FieldTypeDescription(
        field_type=FieldType.PHONE_NUMBER,
        native_value_type=NativeValueType.STRING,
        description="Represent a phone number in standard E.164 format."
        " Maximum length is 40 characters.",
        constraint=PhoneConstraint(),
        indexable=True,
    ),
    FieldTypeDescription(
        field_type=FieldType.URL,
        native_value_type=NativeValueType.STRING,
        description="Represent a validate URL. Maximum length is 255 characters.",
        constraint=UrlConstraint(),
        indexable=True,
    ),
    FieldTypeDescription(
        field_type=FieldType.GEO_LOCATION,
        native_value_type=NativeValueType.OBJECT,
        description="Represent a geolocation. With Latitude and longitude "
        "represented as decimal numbers",
        constraint=GeoLocationConstraint(),
    ),
    FieldTypeDescription(
        field_type=FieldType.UUID,
        native_value_type=NativeValueType.UUID,
        description="Represent a UUID. Universally Unique Identifier.",
        constraint=UUIDConstraint(),
    ),
    FieldTypeDescription(
        field_type=FieldType.NESTED_OBJECT,
        native_value_type=NativeValueType.OBJECT,
        description="Represent a nested object. A field that contains a nested object.",
        constraint=NestedObjectConstraint(),
    ),
    FieldTypeDescription(
        field_type=FieldType.LIST,
        native_value_type=NativeValueType.LIST,
        description="Represent a list of values. A field that contains "
        "a list of values.",
        constraint=ListConstraint(),
    ),
    FieldTypeDescription(
        field_type=FieldType.DICT,
        native_value_type=NativeValueType.DICT,
        description="Represent a dictionary of key-value pairs. A field that contains "
        "a dictionary of key-value pairs.",
        constraint=DictConstraint(),
    ),
]
field_descriptions_by_type: frozendict[FieldType, FieldTypeDescription] = frozendict(
    {field_desc.field_type: field_desc for field_desc in field_descriptions}
)


def field_type_descriptions() -> frozendict[FieldType, FieldTypeDescription]:
    return field_descriptions_by_type


def field_type_description(field_type: FieldType) -> FieldTypeDescription:
    return field_descriptions_by_type[field_type]


common_constraint = CommonConstraint()

numeric_constraint = field_type_description(
    field_type=FieldType.NUMERIC
).cast_constraint(NumericConstraint)

percent_constraint = field_type_description(
    field_type=FieldType.PERCENT
).cast_constraint(PercentConstraint)

currency_constraint = field_type_description(
    field_type=FieldType.CURRENCY
).cast_constraint(CurrencyConstraint)

text_constraint = field_type_description(field_type=FieldType.TEXT).cast_constraint(
    TextConstraint
)

text_area_constraint = field_type_description(
    field_type=FieldType.TEXT_AREA
).cast_constraint(TextAreaConstraint)

long_text_area_constraint = field_type_description(
    field_type=FieldType.LONG_TEXT_AREA
).cast_constraint(LongTextAreaConstraint)

rich_text_area_constraint = field_type_description(
    field_type=FieldType.RICH_TEXT_AREA
).cast_constraint(RichTextAreaConstraint)

timestamp_constraint = field_type_description(
    field_type=FieldType.TIMESTAMP
).cast_constraint(TimestampConstraint)

local_date_constraint = field_type_description(
    field_type=FieldType.LOCAL_DATE
).cast_constraint(LocalDateConstraint)

local_time_of_day_constraint = field_type_description(
    field_type=FieldType.LOCAL_TIME_OF_DAY
).cast_constraint(LocalTimeOfDayConstraint)

time_of_day_constraint = field_type_description(
    field_type=FieldType.TIME_OF_DAY
).cast_constraint(TimeOfDayConstraint)

boolean_checkbox_constraint = field_type_description(
    field_type=FieldType.BOOLEAN_CHECKBOX
).cast_constraint(BooleanCheckboxConstraint)

single_select_constraint = field_type_description(
    field_type=FieldType.SINGLE_SELECT
).cast_constraint(SingleSelectConstraint)

multi_select_constraint = field_type_description(
    field_type=FieldType.MULTI_SELECT
).cast_constraint(MultiSelectConstraint)

email_constraint = field_type_description(field_type=FieldType.EMAIL).cast_constraint(
    EmailConstraint
)

phone_constraint = field_type_description(
    field_type=FieldType.PHONE_NUMBER
).cast_constraint(PhoneConstraint)

url_constraint = field_type_description(field_type=FieldType.URL).cast_constraint(
    UrlConstraint
)

geo_location_constraint = field_type_description(
    field_type=FieldType.GEO_LOCATION
).cast_constraint(GeoLocationConstraint)

default_enum_constraint = field_type_description(
    field_type=FieldType.DEFAULT_ENUM
).cast_constraint(DefaultEnumConstraint)

nested_object_constraint = field_type_description(
    field_type=FieldType.NESTED_OBJECT
).cast_constraint(NestedObjectConstraint)
