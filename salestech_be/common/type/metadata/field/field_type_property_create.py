from typing import Annotated
from uuid import UUID

from pydantic import Field

from salestech_be.common.type.metadata.field.field_type_property import (
    BooleanCheckboxFieldProperty,
    CurrencyFieldProperty,
    CustomFieldTypeProperty,
    EmailFieldProperty,
    GeoLocationFieldProperty,
    LocalDateFieldProperty,
    LocalTimeOfDayFieldProperty,
    LongTextAreaFieldProperty,
    MultiSelectFieldProperty,
    NumericFieldProperty,
    PercentFieldProperty,
    PhoneFieldProperty,
    RichTextAreaFieldProperty,
    SingleSelectFieldProperty,
    TextAreaFieldProperty,
    TextFieldProperty,
    TimeOfDayFieldProperty,
    TimestampFieldProperty,
    UrlFieldProperty,
    UUIDFieldProperty,
)


class SingleSelectFieldPropertyCreate(SingleSelectFieldProperty):
    select_list_id: UUID

    def to_property(self) -> SingleSelectFieldProperty:
        return SingleSelectFieldProperty.model_validate(self.model_dump())


class MultiSelectFieldPropertyCreate(MultiSelectFieldProperty):
    select_list_id: UUI<PERSON>

    def to_property(self) -> MultiSelectFieldProperty:
        return MultiSelectFieldProperty.model_validate(self.model_dump())


CustomFieldTypePropertyCreate = Annotated[
    UUIDFieldProperty
    | NumericFieldProperty
    | CurrencyFieldProperty
    | PercentFieldProperty
    | TextFieldProperty
    | TextAreaFieldProperty
    | LongTextAreaFieldProperty
    | RichTextAreaFieldProperty
    | TimestampFieldProperty
    | LocalTimeOfDayFieldProperty
    | TimeOfDayFieldProperty
    | LocalDateFieldProperty
    | BooleanCheckboxFieldProperty
    | SingleSelectFieldPropertyCreate
    | MultiSelectFieldPropertyCreate
    | EmailFieldProperty
    | PhoneFieldProperty
    | UrlFieldProperty
    | GeoLocationFieldProperty,
    Field(discriminator="field_type"),
]


def custom_field_type_property_create_to_property(
    create: CustomFieldTypePropertyCreate,
) -> CustomFieldTypeProperty:
    if isinstance(
        create, SingleSelectFieldPropertyCreate | MultiSelectFieldPropertyCreate
    ):
        return create.to_property()
    return create
