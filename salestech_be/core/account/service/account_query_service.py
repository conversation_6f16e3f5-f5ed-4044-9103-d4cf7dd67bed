from typing import Annotated
from uuid import UUID

from fastapi import Depends

from salestech_be.common.error_code import ErrorCode
from salestech_be.common.exception import (
    ErrorDetails,
    IllegalStateError,
    ResourceNotFoundError,
)
from salestech_be.common.query_util.domain_fetch_hints import DomainFetchHints
from salestech_be.common.singleton import Singleton
from salestech_be.common.type.patch_request import UNSET, UnsetAware, specified
from salestech_be.core.account.converter import account_v2_from_db
from salestech_be.core.account.fetch_config import account_domain_db_mapping
from salestech_be.core.account.types_v2 import (
    AccountAISuggestedAnnualRevenue,
    AccountAISuggestedCategoryList,
    AccountAISuggestedDescription,
    AccountAISuggestedEmployeeCount,
    AccountAISuggestedLinkedInUrl,
    AccountAISuggestedOfficialWebsite,
    AccountV2,
)
from salestech_be.core.common.domain_fetching_hint import to_sql_selection_spec
from salestech_be.core.common.domain_service import DomainQueryService
from salestech_be.core.custom_object.service.custom_object_service import (
    CustomObjectService,
    get_custom_object_service,
)
from salestech_be.core.custom_object.type.extendable_standard_object import (
    ExtendableStandardObject,
)
from salestech_be.core.ff.feature_flag_service import (
    FeatureFlagService,
    get_feature_flag_service,
)
from salestech_be.core.research_agent.research_agent_service import (
    ResearchAgentService,
    get_research_agent_service,
)
from salestech_be.core.research_agent.types import DBAccountAISuggestedValues
from salestech_be.db.dao.account_repository import AccountRepository
from salestech_be.db.dao.address_repository import AddressRepository
from salestech_be.db.dao.contact_repository import ContactRepository
from salestech_be.db.dao.generic_repository import SelectionSpec
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.account import Account as DbAccount
from salestech_be.db.models.address import Address as DbAddress
from salestech_be.db.models.core.utils import SqlComparator, SqlSelection
from salestech_be.ree_logging import get_logger

logger = get_logger()


class AccountQueryService(DomainQueryService[AccountV2]):
    def __init__(
        self,
        contact_repository: Annotated[ContactRepository, Depends()],
        account_repository: Annotated[AccountRepository, Depends()],
        address_repository: Annotated[AddressRepository, Depends()],
        custom_object_service: Annotated[CustomObjectService, Depends()],
        research_agent_service: Annotated[ResearchAgentService, Depends()],
        feature_flag_service: Annotated[
            FeatureFlagService, Depends(get_feature_flag_service)
        ],
    ):
        super().__init__(feature_flag_service=feature_flag_service)
        self.contact_repository = contact_repository
        self.account_repository = account_repository
        self.address_repository = address_repository
        self.custom_object_service = custom_object_service
        self.research_agent_service = research_agent_service

    async def get_count_of_all_accounts_in_organization(
        self,
        organization_id: UUID,
        exclude_archived: bool = True,
    ) -> int:
        return await self.account_repository.count_by_column_values(
            DbAccount,
            organization_id=organization_id,
            exclude_deleted_or_archived=exclude_archived,
        )

    async def _enrich_account_v2(
        self,
        db_accounts: list[DbAccount],
        organization_id: UUID,
        include_custom_object: bool | None = False,
    ) -> list[AccountV2]:
        if not db_accounts:
            return []
        address_by_id: dict[
            UUID, DbAddress
        ] = await self.address_repository.map_addresses_by_ids(
            ids=list(
                {
                    db_account.address_id
                    for db_account in db_accounts
                    if db_account.address_id
                }
            ),
            organization_id=organization_id,
        )
        extension_custom_object_data_group_dto = (
            await self.custom_object_service.map_custom_object_data_by_extension_ids_if_custom_object_exists(
                organization_id=organization_id,
                parent_object_name=ExtendableStandardObject.account,
                extension_ids={db_account.id for db_account in db_accounts},
            )
            if include_custom_object
            else None
        )
        db_ai_suggested_values_by_ids: dict[UUID, DBAccountAISuggestedValues] = {}
        try:
            # Get AI suggested values for accounts
            db_ai_suggested_values_by_ids = await self.research_agent_service.map_ai_suggested_values_by_account_ids(
                account_ids={db_account.id for db_account in db_accounts}
            )
        except Exception as e:
            logger.warning(
                "Error getting AI suggested values for accounts",
                error=e,
            )

        # Create AccountV2 objects with all data
        accounts_v2 = []
        for db_account in db_accounts:
            # Get AI suggested values before creating the AccountV2 object
            db_ai_suggested_values = db_ai_suggested_values_by_ids.get(db_account.id)
            ai_suggested_annual_revenue = None
            ai_suggested_employee_count = None
            ai_suggested_description = None
            ai_suggested_category_list = None
            ai_suggested_official_website = None
            ai_suggested_linkedin_url = None
            if db_ai_suggested_values:
                try:
                    ai_suggested_annual_revenue = (
                        AccountAISuggestedAnnualRevenue.from_db_ai_suggested_values(
                            db_ai_suggested_values
                        )
                    )
                    ai_suggested_employee_count = (
                        AccountAISuggestedEmployeeCount.from_db_ai_suggested_values(
                            db_ai_suggested_values
                        )
                    )
                    ai_suggested_description = (
                        AccountAISuggestedDescription.from_db_ai_suggested_values(
                            db_ai_suggested_values
                        )
                    )
                    ai_suggested_category_list = (
                        AccountAISuggestedCategoryList.from_db_ai_suggested_values(
                            db_ai_suggested_values
                        )
                    )
                    ai_suggested_official_website = (
                        AccountAISuggestedOfficialWebsite.from_db_ai_suggested_values(
                            db_ai_suggested_values
                        )
                    )
                    ai_suggested_linkedin_url = (
                        AccountAISuggestedLinkedInUrl.from_db_ai_suggested_values(
                            db_ai_suggested_values
                        )
                    )
                except Exception as e:
                    logger.warning(
                        "Error converting AI suggested values to AccountV2 objects",
                        account_id=db_account.id,
                        db_ai_suggested_values=db_ai_suggested_values,
                        error=e,
                    )

            # Create AccountV2 with all data at initialization time
            account_v2 = account_v2_from_db(
                db_account=db_account,
                db_address=address_by_id.get(db_account.address_id)
                if db_account.address_id
                else None,
                extension_custom_object_data_group_dto=extension_custom_object_data_group_dto,
                ai_suggested_annual_revenue=ai_suggested_annual_revenue,
                ai_suggested_employee_count=ai_suggested_employee_count,
                ai_suggested_description=ai_suggested_description,
                ai_suggested_category_list=ai_suggested_category_list,
                ai_suggested_official_website=ai_suggested_official_website,
                ai_suggested_linkedin_url=ai_suggested_linkedin_url,
            )

            accounts_v2.append(account_v2)

        return accounts_v2

    async def list_accounts_v2(
        self,
        organization_id: UUID,
        only_include_account_ids: UnsetAware[set[UUID]] = UNSET,
        include_custom_object: bool | None = False,
        domain_fetch_hints: UnsetAware[DomainFetchHints | None] = UNSET,
        exclude_archived: bool = True,
    ) -> list[AccountV2]:
        if specified(only_include_account_ids) and not only_include_account_ids:
            return []

        if specified(domain_fetch_hints):
            selection_spec: SelectionSpec = to_sql_selection_spec(
                domain_fetch_hints=domain_fetch_hints,
                domain_to_table_model_field_mapping=account_domain_db_mapping,
            )
            if specified(only_include_account_ids) and only_include_account_ids:
                selection_spec = selection_spec._replace(
                    must=(
                        (
                            SqlSelection(
                                column_name="id",
                                sql_comparator=SqlComparator.IN,
                                comparable_value=list(only_include_account_ids),
                            ),
                            *selection_spec.must,
                        )
                    )
                )
            db_accounts: list[
                DbAccount
            ] = await self.account_repository.list_by_selection_spec(
                table_model=DbAccount,
                organization_id=organization_id,
                selection_spec=selection_spec,
            )
        else:
            db_accounts = (
                await self.account_repository.list_by_ids(
                    ids=list(only_include_account_ids),
                    organization_id=organization_id,
                )
                if specified(only_include_account_ids)
                else await self.account_repository.list_all(
                    organization_id=organization_id,
                    exclude_archived=exclude_archived,
                    exclude_locked_by_integrity_jobs=False,
                )
            )

        return await self._enrich_account_v2(
            db_accounts=db_accounts,
            organization_id=organization_id,
            include_custom_object=include_custom_object,
        )

    async def get_account_v2(
        self,
        account_id: UUID,
        organization_id: UUID,
        include_custom_object: bool | None = False,
    ) -> AccountV2:
        list_result = await self.list_accounts_v2(
            organization_id=organization_id,
            only_include_account_ids={account_id},
            include_custom_object=include_custom_object,
        )
        if not list_result:
            raise ResourceNotFoundError(
                additional_error_details=ErrorDetails(
                    code=ErrorCode.ACCOUNT_NOT_FOUND,
                    details="Account not found",
                    reference_id=str(account_id),
                )
            )
        if len(list_result) > 1:
            logger.error(
                "more than 1 account found by account_id and organization_id",
                account_id=account_id,
                organization_id=organization_id,
                found_accounts=list_result,
            )
            raise IllegalStateError(
                "more than 1 account found by account_id and organization_id"
            )
        return list_result[0]

    async def get_db_account_by_id(
        self,
        account_id: UUID,
        organization_id: UUID,
    ) -> DbAccount:
        return await self.account_repository.find_by_tenanted_primary_key_or_fail(
            DbAccount,
            organization_id=organization_id,
            id=account_id,
        )


class SingletonAccountQueryService(Singleton, AccountQueryService):
    pass


def get_account_query_service(
    db_engine: DatabaseEngine,
) -> AccountQueryService:
    if SingletonAccountQueryService.has_instance():
        return SingletonAccountQueryService.get_singleton_instance()
    return SingletonAccountQueryService(
        contact_repository=ContactRepository(engine=db_engine),
        account_repository=AccountRepository(engine=db_engine),
        address_repository=AddressRepository(engine=db_engine),
        custom_object_service=get_custom_object_service(db_engine=db_engine),
        research_agent_service=get_research_agent_service(db_engine),
        feature_flag_service=get_feature_flag_service(),
    )
