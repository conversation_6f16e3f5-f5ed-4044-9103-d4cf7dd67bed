from uuid import UUID

from pydantic import BaseModel

from salestech_be.core.account.types_v2 import AccountV2
from salestech_be.core.approval_request.types import (
    ApprovalRequest,
    IApprovableActionResponse,
)
from salestech_be.core.metadata.types import StageCriteriaEvaluateResult
from salestech_be.db.models.account import AccountStatus


class ShiftAccountStatusRequest(BaseModel):
    target_status: AccountStatus
    approval_request_id: UUID | None = None


class ShiftAccountStatusResponse(BaseModel, IApprovableActionResponse):
    account: AccountV2
    shifted: bool = False
    stage_criteria_evaluation_result: StageCriteriaEvaluateResult | None = None
    approval_request: ApprovalRequest | None = None

    @property
    def is_successful(self) -> bool:
        if not self.stage_criteria_evaluation_result:
            return True
        return self.stage_criteria_evaluation_result.is_success
