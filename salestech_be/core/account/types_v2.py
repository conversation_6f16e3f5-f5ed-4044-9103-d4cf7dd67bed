from __future__ import annotations

import uuid
from decimal import Decimal
from typing import Annotated
from uuid import UUID

from pydantic import Field, NonNegativeInt

from salestech_be.common.schema_manager.std_object_field_identifier import (
    A<PERSON>untField,
    ContactAccountRoleField,
    DomainObjectListField,
    OrganizationUserField,
    PipelineField,
    StdObjectIdentifiers,
)
from salestech_be.common.schema_manager.std_object_relationship import (
    AccountRelationship,
)
from salestech_be.common.type.metadata.common import (
    ObjectAccessStatus,
)
from salestech_be.common.type.metadata.field.field_indexable_config import (
    UniqueIndexableConfig,
)
from salestech_be.common.type.metadata.field.field_type_property import (
    Cur<PERSON>cyFieldProperty,
    DefaultEnumFieldProperty,
    ListFieldProperty,
    LongTextAreaFieldProperty,
    NestedObjectFieldProperty,
    NumericFieldProperty,
    RichTextAreaFieldProperty,
    TextFieldProperty,
    TimestampFieldProperty,
    Url<PERSON>ield<PERSON>roperty,
    UUIDFieldProperty,
)
from salestech_be.common.type.metadata.field.field_value import FieldValueOrAny
from salestech_be.common.type.metadata.schema import (
    InboundRelationship,
    OutboundRelationship,
)
from salestech_be.common.type.numbers import NonNegativeDecimal
from salestech_be.common.type.patch_request import (
    UNSET,
    BaseBulkPatchRequest,
    BasePatchRequest,
    UnsetAware,
)
from salestech_be.core.common.types import (
    BaseAISuggestedValue,
    CustomizableDomainModel,
    FieldMetadata,
)
from salestech_be.core.research_agent.types import DBAccountAISuggestedValues
from salestech_be.db.models.account import AccountStatus
from salestech_be.db.models.address import AddressUpdate
from salestech_be.db.models.core.types import CreatedSource
from salestech_be.db.models.intel_company_info import IntelCompanyInfo
from salestech_be.util.pydantic_types.str import SchemeOptionalHttpUrlStr
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime


class AccountAISuggestedAnnualRevenue(BaseAISuggestedValue):
    """Container for AI-suggested annual revenue for an account."""

    object_id = StdObjectIdentifiers.account_ai_suggested_annual_revenue.identifier
    object_display_name = "AI Suggested Annual Revenue"

    # Override the generic field with specific type
    value: Annotated[
        Decimal | None,
        FieldMetadata(
            type_property=CurrencyFieldProperty(
                total_precision=15,
                decimal_precision=3,
                field_display_name="Value",
                is_ui_displayable=False,
                is_ui_editable=False,
                is_import_field_mappable=False,
            )
        ),
    ] = None

    @classmethod
    def from_intel_company_info(
        cls, intel_company_info: IntelCompanyInfo
    ) -> AccountAISuggestedAnnualRevenue | None:
        if (
            not intel_company_info
            or not intel_company_info.data
            or "company_info" not in intel_company_info.data
        ):
            return None

        # todo: (taiyan) refactor to create/use a general model for `company_info` jsonb
        company_info = intel_company_info.data["company_info"]
        revenue = company_info and company_info.get(
            "estimated_revenue_higher_bound_usd", None
        )
        if not revenue:
            return None

        return cls(
            value=revenue,
            updated_at=intel_company_info.updated_at,
        )

    @classmethod
    def from_db_ai_suggested_values(
        cls, db_ai_suggested_values: DBAccountAISuggestedValues
    ) -> AccountAISuggestedAnnualRevenue | None:
        """Create from values returned by map_ai_suggested_values_by_account_ids.

        Args:
            db_ai_suggested_values: Dictionary with AI suggested values

        Returns:
            AccountAISuggestedAnnualRevenue object, or None if no valid revenue found
        """
        if not db_ai_suggested_values.estimated_revenue_higher_bound_usd:
            return None
        return cls(
            value=db_ai_suggested_values.estimated_revenue_higher_bound_usd,
            updated_at=db_ai_suggested_values.updated_at,
        )


class AccountAISuggestedEmployeeCount(BaseAISuggestedValue):
    """Container for AI-suggested employee count for an account."""

    object_id = StdObjectIdentifiers.account_ai_suggested_employee_count.identifier
    object_display_name = "AI Suggested Employee Count"

    # Override the generic field with specific type
    value: Annotated[
        int | None,
        FieldMetadata(
            type_property=NumericFieldProperty(
                total_precision=18,
                decimal_precision=0,
                field_display_name="Value",
                is_ui_displayable=False,
                is_ui_editable=False,
                is_import_field_mappable=False,
            )
        ),
    ] = None

    @classmethod
    def employee_count_from_employee_count_range(
        cls, employee_count_range: str
    ) -> int | None:
        employee_count: int | None = None
        try:
            if "-" in employee_count_range:
                # Format: xxx-xxx
                employee_count = int(employee_count_range.split("-")[1].strip())
            elif "+" in employee_count_range:
                # Format: xxxxx+
                # Use 20000 as upper bound for ranges like 10000+
                # TODO: keeping this to be consistent with old behavior, but need to revisit this magic number
                employee_count = 20000
            else:
                # Single number
                employee_count = int(employee_count_range.strip())
            return employee_count
        except (ValueError, TypeError):
            return None

    @classmethod
    def from_intel_company_info(
        cls, intel_company_info: IntelCompanyInfo
    ) -> AccountAISuggestedEmployeeCount | None:
        if (
            not intel_company_info
            or not intel_company_info.data
            or "company_info" not in intel_company_info.data
        ):
            return None

        # todo: (taiyan) refactor to create/use a general model for `company_info` jsonb
        company_info = intel_company_info.data["company_info"]
        employee_count_range = company_info and company_info.get(
            "employee_count_range", None
        )
        if not employee_count_range:
            return None
        employee_count = cls.employee_count_from_employee_count_range(
            employee_count_range
        )
        if not employee_count:
            return None
        return cls(
            value=employee_count,
            updated_at=intel_company_info.updated_at,
        )

    @classmethod
    def from_db_ai_suggested_values(
        cls, db_ai_suggested_values: DBAccountAISuggestedValues
    ) -> AccountAISuggestedEmployeeCount | None:
        """Create from values returned by map_ai_suggested_values_by_account_ids.

        Args:
            db_ai_suggested_values: Dictionary with AI suggested values

        Returns:
            AccountAISuggestedEmployeeCount object, or None if no valid employee count found
        """
        employee_count_range = db_ai_suggested_values.employee_count_range
        if not employee_count_range:
            return None
        employee_count = cls.employee_count_from_employee_count_range(
            employee_count_range
        )
        if not employee_count:
            return None
        return cls(
            value=employee_count,
            updated_at=db_ai_suggested_values.updated_at,
        )


class AccountAISuggestedDescription(BaseAISuggestedValue):
    """Container for AI-suggested description for an account."""

    object_id = StdObjectIdentifiers.account_ai_suggested_description.identifier
    object_display_name = "AI Suggested Description"

    # Override the generic field with specific type
    value: Annotated[
        str | None,
        FieldMetadata(
            type_property=LongTextAreaFieldProperty(
                field_display_name="Value",
                is_ui_displayable=False,
                is_ui_editable=False,
                is_import_field_mappable=False,
            )
        ),
    ] = None

    @classmethod
    def from_intel_company_info(
        cls, intel_company_info: IntelCompanyInfo
    ) -> AccountAISuggestedDescription | None:
        if (
            not intel_company_info
            or not intel_company_info.data
            or "company_info" not in intel_company_info.data
        ):
            return None
        company_info = intel_company_info.data["company_info"]
        description = company_info and company_info.get(
            "linkedin_company_description", None
        )
        if not description:
            return None

        return cls(
            value=description,
            updated_at=intel_company_info.updated_at,
        )

    @classmethod
    def from_db_ai_suggested_values(
        cls, db_ai_suggested_values: DBAccountAISuggestedValues
    ) -> AccountAISuggestedDescription | None:
        """Create from values returned by map_ai_suggested_values_by_account_ids.

        Args:
            db_ai_suggested_values: Dictionary with AI suggested values

        Returns:
            AccountAISuggestedDescription object, or None if no valid description found
        """
        description = db_ai_suggested_values.linkedin_company_description
        if not description:
            return None
        return cls(
            value=description,
            updated_at=db_ai_suggested_values.updated_at,
        )


class AccountAISuggestedCategoryList(BaseAISuggestedValue):
    """Container for AI-suggested category list for an account."""

    object_id = StdObjectIdentifiers.account_ai_suggested_category_list.identifier
    object_display_name = "AI Suggested Category List"

    # Override the generic field with specific type
    value: Annotated[
        list[str] | None,
        FieldMetadata(
            type_property=ListFieldProperty(
                field_display_name="Value",
                element_field_type_property=TextFieldProperty(
                    field_display_name="Industry",
                ),
                is_ui_displayable=False,
                is_ui_editable=False,
                is_import_field_mappable=False,
            )
        ),
    ] = None

    @classmethod
    def from_intel_company_info(
        cls, intel_company_info: IntelCompanyInfo
    ) -> AccountAISuggestedCategoryList | None:
        if (
            not intel_company_info
            or not intel_company_info.data
            or "company_info" not in intel_company_info.data
        ):
            return None
        company_info = intel_company_info.data["company_info"]
        categories = company_info and (company_info.get("taxonomy") or {}).get(
            "linkedin_industries", []
        )
        if not categories:
            return None

        return cls(
            value=categories,
            updated_at=intel_company_info.updated_at,
        )

    @classmethod
    def from_db_ai_suggested_values(
        cls, db_ai_suggested_values: DBAccountAISuggestedValues
    ) -> AccountAISuggestedCategoryList | None:
        """Create from values returned by map_ai_suggested_values_by_account_ids.

        Args:
            db_ai_suggested_values: Dictionary with AI suggested values

        Returns:
            AccountAISuggestedCategoryList object, or None if no valid categories found
        """
        categories = db_ai_suggested_values.linkedin_industries
        if not categories:
            return None

        return cls(
            value=categories,
            updated_at=db_ai_suggested_values.updated_at,
        )


class AccountAISuggestedOfficialWebsite(BaseAISuggestedValue):
    object_id = StdObjectIdentifiers.account_ai_suggested_official_website.identifier
    object_display_name = "AI Suggested Official Website"

    # Override the generic field with specific type
    value: Annotated[
        str | None,
        FieldMetadata(
            type_property=UrlFieldProperty(
                field_display_name="Value",
                is_ui_displayable=False,
                is_ui_editable=False,
                is_import_field_mappable=False,
            )
        ),
    ] = None

    @classmethod
    def from_intel_company_info(
        cls, intel_company_info: IntelCompanyInfo
    ) -> AccountAISuggestedOfficialWebsite | None:
        if (
            not intel_company_info
            or not intel_company_info.data
            or "company_info" not in intel_company_info.data
        ):
            return None

        company_info = intel_company_info.data["company_info"]
        company_website = company_info and company_info.get("company_website")
        if not company_website:
            return None

        return cls(
            value=company_website,
            updated_at=intel_company_info.updated_at,
        )

    @classmethod
    def from_db_ai_suggested_values(
        cls, db_ai_suggested_values: DBAccountAISuggestedValues
    ) -> AccountAISuggestedOfficialWebsite | None:
        """Create from values returned by map_ai_suggested_values_by_account_ids.

        Args:
            db_ai_suggested_values: Dictionary with AI suggested values

        Returns:
            AccountAISuggestedOfficialWebsite object, or None if no valid company website found
        """
        company_website = db_ai_suggested_values.company_website
        if not company_website:
            return None

        return cls(
            value=company_website,
            updated_at=db_ai_suggested_values.updated_at,
        )


class AccountAISuggestedLinkedInUrl(BaseAISuggestedValue):
    object_id = StdObjectIdentifiers.account_ai_suggested_linkedin_url.identifier
    object_display_name = "AI Suggested LinkedIn URL"

    # Override the generic field with specific type
    value: Annotated[
        str | None,
        FieldMetadata(
            type_property=UrlFieldProperty(
                field_display_name="Value",
                is_ui_displayable=False,
                is_ui_editable=False,
                is_import_field_mappable=False,
            )
        ),
    ] = None

    @classmethod
    def from_intel_company_info(
        cls, intel_company_info: IntelCompanyInfo
    ) -> AccountAISuggestedLinkedInUrl | None:
        if (
            not intel_company_info
            or not intel_company_info.data
            or "company_info" not in intel_company_info.data
        ):
            return None

        company_info = intel_company_info.data["company_info"]
        linkedin_url = company_info and company_info.get("linkedin_profile_url")
        if not linkedin_url:
            return None

        return cls(
            value=linkedin_url,
            updated_at=intel_company_info.updated_at,
        )

    @classmethod
    def from_db_ai_suggested_values(
        cls, db_ai_suggested_values: DBAccountAISuggestedValues
    ) -> AccountAISuggestedLinkedInUrl | None:
        """Create from values returned by map_ai_suggested_values_by_account_ids.

        Args:
            db_ai_suggested_values: Dictionary with AI suggested values

        Returns:
            AccountAISuggestedLinkedInUrl object, or None if no valid LinkedIn URL found
        """
        linkedin_url = db_ai_suggested_values.linkedin_url
        if not linkedin_url:
            return None

        return cls(
            value=linkedin_url,
            updated_at=db_ai_suggested_values.updated_at,
        )


class AccountV2(CustomizableDomainModel):
    object_id = StdObjectIdentifiers.account.identifier
    object_display_name = "Account"
    field_name_provider = AccountField

    inbound_relationships = (
        InboundRelationship(
            id=AccountRelationship.account__from__pipeline,
            relation_type=InboundRelationship.RelationType.LOOKUP,
            relationship_name="Opportunities",
            self_object_identifier=StdObjectIdentifiers.account.identifier,
            related_object_identifier=StdObjectIdentifiers.pipeline.identifier,
            self_cardinality=InboundRelationship.Cardinality.ONE,
            related_object_cardinality=InboundRelationship.Cardinality.MANY,
            ordered_self_field_identifiers=(AccountField.id.identifier,),
            ordered_related_field_identifiers=(PipelineField.account_id.identifier,),
        ),
        InboundRelationship(
            id=AccountRelationship.domain_object_lists__from__account,
            relation_type=OutboundRelationship.RelationType.LOOKUP,
            relationship_name="Domain Object Lists",
            self_object_identifier=StdObjectIdentifiers.account.identifier,
            related_object_identifier=StdObjectIdentifiers.domain_object_list.identifier,
            self_cardinality=OutboundRelationship.Cardinality.MANY,
            related_object_cardinality=OutboundRelationship.Cardinality.MANY,
            ordered_self_field_identifiers=(AccountField.id.identifier,),
            ordered_related_field_identifiers=(DomainObjectListField.id.identifier,),
        ),
        InboundRelationship(
            id=AccountRelationship.account__from__contact_account_role,
            relation_type=InboundRelationship.RelationType.LOOKUP,
            relationship_name="Contact Account Role",
            self_object_identifier=StdObjectIdentifiers.account.identifier,
            related_object_identifier=StdObjectIdentifiers.contact_account_role.identifier,
            self_cardinality=InboundRelationship.Cardinality.ONE,
            related_object_cardinality=InboundRelationship.Cardinality.MANY,
            ordered_self_field_identifiers=(AccountField.id.identifier,),
            ordered_related_field_identifiers=(
                ContactAccountRoleField.account_id.identifier,
            ),
            is_related_object_junction=True,
        ),
    )
    outbound_relationships = (
        OutboundRelationship(
            relation_type=OutboundRelationship.RelationType.LOOKUP,
            id=AccountRelationship.account__to__created_by_user,
            relationship_name="Created By User",
            self_object_identifier=StdObjectIdentifiers.account.identifier,
            related_object_identifier=StdObjectIdentifiers.user.identifier,
            self_cardinality=OutboundRelationship.Cardinality.MANY,
            related_object_cardinality=OutboundRelationship.Cardinality.ONE,
            ordered_self_field_identifiers=(
                AccountField.created_by_user_id.identifier,
            ),
            ordered_related_field_identifiers=(OrganizationUserField.id.identifier,),
        ),
        OutboundRelationship(
            relation_type=OutboundRelationship.RelationType.LOOKUP,
            id=AccountRelationship.account__to__updated_by_user,
            relationship_name="Updated By User",
            self_object_identifier=StdObjectIdentifiers.account.identifier,
            related_object_identifier=StdObjectIdentifiers.user.identifier,
            self_cardinality=OutboundRelationship.Cardinality.MANY,
            related_object_cardinality=OutboundRelationship.Cardinality.ONE,
            ordered_self_field_identifiers=(
                AccountField.updated_by_user_id.identifier,
            ),
            ordered_related_field_identifiers=(OrganizationUserField.id.identifier,),
        ),
        OutboundRelationship(
            relation_type=OutboundRelationship.RelationType.LOOKUP,
            id=AccountRelationship.account__to__owner_user,
            relationship_name="Assignee",
            self_object_identifier=StdObjectIdentifiers.account.identifier,
            related_object_identifier=StdObjectIdentifiers.user.identifier,
            self_cardinality=OutboundRelationship.Cardinality.MANY,
            related_object_cardinality=OutboundRelationship.Cardinality.ONE,
            ordered_self_field_identifiers=(AccountField.owner_user_id.identifier,),
            ordered_related_field_identifiers=(OrganizationUserField.id.identifier,),
        ),
        OutboundRelationship(
            relation_type=OutboundRelationship.RelationType.LOOKUP,
            id=AccountRelationship.account__to__archived_by_user,
            relationship_name="Archived By User",
            self_object_identifier=StdObjectIdentifiers.account.identifier,
            related_object_identifier=StdObjectIdentifiers.user.identifier,
            self_cardinality=OutboundRelationship.Cardinality.MANY,
            related_object_cardinality=OutboundRelationship.Cardinality.ONE,
            ordered_self_field_identifiers=(
                AccountField.archived_by_user_id.identifier,
            ),
            ordered_related_field_identifiers=(OrganizationUserField.id.identifier,),
        ),
        OutboundRelationship(
            id=AccountRelationship.account__to__participant_users,
            relation_type=OutboundRelationship.RelationType.LOOKUP,
            relationship_name="Participant Users",
            self_object_identifier=StdObjectIdentifiers.account.identifier,
            related_object_identifier=StdObjectIdentifiers.user.identifier,
            self_cardinality=OutboundRelationship.Cardinality.ONE,
            related_object_cardinality=OutboundRelationship.Cardinality.MANY,
            ordered_self_field_identifiers=(
                AccountField.participant_user_id_list.identifier,
            ),
            ordered_related_field_identifiers=(OrganizationUserField.id.identifier,),
        ),
    )

    # Basic information
    id: Annotated[
        UUID,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                index_config=UniqueIndexableConfig(is_indexed=True, is_unique=True),
                field_display_name="ID",
                is_ui_displayable=False,
                is_ui_editable=False,
                is_import_field_mappable=False,
            )
        ),
    ]
    display_name: Annotated[
        str,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Account Name",
                is_ui_displayable=True,
                is_ui_editable=True,
                is_import_field_mappable=True,
            )
        ),
    ]
    domain_name: Annotated[
        str | None,
        FieldMetadata(
            type_property=UrlFieldProperty(
                field_display_name="Domain Name",
                is_ui_displayable=True,
                is_ui_editable=False,
                is_import_field_mappable=True,
            )
        ),
    ] = None
    description: Annotated[
        str | None,
        FieldMetadata(
            type_property=LongTextAreaFieldProperty(
                field_display_name="Description",
                is_ui_displayable=True,
                is_ui_editable=True,
                is_import_field_mappable=True,
            )
        ),
    ] = None
    status: Annotated[
        AccountStatus,
        FieldMetadata(
            type_property=DefaultEnumFieldProperty(
                enum_class=AccountStatus,
                field_display_name="Status",
                is_ui_displayable=True,
                is_ui_editable=False,
                is_import_field_mappable=True,
            )
        ),
    ]

    access_status: Annotated[
        ObjectAccessStatus,
        FieldMetadata(
            type_property=DefaultEnumFieldProperty(
                enum_class=ObjectAccessStatus,
                field_display_name="Access Status",
                is_ui_displayable=False,
                is_ui_editable=False,
                is_import_field_mappable=False,
            )
        ),
    ]

    # Contact information
    official_website: Annotated[
        str | None,
        FieldMetadata(
            type_property=UrlFieldProperty(
                field_display_name="Official Website",
                is_ui_displayable=True,
                is_ui_editable=True,
                is_import_field_mappable=True,
            )
        ),
    ] = None
    linkedin_url: Annotated[
        str | None,
        FieldMetadata(
            type_property=UrlFieldProperty(
                field_display_name="LinkedIn URL",
                is_ui_displayable=True,
                is_ui_editable=True,
                is_import_field_mappable=True,
            )
        ),
    ] = None
    x_url: Annotated[
        str | None,
        FieldMetadata(
            type_property=UrlFieldProperty(
                field_display_name="X URL",
                is_ui_displayable=True,
                is_ui_editable=True,
                is_import_field_mappable=True,
            )
        ),
    ] = None
    facebook_url: Annotated[
        str | None,
        FieldMetadata(
            type_property=UrlFieldProperty(
                field_display_name="Facebook URL",
                is_ui_displayable=True,
                is_ui_editable=True,
                is_import_field_mappable=True,
            )
        ),
    ] = None
    zoominfo_url: Annotated[
        str | None,
        FieldMetadata(
            type_property=UrlFieldProperty(
                field_display_name="Zoominfo URL",
                is_ui_displayable=True,
                is_ui_editable=True,
                is_import_field_mappable=True,
            )
        ),
    ] = None

    # Address
    street_one: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Street One",
                is_ui_displayable=True,
                is_ui_editable=True,
                is_import_field_mappable=True,
            )
        ),
    ] = None
    street_two: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Street Two",
                is_ui_displayable=True,
                is_ui_editable=True,
            )
        ),
    ] = None
    zip_code: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Zip Code",
                is_ui_displayable=True,
                is_ui_editable=True,
                is_import_field_mappable=True,
            )
        ),
    ] = None
    city: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="City",
                is_ui_displayable=True,
                is_ui_editable=True,
                is_import_field_mappable=True,
            )
        ),
    ] = None
    state: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="State",
                is_ui_displayable=True,
                is_ui_editable=True,
                is_import_field_mappable=True,
            )
        ),
    ] = None
    country: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Country",
                is_ui_displayable=True,
                is_ui_editable=True,
                is_import_field_mappable=True,
            )
        ),
    ] = None

    # Company details
    keyword_list: Annotated[
        list[str] | None,
        FieldMetadata(
            type_property=ListFieldProperty(
                field_display_name="Keywords",
                element_field_type_property=TextFieldProperty(
                    field_display_name="Keyword",
                ),
                is_ui_displayable=False,
                is_ui_editable=True,
                is_import_field_mappable=False,
            )
        ),
    ] = None

    category_list: Annotated[
        list[str] | None,
        FieldMetadata(
            type_property=ListFieldProperty(
                field_display_name="Industries",
                element_field_type_property=TextFieldProperty(
                    field_display_name="Industry",
                ),
                is_ui_displayable=True,
                is_ui_editable=True,
                is_import_field_mappable=True,
            )
        ),
    ] = None
    technology_list: Annotated[
        list[str] | None,
        FieldMetadata(
            type_property=ListFieldProperty(
                field_display_name="Technologies",
                element_field_type_property=TextFieldProperty(
                    field_display_name="Technology",
                ),
                is_ui_displayable=True,
                is_ui_editable=True,
                is_import_field_mappable=True,
            )
        ),
    ] = None
    estimated_annual_revenue: Annotated[
        Decimal | None,
        FieldMetadata(
            type_property=CurrencyFieldProperty(
                total_precision=15,
                decimal_precision=3,
                field_display_name="Estimated Annual Revenue",
                is_ui_displayable=True,
                is_ui_editable=True,
                is_import_field_mappable=True,
            )
        ),
    ] = None
    ai_suggested_annual_revenue: Annotated[
        AccountAISuggestedAnnualRevenue | None,
        FieldMetadata(
            type_property=NestedObjectFieldProperty(
                field_display_name="AI Suggested Annual Revenue",
                object_identifier=StdObjectIdentifiers.account_ai_suggested_annual_revenue.identifier,
                is_ui_displayable=False,
                is_ui_editable=False,
                is_import_field_mappable=False,
                is_filterable=False,
            ),
            is_enriched_field=True,
        ),
    ] = None
    estimated_employee_count: Annotated[
        int | None,
        FieldMetadata(
            type_property=NumericFieldProperty(
                total_precision=18,
                decimal_precision=0,
                field_display_name="Estimated Employee Count",
                is_ui_displayable=True,
                is_ui_editable=True,
                is_import_field_mappable=True,
            )
        ),
    ] = None
    ai_suggested_employee_count: Annotated[
        AccountAISuggestedEmployeeCount | None,
        FieldMetadata(
            type_property=NestedObjectFieldProperty(
                field_display_name="AI Suggested Employee Count",
                object_identifier=StdObjectIdentifiers.account_ai_suggested_employee_count.identifier,
                is_ui_displayable=False,
                is_ui_editable=False,
                is_import_field_mappable=False,
                is_filterable=False,
            ),
            is_enriched_field=True,
        ),
    ] = None

    ai_suggested_description: Annotated[
        AccountAISuggestedDescription | None,
        FieldMetadata(
            type_property=NestedObjectFieldProperty(
                field_display_name="AI Suggested Description",
                object_identifier=StdObjectIdentifiers.account_ai_suggested_description.identifier,
                is_ui_displayable=False,
                is_ui_editable=False,
                is_import_field_mappable=False,
                is_filterable=False,
            ),
            is_enriched_field=True,
        ),
    ] = None

    ai_suggested_category_list: Annotated[
        AccountAISuggestedCategoryList | None,
        FieldMetadata(
            type_property=NestedObjectFieldProperty(
                field_display_name="AI Suggested Category List",
                object_identifier=StdObjectIdentifiers.account_ai_suggested_category_list.identifier,
                is_ui_displayable=False,
                is_ui_editable=False,
                is_import_field_mappable=False,
                is_filterable=False,
            ),
            is_enriched_field=True,
        ),
    ] = None

    ai_suggested_official_website: Annotated[
        AccountAISuggestedOfficialWebsite | None,
        FieldMetadata(
            type_property=NestedObjectFieldProperty(
                field_display_name="AI Suggested Official Website",
                object_identifier=StdObjectIdentifiers.account_ai_suggested_official_website.identifier,
                is_ui_displayable=False,
                is_ui_editable=False,
                is_import_field_mappable=False,
                is_filterable=False,
            ),
            is_enriched_field=True,
        ),
    ] = None

    ai_suggested_linkedin_url: Annotated[
        AccountAISuggestedLinkedInUrl | None,
        FieldMetadata(
            type_property=NestedObjectFieldProperty(
                field_display_name="AI Suggested LinkedIn URL",
                object_identifier=StdObjectIdentifiers.account_ai_suggested_linkedin_url.identifier,
                is_ui_displayable=False,
                is_ui_editable=False,
                is_import_field_mappable=False,
                is_filterable=False,
            ),
            is_enriched_field=True,
        ),
    ] = None

    # Ownership and organization
    organization_id: Annotated[
        UUID,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Organization ID",
                is_ui_displayable=False,
                is_ui_editable=False,
                is_import_field_mappable=False,
            )
        ),
    ]
    owner_user_id: Annotated[
        UUID,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Owner ID",
                is_ui_displayable=True,
                is_ui_editable=True,
                is_import_field_mappable=True,
            )
        ),
    ]
    archived_by_user_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Archived By User ID",
                is_ui_displayable=False,
                is_ui_editable=False,
                is_import_field_mappable=False,
            )
        ),
    ] = None
    created_by_user_id: Annotated[
        UUID,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Created By User ID",
                is_ui_displayable=False,
                is_ui_editable=False,
                is_import_field_mappable=False,
            )
        ),
    ]
    updated_by_user_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Updated By User ID",
                is_ui_displayable=False,
                is_ui_editable=False,
                is_import_field_mappable=False,
            )
        ),
    ] = None
    integrity_job_started_by_user_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Integrity Job Started By User ID",
                is_ui_displayable=False,
                is_ui_editable=False,
                is_import_field_mappable=False,
            )
        ),
    ] = None
    integrity_job_finished_by_user_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Integrity Job Finished By User ID",
                is_ui_displayable=False,
                is_ui_editable=False,
                is_import_field_mappable=False,
            )
        ),
    ] = None
    integrity_job_started_by_job_ids: Annotated[
        list[UUID],
        FieldMetadata(
            type_property=ListFieldProperty(
                field_display_name="Integrity Job Started By Job IDs",
                element_field_type_property=UUIDFieldProperty(
                    field_display_name="Integrity Job Started By Job ID",
                ),
                is_ui_displayable=False,
                is_ui_editable=False,
                is_import_field_mappable=False,
            ),
        ),
    ] = Field(default_factory=list)
    research_tldr: Annotated[
        str | None,
        FieldMetadata(
            type_property=RichTextAreaFieldProperty(
                max_length=131072,
                visible_lines=10,
                field_display_name="Research TLDR",
                is_ui_displayable=True,
                is_ui_editable=False,
                is_import_field_mappable=False,
            )
        ),
    ] = None
    research_content: Annotated[
        str | None,
        FieldMetadata(
            type_property=RichTextAreaFieldProperty(
                max_length=131072,
                visible_lines=10,
                field_display_name="Research Content",
                is_ui_displayable=False,
                is_ui_editable=False,
                is_import_field_mappable=False,
            )
        ),
    ] = None
    research_reference_urls: Annotated[
        list[str],
        FieldMetadata(
            type_property=ListFieldProperty(
                field_display_name="Research References",
                element_field_type_property=UrlFieldProperty(
                    field_display_name="Research Reference",
                ),
                is_ui_displayable=False,
                is_ui_editable=False,
                is_import_field_mappable=False,
            )
        ),
    ] = Field(default_factory=list)
    merge_to_account_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Merge To Account ID",
                is_ui_displayable=False,
                is_ui_editable=False,
                is_import_field_mappable=False,
            )
        ),
    ] = None  # to be deprecated

    # Timestamps
    created_at: Annotated[
        ZoneRequiredDateTime,
        FieldMetadata(
            type_property=TimestampFieldProperty(
                field_display_name="Created At",
                is_ui_displayable=True,
                is_ui_editable=False,
                is_import_field_mappable=True,
            )
        ),
    ]
    updated_at: Annotated[
        ZoneRequiredDateTime,
        FieldMetadata(
            type_property=TimestampFieldProperty(
                field_display_name="Last Updated At",
                is_ui_displayable=True,
                is_ui_editable=False,
                is_import_field_mappable=False,
            )
        ),
    ]
    archived_at: Annotated[
        ZoneRequiredDateTime | None,
        FieldMetadata(
            type_property=TimestampFieldProperty(
                field_display_name="Archived At",
                is_ui_displayable=True,
                is_ui_editable=False,
                is_import_field_mappable=False,
            )
        ),
    ] = None
    integrity_job_started_at: Annotated[
        ZoneRequiredDateTime | None,
        FieldMetadata(
            type_property=TimestampFieldProperty(
                field_display_name="Integrity Job Started At",
                is_ui_displayable=False,
                is_ui_editable=False,
                is_import_field_mappable=False,
            )
        ),
    ] = None
    integrity_job_finished_at: Annotated[
        ZoneRequiredDateTime | None,
        FieldMetadata(
            type_property=TimestampFieldProperty(
                field_display_name="Integrity Job Finished At",
                is_ui_displayable=False,
                is_ui_editable=False,
                is_import_field_mappable=False,
            )
        ),
    ] = None

    participant_user_id_list: Annotated[
        list[UUID],
        FieldMetadata(
            type_property=ListFieldProperty(
                field_display_name="Participant User IDs",
                element_field_type_property=UUIDFieldProperty(
                    field_display_name="Participant User ID",
                ),
                is_ui_displayable=True,
                is_ui_editable=True,
                is_import_field_mappable=False,
            )
        ),
    ] = Field(default_factory=list)

    created_source: Annotated[
        CreatedSource | None,
        FieldMetadata(
            type_property=DefaultEnumFieldProperty(
                enum_class=CreatedSource,
                field_display_name="Created Source",
                is_ui_displayable=True,
                is_ui_editable=False,
                is_import_field_mappable=False,
            )
        ),
    ] = None

    company_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Company ID",
                is_ui_displayable=False,
                is_ui_editable=False,
                is_import_field_mappable=False,
            )
        ),
    ] = None


class BulkPatchAccountsRequest(BaseBulkPatchRequest):
    patch_request: PatchAccountRequest


class PatchAccountRequest(BasePatchRequest):
    require_at_least_one_specified_field = True

    display_name: UnsetAware[str] = UNSET
    owner_user_id: UnsetAware[uuid.UUID] = UNSET
    official_website: UnsetAware[SchemeOptionalHttpUrlStr | None] = UNSET
    linkedin_url: UnsetAware[SchemeOptionalHttpUrlStr | None] = UNSET
    facebook_url: UnsetAware[SchemeOptionalHttpUrlStr | None] = UNSET
    zoominfo_url: UnsetAware[SchemeOptionalHttpUrlStr | None] = UNSET
    x_url: UnsetAware[SchemeOptionalHttpUrlStr | None] = UNSET
    description: UnsetAware[str | None] = UNSET
    keyword_list: UnsetAware[list[str] | None] = UNSET
    category_list: UnsetAware[list[str] | None] = UNSET
    technology_list: UnsetAware[list[str] | None] = UNSET
    estimated_annual_revenue: UnsetAware[NonNegativeDecimal | None] = UNSET
    estimated_employee_count: UnsetAware[NonNegativeInt | None] = UNSET
    address: UnsetAware[AddressUpdate | None] = UNSET
    custom_field_data: UnsetAware[dict[uuid.UUID, FieldValueOrAny] | None] = UNSET
    participant_user_id_list: UnsetAware[list[uuid.UUID] | None] = UNSET
    created_source: CreatedSource | None = None
    archived_at: UnsetAware[ZoneRequiredDateTime] | None = UNSET
