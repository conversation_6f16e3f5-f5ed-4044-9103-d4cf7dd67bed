# salestech_be/core/activity/context.py
from pydantic import BaseModel

from salestech_be.core.account.types_v2 import AccountV2
from salestech_be.core.contact.types_v2 import ContactV2
from salestech_be.core.pipeline.types_v2 import PipelineV2
from salestech_be.util.enum_util import NameValueStrEnum


class ActivityContextType(NameValueStrEnum):
    """Type of activity context"""

    MEETING = "MEETING"
    EMAIL = "EMAIL"


class ActivityContext(BaseModel):
    """User facing context surrounding an activity"""

    # Related entities (intentionally limited set)
    contacts: list[ContactV2]
    account: AccountV2 | None = None
    opportunity: PipelineV2 | None = None
