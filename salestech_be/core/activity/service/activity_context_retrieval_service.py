from uuid import UUID

from salestech_be.core.account.service.account_query_service import AccountQueryService
from salestech_be.core.activity.context import ActivityContext, ActivityContextType
from salestech_be.core.contact.service.contact_query_service import ContactQueryService
from salestech_be.core.email.global_email.global_thread_service import (
    GlobalThreadService,
)
from salestech_be.core.meeting.meeting_service import (
    MeetingService,
)
from salestech_be.core.pipeline.service.pipeline_query_service import (
    PipelineQueryService,
)
from salestech_be.db.models.global_thread import GlobalThread
from salestech_be.db.models.meeting import Meeting


class ActivityContextRetrievalService:
    def __init__(
        self,
        meeting_service: MeetingService,
        global_thread_service: GlobalThreadService,
        pipeline_query_service: PipelineQueryService,
        account_query_service: AccountQueryService,
        contact_query_service: ContactQueryService,
    ):
        self.meeting_service = meeting_service
        self.global_thread_service = global_thread_service
        self.pipeline_query_service = pipeline_query_service
        self.account_query_service = account_query_service
        self.contact_query_service = contact_query_service

    async def get_context(
        self,
        activity_type: ActivityContextType,
        activity_id: UUID,
        organization_id: UUID,
    ) -> ActivityContext:
        pipeline = None
        account = None
        contacts = []
        activity: Meeting | GlobalThread | None = None
        activity_account_id: UUID | None = None
        activity_contact_ids: list[UUID] | None = None
        if activity_type == ActivityContextType.MEETING:
            meeting_dto = await self.meeting_service.get_meeting(
                meeting_id=activity_id, organization_id=organization_id
            )
            activity = meeting_dto.meeting
            activity_account_id = activity.account_id
            activity_contact_ids = [
                invitee.contact_id
                for invitee in activity.invitees_or_empty_list()
                if invitee.contact_id
            ]
        elif activity_type == ActivityContextType.EMAIL:
            global_threads = await self.global_thread_service.list_global_thread_by_ids(
                global_thread_ids=[activity_id],
                organization_id=organization_id,
            )
            activity = global_threads[0] if global_threads else None
            if not activity:
                raise ValueError(
                    f"Could not retrieve global thread for activity id: {activity_id}"
                )
            activity_account_id = (
                activity.account_ids[0] if activity.account_ids else None
            )
            activity_contact_ids = activity.contact_ids
        else:
            raise ValueError(f"Invalid activity type: {activity_type}")

        activity_pipeline_id = activity.pipeline_id

        if activity_pipeline_id:
            pipeline = await self.pipeline_query_service.get_pipeline_by_id(
                pipeline_id=activity_pipeline_id,
                organization_id=organization_id,
                include_custom_object=True,
            )
        if activity_account_id:
            account = await self.account_query_service.get_account_v2(
                account_id=activity_account_id,
                organization_id=organization_id,
                include_custom_object=True,
            )
        if activity_contact_ids:
            contacts = await self.contact_query_service.list_contacts_v2(
                organization_id=organization_id,
                only_include_contact_ids=set(activity_contact_ids),
                include_custom_object=True,
            )

        return ActivityContext(
            opportunity=pipeline,
            account=account,
            contacts=contacts,
        )
