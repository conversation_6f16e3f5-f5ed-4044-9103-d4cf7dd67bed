import asyncio
from uuid import UUID

from salestech_be.core.account.service.account_query_service import (
    get_account_query_service,
)
from salestech_be.core.activity.context import ActivityContextType
from salestech_be.core.activity.service.activity_context_retrieval_service import (
    ActivityContextRetrievalService,
)
from salestech_be.core.contact.service.contact_query_service import (
    get_contact_query_service,
)
from salestech_be.core.email.global_email.global_thread_service import (
    get_global_thread_service_by_db_engine,
)
from salestech_be.core.meeting.meeting_service import meeting_service_factory_general
from salestech_be.core.pipeline.service.pipeline_query_service import (
    get_pipeline_query_service,
)
from salestech_be.temporal.database import (
    get_or_init_db_engine,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)


async def main() -> None:
    db_engine = await get_or_init_db_engine()
    context_service = ActivityContextRetrievalService(
        meeting_service=meeting_service_factory_general(db_engine=db_engine),
        global_thread_service=get_global_thread_service_by_db_engine(
            db_engine=db_engine
        ),
        pipeline_query_service=get_pipeline_query_service(db_engine=db_engine),
        account_query_service=get_account_query_service(db_engine=db_engine),
        contact_query_service=get_contact_query_service(db_engine=db_engine),
    )
    context = await context_service.get_context(
        activity_type=ActivityContextType.MEETING,
        activity_id=UUID("0edd47bb-223f-4df4-ab76-e9722f67e98b"),
        organization_id=UUID("cce7b290-8a08-4904-a6c7-2b6613877cf5"),
    )
    print(context.model_dump_json(indent=2))  # noqa: T201


if __name__ == "__main__":
    asyncio.run(main())
