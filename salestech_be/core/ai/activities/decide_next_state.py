from uuid import UUID

from pydantic import BaseModel
from temporalio import activity

from salestech_be.common.ree_llm import LLMTraceMetadata, acompletion
from salestech_be.core.ai.workflows.base import TransitionResponse


class DecideNextStateRequest(BaseModel):
    transition_events: list[TransitionResponse]
    current_state: str
    session_id: UUID
    organization_id: UUID


@activity.defn
async def decide_next_state(request: DecideNextStateRequest) -> str:
    """Placeholder function that decides the next state based on available transitions"""

    # Format transitions for prompt
    transitions_str = "\n".join(
        f"- {t.event}: {', '.join(t.targets)}" for t in request.transition_events
    )

    prompt = f"""Given the current state '{request.current_state}' and the following possible transitions:

{transitions_str}

Which transition should be taken? Respond with just the target state name."""

    response = await acompletion(
        model="gpt-4o-mini",
        messages=[{"role": "user", "content": prompt}],
        metadata=LLMTraceMetadata(
            trace_name="decide_next_state",
            session_id=request.session_id,
            custom_fields={
                "organization_id": str(request.organization_id),
                "workflow_id": activity.info().workflow_id,
            },
        ),
        temperature=0,
    )

    next_state: str = response.message_content.strip()

    # Validate response is a valid target state
    valid_targets = [
        target
        for transition in request.transition_events
        for target in transition.targets
    ]
    if next_state not in valid_targets:
        raise ValueError(f"Invalid next state: {next_state}")

    return next_state
