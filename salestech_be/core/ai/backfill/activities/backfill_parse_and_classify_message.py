import sys
import uuid

from temporalio import activity, workflow

with workflow.unsafe.imports_passed_through():
    import asyncio

    from sqlalchemy.sql import text

    from salestech_be.core.ai.backfill.workflow_inputs.backfill_input import (
        BackfillInput,
    )
    from salestech_be.core.ai.email.activities.classify_message_and_update_metadata import (
        classify_message_and_update_metadata,
    )
    from salestech_be.core.ai.email.activities.parse_main_body_text_and_persist import (
        parse_main_body_text_and_persist,
    )
    from salestech_be.db.dao.message_metadata_repository import (
        MessageMetadataRepository,
    )
    from salestech_be.db.models.message import Message
    from salestech_be.ree_logging import get_logger
    from salestech_be.temporal.database import (
        get_or_init_db_engine,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    )

logger = get_logger(__name__)


@activity.defn
async def backfill_parse_and_classify_message(
    backfill_input: BackfillInput,
) -> str:
    """Backfill parse and classify messages that are missing main body text/html or metadata.

    Args:
        backfill_input: The input for the backfill workflow
    """
    force_reprocess = backfill_input.force_reprocess
    logger.info("getting db engine")

    db_engine = await get_or_init_db_engine()

    logger.info("getting message metadata repository")
    message_metadata_repository = MessageMetadataRepository(engine=db_engine)

    # Get count of messages that need processing
    count_query = text(
        """
        SELECT COUNT(m.*) FROM message m
        LEFT JOIN message_metadata mm ON m.id = mm.message_id
        WHERE m.deleted_at IS NULL
        AND (
            m.main_body_text IS NULL
            OR m.main_body_html IS NULL
            OR mm.id IS NULL
        )
        AND m.organization_id = :organization_id
        """
    ).bindparams(organization_id=backfill_input.organization_id)
    rows = await db_engine.all(count_query)
    count = rows[0][0]
    logger.info(f"Found {count} messages to process")

    pointer = 0
    batch_size = 1000

    while pointer < count:
        # Query for messages that need processing
        query = text(
            """
            SELECT m.* FROM message m
            LEFT JOIN message_metadata mm ON m.id = mm.message_id
            WHERE m.deleted_at IS NULL
            AND (
                m.main_body_text IS NULL
                OR m.main_body_html IS NULL
                OR mm.id IS NULL
            )
            ORDER BY m.created_at ASC
            LIMIT :batch_size
            """
        ).bindparams(batch_size=batch_size)

        logger.info("querying messages")
        rows = await db_engine.all(query)
        logger.info(f"Found {len(rows)} messages to process")
        try:
            messages = await Message.bulk_from_rows(rows=rows)
        except Exception as e:
            logger.error(f"Error processing message {e}")
            raise e

        for message in messages:
            try:
                logger.info(f"Processing message {message.id}")

                # Check if we need to parse main body text/html
                needs_parsing = (
                    force_reprocess
                    or message.main_body_text is None
                    or message.main_body_html is None
                )

                # Check if we need to classify and add metadata
                needs_classification = (
                    force_reprocess
                    or not await message_metadata_repository.get_latest_message_metadata_by_message_id(
                        organization_id=message.organization_id,
                        message_id=message.id,
                    )
                )

                if needs_parsing:
                    logger.info(f"Parsing main body text/html for message {message.id}")
                    await parse_main_body_text_and_persist(message=message)

                if needs_classification:
                    logger.info(f"Classifying message {message.id}")
                    await classify_message_and_update_metadata(message=message)

            except Exception as e:
                logger.error(f"Error processing message {message.id}: {e}")
                continue

        pointer += batch_size
        logger.info(f"Processed {pointer} - {pointer + batch_size} messages")

    logger.info(f"Processed {count} messages")
    return f"Processed {count} messages"


if __name__ == "__main__":
    logger.info("Starting backfill parse and classify message")

    logger.info(f"Script name: {sys.argv[0]}")
    if len(sys.argv) > 1:
        logger.info(f"Arguments: {sys.argv[1:]}")
        asyncio.run(
            backfill_parse_and_classify_message(
                BackfillInput(
                    organization_id=uuid.UUID(sys.argv[1]),
                    force_reprocess=False,
                )
            )
        )
    else:
        logger.info("No arguments provided")
