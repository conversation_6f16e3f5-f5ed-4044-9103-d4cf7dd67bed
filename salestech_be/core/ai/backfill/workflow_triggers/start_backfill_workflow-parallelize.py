import sys

from temporalio import workflow

with workflow.unsafe.imports_passed_through():
    import asyncio
    import time

    from sqlalchemy.sql import text

    from salestech_be.common.stats.metric import custom_metric
    from salestech_be.core.ai.backfill.workflow_inputs.backfill_input import (
        BackfillInput,
    )
    from salestech_be.core.ai.backfill.workflows.backfill_workflow import (
        BackfillWorkflow,
    )
    from salestech_be.integrations.temporal.client import get_temporal_client
    from salestech_be.integrations.temporal.config import DEFAULT_TASK_QUEUE
    from salestech_be.ree_logging import get_logger
    from salestech_be.temporal.database import (
        get_or_init_db_engine,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    )

logger = get_logger(__name__)


async def start_backfill_workflow(
    backfill_input: BackfillInput,
) -> None:
    """Start the workflow to backfill messages.

    Args:
        backfill_input: The input for the backfill workflow
    """
    process_start_time = time.perf_counter()
    client = await get_temporal_client()
    custom_metric.timing(
        metric_name="backfill_get_temporal_client",
        value=(time.perf_counter() - process_start_time) * 1000,
        tags=[
            "backfill_flow:start_backfill_workflow",
        ],
    )

    workflow_id = BackfillWorkflow.get_workflow_id(backfill_input.organization_id)

    logger.info(
        f"Starting backfill workflow for organization {backfill_input.organization_id}",
        extra={
            "workflow_id": workflow_id,
        },
    )

    process_start_time = time.perf_counter()
    try:
        await client.start_workflow(
            BackfillWorkflow.run,
            id=workflow_id,
            task_queue=DEFAULT_TASK_QUEUE,
            args=[backfill_input],
        )
        custom_metric.timing(
            metric_name="backfill_workflow_start_workflow",
            value=(time.perf_counter() - process_start_time) * 1000,
            tags=[
                "workflow:backfill_workflow",
                "status:success",
            ],
        )
    except Exception as e:
        custom_metric.timing(
            metric_name="backfill_workflow_start_workflow",
            value=(time.perf_counter() - process_start_time) * 1000,
            tags=[
                "workflow:backfill_workflow",
                "status:error",
            ],
        )
        logger.error(
            "Failed to start backfill workflow",
            exc_info=e,
            extra={
                "workflow_id": workflow_id,
            },
        )
        raise


async def start_backfill_workflow_for_all_organizations(
    force_reprocess: bool = False,
) -> None:
    logger.info("Starting backfill workflow for all organizations")

    db_engine = await get_or_init_db_engine()

    count_query = text(
        """
        SELECT count(*) FROM organization
        """
    )
    rows = await db_engine.all(count_query)
    count = rows[0][0]
    logger.info(f"Found {count} organizations to process")

    organization_query = text(
        """
        SELECT id FROM organization
        """
    )
    rows = await db_engine.all(organization_query)
    organizations = [row[0] for row in rows]
    logger.info(f"Found {len(organizations)} organizations to process")

    for organization in organizations:
        await start_backfill_workflow(
            BackfillInput(organization_id=organization, force_reprocess=force_reprocess)
        )


if __name__ == "__main__":
    logger.info(f"Script name: {sys.argv[0]}")
    if len(sys.argv) > 1:
        logger.info(f"Force reprocess: {sys.argv[1]}")
        if sys.argv[1] == "force_reprocess=true":
            asyncio.run(
                start_backfill_workflow_for_all_organizations(force_reprocess=True)
            )
        elif sys.argv[1] == "force_reprocess=false":
            asyncio.run(
                start_backfill_workflow_for_all_organizations(force_reprocess=False)
            )
        else:
            logger.info("Invalid argument")
            logger.info(
                "python start_backfill_workflow-parallelize.py force_reprocess=[true|false]"
            )
    else:
        logger.info(
            "python start_backfill_workflow-parallelize.py force_reprocess=[true|false]"
        )
