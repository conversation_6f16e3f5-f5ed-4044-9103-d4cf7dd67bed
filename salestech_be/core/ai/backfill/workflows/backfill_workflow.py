import uuid

from temporalio import workflow
from temporalio.common import RetryPolicy

with workflow.unsafe.imports_passed_through():
    from datetime import datetime, timedelta

    import pytz

    from salestech_be.core.ai.backfill.activities.backfill_parse_and_classify_message import (
        backfill_parse_and_classify_message,
    )
    from salestech_be.core.ai.backfill.workflow_inputs.backfill_input import (
        BackfillInput,
    )
    from salestech_be.integrations.temporal.config import DEFAULT_TASK_QUEUE


@workflow.defn
class BackfillWorkflow:
    @workflow.run
    async def run(self, backfill_input: BackfillInput) -> None:
        await workflow.execute_activity(
            backfill_parse_and_classify_message,
            backfill_input,
            task_queue=DEFAULT_TASK_QUEUE,
            retry_policy=RetryPolicy(maximum_attempts=3),
            start_to_close_timeout=timedelta(weeks=1),  # Longer timeout for backfill
        )

    @staticmethod
    def get_workflow_id(organization_id: uuid.UUID) -> str:
        """Get a deterministic workflow ID for the organization.

        Args:
            organization_id: The organization ID

        Returns:
            A deterministic workflow ID
        """
        return f"backfill-workflow::{organization_id}::@{datetime.now(pytz.utc).strftime('%Y-%m-%d-%H-%M-%S')}"
