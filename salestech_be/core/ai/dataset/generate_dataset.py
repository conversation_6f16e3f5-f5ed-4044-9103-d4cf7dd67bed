import sys

from langfuse import <PERSON><PERSON>

from salestech_be.ree_logging import get_logger
from salestech_be.settings import settings

VALID_ARGS_LENGTH = 2
logger = get_logger(__name__)


def main(dataset_name: str) -> None:
    langfuse = Langfuse(
        public_key=settings.langfuse_public_key.get_secret_value(),
        secret_key=settings.langfuse_secret_key.get_secret_value(),
        host=settings.langfuse_host,
    )
    langfuse.auth_check()

    datasets = langfuse.client.datasets.list()
    logger.debug(f"{datasets=}")
    logger.debug(f"accessing langfuse dataset: {dataset_name}")
    dataset = langfuse.client.datasets.get(dataset_name=dataset_name)
    logger.debug(f"{dataset=}")

    dataset = langfuse.get_dataset(name=dataset_name)
    logger.debug(f"{dataset=}")

    traces = langfuse.fetch_traces()
    logger.debug(f"{traces=}")

    langfuse.create_dataset_item(
        dataset_name=dataset_name,
        # any python object or value, optional
        input={"text": "hello world michael"},
        # any python object or value, optional
        expected_output={"text": "hello world michael"},
        # metadata, optional
        metadata={
            "model": "llama3",
        },
    )


if __name__ == "__main__":
    if len(sys.argv) != VALID_ARGS_LENGTH:
        logger.info("Usage: python generate_dataset.py <dataset_name>")
        sys.exit(1)

    dataset_name = sys.argv[1]
    main(dataset_name=dataset_name)
