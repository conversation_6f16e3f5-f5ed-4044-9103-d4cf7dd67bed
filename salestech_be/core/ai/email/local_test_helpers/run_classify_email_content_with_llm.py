#!/usr/bin/env python3

import asyncio
import uuid

from salestech_be.core.ai.email.activities.classify_message_and_update_metadata import (
    ParticipantType,
)
from salestech_be.core.ai.email.llm_calls.classify_email_content_with_llm import (
    classify_email_content_with_llm,
)
from salestech_be.ree_logging import get_logger

logger = get_logger(__name__)


async def main() -> None:
    # Create input
    email_content = """
    On it!
    """
    sender_type = ParticipantType.UNKNOWN
    recipient_type = ParticipantType.USER
    organization_id = uuid.UUID("7e1e5279-4fd4-4f1d-a01f-3aa68a276bed")
    langfuse_session_id = f"cdc:EMAIL:{uuid.uuid4()}"

    # Run the activity directly
    logger.info(f"Message: {email_content}")
    result = await classify_email_content_with_llm(
        email_content=email_content,
        sender_type=sender_type,
        recipient_type=recipient_type,
        is_part_of_sales_thread=False,
        organization_id=organization_id,
        langfuse_session_id=langfuse_session_id,
    )
    logger.info(f"Classification result: {result}")


if __name__ == "__main__":
    asyncio.run(main())
