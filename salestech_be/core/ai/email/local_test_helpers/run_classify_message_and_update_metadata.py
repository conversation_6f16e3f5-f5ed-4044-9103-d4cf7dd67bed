#!/usr/bin/env python3

import asyncio
import uuid

from salestech_be.core.ai.email.activities.classify_message_and_update_metadata import (
    classify_message_and_update_metadata,
)
from salestech_be.db.dao.message_repository import MessageRepository
from salestech_be.ree_logging import get_logger
from salestech_be.temporal.database import (
    get_or_init_db_engine,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)

logger = get_logger(__name__)


async def main() -> None:
    # Initialize DB engine
    db_engine = await get_or_init_db_engine()

    # Create input
    message_repository = MessageRepository(engine=db_engine)
    message = await message_repository.get_by_message_id(
        message_id=uuid.UUID("43be95a6-bf57-4b28-9f4e-9b7e80ca2034"),
        organization_id=uuid.UUID("7e1e5279-4fd4-4f1d-a01f-3aa68a276bed"),
    )

    # Run the activity directly
    logger.info(f"Message: {message.body_text}")
    result = await classify_message_and_update_metadata(
        message=message,
        is_dry_run=True,
    )
    logger.info(f"Classification result: {result}")


if __name__ == "__main__":
    asyncio.run(main())
