from salestech_be.common.events import DomainEnrichedCDCEvent
from salestech_be.core.account.types_v2 import AccountV2
from salestech_be.core.email.insight.events import NewEmailEvent
from salestech_be.core.meeting.types.meeting_types_v2 import MeetingV2
from salestech_be.core.pipeline.types_v2 import PipelineV2
from salestech_be.db.models.account import Account
from salestech_be.db.models.meeting import Meeting
from salestech_be.db.models.pipeline import Pipeline

PipelineIntelTriggerEvent = (
    DomainEnrichedCDCEvent[Meeting, MeetingV2]
    | DomainEnrichedCDCEvent[Pipeline, PipelineV2]
    | DomainEnrichedCDCEvent[Account, AccountV2]
    | NewEmailEvent
)
