import json
from uuid import UUID

from salestech_be.common.ree_llm import (
    LLMTraceMetadata,
    ReeTraceMetadata,
    acompletion,
)
from salestech_be.core.ai.common.dataset_collection.write_to_csv import (
    write_to_csv,
)
from salestech_be.core.ai.prompt.langfuse_prompt_service import (
    get_langfuse_prompt_service,
)
from salestech_be.core.ai.prompt.schema import PromptEnum, PromptRequest
from salestech_be.core.prompt.types import PromptUseCase
from salestech_be.integrations.langchain.insights.schema import (
    Insight,
    InsightListFromLLM,
)
from salestech_be.ree_logging import get_logger

logger = get_logger()

langfuse_prompt_service = get_langfuse_prompt_service()


async def generate_objection_insights_from_conversation_llm_call(
    transcript: str,
    seller_contacts: str,
    seller_company_name: str,
    customer_contacts: str,
    customer_company_name: str,
    ends_at: str,
    organization_id: UUID,
    langfuse_session_id: str,
    min_num_insights: int,
    max_num_insights: int | None = None,
    output_path: str | None = None,
) -> list[Insight]:
    num_insights = (
        f"{min_num_insights} to {max_num_insights}"
        if max_num_insights
        else f"{min_num_insights}"
    )

    function_args = {
        "transcript": transcript,
        "seller_contacts": seller_contacts,
        "seller_company_name": seller_company_name,
        "customer_contacts": customer_contacts,
        "customer_company_name": customer_company_name,
        "ends_at": ends_at,
        "organization_id": organization_id,
        "langfuse_session_id": langfuse_session_id,
        "min_num_insights": min_num_insights,
        "max_num_insights": max_num_insights,
        "output_path": output_path,
    }

    prompt_variables = {
        "transcript": transcript,
        "seller_contacts": seller_contacts,
        "seller_company_name": seller_company_name,
        "customer_contacts": customer_contacts,
        "customer_company_name": customer_company_name,
        "ends_at": ends_at,
        "num_insights": num_insights,
    }

    prompt_obj = await langfuse_prompt_service.get_prompt(
        request=PromptRequest(
            prompt_name=PromptEnum.GENERATE_OBJECTION_INSIGHTS_FROM_CONVERSATION,
            variables=prompt_variables,
        )
    )

    response = await acompletion(
        model=prompt_obj.get_model(),
        messages=prompt_obj.messages,
        temperature=0,
        tools=[
            {
                "type": "function",
                "function": {
                    "name": "create_objection_insights",
                    "description": "A tool that creates one or more meeting objection insights with descriptions.",
                    "parameters": InsightListFromLLM.model_json_schema(),
                },
            }
        ],
        tool_choice="create_objection_insights",
        metadata=LLMTraceMetadata(
            prompt=prompt_obj.prompt_client,
            trace_name="insights.meeting_objections",
            session_id=langfuse_session_id,
            custom_fields=ReeTraceMetadata(
                organization_id=str(organization_id),
                prompt_use_case=PromptUseCase.OBJECTION_INSIGHTS,
            ),
        ),
    )

    if not response.choices:
        return []

    tool_calls = response.tool_calls
    if not tool_calls:
        return []

    objections = []
    for tool_call in tool_calls:
        if tool_call.function and tool_call.function.arguments:
            json_args = json.loads(tool_call.function.arguments)
            try:
                for insight in json_args.get("insights", []):
                    objections.append(
                        Insight(
                            insight=insight.get("insight", ""),
                            insight_name=insight.get("insight_name", ""),
                            line_numbers=insight.get("line_numbers", []),
                            keyword_tags=insight.get("keyword_tags", []),
                            start_timestamp=insight.get("start_timestamp", None),
                        )
                    )
            except Exception as e:
                logger.error(
                    "[meeting_insight_service] Error parsing tool call arguments",
                    error=e,
                )

    if output_path:
        write_to_csv(prompt_variables, objections, output_path)

    try:
        await langfuse_prompt_service.create_dataset_item(
            dataset_name=f"{PromptEnum.GENERATE_OBJECTION_INSIGHTS_FROM_CONVERSATION}-v0.1",
            dataset_input=function_args,
            expected_output={"objections": objections},
        )
    except Exception as e:
        logger.bind(
            dataset_name=f"{PromptEnum.GENERATE_OBJECTION_INSIGHTS_FROM_CONVERSATION}-v0.1"
        ).warning(f"Error creating dataset item: {e}")

    return objections
