import json
from uuid import UUID

from salestech_be.common.ree_llm import (
    LLMTraceMetadata,
    ReeTraceMetadata,
    acompletion,
)
from salestech_be.core.ai.prompt.langfuse_prompt_service import (
    get_langfuse_prompt_service,
)
from salestech_be.integrations.langchain.insights.schema import (
    SentimentInsight,
)
from salestech_be.core.ai.prompt.schema import PromptEnum, PromptRequest
from salestech_be.ree_logging import get_logger

from salestech_be.core.prompt.types import PromptUseCase
langfuse_prompt_service = get_langfuse_prompt_service()

logger = get_logger(__name__)

async def generate_sentiment_insights_from_conversation_llm_call(
    transcript: str,
    seller_contacts: str,
    seller_company_name: str,
    customer_contacts: str,
    customer_company_name: str,
    ends_at: str,
    organization_id: UUID,
    langfuse_session_id: str,
    min_num_insights: int,
    max_num_insights: int | None = None,
    output_path: str | None = None,
) -> list[SentimentInsight]:
    num_insights = (
        f"{min_num_insights} to {max_num_insights}"
        if max_num_insights
        else f"{min_num_insights}"
    )
    prompt_variables = {
        "transcript": transcript,
        "seller_contacts": seller_contacts,
        "seller_company_name": seller_company_name,
        "customer_contacts": customer_contacts,
        "customer_company_name": customer_company_name,
        "ends_at": ends_at,
        "num_insights": num_insights,
    }

    prompt_obj = await langfuse_prompt_service.get_prompt(
        request=PromptRequest(
            prompt_name=PromptEnum.GENERATE_SENTIMENT_INSIGHTS_FROM_CONVERSATION,
            variables=prompt_variables,
        )
    )

    response = await acompletion(
        model=prompt_obj.get_model(),
        messages=prompt_obj.messages,
        temperature=0,
        tools=[
            {
                "type": "function",
                "function": {
                    "name": "create_sentiment_insights",
                    "description": "A tool that creates one or more meeting sentiment insights with descriptions.",
                    "parameters": SentimentInsight.model_json_schema(),
                },
            }
        ],
        tool_choice="create_sentiment_insights",
        metadata=LLMTraceMetadata(
            trace_name="insights.meeting_sentiment",
            session_id=langfuse_session_id,
            custom_fields=ReeTraceMetadata(
                organization_id=str(organization_id),
                prompt_use_case=PromptUseCase.SENTIMENT_INSIGHTS,
            ),
        ),
    )

    if not response.choices:
        return []
    tool_calls = response.tool_calls
    if not tool_calls:
        return []

    sentiments = []

    for tool_call in tool_calls:
        if tool_call.function and tool_call.function.arguments:
            json_args = json.loads(tool_call.function.arguments)
            try:
                sentiments.append(
                    SentimentInsight(
                        insight_name=json_args.get("insight_name", ""),
                        insight=json_args.get("insight", ""),
                        polarity=json_args.get("polarity", None),
                        emotion=json_args.get("emotion", []),
                        line_numbers=json_args.get("line_numbers", []),
                        keyword_tags=json_args.get("keyword_tags", []),
                    )
                )
            except Exception as e:
                logger.error(
                    "[meeting_insight_service] Error parsing tool call arguments",
                    error=e,
                )

    return sentiments
