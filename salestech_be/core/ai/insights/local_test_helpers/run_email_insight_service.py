import asyncio
from uuid import UUID

from salestech_be.common.exception import (
    IllegalStateError,
)
from salestech_be.core.email.insight.email_insight_service import (
    get_email_insight_service_by_db,
)
from salestech_be.db.dao.thread_repository import ThreadRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.ree_logging import get_logger
from salestech_be.settings import settings

logger = get_logger(__name__)


async def run_email_insight_service(
    organization_id: UUID, global_thread_id: UUID
) -> str:
    engine = DatabaseEngine(url=str(settings.db_url), pool_size=100, max_overflow=500)
    thread_repository = ThreadRepository(engine=engine)
    global_thread = await thread_repository.get_global_thread_by_id(
        global_thread_id=global_thread_id, organization_id=organization_id
    )

    if not global_thread:
        raise IllegalStateError("No global thread found")

    email_insight_service = get_email_insight_service_by_db(db_engine=engine)

    await email_insight_service.generate_insight_for_global_thread(
        global_thread=global_thread,
        organization_id=organization_id,
        skip_if_generated_before=False,
    )

    return "done"


if __name__ == "__main__":
    asyncio.run(
        run_email_insight_service(
            organization_id=UUID("cce7b290-8a08-4904-a6c7-2b6613877cf5"),
            global_thread_id=UUID("c341ec78-9429-4ab0-a670-ff60e248ced4"),
        )
    )
