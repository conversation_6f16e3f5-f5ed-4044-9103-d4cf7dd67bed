from salestech_be.common.ree_llm import LLMTraceMetadata, acompletion
from salestech_be.core.ai.prompt.langfuse_prompt_service import (
    get_langfuse_prompt_service,
)
from salestech_be.core.ai.prompt.schema import PromptEnum, PromptRequest, PromptType
from salestech_be.ree_logging import get_logger

logger = get_logger()

langfuse_prompt_service = get_langfuse_prompt_service()


async def generate_meeting_agenda_llm_call(
    organization_id: str,
    meeting_title: str,
    meeting_description: str,
    meeting_start_time: str,
    meeting_end_time: str,
    meeting_sellers: str,
    meeting_customers: str,
) -> str:
    prompt_variables = {
        "organization_id": organization_id,
        "meeting_title": meeting_title,
        "meeting_description": meeting_description,
        "meeting_start_time": meeting_start_time,
        "meeting_end_time": meeting_end_time,
        "meeting_sellers": meeting_sellers,
        "meeting_customers": meeting_customers,
    }

    prompt_response = await langfuse_prompt_service.get_prompt(
        request=PromptRequest(
            prompt_name=PromptEnum.AI_MEETING_GENERATE_MEETING_AGENDA,
            variables=prompt_variables,
            prompt_type=PromptType.CHAT,
        )
    )
    completion_response = await acompletion(
        model=prompt_response.get_model(),
        messages=prompt_response.messages,
        max_completion_tokens=5000,
        metadata=LLMTraceMetadata(
            prompt=prompt_response.prompt_client,
            trace_name=PromptEnum.AI_MEETING_GENERATE_MEETING_AGENDA,
            custom_fields={
                "organization_id": organization_id,
            },
        ),
    )
    agenda: str = completion_response.message_content

    try:
        await langfuse_prompt_service.create_dataset_item(
            dataset_name=f"{PromptEnum.AI_MEETING_GENERATE_MEETING_AGENDA}-v0.1",
            dataset_input=prompt_variables,
            expected_output={"agenda": agenda},
        )
    except Exception as e:
        logger.bind(
            dataset_name=f"{PromptEnum.AI_MEETING_GENERATE_MEETING_AGENDA}-v0.1"
        ).warning(f"Error creating dataset item: {e}")

    return agenda
