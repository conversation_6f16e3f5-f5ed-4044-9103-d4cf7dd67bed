METRIC_DEFINITION = """A metric is a measurable value that the proposed product/solution provides to the customer. A metric needs to be applicable to the specific organization that is being sold to. Metrics require discovery from the salesperson's side to identify what parts the product would be most useful to the customer. Metrics should be things the potential customer cares about or mentions, not something the seller states;  unless it is re-affirming the metric.

Key identifiers:
- Organization-specific measurements
- Current performance indicators
- Desired improvement targets
- Business impact measurements

Example indicators:
- "Our current efficiency is..."
- "We need to improve X by..."
- "This would help us achieve..." """

DECISION_CRITERIA_DEFINITION = """A decision criteria is the criteria by which a decision to move forward with the product/solution will be judged against. There are several types of criteria worth noting. Technical criteria, meaning does your solution technically meet the requirements to make it feasible for the customer's usage. Economic criteria, meaning how financially viable your solution is for the customer. Relationship criteria, meaning how closely do the values or both the seller and customer's organizations align. These are just a few examples of what criteria could be useful.

Key identifiers:
- Technical requirements
- Financial constraints
- Organizational alignment needs
- Success metrics

Example indicators:
- "The solution must meet..."
- "We need to ensure..."
- "It should align with our..."

Note: For budget amounts, only provide exact decimal numbers (e.g., 1000.50, 2000, 5000.00). Do not use ranges, currency symbols, or text descriptions.
"""

DECISION_PROCESS_DEFINITION = """The decision process is the series of steps that the buyer will follow to make a decision on whether to use the seller's product/solution or not. There are two main types of process steps, those being technical validation, and business approval. Each one of these types can have multiple steps inside of them, these steps will very customer to customer.

Key identifiers:
- Evaluation phases
- Approval stages
- Technical review requirements
- Business case requirements

Example indicators:
- "First we need to validate..."
- "Then it goes through..."
- "Final approval requires..." """

PAPER_PROCESS_DEFINITION = """The paper process is the series of steps that the buyer will follow to go from their decision to a final signature. This mainly involves all the documents that need to be looked at and approved. This usually only begins being defined after the customer has made it clear to the buyer that they will indeed use the buyer's product/solution. However, if the buyer gives indications early on in the deal as to the general paper process their company follows, they can begin being defined then as well.

Key components:
- Required documentation
- Legal review steps
- Contract approval process
- Procurement procedures

Key identifiers:
- Document requirements
- Review cycles
- Signature requirements
- Compliance checks

Example indicators:
- "We need to prepare..."
- "Legal needs to review..."
- "Procurement requires..." """

IDENTIFIED_PAIN_DEFINITION = """An identified pain is a specific problem the customer has with their business that is serious enough to where a solution is needed. A pain point could be an operational inefficiency that results in financial loss or impact usage. A buyer's pain point allows the seller to understand the parts of their product/solution the buyer would be most interested in.

Categories:
- Operational pain
- Financial pain
- Technical pain
- Strategic pain

Key identifiers:
- Problem statements
- Impact descriptions
- Urgency indicators
- Cost of inaction

Example indicators:
- "We're struggling with..."
- "This is costing us..."
- "We can't continue with..." """

COMPETITION_DEFINITION = """The competition is any person, vendor, or initiative competing for the same funds or resources as the seller is. A competitor does not just have to be another organization selling a similar product, it could be a completely alternative solution the buyer is considering, such as resolving the issue by building an internal product/solution instead of purchasing an external one.

Categories:
- Direct competitors
- Internal solutions
- Alternative approaches
- Status quo

Key identifiers:
- Competitor names
- Alternative solutions
- Build vs. buy considerations
- Comparison points

Example indicators:
- "We're also looking at..."
- "We could build this internally..."
- "Compared to X, your solution..." """

# Dictionary containing all definitions for easy reference
MEDDPICC_DEFINITIONS = {
    "metric": METRIC_DEFINITION,
    "decision_criteria": DECISION_CRITERIA_DEFINITION,
    "decision_process": DECISION_PROCESS_DEFINITION,
    "paper_process": PAPER_PROCESS_DEFINITION,
    "identified_pain": IDENTIFIED_PAIN_DEFINITION,
    "competition": COMPETITION_DEFINITION,
}
