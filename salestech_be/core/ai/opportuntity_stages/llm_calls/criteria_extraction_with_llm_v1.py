from uuid import UUID

from pydantic import BaseModel

from salestech_be.common.ree_llm import (
    LLMTraceMetadata,
    ReeTraceMetadata,
    acompletion,
)
from salestech_be.core.ai.prompt.langfuse_prompt_service import (
    get_langfuse_prompt_service,
)
from salestech_be.core.ai.prompt.schema import PromptEnum, PromptRequest
from salestech_be.core.prompt.types import PromptUseCase
from salestech_be.ree_logging import get_logger

langfuse_prompt_service = get_langfuse_prompt_service()
logger = get_logger(__name__)


class CriteriaCompletion(BaseModel):
    criteria_name: str
    is_completed: bool
    explanation: str


class CriteriaCompletionList(BaseModel):
    criteria_completions: list[CriteriaCompletion]


async def extract_criteria_with_llm(
    session_id: str,
    organization_id: UUID,
    sales_methodology: str,
    opportunity: str,
    stage: str,
    criteria_details: str,
    context: str,
) -> CriteriaCompletionList:
    """Extracts criteria extraction results using LLM.
    Args:
        request: The request to extract criteria from

    Returns:
        CriteriaCompletionList containing criteria completion
    """

    prompt_variables = {
        "sales_methodology": sales_methodology,
        "opportunity": opportunity,
        "stage": stage,
        "criteria_details": criteria_details,
        "context": context,
    }

    prompt_obj = await langfuse_prompt_service.get_prompt(
        request=PromptRequest(
            prompt_name=PromptEnum.EXTRACT_OPPORTUNITY_STAGE_CRITERIA,
            variables=prompt_variables,
        )
    )

    llm_response = await acompletion(
        model=prompt_obj.get_model(),
        messages=prompt_obj.messages,
        temperature=0,
        tools=[
            {
                "type": "function",
                "function": {
                    "name": "determine_criteria_completion",
                    "description": "A tool that determines if the criteria is completed.",
                    "parameters": CriteriaCompletionList.model_json_schema(),
                },
            }
        ],
        tool_choice="determine_criteria_completion",
        metadata=LLMTraceMetadata(
            prompt=prompt_obj.prompt_client,
            trace_name=PromptEnum.EXTRACT_OPPORTUNITY_STAGE_CRITERIA,
            session_id=session_id,
            custom_fields=ReeTraceMetadata(
                organization_id=str(organization_id),
                prompt_use_case=PromptUseCase.EXTRACT_OPPORTUNITY_STAGE_CRITERIA,
            ),
        ),
    )

    if llm_response.choices[0].message.tool_calls is None:
        return CriteriaCompletionList(
            criteria_completions=[],
        )

    content = llm_response.choices[0].message.tool_calls[0].function.arguments
    return CriteriaCompletionList.model_validate_json(content)
