import asyncio
from uuid import UUID

from salestech_be.core.ai.opportuntity_stages.llm_calls.criteria_update_with_llm import (
    criteria_update_with_llm,
)
from salestech_be.core.opportunity_stage_criteria.criteria_types import (
    CriteriaExtractionSourceObjectId,
    CriteriaExtractionSourceObjectType,
    CriteriaUpdateRequest,
    LLMCriteriaUpdateRequest,
)
from salestech_be.core.opportunity_stage_criteria.standard_criteria.decision_criteria import (
    DecisionCriteriaContextUpdateWithRecTypes,
    DecisionCriteriaItemUpdateMulti,
)


async def run_criteria_extraction_with_llm() -> None:
    update_request = LLMCriteriaUpdateRequest(
        organization_id=UUID("cce7b290-8a08-4904-a6c7-2b6613877cf5"),
        pipeline_id=UUID("e69b6614-f53c-43ab-b651-1e96e0390939"),
        source_object=CriteriaExtractionSourceObjectId(
            object_type=CriteriaExtractionSourceObjectType.EMAIL,
            object_id=UUID("df3b214d-260a-476c-9688-a5fecbaa295a"),
        ),
        request=CriteriaUpdateRequest(
            context_update_type=DecisionCriteriaContextUpdateWithRecTypes,
            items_update_type=DecisionCriteriaItemUpdateMulti,
            previous_context=None,
        ),
    )
    await criteria_update_with_llm(criteria_update_request=update_request)


if __name__ == "__main__":
    asyncio.run(run_criteria_extraction_with_llm())
