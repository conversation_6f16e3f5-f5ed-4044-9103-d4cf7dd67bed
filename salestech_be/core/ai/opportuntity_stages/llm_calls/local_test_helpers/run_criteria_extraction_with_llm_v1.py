import asyncio
import uuid

from salestech_be.core.ai.opportuntity_stages.llm_calls.criteria_extraction_with_llm_v1 import (
    CriteriaCompletionList,
    extract_criteria_with_llm,
)


async def run_criteria_extraction_with_llm() -> CriteriaCompletionList:
    session_id = "test"
    organization_id = uuid.UUID("cce7b290-8a08-4904-a6c7-2b6613877cf5")
    sales_methodology = """
        <sales_methodology>
            <name>
                Airplanes
            </name>
        </sales_methodology>
        <stages>
            <stage>
                <name>Discovery</name>
            </stage>
            <stage>
                <name>Scope</name>
            </stage>
            <stage>
                <name>Negotiate</name>
            </stage>
            <stage>
                <name>Close</name>
            </stage>
        </stages>
    """
    opportunity = "Name: Airplane Corp.\nValue: 100,000"
    stage = """
        <stage>
            <name>Discovery</name>
        </stage>
    """
    criteria_details = """
        <criteria_list>
            <criteria>
            <name>
                Key decision makers identified
            </name>
            <is_required>
                True
            </is_required>
            <is_completed>
            False
            </is_completed>
            </criteria>
            <criteria>
            <name>
                Implementation timeline discussed
            </name>
            <is_required>
                False
            </is_required>
            <is_completed>
            False
            </is_completed>
            </criteria>
            <criteria>
            <name>
                Success criteria defined
            </name>
            <is_required>
                True
            </is_required>
            <is_completed>
            False
            </is_completed>
            </criteria>
            <criteria>
            <name>
                Clearly defined pain points
            </name>
            <is_required>
                True
            </is_required>
            <is_completed>
            False
            </is_completed>
            </criteria>
            <criteria>
            <name>
                Budget range identified
            </name>
            <is_required>
                True
            </is_required>
            <is_completed>
            False
            </is_completed>
            </criteria>
        </criteria_list>
    """
    context = """
        Hi Mike,

        Appreciate the call today. Airplanes looks to have a compelling product. I'll need to speak with Bob about if we have budget for this. He's the CFO of the company.

        The product looks great though! We have problems with stream lining our manufacturing operations and this could improve our workflows.

        Best,
        Rodaan
    """

    return await extract_criteria_with_llm(
        session_id=session_id,
        organization_id=organization_id,
        sales_methodology=sales_methodology,
        opportunity=opportunity,
        stage=stage,
        criteria_details=criteria_details,
        context=context,
    )


if __name__ == "__main__":
    asyncio.run(run_criteria_extraction_with_llm())
