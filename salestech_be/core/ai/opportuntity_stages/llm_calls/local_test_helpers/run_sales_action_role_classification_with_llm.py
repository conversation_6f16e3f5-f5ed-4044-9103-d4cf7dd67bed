import asyncio
from uuid import UUID

from salestech_be.core.ai.opportuntity_stages.llm_calls.contact_pipeline_role_classification_with_llm import (
    ContactPipelineRoleClassificationRequest,
    contact_pipeline_role_classification_with_llm,
)
from salestech_be.core.opportunity_stage_criteria.criteria_types import (
    CriteriaExtractionSourceObjectId,
    CriteriaExtractionSourceObjectType,
)


async def run_sales_action_role_classification_with_llm() -> None:
    # sales_action_request = SalesActionClassificationRequest(
    #     organization_id=UUID("cce7b290-8a08-4904-a6c7-2b6613877cf5"),
    #     pipeline_id=UUID("e69b6614-f53c-43ab-b651-1e96e0390939"),
    #     source_object=CriteriaExtractionSourceObjectId(
    #         object_type=CriteriaExtractionSourceObjectType.EMAIL,
    #         object_id=UUID("27640a96-5b3b-4877-bf3c-98a337040d74"),
    #     ),
    #     current_sales_action_types=[StandardSalesActionType.INTRO],
    # )
    # result = await sales_action_classification_with_llm(sales_action_request)
    # print(LLMSalesActionClassificationResult.model_dump_json(result, indent=2))

    contact_pipeline_role_request = ContactPipelineRoleClassificationRequest(
        organization_id=UUID("cce7b290-8a08-4904-a6c7-2b6613877cf5"),
        pipeline_id=UUID("e69b6614-f53c-43ab-b651-1e96e0390939"),
        source_object=CriteriaExtractionSourceObjectId(
            object_type=CriteriaExtractionSourceObjectType.EMAIL,
            object_id=UUID("27640a96-5b3b-4877-bf3c-98a337040d74"),
        ),
    )
    await contact_pipeline_role_classification_with_llm(contact_pipeline_role_request)
    # print(LLMContactPipelineRoleClassificationResult.model_dump_json(contact_pipeline_role_result, indent=2))


if __name__ == "__main__":
    asyncio.run(run_sales_action_role_classification_with_llm())
