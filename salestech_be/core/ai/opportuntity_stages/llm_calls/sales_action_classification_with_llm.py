from uuid import UUID

from anthropic.types import (
    CitationContentBlockLocation,
    ContentBlockParam,
    Message,
    MessageParam,
    TextCitation,
)
from pydantic import BaseModel

from salestech_be.common.core_crm.sales_action import (
    StandardSalesActionRequirement,
    StandardSalesActionType,
)
from salestech_be.common.ree_llm import (
    LLMTraceMetadata,
    ReeTraceMetadata,
    anthropic_completion,
)
from salestech_be.core.ai.common.helpers.prompt_context_info_retrieval import (
    get_seller_contacts_prompt_input,
)
from salestech_be.core.ai.common.llm_types import ModelTypes
from salestech_be.core.ai.common.types import (
    EmailAugmented,
    IntelContext,
    MeetingAugmented,
    SourceContext,
    VoiceCallAugmented,
)
from salestech_be.core.ai.opportuntity_stages.llm_calls.criteria_extraction_with_llm import (
    build_block_messages,
    convert_source_object_to_intel_context,
)
from salestech_be.core.ai.opportuntity_stages.utils.multi_meeting_transcript_helpers import (
    add_criteria_multi_meeting_citation,
)
from salestech_be.core.citation.service.citation_service import (
    CitationService,
    get_citation_service,
)
from salestech_be.core.email.global_email.global_message_ai_rec_service import (
    PatchGlobalMessageSalesActionsRequest,
    PatchGlobalMessageSalesActionsRequestFields,
)
from salestech_be.core.meeting.meeting_ai_rec_service import (
    PatchMeetingSalesActionsRequest,
    PatchMeetingSalesActionsRequestFields,
)
from salestech_be.core.opportunity_stage_criteria.criteria_types import (
    CriteriaCitation,
    CriteriaExtractionSourceObjectId,
    FieldCitationPair,
    FieldInfo,
    KeepCriteriaChange,
)
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.citation import (
    CitationForObjectType,
    CitationSourceType,
    EmailCitationMetadata,
    MeetingCitationMetadata,
)
from salestech_be.db.models.crm_ai_rec import CrmAIRecType
from salestech_be.ree_logging import get_logger
from salestech_be.settings import settings
from salestech_be.temporal.database import (
    get_or_init_db_engine,  # fmt: skip  # tach-ignore
)

logger = get_logger(__name__)


class SalesActionClassificationRequest(BaseModel):
    organization_id: UUID
    pipeline_id: UUID
    source_object: CriteriaExtractionSourceObjectId
    current_sales_action_types: list[StandardSalesActionType] | None = None
    sales_action_requirements: list[StandardSalesActionRequirement] | None = None


class LLMPatchMeetingSalesActionsRequest(BaseModel):
    request: PatchMeetingSalesActionsRequest
    citations: list[CriteriaCitation] | None = None
    field_update_types: dict[PatchMeetingSalesActionsRequestFields, CrmAIRecType] | None


class LLMPatchGlobalMessageSalesActionsRequest(BaseModel):
    request: PatchGlobalMessageSalesActionsRequest
    citations: list[CriteriaCitation] | None = None
    field_update_types: (
        dict[PatchGlobalMessageSalesActionsRequestFields, CrmAIRecType] | None
    )


class LLMSalesActionClassificationResult(BaseModel):
    patch_sales_actions_request: (
        LLMPatchMeetingSalesActionsRequest
        | LLMPatchGlobalMessageSalesActionsRequest
        | None
    ) = None


async def sales_action_classification_with_llm(
    request: SalesActionClassificationRequest,
) -> LLMSalesActionClassificationResult:
    langfuse_session_id = f"sales_action_classification:{request.source_object.object_type}:{request.source_object.object_id}"
    intel_context: IntelContext = await convert_source_object_to_intel_context(
        organization_id=request.organization_id,
        pipeline_id=request.pipeline_id,
        langfuse_session_id=langfuse_session_id,
        source_object=request.source_object,
    )
    context_messages = build_block_messages(intel_context)
    if not context_messages:
        logger.warning("No messages found for primary object")
        return LLMSalesActionClassificationResult()
    seller_contacts = await get_seller_contacts_prompt_input(
        intel_context, request.organization_id
    )
    llm_messages = build_sales_action_classification_prompt(
        context_messages,
        request.current_sales_action_types,
        request.sales_action_requirements,
        seller_contacts,
    )

    llm_response: Message = await anthropic_completion(
        model=ModelTypes.CLAUDE_3_7_SONNET_20250219,
        max_tokens=8192,
        messages=llm_messages,
        tools=[
            {
                "input_schema": LLMSalesActionClassificationResult.model_json_schema(),
                "name": "update_sales_action_type",
                "description": "A tool that updates the sales action and role classification from the given provided context.",
            }
        ],
        metadata=LLMTraceMetadata(
            trace_name="opportunity_stages.sales_action_role_classification",
            session_id=langfuse_session_id,
            custom_fields=ReeTraceMetadata(
                organization_id=str(request.organization_id),
            ),
        ),
    )
    if not llm_response.content or llm_response.content[-1].type != "tool_use":
        return LLMSalesActionClassificationResult()
    response = LLMSalesActionClassificationResult.model_validate(
        llm_response.content[-1].input
    )
    if not response.patch_sales_actions_request:
        logger.info("No sales action types to patch")
        return LLMSalesActionClassificationResult()
    db_engine: DatabaseEngine = await get_or_init_db_engine()
    citation_service = get_citation_service(db_engine)
    patched_sales_action_types: list[StandardSalesActionType] = []
    if isinstance(
        response.patch_sales_actions_request.request.sales_action_types, list
    ):
        patched_sales_action_types = (
            response.patch_sales_actions_request.request.sales_action_types
        )
    patch_request_citations: list[CriteriaCitation] = []
    for patched_sales_action_type in patched_sales_action_types:
        if patched_sales_action_type in (request.current_sales_action_types or []):
            continue
        citation = await create_sales_action_classification_citation(
            messages=context_messages,
            current_sales_action_types=request.current_sales_action_types,
            sales_action_requirements=request.sales_action_requirements,
            sales_action_types_change=patched_sales_action_type,
            langfuse_session_id=langfuse_session_id,
            organization_id=request.organization_id,
            seller_contacts=seller_contacts,
        )
        if citation:
            criteria_citations = (
                await add_sales_action_role_classification_citation_to_db(
                    source_context=intel_context,
                    field_name="sales_action_types",
                    for_object_type=CitationForObjectType.SALES_ACTION,
                    organization_id=request.organization_id,
                    citation_service=citation_service,
                    citation=citation,
                )
            )
            patch_request_citations.extend(criteria_citations)
        else:
            # Remove the sales action type from the current sales action types
            patched_sales_action_types.remove(patched_sales_action_type)
    response.patch_sales_actions_request.citations = patch_request_citations
    return response


def build_sales_action_classification_prompt(
    messages: list[str],
    current_sales_action_types: list[StandardSalesActionType] | None,
    sales_action_requirements: list[StandardSalesActionRequirement] | None,
    seller_contacts: str,
) -> list[MessageParam]:
    message_content: list[ContentBlockParam] = [
        {
            "type": "document",
            "source": {
                "type": "content",
                "content": [
                    {
                        "type": "text",
                        "text": text,
                    }
                    for text in messages
                ],
            },
            "title": "Context",
            "context": "This is the source content to extract sales actions from.",
            "citations": {"enabled": True},
        },
        {
            "type": "text",
            "text": f"""This is the information about the seller contacts: {seller_contacts}. Anyone in the source content that is not in this list is a buyer contact.
            This is the list of all sales actions currently associated with the activity {current_sales_action_types}. Make sure to take these into account when updating the sales action types.
            This is the list of all the standard sales action requirements for the activity {sales_action_requirements}. The description indicates the expectation for the sales action to be included in the sales action types.
            The sales action types are regarding the actions the seller took in this activity, and not for what happened in the past or what has been scheduled for or expected to happen in the future.
            Since the sales action types are based on the seller's action, the buyer's responses are used for context, but not for the sales action types.
            Only use what is explicity said by the seller in the source content, and not anything that is implied or inferred.
            Given the provided context, please classify the sales action types of this activity.
            Follow these instructions in the order they are given:
            - Classify the sales action types for the given context, if you are going to add any sales action type, please explain why you are adding this type. Do not populate the citations field in the response.
            """,
        },
    ]
    message_params: list[MessageParam] = [
        {
            "role": "user",
            "content": message_content,
        }
    ]
    return message_params


async def create_sales_action_classification_citation(
    messages: list[str],
    current_sales_action_types: list[StandardSalesActionType] | None,
    sales_action_requirements: list[StandardSalesActionRequirement] | None,
    sales_action_types_change: StandardSalesActionType,
    langfuse_session_id: str,
    organization_id: UUID,
    seller_contacts: str,
) -> TextCitation | None:
    result = await anthropic_completion(
        model=ModelTypes.CLAUDE_3_7_SONNET_20250219,
        max_tokens=8192,
        messages=[
            {
                "role": "user",
                "content": [
                    {
                        "type": "document",
                        "source": {
                            "type": "content",
                            "content": [
                                {
                                    "type": "text",
                                    "text": text,
                                }
                                for text in messages
                            ],
                        },
                        "title": "Context",
                        "context": "This is the source content to extract sales action classification from.",
                        "citations": {"enabled": True},
                    },
                    {
                        "type": "text",
                        "text": f"""
                        This is the information about the seller contacts: {seller_contacts}. Anyone in the source content that is not in this list is a buyer contact.
                        This is the list of all sales actions currently associated with the activity {current_sales_action_types}.
                        This is the list of all the standard sales action requirements for the activity {sales_action_requirements}. The description indicates the expectation for the sales action to be included in the sales action types.
                        From this activity, there has been an update to the existing sales action types to include a new type, {sales_action_types_change}.
                        Check if that given the context, the change is valid. Note that the sales action types are regarding the actions the seller took in this activity, and not for what happened in the past or what has been scheduled for or expected to happen in the future.
                        Since the sales action types are based on the seller's action, the buyer's responses are used for context, but not for the sales action types.
                        If so, please provide one citation for where this change could have come from. Only one citation is necessary, the most relevant one.
                        If you cannot find a citation, that means this update is not valid, and you should return None.
                        """,
                    },
                ],
            }
        ],
        tools=[
            {
                "input_schema": KeepCriteriaChange.model_json_schema(),
                "name": "keep_criteria_change",
                "description": "A tool that determines if a criteria change should be kept.",
            }
        ],
        metadata=LLMTraceMetadata(
            trace_name="opportunity_stages.citation_for_sales_action_classification",
            session_id=langfuse_session_id,
            custom_fields=ReeTraceMetadata(
                organization_id=str(organization_id),
            ),
        ),
    )
    if not result.content or result.content[-1].type != "tool_use":
        return None
    keep_criteria_change = KeepCriteriaChange.model_validate(result.content[-1].input)
    if not keep_criteria_change.should_keep_change:
        return None
    for content_block in result.content:
        if content_block.type == "text" and content_block.citations:
            return content_block.citations[0]
    return None


async def add_sales_action_role_classification_citation_to_db(
    source_context: SourceContext,
    field_name: str,
    for_object_type: CitationForObjectType,
    organization_id: UUID,
    citation_service: CitationService,
    citation: TextCitation,
) -> list[CriteriaCitation]:
    if not isinstance(citation, CitationContentBlockLocation):
        logger.warning(f"Invalid citation type: {type(citation)}")
        return []

    if isinstance(source_context, list):
        field_citation_pair = FieldCitationPair(
            field=FieldInfo(field_name=field_name, field_item_id=None),
            citation=citation,
        )
        return await add_criteria_multi_meeting_citation(
            field_citation_pairs=[field_citation_pair],
            organization_id=organization_id,
            citation_service=citation_service,
            object_type=for_object_type,
            multi_meeting_transcript=source_context,
        )
    criteria_citations: list[CriteriaCitation] = []
    if isinstance(
        source_context.primary_object, (MeetingAugmented, VoiceCallAugmented)
    ):
        source_id = source_context.primary_object.id
        if isinstance(source_context.primary_object, VoiceCallAugmented):
            source_id = source_context.primary_object.meeting.id

        db_citation = await citation_service.create_citation(
            organization_id=organization_id,
            for_object_id=UUID("00000000-0000-0000-0000-000000000000"),
            for_object_type=for_object_type,
            source_type=CitationSourceType.MEETING,
            source_id=source_id,
            metadata=MeetingCitationMetadata(
                source_text=citation.cited_text,
                start_turn_id=citation.start_block_index,
                end_turn_id=citation.end_block_index,
            ),
            created_by_user_id=UUID(settings.intel_hardcoded_user_id),
            field_name=field_name,
        )
        criteria_citations.append(
            CriteriaCitation(id=db_citation.id, field_name=field_name)
        )
    elif isinstance(source_context.primary_object, EmailAugmented):
        for index in range(citation.start_block_index, citation.end_block_index):
            if index >= len(source_context.primary_object.messages):
                logger.warning(f"Message index {index} out of range")
                continue
            db_citation = await citation_service.create_citation(
                organization_id=organization_id,
                for_object_id=UUID("00000000-0000-0000-0000-000000000000"),
                for_object_type=for_object_type,
                source_type=CitationSourceType.EMAIL,
                source_id=source_context.primary_object.id,
                metadata=EmailCitationMetadata(
                    source_text=citation.cited_text,
                    global_message_id=str(
                        source_context.primary_object.messages[index].global_message_id
                    ),
                ),
                created_by_user_id=UUID(settings.intel_hardcoded_user_id),
                field_name=field_name,
            )
            criteria_citations.append(
                CriteriaCitation(id=db_citation.id, field_name=field_name)
            )
        logger.bind(
            source_context_type=type(source_context.primary_object),
            source_id=source_context.primary_object.id,
        ).warning("Primary object is not a meeting, voice call, or email")
    return criteria_citations
