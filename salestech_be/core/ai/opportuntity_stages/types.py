from collections.abc import Awaitable, Callable
from dataclasses import dataclass
from typing import Generic, TypeVar
from uuid import UUID

from pydantic import BaseModel

from salestech_be.core.common.types import DomainModel
from salestech_be.core.opportunity_stage_criteria.criteria_types import (
    CriteriaCitation,
    CriteriaContextUpdateWithRecTypes,
    CriteriaItem,
    CriteriaItemsUpdate,
    CriteriaItemUpdateWithRecTypes,
)
from salestech_be.core.pipeline.service.pipeline_qualification_property_service import (
    PipelineQualificationPropertyService,
)
from salestech_be.core.pipeline.service_api_schema import (
    CreateItemRequest,
    PatchCriteriaRequest,
    PatchItemRequest,
)
from salestech_be.db.models.crm_ai_rec import CrmAIRecType, CrmPropertyAiRec

T_ContextUpdate = TypeVar(
    "T_ContextUpdate", bound=CriteriaContextUpdateWithRecTypes | None
)
T_ItemsUpdate = TypeVar("T_ItemsUpdate", bound=CriteriaItemsUpdate | None)
T_Item = TypeVar("T_Item", bound=CriteriaItem | None)
T_ItemUpdate = TypeVar("T_ItemUpdate", bound=CriteriaItemUpdateWithRecTypes | None)
T_PatchRequest = TypeVar("T_PatchRequest", bound=PatchCriteriaRequest | None)
T_CreateItemRequest = TypeVar("T_CreateItemRequest", bound=CreateItemRequest | None)
T_PatchItemRequest = TypeVar("T_PatchItemRequest", bound=PatchItemRequest)


@dataclass
class CriteriaConfig(
    Generic[
        T_ContextUpdate,
        T_ItemsUpdate,
        T_Item,
        T_ItemUpdate,
        T_PatchRequest,
        T_CreateItemRequest,
        T_PatchItemRequest,
    ]
):
    get_previous_criteria: Callable[
        [PipelineQualificationPropertyService, UUID, UUID, UUID], Awaitable[DomainModel]
    ]
    context_update_type: type[T_ContextUpdate] | None = None
    items_update_type: type[T_ItemsUpdate] | None = None
    patch_request_type: type[T_PatchRequest] | None = None
    create_item_request_type: type[T_CreateItemRequest] | None = None
    create_ai_recs_from_patch_request: (
        Callable[
            [
                PipelineQualificationPropertyService,
                UUID,
                UUID,
                UUID,
                CrmAIRecType,
                T_PatchRequest,
                list[CriteriaCitation],
                dict[str, CrmAIRecType],
            ],
            Awaitable[list[CrmPropertyAiRec]],
        ]
        | None
    ) = None
    criteria_item_type: type[T_Item] | None = None
    create_criteria_item: (
        Callable[
            [
                PipelineQualificationPropertyService,
                UUID,
                UUID,
                UUID,
                T_CreateItemRequest,
                list[CriteriaCitation],
            ],
            Awaitable[BaseModel],
        ]
        | None
    ) = None
    delete_criteria_item: (
        Callable[
            [
                PipelineQualificationPropertyService,
                UUID,
                UUID,
                UUID,
                UUID,
                list[CriteriaCitation],
            ],
            Awaitable[None],
        ]
        | None
    ) = None
    update_item_type: type[T_ItemUpdate] | None = None
    update_item_request_type: type[T_PatchItemRequest] | None = None
    create_item_ai_recs_from_patch_request: (
        Callable[
            [
                PipelineQualificationPropertyService,
                UUID,
                UUID,
                UUID,
                CrmAIRecType,
                UUID,
                T_PatchItemRequest,
                list[CriteriaCitation],
                dict[str, CrmAIRecType],
            ],
            Awaitable[list[CrmPropertyAiRec]],
        ]
        | None
    ) = None
