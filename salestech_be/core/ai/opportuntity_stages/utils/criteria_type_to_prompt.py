from salestech_be.core.ai.prompt.schema import PromptEnum
from salestech_be.core.opportunity_stage_criteria.criteria_types import CriteriaContextT
from salestech_be.core.opportunity_stage_criteria.standard_criteria.champion import (
    ChampionContext,
)
from salestech_be.core.opportunity_stage_criteria.standard_criteria.competition import (
    CompetitionContext,
)
from salestech_be.core.opportunity_stage_criteria.standard_criteria.decision_criteria import (
    DecisionCriteriaContext,
)
from salestech_be.core.opportunity_stage_criteria.standard_criteria.decision_process import (
    DecisionProcessContext,
)
from salestech_be.core.opportunity_stage_criteria.standard_criteria.economic_buyer import (
    EconomicBuyerContext,
)
from salestech_be.core.opportunity_stage_criteria.standard_criteria.identified_pain import (
    IdentifiedPainPointContext,
)
from salestech_be.core.opportunity_stage_criteria.standard_criteria.metric import (
    MetricContext,
)
from salestech_be.core.opportunity_stage_criteria.standard_criteria.paper_process import (
    PaperProcessContext,
)
from salestech_be.core.opportunity_stage_criteria.standard_criteria.whys import (
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    WhyUsContext,
)


def criteria_type_to_prompt_name(criteria_type: type[CriteriaContextT]) -> PromptEnum:  # noqa: C901, PLR0911
    if criteria_type == ChampionContext:
        return PromptEnum.EXTRACT_OPPORTUNITY_STAGE_CRITERIA_CHAMPION
    elif criteria_type == CompetitionContext:
        return PromptEnum.EXTRACT_OPPORTUNITY_STAGE_CRITERIA_COMPETITION
    elif criteria_type == DecisionCriteriaContext:
        return PromptEnum.EXTRACT_OPPORTUNITY_STAGE_CRITERIA_DECISION_CRITERIA
    elif criteria_type == DecisionProcessContext:
        return PromptEnum.EXTRACT_OPPORTUNITY_STAGE_CRITERIA_DECISION_PROCESS
    elif criteria_type == EconomicBuyerContext:
        return PromptEnum.EXTRACT_OPPORTUNITY_STAGE_CRITERIA_ECONOMIC_BUYER
    elif criteria_type == IdentifiedPainPointContext:
        return PromptEnum.EXTRACT_OPPORTUNITY_STAGE_CRITERIA_IDENTIFIED_PAINPOINT
    elif criteria_type == MetricContext:
        return PromptEnum.EXTRACT_OPPORTUNITY_STAGE_CRITERIA_METRIC
    elif criteria_type == PaperProcessContext:
        return PromptEnum.EXTRACT_OPPORTUNITY_STAGE_CRITERIA_PAPER_PROCESS
    elif criteria_type == WhyAnyContext:
        return PromptEnum.EXTRACT_OPPORTUNITY_STAGE_CRITERIA_WHY_ANY
    elif criteria_type == WhyNowContext:
        return PromptEnum.EXTRACT_OPPORTUNITY_STAGE_CRITERIA_WHY_NOW
    elif criteria_type == WhyUsContext:
        return PromptEnum.EXTRACT_OPPORTUNITY_STAGE_CRITERIA_WHY_US
    else:
        raise ValueError(f"No prompt name found for criteria type: {criteria_type}")
