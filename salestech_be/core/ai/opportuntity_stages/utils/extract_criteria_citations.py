from pydantic import BaseModel

from salestech_be.core.opportunity_stage_criteria.criteria_types import (
    CriteriaCitation,
    CriteriaContextUpdateWithRecTypes,
    CriteriaItemsUpdate,
    CriteriaUpdateResult,
)


class ExtractedCriteriaCitations(BaseModel):
    field_citations: list[CriteriaCitation]
    added_item_citations: list[CriteriaCitation]
    removed_item_citations: list[CriteriaCitation]
    updated_item_citations: list[CriteriaCitation]


def extract_criteria_citations(
    update_result: CriteriaUpdateResult[
        CriteriaContextUpdateWithRecTypes, CriteriaItemsUpdate
    ],
) -> ExtractedCriteriaCitations:
    field_citations = []
    added_item_citations = []
    removed_item_citations = []
    updated_item_citations = []
    if not update_result.citations:
        return ExtractedCriteriaCitations(
            field_citations=[],
            added_item_citations=[],
            removed_item_citations=[],
            updated_item_citations=[],
        )
    for citation in update_result.citations:
        if citation.field_item_id is None:
            field_citations.append(citation)
            continue
        if not update_result.updated_items:
            continue
        if (
            update_result.updated_items.delete
            and citation.field_item_id in update_result.updated_items.delete
        ):
            removed_item_citations.append(citation)
            continue
        if update_result.updated_items.create:
            for item in update_result.updated_items.create:
                if item.id == citation.field_item_id:
                    added_item_citations.append(citation)
                    continue
        if update_result.updated_items.update:
            updated_item_citations.append(citation)

    return ExtractedCriteriaCitations(
        field_citations=field_citations,
        added_item_citations=added_item_citations,
        removed_item_citations=removed_item_citations,
        updated_item_citations=updated_item_citations,
    )
