from typing import Any

from langfuse import <PERSON><PERSON>
from langfuse.api.resources.prompts.types.prompt import Prompt
from langfuse.model import Chat<PERSON>essageDict, DatasetItem, PromptClient

from salestech_be.common.singleton import Singleton
from salestech_be.core.ai.prompt.schema import (
    PromptMessage,
    PromptRequest,
    PromptResponse,
    PromptRole,
    PromptType,
)
from salestech_be.ree_logging import get_logger
from salestech_be.settings import settings
from salestech_be.util.asyncio_util.adapter import run_in_pool

logger = get_logger(__name__)


class LangfusePromptService:
    """
    Service for working with prompt templates in a Langfuse's prompt management backend
    """

    def __init__(self) -> None:
        self.langfuse = Langfuse(
            public_key=settings.langfuse_public_key.get_secret_value(),
            secret_key=settings.langfuse_secret_key.get_secret_value(),
            host=settings.langfuse_host,
            environment=settings.environment,
        )

    def get_prompt_info(self, prompt_name: str) -> Prompt:
        return self.langfuse.client.prompts.get(prompt_name)

    async def get_prompt(self, request: PromptRequest) -> PromptResponse:
        """Get a prompt and resolve the variables into a list of messages"""
        try:
            prompt_client = await self._get_langfuse_prompt(request)
            prompt: str | list[ChatMessageDict] = prompt_client.compile(
                **request.variables
            )
            messages: list[PromptMessage] = (
                [{"role": PromptRole.USER, "content": prompt}]
                if isinstance(prompt, str)
                else prompt
            )

            return PromptResponse(
                messages=messages,
                prompt_client=prompt_client,
                config=prompt_client.config,
            )
        except Exception as e:
            logger.bind(exc_info=e, prompt_name=request.prompt_name).error(
                f"Error getting prompt: {e}"
            )
            raise e

    async def _get_langfuse_prompt(
        self,
        request: PromptRequest,
    ) -> PromptClient:
        return await run_in_pool(
            self.langfuse.get_prompt,
            name=request.prompt_name,
            version=request.version,
            label=request.label,
            type="chat" if request.prompt_type == PromptType.CHAT else "text",
            cache_ttl_seconds=request.cache_ttl_seconds,
        )

    async def create_dataset_item(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self,
        dataset_name: str,
        dataset_input: dict[str, Any] | None = None,
        expected_output: dict[str, Any] | None = None,
        metadata: dict[str, Any] | None = None,
    ) -> DatasetItem:
        return self.langfuse.create_dataset_item(
            dataset_name=dataset_name,
            # any python object or value, optional
            input=dataset_input,
            # any python object or value, optional
            expected_output=expected_output,
            # metadata, optional
            metadata=metadata,
        )


class SingletonLangfusePromptService(Singleton, LangfusePromptService):
    pass


def get_langfuse_prompt_service() -> LangfusePromptService:
    return SingletonLangfusePromptService()
