from enum import StrEnum
from typing import Any, TypedDict

from langfuse.model import PromptClient
from pydantic import BaseModel, ValidationError, model_validator

from salestech_be.ree_logging import get_logger

logger = get_logger(__name__)


class PromptType(StrEnum):
    TEXT = "text"
    CHAT = "chat"


class PromptRole(StrEnum):
    USER = "user"
    SYSTEM = "system"
    ASSISTANT = "assistant"


class LLMTraceMetadataTraceName(StrEnum):
    GENERATE_TASKS_FROM_EMAIL = "intel.generate_tasks_from_email"
    GENERATE_TASKS_FROM_CONVERSATION = "intel.generate_tasks_from_conversation"
    GENERATE_TASKS_FROM_OBJECTIONS = "tasks.generate_tasks_from_objections"
    INTEL_CREATE_TASKS = "intel.create_tasks"
    TASKS_FROM_OBJECTIONS = "tasks.generate_tasks_from_objections"
    EXTRACT_OPPORTUNITY_STAGE_CRITERIA_CHAMPION = (
        "opportunity_stages.extract_exit_criteria_champion"
    )
    EXTRACT_OPPORTUNITY_STAGE_CRITERIA_COMPETITION = (
        "opportunity_stages.extract_exit_criteria_competition"
    )
    EXTRACT_OPPORTUNITY_STAGE_CRITERIA_DECISION_CRITERIA = (
        "opportunity_stages.extract_exit_criteria_decision_criteria"
    )
    EXTRACT_OPPORTUNITY_STAGE_CRITERIA_DECISION_PROCESS = (
        "opportunity_stages.extract_exit_criteria_decision_process"
    )
    EXTRACT_OPPORTUNITY_STAGE_CRITERIA_ECONOMIC_BUYER = (
        "opportunity_stages.extract_exit_criteria_economic_buyer"
    )
    EXTRACT_OPPORTUNITY_STAGE_CRITERIA_IDENTIFIED_PAINPOINT = (
        "opportunity_stages.extract_exit_criteria_identified_painpoint"
    )
    EXTRACT_OPPORTUNITY_STAGE_CRITERIA_METRIC = (
        "opportunity_stages.extract_exit_criteria_metric"
    )
    EXTRACT_OPPORTUNITY_STAGE_CRITERIA_PAPER_PROCESS = (
        "opportunity_stages.extract_exit_criteria_paper_process"
    )
    EXTRACT_OPPORTUNITY_STAGE_CRITERIA_WHY_ANY = (
        "opportunity_stages.extract_exit_criteria_why_any"
    )
    EXTRACT_OPPORTUNITY_STAGE_CRITERIA_WHY_NOW = (
        "opportunity_stages.extract_exit_criteria_why_now"
    )
    EXTRACT_OPPORTUNITY_STAGE_CRITERIA_WHY_US = (
        "opportunity_stages.extract_exit_criteria_why_us"
    )


class PromptEnum(StrEnum):
    # Email
    EXTRACT_MAIN_EMAIL_CONTENT = "email.extract_main_email_content"
    SUMMARIZE_EMAIL_CONTENT = "email.summarize_email_content"
    CLASSIFY_EMAIL = "email.classify"
    STRIP_HTML_TAGS = "email.strip_html_tags"

    # Insights
    GENERATE_BRIEF_INSIGHTS_FROM_EMAIL_THREAD = (
        "insights.generate_brief_from_email_thread"
    )
    GENERATE_OBJECTION_INSIGHTS_FROM_EMAIL_THREAD = (
        "insights.generate_objections_from_email_thread"
    )
    GENERATE_SUMMARY_INSIGHTS_FROM_EMAIL_THREAD = (
        "insights.generate_summary_from_email_thread"
    )
    GENERATE_SENTIMENT_INSIGHTS_FROM_EMAIL_THREAD = (
        "insights.generate_sentiment_from_email_thread"
    )
    GENERATE_BRIEF_INSIGHTS_FROM_CONVERSATION = (
        "insights.generate_brief_from_conversation"
    )
    GENERATE_SENTIMENT_INSIGHTS_FROM_CONVERSATION = (
        "insights.generate_sentiment_from_conversation"
    )
    GENERATE_SUMMARY_INSIGHTS_FROM_CONVERSATION = (
        "insights.generate_summary_from_conversation"
    )
    GENERATE_ACTION_ITEMS_INSIGHTS_FROM_CALL = (
        "insights.generate_action_items_from_call"
    )
    GENERATE_TOPIC_OUTLINE_INSIGHTS_FROM_CONVERSATION = (
        "insights.generate_topic_outline_from_conversation"
    )
    GENERATE_FIELD_EXTRACTION_INSIGHTS_FROM_CONVERSATION = (
        "insights.generate_field_extraction_from_conversation"
    )
    GENERATE_OBJECTION_INSIGHTS_FROM_CONVERSATION = (
        "insights.generate_objections_from_conversation"
    )

    # Tasks
    GENERATE_TASKS_FROM_CONVERSATION = "intel.generate_tasks_from_conversation"
    GENERATE_TASKS_FROM_EMAIL = "intel.generate_tasks_from_email"
    GENERATE_TASK_DUE_DATE = "intel.generate_task_due_date"
    GENERATE_TASK_PRIORITY = "intel.generate_task_priority"
    GENERATE_TASK_OWNERSHIP = "intel.generate_task_ownership"
    CLOSE_EXISTING_TASKS = "intel.close_existing_tasks"
    CHECK_GENERATED_TASKS_ALREADY_EXIST = "intel.check_generated_tasks_already_exist"
    GENERATE_TASKS_FROM_OBJECTION = "tasks.generate_tasks_from_objections"

    # Research Agent
    RETRIEVE_GENERAL_COMPANY_INFORMATION = "research.company_site"
    RETRIEVE_COMPANY_CASE_STUDIES = "research.company_site_case_studies"
    RESEARCH_AGENT_COMPANY_NEWS_GROUNDING = "research.company_news.grounding"
    RESEARCH_AGENT_COMPANY_NEWS_FORMATTING = "research.company_news.formatting"

    # Opportunity Stage Criteria
    EXTRACT_OPPORTUNITY_STAGE_CRITERIA = "opportunity_stages.extract_exit_criteria"
    EXTRACT_OPPORTUNITY_STAGE_CRITERIA_CHAMPION = (
        "opportunity_stages.extract_exit_criteria_champion"
    )
    EXTRACT_OPPORTUNITY_STAGE_CRITERIA_COMPETITION = (
        "opportunity_stages.extract_exit_criteria_competition"
    )
    EXTRACT_OPPORTUNITY_STAGE_CRITERIA_DECISION_CRITERIA = (
        "opportunity_stages.extract_exit_criteria_decision_criteria"
    )
    EXTRACT_OPPORTUNITY_STAGE_CRITERIA_DECISION_PROCESS = (
        "opportunity_stages.extract_exit_criteria_decision_process"
    )
    EXTRACT_OPPORTUNITY_STAGE_CRITERIA_ECONOMIC_BUYER = (
        "opportunity_stages.extract_exit_criteria_economic_buyer"
    )
    EXTRACT_OPPORTUNITY_STAGE_CRITERIA_IDENTIFIED_PAINPOINT = (
        "opportunity_stages.extract_exit_criteria_identified_painpoint"
    )
    EXTRACT_OPPORTUNITY_STAGE_CRITERIA_METRIC = (
        "opportunity_stages.extract_exit_criteria_metric"
    )
    EXTRACT_OPPORTUNITY_STAGE_CRITERIA_PAPER_PROCESS = (
        "opportunity_stages.extract_exit_criteria_paper_process"
    )
    EXTRACT_OPPORTUNITY_STAGE_CRITERIA_WHY_ANY = (
        "opportunity_stages.extract_exit_criteria_why_any"
    )
    EXTRACT_OPPORTUNITY_STAGE_CRITERIA_WHY_NOW = (
        "opportunity_stages.extract_exit_criteria_why_now"
    )
    EXTRACT_OPPORTUNITY_STAGE_CRITERIA_WHY_US = (
        "opportunity_stages.extract_exit_criteria_why_us"
    )

    # Meeting Prep
    AI_MEETING_GENERATE_KEY_TALKING_POINTS = "ai_meeting.generate_key_talking_points"
    AI_MEETING_GENERATE_MEETING_AGENDA = "ai_meeting.generate_agenda"


class PromptMessage(TypedDict):
    role: PromptRole
    content: str


class PromptRequest(BaseModel):
    """Schema for Langfuse prompt retrieval request parameters"""

    prompt_name: str
    prompt_type: PromptType = PromptType.CHAT
    version: int | None = None
    variables: dict[str, str] | None = None
    label: str | None = "production"
    cache_ttl_seconds: int | None = 600

    @model_validator(mode="after")
    def validate_version_and_label(self) -> "PromptRequest":
        if self.version is not None and self.label is not None:
            raise ValueError("version and label cannot be set simultaneously")
        return self


class PromptTools(BaseModel):  # type: ignore[explicit-any] # TODO: fix-any-annotation
    tools: list[dict[str, Any]]  # type: ignore[explicit-any] # TODO: fix-any-annotation


class PromptResponse(BaseModel):  # type: ignore[explicit-any] # TODO: fix-any-annotation
    """Schema for Langfuse prompt response data"""

    model_config = {"arbitrary_types_allowed": True}

    messages: list[PromptMessage]
    """Prompt, which consists of a list of messages. For example, for a chat prompt, it will be a list of PromptMessage (user/system)."""

    config: dict[str, Any]  # type: ignore[explicit-any] # TODO: fix-any-annotation
    """Configuration for the prompt, including the prompt template and variables."""

    prompt_client: PromptClient
    """Actual prompt object from Langfuse, can be used for tracing etc."""

    @property
    def version(self) -> int | None:
        """Get the version of the prompt template."""
        return self.prompt_client.version if self.prompt_client else None

    def get_model(self) -> str:
        selected_model = self.config.get("model", "")
        if selected_model == "" or not isinstance(selected_model, str):
            logger.warning(
                "model is not set or defined correctly (not a valid string) in the prompt config, using default model"
            )
            # define a default model, in this case, we use gpt-4o-mini
            return "gpt-4o-mini"
        logger.info(f"model is set in the prompt config, using {selected_model}")

        return selected_model

    def get_tools(self) -> list[Any] | None:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        try:
            tools = PromptTools(tools=self.config["tools"])
        except ValidationError as e:
            logger.exception(
                f"tools are not set or defined correctly (not a valid list of dicts) in the prompt config, using no tools, error: {e}"
            )
            return None
        except KeyError as e:
            logger.exception(
                f"the tools field is not set in the prompt config, using no tools, error: {e}"
            )
            return None
        logger.info(f"tools are set in the prompt config, using {tools.tools}")

        return tools.tools
