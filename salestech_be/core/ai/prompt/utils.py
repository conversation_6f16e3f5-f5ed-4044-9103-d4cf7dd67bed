from salestech_be.core.ai.prompt.schema import (
    LLMTraceMetadataTraceName,
    PromptEnum,
)
from salestech_be.core.ai.workflows.schema import (
    IntelTriggerObjectType,
)


def get_prompt_name(intel_trigger_type: IntelTriggerObjectType) -> PromptEnum:
    if intel_trigger_type == IntelTriggerObjectType.MEETING:
        return PromptEnum.GENERATE_TASKS_FROM_CONVERSATION
    elif intel_trigger_type == IntelTriggerObjectType.GLOBAL_THREAD:
        return PromptEnum.GENERATE_TASKS_FROM_EMAIL
    elif intel_trigger_type == IntelTriggerObjectType.OBJECTION:
        return PromptEnum.GENERATE_TASKS_FROM_OBJECTION
    else:
        raise ValueError("No valid object type provided")


def get_trace_name(prompt_name: PromptEnum) -> LLMTraceMetadataTraceName:
    mapping = {
        PromptEnum.GENERATE_TASKS_FROM_CONVERSATION: LLMTraceMetadataTraceName.INTEL_CREATE_TASKS,
        PromptEnum.GENERATE_TASKS_FROM_EMAIL: LLMTraceMetadataTraceName.GENERATE_TASKS_FROM_EMAIL,
        PromptEnum.GENERATE_TASKS_FROM_OBJECTION: LLMTraceMetadataTraceName.TASKS_FROM_OBJECTIONS,
        PromptEnum.EXTRACT_OPPORTUNITY_STAGE_CRITERIA_CHAMPION: LLMTraceMetadataTraceName.EXTRACT_OPPORTUNITY_STAGE_CRITERIA_CHAMPION,
        PromptEnum.EXTRACT_OPPORTUNITY_STAGE_CRITERIA_COMPETITION: LLMTraceMetadataTraceName.EXTRACT_OPPORTUNITY_STAGE_CRITERIA_COMPETITION,
        PromptEnum.EXTRACT_OPPORTUNITY_STAGE_CRITERIA_DECISION_CRITERIA: LLMTraceMetadataTraceName.EXTRACT_OPPORTUNITY_STAGE_CRITERIA_DECISION_CRITERIA,
        PromptEnum.EXTRACT_OPPORTUNITY_STAGE_CRITERIA_DECISION_PROCESS: LLMTraceMetadataTraceName.EXTRACT_OPPORTUNITY_STAGE_CRITERIA_DECISION_PROCESS,
        PromptEnum.EXTRACT_OPPORTUNITY_STAGE_CRITERIA_ECONOMIC_BUYER: LLMTraceMetadataTraceName.EXTRACT_OPPORTUNITY_STAGE_CRITERIA_ECONOMIC_BUYER,
        PromptEnum.EXTRACT_OPPORTUNITY_STAGE_CRITERIA_IDENTIFIED_PAINPOINT: LLMTraceMetadataTraceName.EXTRACT_OPPORTUNITY_STAGE_CRITERIA_IDENTIFIED_PAINPOINT,
        PromptEnum.EXTRACT_OPPORTUNITY_STAGE_CRITERIA_METRIC: LLMTraceMetadataTraceName.EXTRACT_OPPORTUNITY_STAGE_CRITERIA_METRIC,
        PromptEnum.EXTRACT_OPPORTUNITY_STAGE_CRITERIA_PAPER_PROCESS: LLMTraceMetadataTraceName.EXTRACT_OPPORTUNITY_STAGE_CRITERIA_PAPER_PROCESS,
        PromptEnum.EXTRACT_OPPORTUNITY_STAGE_CRITERIA_WHY_ANY: LLMTraceMetadataTraceName.EXTRACT_OPPORTUNITY_STAGE_CRITERIA_WHY_ANY,
        PromptEnum.EXTRACT_OPPORTUNITY_STAGE_CRITERIA_WHY_NOW: LLMTraceMetadataTraceName.EXTRACT_OPPORTUNITY_STAGE_CRITERIA_WHY_NOW,
        PromptEnum.EXTRACT_OPPORTUNITY_STAGE_CRITERIA_WHY_US: LLMTraceMetadataTraceName.EXTRACT_OPPORTUNITY_STAGE_CRITERIA_WHY_US,
    }

    if prompt_name not in mapping:
        raise ValueError("No valid prompt provided")

    return mapping[prompt_name]
