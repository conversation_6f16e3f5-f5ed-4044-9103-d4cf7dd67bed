from enum import St<PERSON><PERSON>num
from typing import Annotated, Any, Literal

from pydantic import BaseModel, Field

TS_TASK_QUEUE = "ts-task-queue"


class XStateHelperFunctions(StrEnum):
    GET_TRANSITIONS = "getTransitions"
    GET_MACHINE_METADATA = "getMachineMetadata"


class XStateAction(BaseModel):  # type: ignore[explicit-any] # TODO: fix-any-annotation
    type: str
    value: Any | None = None  # type: ignore[explicit-any] # TODO: fix-any-annotation


class XStateTransition(BaseModel):
    target: str | list[str]
    actions: list[str | XStateAction] | None = None
    cond: str | None = None
    description: str | None = None


class XStateState(BaseModel):  # type: ignore[explicit-any] # TODO: fix-any-annotation
    type: Literal["atomic", "compound", "parallel", "final"] | None = "atomic"
    initial: str | None = None
    states: dict[str, "XStateState"] | None = None
    on: (
        dict[str, str | list[str] | XStateTransition | list[XStateTransition]] | None
    ) = None
    entry: list[str | XStateAction] | None = None
    exit: list[str | XStateAction] | None = None
    invoke: list[dict[str, Any]] | None = None  # type: ignore[explicit-any] # TODO: fix-any-annotation
    description: str | None = None


XStateState.model_rebuild()  # rebuild to allow self-referencing


class XStateMachine(BaseModel):  # type: ignore[explicit-any] # TODO: fix-any-annotation
    id: str
    initial: str
    context: Annotated[dict[str, Any], Field(default_factory=dict)] | None = None  # type: ignore[explicit-any] # TODO: fix-any-annotation
    states: dict[str, XStateState]
    description: str | None = None

    @property
    def initial_state(self) -> str:
        return self.initial

    @property
    def final_states(self) -> list[str]:
        """Returns list of states that have no outgoing transitions"""
        final_states = []
        for state_name, state in self.states.items():
            if state.type == "final":
                final_states.append(state_name)
        return final_states


test_json = {
    "context": {},
    "id": "premeetingAnalysis",
    "initial": "gatheringClientInformation",
    "states": {
        "gatheringClientInformation": {
            "on": {"next": {"target": "reviewingClientHistory"}},
            "description": "In this state, the salesperson gathers all relevant information about the client, including their business needs, past interactions, and any other pertinent details.",
        },
        "reviewingClientHistory": {
            "on": {"next": {"target": "identifyingMeetingObjectives"}},
            "description": "The salesperson reviews the client's history, including past meetings, purchases, and any previous concerns or feedback to understand the relationship better.",
        },
        "identifyingMeetingObjectives": {
            "on": {"next": {"target": "preparingPresentationMaterials"}},
            "description": "In this state, the salesperson sets clear objectives for the upcoming meeting, such as understanding the client's current needs, proposing solutions, or closing a deal.",
        },
        "preparingPresentationMaterials": {
            "on": {"next": {"target": "finalizingLogistics"}},
            "description": "The salesperson prepares all necessary presentation materials, including slides, product demos, and any other resources needed to communicate effectively during the meeting.",
        },
        "finalizingLogistics": {
            "on": {"next": {"target": "conductingPremeetingRehearsal"}},
            "description": "In this state, the salesperson finalizes all logistical details, such as confirming the meeting time and location, arranging travel if necessary, and ensuring all technical requirements are met.",
        },
        "conductingPremeetingRehearsal": {
            "on": {"next": {"target": "qualityCheck"}},
            "description": "The salesperson conducts a rehearsal of the meeting to practice the presentation, anticipate client questions, and refine their approach.",
        },
        "qualityCheck": {
            "on": {
                "pass": {"target": "readyForMeeting"},
                "fail": {"target": "preparingPresentationMaterials"},
            },
            "description": "This state involves checking the quality of the preparation. If the preparation is satisfactory, the process moves forward. Otherwise, it loops back for further refinement.",
        },
        "readyForMeeting": {
            "description": "The salesperson is fully prepared for the meeting, having completed all necessary premeeting analysis and preparation steps."
        },
    },
}


# if __name__ == "__main__":
#     machine = XStateMachine(**test_json)
#     print("machine", machine)
#     print("machine.model_dump_json()", machine.model_dump_json())
