import json
from typing import Any
from uuid import UUID

from fastapi import Request
from langfuse import Langfuse
from langfuse.model import TextPromptClient

from salestech_be.common.lifespan import get_db_engine
from salestech_be.common.ree_llm import LLMTraceMetadata, acompletion
from salestech_be.core.ai.service.rag.schema import (
    EmailComposeLLMResponse,
    EmailRAGContext,
)
from salestech_be.core.ai.tools.domain_objects.contact import (
    fetch_contact_domain_object_from_db,
)
from salestech_be.core.ai.tools.domain_objects.organization import (
    fetch_organization_domain_object_from_db,
)
from salestech_be.core.ai.tools.domain_objects.user import (
    fetch_user_domain_object_from_db,
)
from salestech_be.core.email.service.message_service import (
    MessageService,
    get_message_service_by_db_engine,
)
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.message import Message
from salestech_be.ree_logging import get_logger
from salestech_be.settings import settings
from salestech_be.util.litellm_response_util import get_choice_message_content
from salestech_be.util.validation import not_none
from salestech_be.web.api.ai.email.schema import (
    AIEmailOperation,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)

logger = get_logger()


class AIEmailRAGService:
    def __init__(
        self,
        message_service: MessageService,
        db_engine: DatabaseEngine,
    ):
        self.message_service = message_service
        self.db_engine = db_engine

        # TODO: Move to use langfuse service once it's implemented
        self.langfuse_client: Langfuse = Langfuse(
            public_key=settings.langfuse_public_key.get_secret_value(),
            secret_key=settings.langfuse_secret_key.get_secret_value(),
            host=settings.langfuse_host,
        )

    def _get_langfuse_prompt(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self,
        prompt_name: str,
        version: int | None = None,
        label: str | None = None,
        prompt_variables: dict[str, Any] | None = None,
    ) -> tuple[str, TextPromptClient]:
        # Note: `production` labeled prompts are used by default, without a version or label specified
        prompt_template: TextPromptClient = self.langfuse_client.get_prompt(
            name=prompt_name,
            version=version,
            label=label,
        )

        if prompt_variables:
            return str(prompt_template.compile(**prompt_variables)), prompt_template
        return str(prompt_template.prompt), prompt_template

    async def _get_email_from_message_id(
        self, organization_id: UUID, message_id: UUID
    ) -> Message:
        return await self.message_service.get_message_by_id(
            organization_id=organization_id, message_id=message_id
        )

    async def get_ai_email_reply_response(
        self,
        context: EmailRAGContext,
        content_gen_history: list[str] | None = None,
    ) -> AIEmailOperation.Reply.Response:
        try:
            # Get the email content from the message
            email_message = await self._get_email_from_message_id(
                organization_id=context.organization_id,
                message_id=not_none(context.message_id),
            )

            # Get prompt template and fill in variables
            prompt, prompt_template = self._get_langfuse_prompt(
                prompt_name="ai_email.generate_new_reply",
                prompt_variables={
                    "email_content": email_message.body_html,
                    "content_gen_history": content_gen_history,
                },
            )

            # Make the completion call
            completion_response = await acompletion(
                model=prompt_template.config.get("model", "gpt-4o-mini"),
                messages=[
                    {
                        "role": "system",
                        "content": """
You are an AI assistant that generates professional and contextually relevant email replies. Your task is to create responses that effectively push forward sales motions, such as setting up a meeting, addressing customer questions, or advancing toward closing a deal.

### Requirements:
1. Use the content of the received email to craft an appropriate reply.
2. If key insights are available, incorporate them into the reply. If not, proceed without them.
3. The response should always come from the user logged in, never from the contact who sent the original email.
4. Maintain a professional tone while being clear, concise, and actionable.

### Output Format:
- Return the email reply in **HTML format** supported by email clients.
- DO NOT wrap the output in code fences (backticks or tildes like ``` or ~~~).
- DO NOT include newline characters (`\n`) in the HTML.
- DO NOT use placeholders, variables, or templates (e.g., {{key}} or [key}).
- DO NOT include signature information (limit to the main email body).

### Additional Instructions:
- If the email contains objections or concerns, address them directly and professionally without overanalyzing or making assumptions beyond the provided content.
- Ensure the response progresses the sales conversation effectively and aligns with the users goal (e.g., setting up a meeting, addressing objections, etc.).
- Always adapt the reply based on the available context.
                        """,
                    },
                    {"role": "user", "content": prompt},
                ],
                n=3,  # Generate 3 versions (1 main + 2 alternatives)
                max_completion_tokens=5000,
                metadata=LLMTraceMetadata(
                    prompt=prompt_template,
                    trace_name="ai_email.generate_new_reply",
                    custom_fields={
                        "organization_id": str(context.organization_id),
                    },
                ),
            )

            # Extract the generated responses
            responses: list[str] = [
                get_choice_message_content(choice)
                for choice in completion_response.choices
            ]

            # First response is the main reply, others are alternatives
            main_reply = responses[0]
            alternative_versions = responses[1:] if len(responses) > 1 else []

            return AIEmailOperation.Reply.Response(
                generated_reply=main_reply,
                alternative_versions=alternative_versions,
                content_gen_history=([*(content_gen_history or []), main_reply]),
            )

        except Exception as e:
            logger.error("Error generating AI email reply")
            raise e

    async def get_ai_email_rephrase_response(
        self,
        context: EmailRAGContext,
        desired_outcome: str,
        text_selected: str,
        content_gen_history: list[str] | None = None,
        text_before: str | None = None,
        text_after: str | None = None,
    ) -> AIEmailOperation.Rephrase.Response:
        try:
            # Get prompt template and fill in variables
            prompt, prompt_template = self._get_langfuse_prompt(
                prompt_name="ai_email.generate_new_phrase",
                prompt_variables={
                    "text_selected": text_selected,
                    "text_before": text_before,
                    "text_after": text_after,
                    "desired_outcome": desired_outcome,
                    "content_gen_history": content_gen_history,
                },
            )

            # Make the completion call
            completion_response = await acompletion(
                model=prompt_template.config.get("model", "gpt-4o-mini"),
                messages=[
                    {
                        "role": "system",
                        "content": """
You are an AI assistant helping to rephrase email content. Your task is to rewrite the selected text while maintaining the core message and adjusting the tone, style, or length according to the specified desired outcome. Ensure that the rewritten text seamlessly fits into the original email.

### Guidelines:
1. Focus only on the **text_selected** provided by the user for rephrasing.
2. Use the **text_before** and **text_after** for context but do not modify them.
3. Adjust the **text_selected** according to the desired outcome:
   - **Make Longer**: Expand the text with additional details or supporting information.
   - **Make Shorter**: Condense the text while preserving its meaning.
   - **Simplify**: Make the text clearer and easier to understand.
   - **More Casual**: Adjust the tone to be friendlier and less formal.
4. Return the rewritten text in plain text format without wrapping it in HTML or code fences.
5. Reply in the same language as the original text.
6. DO NOT include newline characters (`\n`) in the output.
7. DO NOT use placeholders, variables, or templates in the output (e.g., no curly braces or square braces).
                        """,
                    },
                    {"role": "user", "content": prompt},
                ],
                n=3,  # Generate 3 versions (1 main + 2 alternatives)
                max_completion_tokens=5000,
                metadata=LLMTraceMetadata(
                    prompt=prompt_template,
                    trace_name="ai_email.generate_new_phrase",
                    custom_fields={
                        "organization_id": str(context.organization_id),
                    },
                ),
            )

            # Extract the generated responses
            responses: list[str] = [
                get_choice_message_content(choice)
                for choice in completion_response.choices
            ]

            # First response is the main rephrased text, others are alternatives
            main_rephrased = responses[0]
            alternative_versions = responses[1:] if len(responses) > 1 else []

            return AIEmailOperation.Rephrase.Response(
                rephrased_text=main_rephrased,
                alternative_versions=alternative_versions,
                content_gen_history=([*(content_gen_history or []), main_rephrased]),
            )

        except Exception as e:
            logger.error("Error rephrasing email content")
            raise e

    async def get_ai_email_compose_response(
        self,
        context: EmailRAGContext,
        email_purpose: str,
        key_points: list[str],
        desired_outcome: str,
        content_gen_history: list[str] | None = None,
    ) -> AIEmailOperation.Compose.Response:
        try:
            contact_domain_object = (
                await fetch_contact_domain_object_from_db(
                    contact_id=context.contact_ids[0],
                    organization_id=context.organization_id,
                    db_engine=self.db_engine,
                )
                if context.contact_ids
                else None
            )
            user_domain_object = (
                await fetch_user_domain_object_from_db(
                    user_id=context.user_id,
                    organization_id=context.organization_id,
                    db_engine=self.db_engine,
                )
                if context.user_id
                else None
            )
            organization_domain_object = await fetch_organization_domain_object_from_db(
                organization_id=context.organization_id, db_engine=self.db_engine
            )

            # Get prompt template and fill in variables
            prompt, prompt_template = self._get_langfuse_prompt(
                prompt_name="ai_email.generate_new_email",
                prompt_variables={
                    "email_purpose": email_purpose,
                    "key_points": "\n".join(f"- {point}" for point in key_points),
                    "desired_outcome": desired_outcome,
                    "content_gen_history": content_gen_history,
                    "seller_company_info": organization_domain_object.model_dump(
                        exclude_defaults=True,
                        exclude_none=True,
                        exclude_unset=True,
                        mode="json",
                    )
                    if organization_domain_object
                    else None,
                    "context_json": json.dumps(
                        {
                            # for recipient
                            "contact_info": contact_domain_object.model_dump(
                                exclude_defaults=True,
                                exclude_none=True,
                                exclude_unset=True,
                                mode="json",
                            )
                            if contact_domain_object
                            else None,
                            # for sender
                            "sender_info": user_domain_object.model_dump(
                                exclude_defaults=True,
                                exclude_none=True,
                                exclude_unset=True,
                                mode="json",
                            )
                            if user_domain_object
                            else None,
                        }
                    ),
                },
            )

            # Update system prompt to explicitly request subject lines
            completion_response = await acompletion(
                model=prompt_template.config.get("model", "gpt-4o-mini"),
                messages=[
                    {
                        "role": "system",
                        "content": """
You are an AI assistant helping to compose professional emails. Your responses should be well-structured, effectively communicate the key points, and work towards the desired outcome.

### Requirements:
1. Use the provided purpose, key points, and desired outcome to craft an email that aligns with the recipient's needs.
2. Include a subject line that is recipient-focused and incorporates their company name for personalization.
3. Tailor the email to the specified purpose:
   - **Introduction**: Write an introductory email about the company, highlighting offerings and benefits.
   - **Demo Request**: Focus on inviting the recipient to schedule a demo, automatically including the scheduling link.
   - **Proposal**: Present a proposal and include the scheduling link for follow-up discussions.
   - **Follow-Up**: Reference a prior note (if available) and create a professional follow-up email, including the scheduling link.

### Output Format:
Return the email content in **HTML format** supported by email clients, with the following JSON structure:
{
  "email_body": "<html>...</html>",
  "subject_lines": ["...", "..."]
}

### Additional Instructions:
- Limit the HTML to standards supported by email clients.
- DO NOT wrap the output in code fences (backticks or tildes like ``` or ~~~).
- DO NOT include newline characters (`\n`) in the HTML.
- DO NOT use placeholders, variables, or templates in the email content (e.g., {{key}} or [key}).
- DO NOT include signature information—focus on the main email content only.
- Ensure the subject line is concise, compelling, and includes the recipient or their company name where relevant.
                        """,
                    },
                    {"role": "user", "content": prompt},
                ],
                n=3,  # Generate 3 versions (1 main + 2 alternatives)
                max_completion_tokens=5000,
                metadata=LLMTraceMetadata(
                    prompt=prompt_template,
                    trace_name="ai_email.generate_new_email",
                    custom_fields={
                        "organization_id": str(context.organization_id),
                    },
                ),
                response_format=EmailComposeLLMResponse,
            )

            # Extract the generated responses and parse JSON
            responses = [
                json.loads(get_choice_message_content(choice))
                for choice in completion_response.choices
            ]

            # First response is the main composed email, others are alternatives
            main_email = responses[0]["email_body"]
            alternative_versions = (
                [r["email_body"] for r in responses[1:]] if len(responses) > 1 else []
            )

            # Collect all subject line suggestions
            subject_line_suggestions: list[str] = []
            for response in responses:
                subject_line_suggestions.extend(response["subject_lines"])

            return AIEmailOperation.Compose.Response(
                composed_email=main_email,
                alternative_versions=alternative_versions,
                subject_line_suggestions=subject_line_suggestions,
                content_gen_history=([*(content_gen_history or []), main_email]),
            )

        except Exception as e:
            logger.error("Error composing email content")
            raise e


def get_ai_email_rag_service(request: Request) -> AIEmailRAGService:
    db_engine = get_db_engine(request=request)
    return AIEmailRAGService(
        message_service=get_message_service_by_db_engine(db_engine=db_engine),
        db_engine=db_engine,
    )
