from typing import Any
from uuid import UUID

from pydantic import BaseModel, Field


class EmailRAGContext(BaseModel):  # type: ignore[explicit-any] # TODO: fix-any-annotation
    """Context about the Email RAG operation and related entities"""

    organization_id: UUID = Field(..., description="ID of the organization")
    user_id: UUID | None = Field(
        default=None, description="ID of the user making the request"
    )
    thread_id: UUID | None = Field(default=None, description="ID of the email thread")
    message_id: UUID | None = Field(
        default=None, description="ID of the specific email message"
    )
    contact_ids: list[UUID] | None = Field(
        default=None, description="IDs of the contacts being emailed"
    )
    account_id: UUID | None = Field(
        default=None, description="ID of the account being emailed"
    )
    pipeline_id: UUID | None = Field(
        default=None, description="ID of the associated pipeline"
    )
    metadata: dict[str, Any] | None = Field(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        default=None, description="Additional metadata for the RAG operation"
    )


class EmailComposeLLMResponse(BaseModel):
    email_body: str = Field(..., description="The generated email body")
    subject_lines: list[str] = Field(..., description="The generated subject lines")
