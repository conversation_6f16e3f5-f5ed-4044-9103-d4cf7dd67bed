from uuid import UUID

from pydantic import BaseModel
from temporalio import activity

from salestech_be.common.ree_llm import (
    LLMTraceMetadata,
    ReeTraceMetadata,
    acompletion,
)
from salestech_be.core.ai.common.helpers.get_intel_context import get_intel_context
from salestech_be.core.ai.prompt.langfuse_prompt_service import (
    get_langfuse_prompt_service,
)
from salestech_be.core.ai.prompt.schema import PromptEnum, PromptRequest
from salestech_be.core.ai.workflows.schema import IntelInput
from salestech_be.core.prompt.types import PromptUseCase
from salestech_be.core.task.service.task_v2_service import get_task_v2_service_general
from salestech_be.core.task.types_v2 import TaskV2
from salestech_be.db.models.task import TaskStatus
from salestech_be.temporal.database import (
    get_or_init_db_engine,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.web.api.task.schema import (
    PatchTaskRequest,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)

langfuse_prompt_service = get_langfuse_prompt_service()


class TaskToCloseFromLLM(BaseModel):
    title: str
    note: str
    id: str | None


class TasksToCloseRequestListFromLLM(BaseModel):
    tasks: list[TaskToCloseFromLLM]


@activity.defn
async def close_existing_tasks(
    intel_input: IntelInput,
) -> str:
    """Close the existing tasks for the meeting pipeline intel."""
    db_engine = await get_or_init_db_engine()
    task_v2_service = get_task_v2_service_general(db_engine=db_engine)
    # Retrieve database event
    intel_context = await get_intel_context(intel_input)

    if intel_context.organization is None:
        raise ValueError("No organization found")

    prompt_name = PromptEnum.CLOSE_EXISTING_TASKS

    prompt_variables = await intel_context.to_prompt_variables(prompt_name=prompt_name)
    if not prompt_variables["object_content"]:
        return "No object content found"

    # Retrieve prompt
    prompt_response = await langfuse_prompt_service.get_prompt(
        request=PromptRequest(
            prompt_name=prompt_name,
            variables=prompt_variables,
        )
    )

    # Run prompt
    response = await acompletion(
        model="anthropic/claude-3-5-sonnet-20241022",
        messages=prompt_response.messages,
        temperature=0,
        tools=[
            {
                "type": "function",
                "function": {
                    "name": "close_tasks",
                    "description": "A tool that closes one or more tasks.",
                    "parameters": TasksToCloseRequestListFromLLM.model_json_schema(),
                },
            }
        ],
        # response_format={"type": "json_object"},
        metadata=LLMTraceMetadata(
            prompt=prompt_response.prompt_client,
            trace_name="intel.close_tasks",
            session_id=intel_input.langfuse_session_id,
            custom_fields=ReeTraceMetadata(
                organization_id=str(intel_input.organization_id),
                prompt_use_case=PromptUseCase.INTEL_TASKS,
                # workflow_id=activity.info().workflow_id,
            ),
        ),
        tool_choice="any",  # Force at least one tool call. Not sure why a specific tool call isn't working based on docs
    )

    if not response.choices:
        return "No tasks closed"

    tool_calls = response.tool_calls
    if not tool_calls:
        return "No tasks closed"

    tool_call = tool_calls[0]
    if not isinstance(tool_call.function.name, str):
        raise ValueError("Tool call name is not a string")

    task_json = tool_call.function.arguments
    if not isinstance(task_json, str):
        raise ValueError("Tool call arguments is not a string")

    task_request_list = TasksToCloseRequestListFromLLM.model_validate_json(task_json)

    await langfuse_prompt_service.create_dataset_item(
        dataset_name=f"raw_{prompt_name}",
        dataset_input=prompt_variables,
        expected_output=task_request_list.model_dump(),
        metadata={
            "model": "anthropic/claude-3-5-sonnet-20241022",
            "organization_id": str(intel_input.organization_id),
            "pipeline_id": str(intel_input.pipeline_id),
            "object_id": str(intel_input.object_id),
            "object_type": str(intel_input.object_type),
            "prompt_name": prompt_name,
        },
    )

    tasks: list[TaskV2] = []
    for task_request in task_request_list.tasks:
        if task_request.id is None:
            continue

        user_id = (
            intel_context.pipeline.owner_user_id
            if intel_context.pipeline
            else intel_context.organization.created_by_user_id
        )
        task = await task_v2_service.patch_by_id_v2(
            user_id=user_id,
            organization_id=intel_input.organization_id,
            task_id=UUID(task_request.id),
            request=PatchTaskRequest(
                status=TaskStatus.COMPLETED,
                title=task_request.title,
                contact_ids=None,
                owner_user_id=user_id,
                due_at=None,
                note=None,
            ),
        )
        tasks.append(task)

    return "\n".join([t.title for t in task_request_list.tasks])
