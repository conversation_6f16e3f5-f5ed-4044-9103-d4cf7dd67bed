import json

import pandas as pd

from salestech_be.common.ree_llm import (
    LLMResponse,
    LLMTraceMetadata,
    ReeTraceMetadata,
    acompletion,
)
from salestech_be.core.ai.common.types import (
    TaskOwnershipResponse,
)
from salestech_be.core.ai.prompt.langfuse_prompt_service import (
    get_langfuse_prompt_service,
)
from salestech_be.core.ai.prompt.schema import PromptEnum, PromptRequest
from salestech_be.core.prompt.types import PromptUseCase
from salestech_be.ree_logging import get_logger

logger = get_logger()


langfuse_prompt_service = get_langfuse_prompt_service()


async def generate_task_ownership_llm_call(
    task_title: str,
    task_note: str,
    object_content: str,
    users: str,
    account_owner: str,
    organization_id: str,
    langfuse_session_id: str,
    workflow_id: str,
    output_path: str | None = None,
) -> LLMResponse[TaskOwnershipResponse]:
    function_args = {
        "task_title": task_title,
        "task_note": task_note,
        "object_content": object_content,
        "users": users,
        "account_owner_user": account_owner,
        "organization_id": organization_id,
        "langfuse_session_id": langfuse_session_id,
        "workflow_id": workflow_id,
    }

    prompt_variables = {
        "task_title": task_title,
        "task_note": task_note,
        "object_content": object_content,
        "users": users,
        "account_owner_user": account_owner,
    }

    prompt_obj = await langfuse_prompt_service.get_prompt(
        request=PromptRequest(
            prompt_name=PromptEnum.GENERATE_TASK_OWNERSHIP,
            variables=prompt_variables,
        )
    )
    result = await acompletion(
        model=prompt_obj.get_model(),
        messages=prompt_obj.messages,
        temperature=0,
        max_completion_tokens=1024,
        response_format=TaskOwnershipResponse,
        metadata=LLMTraceMetadata(
            prompt=prompt_obj.prompt_client,
            trace_name=PromptEnum.GENERATE_TASK_OWNERSHIP,
            session_id=langfuse_session_id,
            custom_fields=ReeTraceMetadata(
                organization_id=organization_id,
                prompt_use_case=PromptUseCase.INTEL_TASKS,
                workflow_id=workflow_id,
            ),
        ),
    )
    if output_path:
        try:
            data = {
                "input": json.dumps(prompt_variables),
                "output": result.message_content.ownership_id,
            }
            logger.info(f"Writing data {data} to {output_path}")
            df = pd.DataFrame(data, index=[0])
            df.to_csv(output_path, mode="a", header=False, index=False)
        except Exception as e:
            logger.error(f"Error writing data to {output_path}: {e}")

    try:
        await langfuse_prompt_service.create_dataset_item(
            dataset_name=f"{PromptEnum.GENERATE_TASK_OWNERSHIP}-v0.1",
            dataset_input=function_args,
            expected_output={"ownership_id": result.message_content.ownership_id},
        )
    except Exception as e:
        logger.bind(dataset_name=f"{PromptEnum.GENERATE_TASK_OWNERSHIP}-v0.1").warning(
            f"Error creating dataset item: {e}"
        )

    return result
