from uuid import UUID

from salestech_be.common.ree_llm import (
    LLMTraceMetadata,
    ReeTraceMetadata,
    acompletion,
)
from salestech_be.core.ai.activities.meeting_pipeline_intel_activities import (
    TaskRequestListFromLLM,
)
from salestech_be.core.ai.prompt.langfuse_prompt_service import (
    get_langfuse_prompt_service,
)
from salestech_be.core.ai.prompt.schema import PromptEnum, PromptRequest
from salestech_be.core.prompt.types import PromptUseCase
from salestech_be.ree_logging import get_logger

logger = get_logger()

langfuse_prompt_service = get_langfuse_prompt_service()


async def generate_tasks_from_objection_llm_call(
    object_content: str,
    additional_context: str,
    contacts: str,
    organization_id: UUID,
    langfuse_session_id: str,
    workflow_id: str,
) -> TaskRequestListFromLLM:
    """Generate tasks from objection content using LLM.

    Args:
        object_content: The objection content to generate tasks from
        additional_context: Additional context about the objection
        contacts: Information about contacts involved
        organization_id: ID of the organization
        langfuse_session_id: Session ID for Langfuse tracking
        workflow_id: ID of the workflow

    Returns:
        TaskRequestListFromLLM containing the generated tasks
    """

    function_args = {
        "object_content": object_content,
        "additional_context": additional_context,
        "contacts": contacts,
        "organization_id": organization_id,
        "langfuse_session_id": langfuse_session_id,
        "workflow_id": workflow_id,
    }

    prompt_variables = {
        "object_content": object_content,
        "additional_context": additional_context,
        "contacts": contacts,
    }

    prompt_response = await langfuse_prompt_service.get_prompt(
        request=PromptRequest(
            prompt_name=PromptEnum.GENERATE_TASKS_FROM_OBJECTION,
            variables=prompt_variables,
        )
    )

    response = await acompletion(
        model=prompt_response.get_model(),
        messages=prompt_response.messages,
        temperature=0,
        tools=[
            {
                "type": "function",
                "function": {
                    "name": "create_tasks",
                    "description": "A tool that creates one or more tasks with descriptions from objection content.",
                    "parameters": TaskRequestListFromLLM.model_json_schema(),
                },
            }
        ],
        metadata=LLMTraceMetadata(
            prompt=prompt_response.prompt_client,
            trace_name=PromptEnum.GENERATE_TASKS_FROM_OBJECTION,
            session_id=langfuse_session_id,
            custom_fields=ReeTraceMetadata(
                organization_id=str(organization_id),
                prompt_use_case=PromptUseCase.INTEL_TASKS,
                workflow_id=workflow_id,
            ),
        ),
        tool_choice="any",
    )

    no_tasks_response = TaskRequestListFromLLM(tasks=[])

    if not response.choices:
        logger.debug("No tasks generated: response.choices is empty")
        return no_tasks_response

    tool_calls = response.tool_calls
    if not tool_calls:
        logger.debug("No tasks generated: response.tool_calls is empty")
        return no_tasks_response

    tool_call = tool_calls[0]
    if not isinstance(tool_call.function.name, str):
        logger.warning("Tool call name is not a string")
        return no_tasks_response

    task_json = tool_call.function.arguments
    if not isinstance(task_json, str):
        logger.warning("Tool call arguments is not a string")
        return no_tasks_response

    task_request_list = TaskRequestListFromLLM.model_validate_json(task_json)

    try:
        await langfuse_prompt_service.create_dataset_item(
            dataset_name=f"{PromptEnum.GENERATE_TASKS_FROM_OBJECTION}-v0.1",
            dataset_input=function_args,
            expected_output=task_request_list.model_dump(),
            metadata={
                "model": prompt_response.get_model(),
                "organization_id": str(organization_id),
                "prompt_name": PromptEnum.GENERATE_TASKS_FROM_OBJECTION,
            },
        )
    except Exception as e:
        logger.bind(
            dataset_name=f"{PromptEnum.GENERATE_TASKS_FROM_OBJECTION}-v0.1"
        ).warning(f"Error creating dataset item: {e}")

    return task_request_list
