#!/usr/bin/env python3

import asyncio
import uuid

from salestech_be.core.ai.tasks.activities.close_existing_tasks import (
    close_existing_tasks,
)
from salestech_be.core.ai.workflows.schema import IntelInput, IntelTriggerObjectType
from salestech_be.ree_logging import get_logger
from salestech_be.temporal.database import (
    get_or_init_db_engine,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)

logger = get_logger(__name__)


async def main() -> None:
    # Initialize DB engine
    await get_or_init_db_engine()

    # Create input
    activity_input = IntelInput(
        object_id=uuid.UUID("dae63e26-e2b7-478b-84a2-d38fc1dbee79"),
        object_type=IntelTriggerObjectType.GLOBAL_THREAD,
        organization_id=uuid.UUID("cce7b290-8a08-4904-a6c7-2b6613877cf5"),
        pipeline_id=uuid.UUID("7980a8fd-1066-4ccc-ba48-ee8702d215f8"),
        account_ids=[uuid.UUID("fdd3aed2-308a-45c5-b287-9f9ac7c26735")],
        langfuse_session_id="test_session_id",
    )

    # Run the activity directly
    result = await close_existing_tasks(activity_input)
    logger.info(f"Activity result: {result}")


if __name__ == "__main__":
    asyncio.run(main())
