from temporalio import workflow

with workflow.unsafe.imports_passed_through():
    import asyncio
    import uuid

    from salestech_be.core.ai.tasks.workflows.close_existing_tasks import (
        CloseExistingTasksWorkflow,
    )
    from salestech_be.core.ai.workflows.schema import IntelInput, IntelTriggerObjectType
    from salestech_be.integrations.temporal.client import get_temporal_client
    from salestech_be.integrations.temporal.config import DEFAULT_TASK_QUEUE
    from salestech_be.ree_logging import get_logger

logger = get_logger(__name__)


async def run_workflow(workflow_input: IntelInput) -> str:
    """Execute the close existing tasks workflow."""
    temporal_client = await get_temporal_client()
    await temporal_client.start_workflow(
        CloseExistingTasksWorkflow.run,
        id=CloseExistingTasksWorkflow.get_workflow_id(
            workflow_input.organization_id,
            workflow_input.object_id,
            workflow_input.object_type,
        ),
        task_queue=DEFAULT_TASK_QUEUE,
        start_signal="enqueue_input",
        start_signal_args=[workflow_input],
    )
    return "running workflow..."


if __name__ == "__main__":
    logger.info("Starting close existing tasks workflow")
    # Test organization ID
    workflow_input = IntelInput(
        object_id=uuid.UUID("dae63e26-e2b7-478b-84a2-d38fc1dbee79"),
        object_type=IntelTriggerObjectType.GLOBAL_THREAD,
        organization_id=uuid.UUID("cce7b290-8a08-4904-a6c7-2b6613877cf5"),
        pipeline_id=uuid.UUID("7980a8fd-1066-4ccc-ba48-ee8702d215f8"),
        account_ids=[uuid.UUID("fdd3aed2-308a-45c5-b287-9f9ac7c26735")],
        langfuse_session_id="test_session_id",
    )

    asyncio.run(run_workflow(workflow_input))
