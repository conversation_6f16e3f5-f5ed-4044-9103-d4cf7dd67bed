#!/usr/bin/env python3
import asyncio
import uuid

from salestech_be.core.ai.tasks.activities.generate_intel_tasks import (
    generate_intel_tasks,
)
from salestech_be.core.ai.workflows.schema import IntelInput, IntelTriggerObjectType
from salestech_be.ree_logging import get_logger
from salestech_be.temporal.database import (
    get_or_init_db_engine,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)

logger = get_logger(__name__)


async def main() -> None:
    # Initialize DB engine
    await get_or_init_db_engine()

    # Create input
    # Non Sales Email - on Dev
    # activity_input = IntelInput(
    #     object_id=uuid.UUID("33065cfd-1748-4c16-bfc9-ba117f171d61"),
    #     object_type=IntelTriggerObjectType.GLOBAL_THREAD,
    #     organization_id=uuid.UUID("cce7b290-8a08-4904-a6c7-2b6613877cf5"),
    #     pipeline_id=None,
    #     account_ids=None,
    #     langfuse_session_id="test_session_id",
    #     output_path="task_ownership_dataset.csv",
    # )

    # Sales Email - on Dev
    activity_input = IntelInput(
        object_id=uuid.UUID("df3b214d-260a-476c-9688-a5fecbaa295a"),
        object_type=IntelTriggerObjectType.GLOBAL_THREAD,
        organization_id=uuid.UUID("cce7b290-8a08-4904-a6c7-2b6613877cf5"),
        pipeline_id=None,
        account_ids=[uuid.UUID("74eeb326-58ea-4999-a9b4-90a43db1cf50")],
        langfuse_session_id="test_session_id",
        non_temporal=True,
        bypass_dedup=True,
        output_path="task_ownership_dataset.csv",
        # test_stub=[
        #     #     MessageWithGlobalMessageId(
        #     #         global_message_id=uuid.UUID("df3b214d-260a-476c-9688-a5fecbaa295a"),
        #     #         id=uuid.UUID("df3b214d-260a-476c-9688-a5fecbaa295a"),
        #     #         subject="Hello, how are you?",
        #     #         send_from=[],
        #     #         send_to=[],
        #     #         cc=[],
        #     #         bcc=[],
        #     #         body_text="hello there how are you?",
        #     #         body_html="<p>hello there how are you?</p>",
        #     #         received_at=datetime.now(pytz.utc),
        #     #         thread_id=uuid.UUID("df3b214d-260a-476c-9688-a5fecbaa295a"),
        #     #         email_account_id=uuid.UUID("df3b214d-260a-476c-9688-a5fecbaa295a"),
        #     #         organization_id="431635d2-2991-4fdc-8ce5-3033e9d7d071",
        #     #         created_at=datetime.now(pytz.utc),
        #     #         updated_at=datetime.now(pytz.utc),
        #     #         deleted_at=None,
        #     #         cancelled_at=None,
        #     #         snippet="hello there how are you?",
        #     #
        #     #     ),
        #     #     MessageWithGlobalMessageId(
        #     #         global_message_id=uuid.UUID("df3b214d-260a-476c-9688-a5fecbaa295a"),
        #     #         id=uuid.UUID("df3b214d-260a-476c-9688-a5fecbaa295a"),
        #     #         subject="Hello, how are you?",
        #     #         send_from=[],
        #     #         send_to=[],
        #     #         cc=[],
        #     #         bcc=[],
        #     #         body_text="I'm good thanks! I need a demo of your product.",
        #     #         body_html="<p>I'm good thanks! I need a demo of your product.</p>",
        #     #         received_at=datetime.now(pytz.utc),
        #     #         thread_id=uuid.UUID("df3b214d-260a-476c-9688-a5fecbaa295a"),
        #     #         email_account_id=uuid.UUID("df3b214d-260a-476c-9688-a5fecbaa295a"),
        #     #         organization_id="431635d2-2991-4fdc-8ce5-3033e9d7d071",
        #     #         created_at=datetime.now(pytz.utc),
        #     #         updated_at=datetime.now(pytz.utc),
        #     #         deleted_at=None,
        #     #         cancelled_at=None,
        #     #         snippet="I'm good thanks! I need a demo of your product.",
        #     #     ),
        #     #     MessageWithGlobalMessageId(
        #     #         global_message_id=uuid.UUID("df3b214d-260a-476c-9688-a5fecbaa295a"),
        #     #         id=uuid.UUID("df3b214d-260a-476c-9688-a5fecbaa295a"),
        #     #         subject="Okay I'll set up a demo for you on tuesday.",
        #     #         send_from=[],
        #     #         send_to=[],
        #     #         cc=[],
        #     #         bcc=[],
        #     #         body_text="Okay I'll set up a demo for you on tuesday.",
        #     #         body_html="<p>Okay I'll set up a demo for you on tuesday.</p>",
        #     #         received_at=datetime.now(pytz.utc),
        #     #         thread_id=uuid.UUID("df3b214d-260a-476c-9688-a5fecbaa295a"),
        #     #         email_account_id=uuid.UUID("df3b214d-260a-476c-9688-a5fecbaa295a"),
        #     #         organization_id="431635d2-2991-4fdc-8ce5-3033e9d7d071",
        #     #         created_at=datetime.now(pytz.utc),
        #     #         updated_at=datetime.now(pytz.utc),
        #     #         deleted_at=None,
        #     #         cancelled_at=None,
        #     #         snippet="Okay I'll set up a demo for you on tuesday.",
        #     #     ),
        #     # ],
    )

    # Sales Video Call - on Dev
    # activity_input = IntelInput(
    #     object_id=uuid.UUID("c0ce798d-d518-427e-9e4d-f861d63e1b22"),
    #     object_type=IntelTriggerObjectType.MEETING,
    #     organization_id=uuid.UUID("e309ff95-96ba-4cf5-b65b-5facd6f54ca5"),
    #     pipeline_id=None,
    #     account_ids=None,
    #     langfuse_session_id="test_session_id",
    #     non_temporal=True,
    #     bypass_dedup=True,
    #     output_path="task_ownership_dataset.csv",
    #     test_stub=[
    #         TranscriptSentence(
    #             text="Hello, how are you?",
    #             start_timestamp=0,
    #             end_timestamp=10,
    #             speaker="John",
    #             language="en",
    #             average_confidence=0.95,
    #         ),
    #         TranscriptSentence(
    #             text="Good, thanks! I need a demo of your product.",
    #             start_timestamp=10,
    #             end_timestamp=20,
    #             speaker="Michael",
    #             language="en",
    #             average_confidence=0.95,
    #         ),
    #         TranscriptSentence(
    #             text="Okay I'll set up a demo for you on tuesday.",
    #             start_timestamp=20,
    #             end_timestamp=30,
    #             speaker="John",
    #             language="en",
    #             average_confidence=0.95,
    #         ),
    #         TranscriptSentence(
    #             text="Great, thank you!",
    #             start_timestamp=30,
    #             end_timestamp=40,
    #             speaker="Michael",
    #             language="en",
    #             average_confidence=0.95,
    #         ),
    #     ],
    # )

    # Sales Voice Call - on Dev
    # activity_input = IntelInput(
    #     object_id=uuid.UUID("7fe73821-3a53-47f9-9928-98a6ddcc1ef4"),
    #     object_type=IntelTriggerObjectType.MEETING,
    #     organization_id=uuid.UUID("cce7b290-8a08-4904-a6c7-2b6613877cf5"),
    #     pipeline_id=uuid.UUID("55c5a84d-ef00-4906-af5c-f6f6ca561283"),
    #     account_ids=[uuid.UUID("a2ad36bd-d82c-4e3f-8f02-cf2f8e5357a8")],
    #     langfuse_session_id="test_session_id",
    #     non_temporal=True,
    #     bypass_dedup=True,
    #     output_path="task_ownership_dataset.csv",
    # test_stub=[
    #     TranscriptSentence(
    #         text="Hello, how are you?",
    #         start_timestamp=0,
    #         end_timestamp=10,
    #         speaker="John",
    #         language="en",
    #         average_confidence=0.95,
    #     ),
    #     TranscriptSentence(
    #         text="Good, thanks! I need a demo of your product.",
    #         start_timestamp=10,
    #         end_timestamp=20,
    #         speaker="Michael",
    #         language="en",
    #         average_confidence=0.95,
    #     ),
    #     TranscriptSentence(
    #         text="Okay I'll set up a demo for you on tuesday.",
    #         start_timestamp=20,
    #         end_timestamp=30,
    #         speaker="John",
    #         language="en",
    #         average_confidence=0.95,
    #     ),
    #     TranscriptSentence(
    #         text="Great, thank you!",
    #         start_timestamp=30,
    #         end_timestamp=40,
    #         speaker="Michael",
    #         language="en",
    #         average_confidence=0.95,
    #     ),
    # ],
    # )

    # Sales Objection - on Dev
    # activity_input = IntelInput(
    #     object_id=uuid.UUID("********-a71f-4758-810e-3383bb4e0efb"),
    #     object_type=IntelTriggerObjectType.OBJECTION,
    #     organization_id=uuid.UUID("e309ff95-96ba-4cf5-b65b-5facd6f54ca5"),
    #     pipeline_id=None,
    #     account_ids=None,
    #     langfuse_session_id="test_session_id",
    #     non_temporal=True,
    #     bypass_dedup=True,
    #     test_stub=[
    #         TranscriptSentence(
    #             text="Hello, how are you?",
    #             start_timestamp=0,
    #             end_timestamp=10,
    #             speaker="John",
    #             language="en",
    #             average_confidence=0.95,
    #         ),
    #         TranscriptSentence(
    #             text="I'm good thanks! I need a demo of your product.",
    #             start_timestamp=10,
    #             end_timestamp=20,
    #             speaker="Michael",
    #             language="en",
    #             average_confidence=0.95,
    #         ),
    #     ],
    #     output_path="task_ownership_dataset.csv",
    # )

    # Sales Meeting - on Prod
    # activity_input = IntelInput(
    #     object_id=uuid.UUID("c0ce798d-d518-427e-9e4d-f861d63e1b22"),
    #     object_type=IntelTriggerObjectType.MEETING,
    #     organization_id=uuid.UUID("e309ff95-96ba-4cf5-b65b-5facd6f54ca5"),
    #     pipeline_id=None,
    #     account_ids=None,
    #     langfuse_session_id="test_session_id",
    #     non_temporal=True,
    #     bypass_dedup=True,
    #     output_path="task_ownership_dataset.csv",
    # )

    # Run the activity directly
    result = await generate_intel_tasks(activity_input)
    logger.info(f"Activity result: {result}")


if __name__ == "__main__":
    asyncio.run(main())
