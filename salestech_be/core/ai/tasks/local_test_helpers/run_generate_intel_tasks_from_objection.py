#!/usr/bin/env python3
import asyncio
import uuid
from datetime import datetime

import pytz

from salestech_be.core.ai.tasks.activities.generate_intel_tasks import (
    generate_intel_tasks,
)
from salestech_be.core.ai.workflows.schema import IntelInput, IntelTriggerObjectType
from salestech_be.ree_logging import get_logger
from salestech_be.temporal.database import (
    get_or_init_db_engine,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)

logger = get_logger(__name__)


async def main() -> None:
    # Initialize DB engine
    await get_or_init_db_engine()

    # Create input
    activity_input = IntelInput(
        object_id=uuid.UUID("eedf32f8-f880-451a-93a7-ddbcc591e9f2"),
        object_type=IntelTriggerObjectType.OBJECTION,
        organization_id=uuid.UUID("cce7b290-8a08-4904-a6c7-2b6613877cf5"),
        pipeline_id=None,
        account_ids=None,
        langfuse_session_id=f"test_session_id-{datetime.now(pytz.utc).strftime('%Y-%m-%d-%H-%M-%S')}",
    )

    # Run the activity directly
    result = await generate_intel_tasks(activity_input)
    logger.info(f"Activity result: {result}")


if __name__ == "__main__":
    asyncio.run(main())
