#!/usr/bin/env python3

import asyncio
import datetime
import uuid

from salestech_be.core.ai.common.helpers.get_intel_context import get_intel_context
from salestech_be.core.ai.tasks.activities.generate_intel_tasks import (
    generate_task_citations,
)
from salestech_be.core.ai.workflows.schema import IntelInput, IntelTriggerObjectType
from salestech_be.core.task.types_v2 import TaskV2
from salestech_be.db.models.task import (
    TaskPriority,
    TaskSourceType,
    TaskStatus,
    TaskType,
)
from salestech_be.ree_logging import get_logger
from salestech_be.temporal.database import (
    get_or_init_db_engine,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)

logger = get_logger(__name__)


async def main() -> None:
    # Initialize DB engine
    await get_or_init_db_engine()

    # Create input
    # activity_input = IntelInput(
    #     object_id=uuid.UUID("dae63e26-e2b7-478b-84a2-d38fc1dbee79"),
    #     object_type=IntelTriggerObjectType.GLOBAL_THREAD,
    #     organization_id=uuid.UUID("cce7b290-8a08-4904-a6c7-2b6613877cf5"),
    #     pipeline_id=uuid.UUID("7980a8fd-1066-4ccc-ba48-ee8702d215f8"),
    #     account_ids=[uuid.UUID("fdd3aed2-308a-45c5-b287-9f9ac7c26735")],
    #     langfuse_session_id="test_session_id",
    # )
    # activity_input = IntelInput(
    #     object_id=uuid.UUID("df3b214d-260a-476c-9688-a5fecbaa295a"),
    #     object_type=IntelTriggerObjectType.GLOBAL_THREAD,
    #     organization_id=uuid.UUID("cce7b290-8a08-4904-a6c7-2b6613877cf5"),
    #     pipeline_id=uuid.UUID("e69b6614-f53c-43ab-b651-1e96e0390939"),
    #     account_ids=[uuid.UUID("d21ebf0e-333b-4d49-86a5-f47c03291a26")],
    #     langfuse_session_id="test_session_id",
    # )

    activity_input = IntelInput(
        object_id=uuid.UUID("7fe73821-3a53-47f9-9928-98a6ddcc1ef4"),
        object_type=IntelTriggerObjectType.MEETING,
        organization_id=uuid.UUID("cce7b290-8a08-4904-a6c7-2b6613877cf5"),
        pipeline_id=uuid.UUID("74e79f27-d5fd-4b9f-a128-41feb180bf8d"),
        account_ids=[uuid.UUID("53a784b5-cb44-4598-8dca-2fb08a480753")],
        langfuse_session_id="test_session_id",
        non_temporal=True,
        bypass_dedup=True,
    )

    intel_context = await get_intel_context(activity_input)
    logger.info(f"intel context {intel_context}")

    tasks = [
        TaskV2(
            id=uuid.UUID("e44b5bf1-acd8-496e-9dbd-1b22760806a6"),
            property_metadata=None,
            custom_field_data=None,
            custom_field_data_v2=None,
            organization_id=uuid.UUID("e309ff95-96ba-4cf5-b65b-5facd6f54ca5"),
            title="Objection Response: Compile customer-specific ROI and success metrics comparison",
            status=TaskStatus.OPEN,
            priority=TaskPriority.HIGH,
            type=TaskType.ACTION_ITEM,
            due_at=None,
            note="Research and compile industry-specific evidence of Airplane's superior performance and ROI compared to competitors, tailored to this customer's use case.\n\nKey components:\n1. Analyze customer's specific industry challenges and requirements\n2. Gather relevant case studies from similar customers who chose us over competitors\n3. Calculate projected ROI based on customer's specific usage patterns\n4. Document maintenance and reliability metrics specific to their operating environment\n5. Compile total cost of ownership comparison over 5 years\n\nNext steps:\n- Interview 2-3 similar customers about their decision process\n- Pull performance data from comparable deployments\n- Calculate customer-specific cost savings\n- Document reliability metrics in their geographic region\n- Create comparison matrix of long-term value metrics\n- Prepare summary of post-sale support advantages\n\nThis evidence will complement the general presentation and demo by providing concrete, customer-specific proof of our value proposition.",
            created_at=datetime.datetime(
                2025, 5, 8, 5, 34, 26, 206733, tzinfo=datetime.UTC
            ),
            updated_at=datetime.datetime(
                2025, 5, 8, 5, 34, 26, 206733, tzinfo=datetime.UTC
            ),
            completed_at=None,
            completed_by_user_id=None,
            archived_at=None,
            owner_user_id=uuid.UUID("a9d2616b-35fc-4118-a604-6635d1cb76c2"),
            created_by_user_id=uuid.UUID("a9d2616b-35fc-4118-a604-6635d1cb76c2"),
            updated_by_user_id=None,
            archived_by_user_id=None,
            pipeline_id=None,
            account_id=None,
            citation_id=None,
            sequence_id=None,
            sequence_enrollment_id=None,
            sequence_step_id=None,
            sequence_step_execution_id=None,
            sequence_step_variant_id=None,
            email_ids=[],
            email_thread_ids=[],
            meeting_id=None,
            result_meeting_ids=None,
            contact_ids=[],
            insight_id=uuid.UUID("********-a71f-4758-810e-3383bb4e0efb"),
            task_comments=[],
            source_type=TaskSourceType.SYSTEM,
            participant_user_id_list=[],
            disposition=None,
        )
    ]

    # Run the activity directly
    result = await generate_task_citations(
        intel_input=activity_input,
        tasks=tasks,
        intel_context=intel_context,
    )
    logger.info(f"Result: {result}")


if __name__ == "__main__":
    asyncio.run(main())
