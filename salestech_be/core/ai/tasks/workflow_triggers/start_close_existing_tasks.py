import time
import uuid
from uuid import UUID

from salestech_be.common.stats.metric import custom_metric
from salestech_be.core.ai.tasks.workflows.close_existing_tasks import (
    CloseExistingTasksWorkflow,
)
from salestech_be.core.ai.workflows.schema import IntelInput, IntelTriggerObjectType
from salestech_be.integrations.temporal.client import get_temporal_client
from salestech_be.integrations.temporal.config import AI_TASK_QUEUE
from salestech_be.ree_logging import get_logger

logger = get_logger(__name__)


async def start_close_existing_tasks(
    organization_id: UUID,
    object_id: UUID,
    object_type: IntelTriggerObjectType,
    account_ids: list[UUID] | None = None,
    pipeline_id: UUID | None = None,
) -> None:
    """Start the workflow to close existing tasks.

    Args:
        organization_id: The organization ID
        object_id: The ID of the object (meeting, email, etc)
        object_type: The type of object triggering the workflow
        account_id: Optional account ID
        pipeline_id: Optional pipeline ID
    """
    workflow_input = IntelInput(
        organization_id=organization_id,
        object_id=object_id,
        object_type=object_type,
        pipeline_id=pipeline_id,
        account_ids=account_ids,
        langfuse_session_id=str(uuid.uuid4()),
    )

    process_start_time = time.perf_counter()
    client = await get_temporal_client()
    custom_metric.timing(
        metric_name="cdc_event_processor_get_temporal_client",
        value=(time.perf_counter() - process_start_time) * 1000,
        tags=[
            "cdc_flow:start_close_existing_tasks_workflow",
        ],
    )

    workflow_id = CloseExistingTasksWorkflow.get_workflow_id(
        organization_id, object_id, object_type
    )

    logger.info(
        "Starting close existing tasks workflow",
        extra={
            "workflow_id": workflow_id,
            "organization_id": organization_id,
            "object_id": object_id,
            "object_type": object_type.value,
        },
    )

    process_start_time = time.perf_counter()
    try:
        await client.start_workflow(
            CloseExistingTasksWorkflow.run,
            id=workflow_id,
            task_queue=AI_TASK_QUEUE,
            start_signal="enqueue_input",
            start_signal_args=[workflow_input],
        )
        custom_metric.timing(
            metric_name="close_tasks_start_workflow",
            value=(time.perf_counter() - process_start_time) * 1000,
            tags=[
                "workflow:close_existing_tasks",
                f"object_type:{object_type.value}",
                "status:success",
            ],
        )
    except Exception as e:
        custom_metric.timing(
            metric_name="close_tasks_start_workflow",
            value=(time.perf_counter() - process_start_time) * 1000,
            tags=[
                "workflow:close_existing_tasks",
                f"object_type:{object_type.value}",
                "status:error",
            ],
        )
        logger.error(
            "Failed to start close existing tasks workflow",
            exc_info=e,
            extra={
                "workflow_id": workflow_id,
                "organization_id": organization_id,
                "object_id": object_id,
                "object_type": object_type.value,
            },
        )
        raise
