import uuid
from datetime import timedelta

from temporalio import workflow
from temporalio.common import RetryPolicy

from salestech_be.core.ai.workflows.schema import IntelInput, IntelTriggerObjectType
from salestech_be.integrations.temporal.config import AI_TASK_QUEUE

with workflow.unsafe.imports_passed_through():
    from salestech_be.core.ai.tasks.activities.close_existing_tasks import (
        close_existing_tasks,
    )


@workflow.defn
class CloseExistingTasksWorkflow:
    def __init__(self) -> None:
        self._input_queue: list[IntelInput] = []

    @workflow.signal
    async def enqueue_input(self, workflow_input: IntelInput) -> None:
        self._input_queue.append(workflow_input)

    async def _process_single_input(self, workflow_input: IntelInput) -> None:
        object_id = workflow_input.object_id
        object_type = workflow_input.object_type

        langfuse_session_id = f"intel:{object_type.value}:{object_id}"

        workflow_input = IntelInput(
            organization_id=workflow_input.organization_id,
            pipeline_id=workflow_input.pipeline_id,
            account_ids=workflow_input.account_ids,
            object_id=object_id,
            object_type=object_type,
            langfuse_session_id=langfuse_session_id,
        )
        await workflow.execute_activity(
            close_existing_tasks,
            workflow_input,
            task_queue=AI_TASK_QUEUE,
            retry_policy=RetryPolicy(maximum_attempts=3),
            start_to_close_timeout=timedelta(seconds=60),
        )

    async def _process_queue(self) -> None:
        # Process the queue until it's empty, which allows us to queue up jobs that are enqueued in parallel as a lock on pipeline_intel
        while len(self._input_queue) > 0:
            next_input = self._input_queue.pop(0)
            await self._process_single_input(next_input)

    @workflow.run
    async def run(self) -> None:
        # Wait for the first input to be enqueued from signal_with_start
        await workflow.wait_condition(lambda: len(self._input_queue) > 0)

        # Start processing the queue
        await self._process_queue()

    @staticmethod
    def get_workflow_id(
        organization_id: uuid.UUID,
        object_id: uuid.UUID,
        object_type: IntelTriggerObjectType,
    ) -> str:
        return f"close-existing-tasks-{object_id}-{object_type.value}-{organization_id}"
