from uuid import UUID

from salestech_be.common.exception import (
    ResourceNotFoundError,
)
from salestech_be.core.account.service.account_service import get_account_service
from salestech_be.core.account.types_v2 import AccountV2
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.dto.research_dto import AccountResearchDto


async def fetch_account_domain_object_from_db(
    account_id: UUID, organization_id: UUID, db_engine: DatabaseEngine
) -> AccountV2 | None:
    account_service = get_account_service(
        db_engine=db_engine,
    )

    try:
        return await account_service.get_account_v2(
            account_id=account_id, organization_id=organization_id
        )
    except ResourceNotFoundError:
        return None
    except Exception as e:
        raise e


async def fetch_account_research_from_db(
    account_id: UUID, organization_id: UUID, db_engine: DatabaseEngine
) -> AccountResearchDto | None:
    account_service = get_account_service(
        db_engine=db_engine,
    )

    return await account_service.get_account_research(
        account_id=account_id, organization_id=organization_id
    )
