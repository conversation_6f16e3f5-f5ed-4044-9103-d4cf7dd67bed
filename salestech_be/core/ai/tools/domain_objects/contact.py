from uuid import UUID

from salestech_be.common.exception import (
    ResourceNotFoundError,
)
from salestech_be.core.contact.service.contact_service import get_contact_service
from salestech_be.core.contact.types_v2 import ContactV2
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.dto.research_dto import ContactResearchDto


async def fetch_contact_domain_object_from_db(
    contact_id: UUID, organization_id: UUID, db_engine: DatabaseEngine
) -> ContactV2 | None:
    contact_service = get_contact_service(
        db_engine=db_engine,
    )

    try:
        return await contact_service.get_contact_v2(
            contact_id=contact_id, organization_id=organization_id
        )
    except ResourceNotFoundError:
        return None
    except Exception as e:
        raise e


async def fetch_contact_research_from_db(
    contact_id: UUID, organization_id: UUID, db_engine: DatabaseEngine
) -> ContactResearchDto | None:
    contact_service = get_contact_service(
        db_engine=db_engine,
    )

    return await contact_service.get_contact_research(
        contact_id=contact_id, organization_id=organization_id, referer=None
    )
