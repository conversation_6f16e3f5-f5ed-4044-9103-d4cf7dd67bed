from uuid import UUID

from salestech_be.common.exception import (
    ResourceNotFoundError,
)
from salestech_be.core.meeting.service.meeting_query_service import (
    get_meeting_query_service,
)
from salestech_be.core.meeting.types.meeting_types_v2 import MeetingV2
from salestech_be.db.dbengine.core import DatabaseEngine


async def fetch_meeting_domain_object_from_db(
    meeting_id: UUID, organization_id: UUID, db_engine: DatabaseEngine
) -> MeetingV2 | None:
    meeting_query_service = get_meeting_query_service(db_engine=db_engine)

    try:
        return await meeting_query_service.get_meeting_v2(
            meeting_id=meeting_id, organization_id=organization_id
        )
    except ResourceNotFoundError:
        return None
    except Exception as e:
        raise e
