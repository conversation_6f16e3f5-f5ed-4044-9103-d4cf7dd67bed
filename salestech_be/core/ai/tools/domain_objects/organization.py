from uuid import UUID

from salestech_be.core.organization.service.organization_service import (
    get_organization_service_general,
)
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.web.api.organization.schema import (
    OrganizationResponse,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)


async def fetch_organization_domain_object_from_db(
    organization_id: UUID, db_engine: DatabaseEngine
) -> OrganizationResponse | None:
    org_service = get_organization_service_general(db_engine=db_engine)

    try:
        return await org_service.get_organization_by_id(organization_id=organization_id)
    except Exception as e:
        raise e
