from uuid import UUID

from salestech_be.common.exception import (
    ResourceNotFoundError,
)
from salestech_be.core.pipeline.service.pipeline_query_service import (
    get_pipeline_query_service,
)
from salestech_be.core.pipeline.types_v2 import PipelineV2
from salestech_be.db.dbengine.core import DatabaseEngine


async def fetch_pipeline_domain_object_from_db(
    pipeline_id: UUID, organization_id: UUID, db_engine: DatabaseEngine
) -> PipelineV2 | None:
    pipeline_query_service = get_pipeline_query_service(db_engine=db_engine)

    try:
        return await pipeline_query_service.get_pipeline_by_id(
            pipeline_id=pipeline_id, organization_id=organization_id
        )
    except ResourceNotFoundError:
        return None
    except Exception as e:
        raise e
