from uuid import UUID

from salestech_be.common.exception import (
    ResourceNotFoundError,
)
from salestech_be.core.user.service.user_service import get_user_service_general
from salestech_be.core.user.types_v2 import OrganizationUserV2
from salestech_be.db.dbengine.core import DatabaseEngine


async def fetch_user_domain_object_from_db(
    user_id: UUID, organization_id: UUID, db_engine: DatabaseEngine
) -> OrganizationUserV2 | None:
    user_service = get_user_service_general(
        db_engine=db_engine,
    )

    try:
        return await user_service.get_user_v2(
            user_id=user_id, organization_id=organization_id
        )
    except ResourceNotFoundError:
        return None
    except Exception as e:
        raise e
