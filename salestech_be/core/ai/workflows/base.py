from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Any, Generic, TypeVar, cast
from uuid import UUID, uuid4

from pydantic import BaseModel
from temporalio import workflow
from temporalio.common import RetryPolicy

from salestech_be.core.ai.schema import TS_TASK_QUEUE, XStateHelperFunctions
from salestech_be.util.validation import not_none

with workflow.unsafe.imports_passed_through():
    from datetime import timedelta

    from salestech_be.core.ai.activities.decide_next_state import (
        DecideNextStateRequest,
        decide_next_state,
    )


# State and Result type vars for generic workflow
S = TypeVar("S", bound=str)
R = TypeVar("R")


@dataclass
class WorkflowState(Generic[S, R]):
    """Represents the current state and result of a workflow step"""

    state: S
    result: R | None = None


class GetTransitionsRequest(BaseModel):  # type: ignore[explicit-any] # TODO: fix-any-annotation
    machine_id: str
    current_state: str
    context: dict[str, Any] | None = None  # type: ignore[explicit-any] # TODO: fix-any-annotation
    session_id: UUID | None = None
    organization_id: UUID


class GetMachineMetadataRequest(BaseModel):
    machine_id: str
    session_id: UUID | None = None


class TransitionResponse(BaseModel):
    event: str
    targets: list[str]


class GetTransitionsResponse(BaseModel):
    transitions: list[TransitionResponse]


class AIWorkflow(ABC, Generic[S, R]):
    """Base class for AI workflows that follow a state machine pattern"""

    def __init__(
        self,
        machine_id: str,
        organization_id: UUID,
        user_id: UUID | None = None,
        job_id: UUID | None = None,
        session_id: UUID | None = None,
    ):
        self.machine_id = machine_id
        self.organization_id = organization_id
        self.user_id = user_id
        self.job_id = job_id
        self.session_id = session_id or uuid4()
        self.state = self._get_initial_state()
        # self._metadata = LLMTraceMetadata(
        #     user_id=user_id,
        #     session_id=self.session_id,
        #     custom_fields={
        #         "organization_id": str(organization_id),
        #         "job_id": str(job_id),
        #     },
        # )
        self._context = None

    async def setup(self) -> None:
        """Setup the workflow"""
        machine_metadata = await workflow.execute_activity(
            XStateHelperFunctions.GET_MACHINE_METADATA,
            GetMachineMetadataRequest(
                machine_id=self.machine_id,
                session_id=self.session_id,
            ),
            task_queue=TS_TASK_QUEUE,
        )
        self.initial_state: S = machine_metadata.initial_state
        self.final_states: list[S] = machine_metadata.final_states
        await self._setup()

    def _get_initial_state(self) -> S:
        """Return the initial state for the workflow"""
        return self.initial_state

    def _is_terminal_state(self, state: S) -> bool:
        """Return True if the state is terminal"""
        return state in self.final_states

    @abstractmethod
    async def _setup(self) -> None:
        """Setup the workflow"""
        raise NotImplementedError

    @abstractmethod
    async def _handle_state(self, current_state: S) -> R:
        """Handle the current state and return the next state"""
        raise NotImplementedError

    @workflow.run
    async def run(self) -> R:
        """Run the workflow until a terminal state is reached"""
        await self.setup()
        self._current_state: S = self._get_initial_state()

        retry_policy = RetryPolicy(
            initial_interval=timedelta(seconds=1),
            maximum_interval=timedelta(minutes=1),
            maximum_attempts=3,
        )

        wf_result = None
        while not self._is_terminal_state(self._current_state):
            try:
                transitions_response: GetTransitionsResponse = (
                    await workflow.execute_activity(
                        XStateHelperFunctions.GET_TRANSITIONS,
                        GetTransitionsRequest(
                            machine_id=self.machine_id,
                            current_state=str(self._current_state),
                            context=self._context,
                            session_id=self.session_id,
                            organization_id=self.organization_id,
                        ),
                        task_queue=TS_TASK_QUEUE,
                        start_to_close_timeout=timedelta(minutes=5),
                        retry_policy=retry_policy,
                    )
                )
                not_none(transitions_response)
                next_state = await workflow.execute_activity(
                    decide_next_state,
                    DecideNextStateRequest(
                        transition_events=transitions_response.transitions,
                        current_state=str(self._current_state),
                        session_id=self.session_id,
                        organization_id=self.organization_id,
                    ),
                    task_queue=TS_TASK_QUEUE,
                )
                typed_next_state = cast(S, next_state)
                await self._handle_state(typed_next_state)
                self._current_state = typed_next_state

            except Exception as e:
                # Log error and potentially transition to error state
                raise e

        return not_none(wf_result)
        # return self._current_state
