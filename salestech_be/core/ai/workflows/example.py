from enum import StrEnum
from typing import TypedDict
from uuid import UUID

from temporalio import workflow
from temporalio.common import RetryPolicy

from salestech_be.core.ai.workflows.base import AIWorkflow, WorkflowState

with workflow.unsafe.imports_passed_through():
    from datetime import timedelta


class ResearchState(StrEnum):
    INIT = "INIT"
    GATHERING_COMPANY_INFO = "GATHERING_COMPANY_INFO"
    ANALYZING_COMPETITORS = "ANALYZING_COMPETITORS"
    IDENTIFYING_PAIN_POINTS = "IDENTIFYING_PAIN_POINTS"
    PREPARING_SUMMARY = "PREPARING_SUMMARY"
    COMPLETE = "COMPLETE"
    ERROR = "ERROR"


class ResearchResult(TypedDict):
    company_info: dict[str, str]
    competitor_analysis: list[dict[str, list[str] | str]]
    pain_points: list[str]
    summary: str


# TODO: Should probably add a TS activity to pull in the parsed states?


class SalesResearchWorkflow(AIWorkflow[ResearchState, ResearchResult]):
    def __init__(
        self,
        machine_id: str,
        organization_id: UUID,
        user_id: UUID | None = None,
        job_id: UUID | None = None,
        session_id: UUID | None = None,
    ):
        super().__init__(machine_id, organization_id, user_id, job_id, session_id)
        self.machine_id = machine_id

    async def _handle_state(self, current_state: ResearchState) -> ResearchResult:
        result: ResearchResult = {
            "company_info": {},
            "competitor_analysis": [],
            "pain_points": [],
            "summary": "",
        }

        workflow_state = WorkflowState[ResearchState, ResearchResult](
            state=current_state, result=result
        )

        retry_policy = RetryPolicy(
            initial_interval=timedelta(seconds=1),
            maximum_interval=timedelta(minutes=1),
            maximum_attempts=3,
        )

        workflow_state = await workflow.execute_activity(
            "decideNextStep",
            workflow_state,
            task_queue="ts-research",
            start_to_close_timeout=timedelta(minutes=5),
            retry_policy=retry_policy,
        )

        match workflow_state.state:
            case ResearchState.INIT:
                return result

            case ResearchState.GATHERING_COMPANY_INFO:
                result = workflow_state.result or result
                result["company_info"] = {"name": "Example Corp", "industry": "Tech"}
                return result

            case ResearchState.ANALYZING_COMPETITORS:
                result = workflow_state.result or result
                result["competitor_analysis"] = [
                    {"name": "Competitor A", "strengths": ["Feature X"]}
                ]
                return result

            case ResearchState.IDENTIFYING_PAIN_POINTS:
                result = workflow_state.result or result
                result["pain_points"] = ["Outdated tech stack", "High customer churn"]
                return result

            case ResearchState.PREPARING_SUMMARY:
                result = workflow_state.result or result
                result["summary"] = (
                    "Example Corp is a tech company facing challenges..."
                )
                return result

            case _:
                raise ValueError(f"Unexpected state: {workflow_state.state}")
