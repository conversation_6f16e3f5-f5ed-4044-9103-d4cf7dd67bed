# from enum import StrEnum
# from uuid import UUID

# from pydantic import BaseModel
# from salestech_be.core.ai.workflows.base import AIWorkflow, WorkflowState
# from temporalio import workflow
# from temporalio.common import RetryPolicy

# with workflow.unsafe.imports_passed_through():
#     from datetime import timedelta


# class HowToWinTheDealState(StrEnum):
#     INIT = "INIT"
#     FETCHING_TRANSCRIPT = "FETCHING_TRANSCRIPT"
#     FETCHING_PIPELINE = "FETCHING_PIPELINE"
#     DETERMINING_GAPS = "DETERMINING_GAPS"
#     FINDING_NEXT_STEPS = "FINDING_NEXT_STEPS"
#     COMPLETE = "COMPLETE"
#     ERROR = "ERROR"


# class HowToWinTheDealResult(BaseModel):
#     summary: str


# class HowToWinTheDealWorkflow(AIWorkflow[HowToWinTheDealState, HowToWinTheDealResult]):
#     def __init__(
#         self,
#         machine_id: str,
#         organization_id: UUID,
#         user_id: UUID | None = None,
#         job_id: UUID | None = None,
#         session_id: UUID | None = None,
#     ):
#         super().__init__(machine_id, organization_id, user_id, job_id, session_id)
#         self.machine_id = machine_id

#     async def _handle_state(
#         self, current_state: HowToWinTheDealState
#     ) -> HowToWinTheDealResult:
#         result = HowToWinTheDealResult(summary="")
#         steps = []

#         workflow_state = WorkflowState[HowToWinTheDealState, HowToWinTheDealResult](
#             state=current_state, result=result
#         )

#         retry_policy = RetryPolicy(
#             initial_interval=timedelta(seconds=1),
#             maximum_interval=timedelta(minutes=1),
#             maximum_attempts=3,
#         )

#         workflow_state = await workflow.execute_activity(
#             "decideNextStep",
#             workflow_state,
#             task_queue="ts-research",
#             start_to_close_timeout=timedelta(minutes=5),
#             retry_policy=retry_policy,
#         )

#         match workflow_state.state:
#             case HowToWinTheDealState.INIT:
#                 return result

#             case HowToWinTheDealState.FETCHING_TRANSCRIPT:
#                 result = workflow_state.result or result
#                 steps.append(
#                     {
#                         "step": "Call the CEO",
#                         "summary": "Call the CEO to discuss the opportunity",
#                     }
#                 )
#                 return result

#             case HowToWinTheDealState.FETCHING_PIPELINE:
#                 result = workflow_state.result or result
#                 steps.append(
#                     {
#                         "step": "Call the CEO",
#                         "summary": "Call the CEO to discuss the opportunity",
#                     }
#                 )
#                 return result

#             case HowToWinTheDealState.DETERMINING_GAPS:
#                 result = workflow_state.result or result
#                 steps.append(
#                     {
#                         "step": "Call the CEO",
#                         "summary": "Call the CEO to discuss the opportunity",
#                     }
#                 )
#                 return result

#             case HowToWinTheDealState.FINDING_NEXT_STEPS:
#                 result = workflow_state.result or result
#                 steps.append(
#                     {
#                         "step": "Call the CEO",
#                         "summary": "Call the CEO to discuss the opportunity",
#                     }
#                 )
#                 return result

#             case _:
#                 raise ValueError(f"Unexpected state: {workflow_state.state}")
