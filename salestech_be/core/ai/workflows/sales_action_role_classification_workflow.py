import asyncio

from temporalio import workflow
from temporalio.common import RetryPolicy

with workflow.unsafe.imports_passed_through():
    from datetime import timedelta
    from uuid import UUID

    from salestech_be.core.ai.opportuntity_stages.activities.generate_sales_action_role_classification import (
        generate_contact_pipeline_role_classification,
        generate_sales_action_classification,
    )
    from salestech_be.core.ai.workflows.schema import SalesActionRoleClassificationInput
    from salestech_be.core.opportunity_stage_criteria.criteria_types import (
        CriteriaExtractionSourceObjectId,
    )
    from salestech_be.integrations.temporal.config import AI_TASK_QUEUE


@workflow.defn
class SalesActionRoleClassificationWorkflow:
    @workflow.run
    async def run(self, workflow_input: SalesActionRoleClassificationInput) -> None:
        sales_action_activity = workflow.execute_activity(
            generate_sales_action_classification,
            args=(
                workflow_input.organization_id,
                workflow_input.pipeline_id,
                workflow_input.source_object,
            ),
            task_queue=AI_TASK_QUEUE,
            start_to_close_timeout=timedelta(minutes=5),
            schedule_to_close_timeout=timedelta(minutes=10),
            retry_policy=RetryPolicy(
                maximum_attempts=3,
                initial_interval=timedelta(seconds=60),
                backoff_coefficient=3.0,
            ),
        )

        contact_pipeline_role_activity = workflow.execute_activity(
            generate_contact_pipeline_role_classification,
            args=(
                workflow_input.organization_id,
                workflow_input.pipeline_id,
                workflow_input.source_object,
            ),
            task_queue=AI_TASK_QUEUE,
            start_to_close_timeout=timedelta(minutes=5),
            schedule_to_close_timeout=timedelta(minutes=10),
            retry_policy=RetryPolicy(
                maximum_attempts=3,
                initial_interval=timedelta(seconds=60),
                backoff_coefficient=3.0,
            ),
        )

        try:
            await asyncio.gather(sales_action_activity, contact_pipeline_role_activity)
        except Exception as e:
            workflow.logger.exception(
                "Error during parallel sales action and contact pipeline role classification"
            )
            raise e

    @staticmethod
    def get_workflow_id(
        organization_id: UUID,
        pipeline_id: UUID,
        source_object: CriteriaExtractionSourceObjectId,
    ) -> str:
        return f"sales-action-role-classification-{source_object.object_type.value}-{source_object.object_id}-{organization_id}-{pipeline_id}"
