from temporalio import workflow
from temporalio.common import RetryPolicy

with workflow.unsafe.imports_passed_through():
    import asyncio
    from datetime import timedelta
    from uuid import UUID

    from salestech_be.core.ai.opportuntity_stages.activities.generate_criteria import (
        generate_criteria,
    )
    from salestech_be.core.ai.opportuntity_stages.criteria_configs import (
        CRITERIA_CONFIG_MAP,
    )
    from salestech_be.core.ai.workflows.schema import StageCriteriaWorkflowInput
    from salestech_be.core.opportunity_stage_criteria.criteria_types import (
        CriteriaExtractionSourceObjectId,
    )
    from salestech_be.integrations.temporal.config import AI_TASK_QUEUE


@workflow.defn
class StageCriteriaWorkflow:
    @workflow.run
    async def run(
        self,
        workflow_input: StageCriteriaWorkflowInput,
    ) -> None:
        criteria_activities = []
        for config_type in CRITERIA_CONFIG_MAP:
            criteria_activity = workflow.execute_activity(
                generate_criteria,
                args=(
                    workflow_input.organization_id,
                    workflow_input.pipeline_id,
                    workflow_input.user_id,
                    workflow_input.source_object,
                    config_type,
                ),
                task_queue=AI_TASK_QUEUE,
                start_to_close_timeout=timedelta(minutes=15),
                schedule_to_close_timeout=timedelta(minutes=20),
                retry_policy=RetryPolicy(
                    maximum_attempts=3,
                    initial_interval=timedelta(seconds=60),
                    backoff_coefficient=3.0,
                ),
            )
            criteria_activities.append(criteria_activity)

        try:
            await asyncio.gather(*criteria_activities)
        except Exception as e:
            workflow.logger.exception("Error during parallel organization indexing")
            raise e

    @staticmethod
    def get_workflow_id(
        organization_id: UUID,
        pipeline_id: UUID,
        source_object: CriteriaExtractionSourceObjectId,
    ) -> str:
        return f"stage-criteria-{source_object.object_type.value}-{source_object.object_id}-{organization_id}-{pipeline_id}"
