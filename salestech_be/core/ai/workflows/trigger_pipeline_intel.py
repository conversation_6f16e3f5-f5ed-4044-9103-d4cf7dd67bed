import asyncio
import uuid
from datetime import timedelta

from temporalio import workflow
from temporalio.common import RetryPolicy

from salestech_be.core.ai.workflows.schema import (
    MeetingTriggerPipelineIntelInput,
)
from salestech_be.integrations.temporal.config import AI_TASK_QUEUE

with workflow.unsafe.imports_passed_through():
    from salestech_be.core.ai.activities.meeting_pipeline_intel_activities import (
        generate_meeting_activity_summary,
        generate_meeting_how_to_win,
        generate_meeting_objections,
        generate_meeting_risks,
        upsert_pipeline_intel,
    )
    from salestech_be.db.models.pipeline_intel import PipelineIntel


@workflow.defn
class MeetingTriggerPipelineIntelWorkflow:
    def __init__(self) -> None:
        self._input_queue: list[MeetingTriggerPipelineIntelInput] = []

    @workflow.signal
    async def enqueue_input(
        self, workflow_input: MeetingTriggerPipelineIntelInput
    ) -> None:
        self._input_queue.append(workflow_input)

    async def _process_single_input(
        self, workflow_input: MeetingTriggerPipelineIntelInput
    ) -> None:
        # Generate insights from meeting in parallel
        activity_summary_task = workflow.execute_activity(
            generate_meeting_activity_summary,
            workflow_input,
            task_queue=AI_TASK_QUEUE,
            retry_policy=RetryPolicy(maximum_attempts=3),
            start_to_close_timeout=timedelta(seconds=60),
        )

        how_to_win_task = workflow.execute_activity(
            generate_meeting_how_to_win,
            workflow_input,
            task_queue=AI_TASK_QUEUE,
            retry_policy=RetryPolicy(maximum_attempts=3),
            start_to_close_timeout=timedelta(seconds=60),
        )

        risks_task = workflow.execute_activity(
            generate_meeting_risks,
            workflow_input,
            task_queue=AI_TASK_QUEUE,
            retry_policy=RetryPolicy(maximum_attempts=3),
            start_to_close_timeout=timedelta(seconds=60),
        )

        objections_task = workflow.execute_activity(
            generate_meeting_objections,
            workflow_input,
            task_queue=AI_TASK_QUEUE,
            retry_policy=RetryPolicy(maximum_attempts=3),
            start_to_close_timeout=timedelta(seconds=60),
        )

        (
            activity_summary,
            how_to_win,
            risks,
            objections,
        ) = await asyncio.gather(
            activity_summary_task,
            how_to_win_task,
            risks_task,
            objections_task,
        )

        # Create/update pipeline intel
        pipeline_intel = PipelineIntel(
            pipeline_id=workflow_input.pipeline_id,
            organization_id=workflow_input.organization_id,
            account_id=workflow_input.account_id,
            activity_summary=activity_summary,
            how_to_win_the_deal=how_to_win,
            perceived_risks=risks,
            direct_objections=objections,
            langfuse_session_id=str(workflow_input.langfuse_session_id),
        )

        await workflow.execute_activity(
            upsert_pipeline_intel,
            pipeline_intel,
            task_queue=AI_TASK_QUEUE,
            retry_policy=RetryPolicy(maximum_attempts=3),
            start_to_close_timeout=timedelta(seconds=30),
        )

    async def _process_queue(self) -> None:
        # Process the queue until it's empty, which allows us to queue up jobs that are enqueued in parallel as a lock on pipeline_intel
        while len(self._input_queue) > 0:
            next_input = self._input_queue.pop(0)
            await self._process_single_input(next_input)

    @workflow.run
    async def run(self) -> None:
        # Wait for the first input to be enqueued from signal_with_start
        await workflow.wait_condition(lambda: len(self._input_queue) > 0)
        # Start processing the queue
        await self._process_queue()

    @staticmethod
    def get_workflow_id(
        meeting_id: uuid.UUID | None, organization_id: uuid.UUID
    ) -> str:
        return f"meeting-pipeline-intel-{meeting_id or 'no-meeting'}-{organization_id}"
