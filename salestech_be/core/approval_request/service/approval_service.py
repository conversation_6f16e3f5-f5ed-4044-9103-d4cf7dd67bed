from typing import Annotated
from uuid import UUID, uuid4

from fastapi import Depends
from starlette.requests import Request

from salestech_be.common.exception import ConflictResourceError, ResourceNotFoundError
from salestech_be.common.lifespan import get_db_engine
from salestech_be.core.approval_request.types import (
    ApprovalRequest,
    UpsertApprovalRequestRequest,
)
from salestech_be.db.dao.approval_request_repository import ApprovalRequestRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.approval_request import (
    ApprovalRequest as DbApprovalRequest,
)
from salestech_be.db.models.approval_request import (
    ApprovalRequestReference,
    ApprovalRequestStatus,
    ApprovalRequestType,
    ApproveRequestUpdate,
)
from salestech_be.ree_logging import get_logger
from salestech_be.settings import settings
from salestech_be.util.time import zoned_utc_now

logger = get_logger(__name__)


class ApprovalService:
    def __init__(
        self,
        approval_request_repository: Annotated[ApprovalRequestRepository, Depends()],
    ):
        super().__init__()
        self.approval_request_repository = approval_request_repository
        self.logger = logger

    async def get_by_id(
        self, request_id: UUID, organization_id: UUID
    ) -> ApprovalRequest:
        db_request = (
            await self.approval_request_repository.find_by_tenanted_primary_key_or_fail(
                table_model=DbApprovalRequest,
                organization_id=organization_id,
                id=request_id,
            )
        )
        return ApprovalRequest.map_from_db(db_request)

    async def list_all(
        self,
        organization_id: UUID,
        include_deleted: bool | None = False,
    ) -> list[ApprovalRequest]:
        all_db_approval_request = (
            await self.approval_request_repository.list_by_organization_id(
                organization_id=organization_id,
                exclude_deleted_or_archived=not include_deleted,
            )
        )
        return [
            ApprovalRequest.map_from_db(db_approval_request=request)
            for request in all_db_approval_request
        ]

    async def update_approval_request(
        self,
        user_id: UUID,
        organization_id: UUID,
        request_id: UUID,
        update_approval_request: UpsertApprovalRequestRequest,
    ) -> ApprovalRequest:
        db_request = (
            await self.approval_request_repository.update_by_tenanted_primary_key(
                DbApprovalRequest,
                primary_key_to_value={"id": request_id},
                organization_id=organization_id,
                column_to_update={
                    "updated_by": user_id,
                    "updated_at": zoned_utc_now(),
                    "status": update_approval_request.status,
                    "reference": update_approval_request.reference,
                    "hash_key": update_approval_request.hash_key,
                },
            )
        )
        if not db_request:
            raise ResourceNotFoundError(f"approval request not found: {request_id}")
        return ApprovalRequest.map_from_db(db_approval_request=db_request)

    async def create_approval_request(
        self,
        user_id: UUID,
        organization_id: UUID,
        create_approval_request: UpsertApprovalRequestRequest,
    ) -> ApprovalRequest:
        logger.bind(
            user_id=user_id,
            organization_id=organization_id,
            request=create_approval_request,
        ).info("Create note request")

        if (
            existing_request
            := await self.approval_request_repository.find_by_org_hash_key(
                organization_id=organization_id,
                hash_key=create_approval_request.hash_key,
            )
        ):
            self.logger.bind(
                organization_id=organization_id,
                create_approval_request=create_approval_request,
                existing_request=existing_request,
            ).warning("Found and returned existing approval request.")
            return ApprovalRequest.map_from_db(db_approval_request=existing_request)

        created_request = await self.approval_request_repository.insert(
            instance=DbApprovalRequest(
                id=uuid4(),
                organization_id=organization_id,
                type=create_approval_request.type,
                status=create_approval_request.status,
                description=create_approval_request.description,
                display_name=create_approval_request.display_name,
                reference=create_approval_request.reference,
                hash_key=create_approval_request.hash_key,
                created_at=zoned_utc_now(),
                created_by_user_id=user_id,
            )
        )
        return ApprovalRequest.map_from_db(db_approval_request=created_request)

    async def approve_request(
        self,
        organization_id: UUID,
        user_id: UUID,
        request_id: UUID,
        allow_approved: bool = True,
    ) -> ApprovalRequest:
        request = await self.approval_request_repository.find_by_tenanted_primary_key(
            table_model=DbApprovalRequest,
            id=request_id,
            organization_id=organization_id,
        )
        if not request:
            raise ResourceNotFoundError(
                f"Record not found for approval_request with id {request_id}",
            )

        allowed_statuses = [ApprovalRequestStatus.REQUESTED]
        if allow_approved:
            allowed_statuses.append(ApprovalRequestStatus.APPROVED)
        if request.status not in allowed_statuses:
            raise ConflictResourceError(
                f"Request of id {request_id} has status {request.status} "
                f"which is not in allowed statuses: {allowed_statuses}",
            )

        db_approve_request = await self.approval_request_repository.patch_by_id(
            organization_id=organization_id,
            approval_request_id=request_id,
            approve_request_update=ApproveRequestUpdate(
                status=ApprovalRequestStatus.APPROVED,
                approved_at=zoned_utc_now(),
                approved_by_user_id=user_id,
                updated_at=zoned_utc_now(),
                updated_by_user_id=user_id,
            ),
        )

        return ApprovalRequest.map_from_db(db_approve_request)

    async def deny_request(
        self,
        organization_id: UUID,
        user_id: UUID,
        request_id: UUID,
    ) -> ApprovalRequest:
        request = await self.approval_request_repository.find_by_tenanted_primary_key(
            table_model=DbApprovalRequest,
            id=request_id,
            organization_id=organization_id,
        )
        if not request:
            raise ResourceNotFoundError(
                f"Record not found for approval_requestwith id {request_id}",
            )

        if request.status != ApprovalRequestStatus.REQUESTED:
            raise ConflictResourceError(
                f"Request not in REQUESTED statuswith id {request_id}",
            )

        denied_reqeust = await self.approval_request_repository.patch_by_id(
            organization_id=organization_id,
            approval_request_id=request_id,
            approve_request_update=ApproveRequestUpdate(
                status=ApprovalRequestStatus.DENIED,
                decision_details="",
                denied_at=zoned_utc_now(),
                denied_by_user_id=user_id,
                updated_at=zoned_utc_now(),
                updated_by_user_id=user_id,
            ),
        )
        return ApprovalRequest.map_from_db(denied_reqeust)

    async def check_approval_for_shift(
        self,
        organization_id: UUID,
        user_id: UUID,
        approval_request_type: ApprovalRequestType,
        approval_request_display_name: str,
        approval_request_description: str,
        approval_request_ref: ApprovalRequestReference,
        existing_approval_request_id: UUID | None = None,
        existing_approval_type: ApprovalRequestType | None = None,
    ) -> tuple[bool, ApprovalRequest | None]:
        """
        cases:
            if approval requirements disabled in settings -> check cleared; return no request
            if request has approve_request_id -> verify given request, and
                if request approved -> check cleared; return existing request
                else -> check blocked; return existing approve request
            else -> check blocked; create new approve request and return
        returns:
            approval_all_clear: bool
            approval_request: ApprovalRequest | None
        """
        if not settings.enable_approval_requirements:
            return True, None
        if existing_approval_request_id:
            existing_request = await self.get_by_id(
                organization_id=organization_id, request_id=existing_approval_request_id
            )
            if (
                existing_approval_type
                and existing_request.type != existing_approval_type
            ):
                # TODO log approval request type mismatch
                return False, None
            return (
                existing_request.status == ApprovalRequestStatus.APPROVED
            ), existing_request
        # otherwise create new request
        new_approval_request = await self.create_approval_request(
            user_id=user_id,
            organization_id=organization_id,
            create_approval_request=UpsertApprovalRequestRequest(
                type=approval_request_type,
                description=approval_request_description,
                display_name=approval_request_display_name,
                reference=approval_request_ref,
            ),
        )
        return False, new_approval_request

    async def update_approval_state(
        self,
        organization_id: UUID,
        user_id: UUID,
        approval_request: ApprovalRequest,
        approval_request_status: ApprovalRequestStatus,
    ) -> ApprovalRequest:
        return await self.update_approval_request(
            user_id=user_id,
            organization_id=organization_id,
            request_id=approval_request.id,
            update_approval_request=UpsertApprovalRequestRequest(
                type=approval_request.type,
                description=approval_request.description,
                display_name=approval_request.display_name,
                reference=approval_request.reference,
                status=approval_request_status,
            ),
        )


def get_approval_service_with_engine(
    db_engine: DatabaseEngine,
) -> ApprovalService:
    return ApprovalService(
        approval_request_repository=ApprovalRequestRepository(engine=db_engine),
    )


def get_approval_service(request: Request) -> ApprovalService:
    return get_approval_service_with_engine(db_engine=get_db_engine(request))
