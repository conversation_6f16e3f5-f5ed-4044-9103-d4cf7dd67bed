from __future__ import annotations

from abc import ABC, abstractmethod
from typing import Any
from uuid import UUID

from pydantic import BaseModel

from salestech_be.db.models.approval_request import (
    ApprovalRequest as DbApprovalRequest,
)
from salestech_be.db.models.approval_request import (
    ApprovalRequestReference,
    ApprovalRequestStatus,
    ApprovalRequestType,
)
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime


class ApprovalRequest(BaseModel):
    """The ApprovalRequest model."""

    id: UUID
    organization_id: UUID
    type: ApprovalRequestType
    status: ApprovalRequestStatus
    description: str | None = None
    display_name: str
    reference: ApprovalRequestReference
    hash_key: str
    decision_details: str | None = None
    created_at: ZoneRequiredDateTime
    created_by_user_id: UUID
    updated_at: ZoneRequiredDateTime | None = None
    updated_by_user_id: UUID | None = None
    deleted_at: ZoneRequiredDateTime | None = None
    deleted_by_user_id: UUID | None = None
    approved_at: ZoneRequiredDateTime | None = None
    approved_by_user_id: UUID | None = None
    denied_at: ZoneRequiredDateTime | None = None
    denied_by_user_id: UUID | None = None
    cancelled_at: ZoneRequiredDateTime | None = None
    cancelled_by_user_id: UUID | None = None

    @classmethod
    def map_from_db(cls, db_approval_request: DbApprovalRequest) -> ApprovalRequest:
        return ApprovalRequest(
            id=db_approval_request.id,
            organization_id=db_approval_request.organization_id,
            type=db_approval_request.type,
            status=db_approval_request.status,
            description=db_approval_request.description,
            display_name=db_approval_request.display_name,
            reference=db_approval_request.reference,
            hash_key=db_approval_request.hash_key,
            decision_details=db_approval_request.decision_details,
            created_at=db_approval_request.created_at,
            created_by_user_id=db_approval_request.created_by_user_id,
            updated_at=db_approval_request.updated_at,
            updated_by_user_id=db_approval_request.updated_by_user_id,
            deleted_at=db_approval_request.deleted_at,
            deleted_by_user_id=db_approval_request.deleted_by_user_id,
            approved_at=db_approval_request.approved_at,
            approved_by_user_id=db_approval_request.approved_by_user_id,
            denied_at=db_approval_request.denied_at,
            denied_by_user_id=db_approval_request.denied_by_user_id,
            cancelled_at=db_approval_request.cancelled_at,
            cancelled_by_user_id=db_approval_request.cancelled_by_user_id,
        )

    def __eq__(self, other: Any) -> bool:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        return (
            other is not None
            and type(other) is ApprovalRequest
            and self.dict() == other.dict()
        )

    def __hash__(self) -> int:
        return hash(self.id)


class UpsertApprovalRequestRequest(BaseModel):
    type: ApprovalRequestType
    description: str | None = None
    display_name: str
    reference: ApprovalRequestReference
    status: ApprovalRequestStatus = ApprovalRequestStatus.REQUESTED

    @property
    def hash_key(self) -> str:
        return self.reference.hash_key


class IApprovableActionResponse(ABC):
    @property
    @abstractmethod
    def is_successful(self) -> bool:
        raise NotImplementedError
