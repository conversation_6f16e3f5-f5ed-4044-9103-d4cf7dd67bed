from uuid import UUID

from pydantic import BaseModel

from salestech_be.util.enum_util import NameValueStrEnum


class SalesMethodologyType(NameValueStrEnum):
    MEDDPICC = "MEDDPICC"
    MEDDIC = "MEDDIC"
    SPICED = "SPICED"


class SalesMethodologyRequest(BaseModel):
    methodology: SalesMethodologyType


class SalesMethodologyTemplate(BaseModel):
    type: SalesMethodologyType
    display_name: str
    description: str


class OrganizationSalesMethodology(BaseModel):
    organization_id: UUID
    sales_methodology_id: UUID
    type: SalesMethodologyType
    display_name: str
    description: str
