from uuid import UUID

from salestech_be.core.calendar.user_calendar_data_integrity_service import (
    UserCalendarDataIntegrityService,
)
from salestech_be.core.contact.service.contact_service import ContactService
from salestech_be.core.crm_integrity.event_processors.abstract_processor import (
    AbstractDataOperationProcessor,
)
from salestech_be.core.crm_integrity.event_processors.processor_utils import (
    ProcessorUtils,
)
from salestech_be.core.crm_integrity.types.activity_params import (
    CrmIdReplacementTrackParam,
    DataMovementParam,
)
from salestech_be.core.crm_integrity.types.request_schema import (
    IntegrityAssociatedDataOperationPreviewIdentifiers,
)
from salestech_be.db.models.contact import ContactEmail
from salestech_be.db.models.crm_integrity import (
    AssociatedEntityField,
    AssociatedEntityFieldType,
    AssociatedEntityOperation,
    AssociatedEntityType,
    CRMIntegrityJobUserOption,
)
from salestech_be.util.validation import not_none


class CalendarContactEmailMoveEventProcessor(
    AbstractDataOperationProcessor[DataMovementParam, CrmIdReplacementTrackParam]
):
    """
    Event processor for Contact Email Move within Calendar domain and associated activities.

    - update account_id in all associated calendar events
    - unset existing account_id if it's not associated to the new contact_id

    """

    def __init__(
        self,
        user_calendar_data_integrity_service: UserCalendarDataIntegrityService,
        contact_service: ContactService,
    ) -> None:
        self.user_calendar_data_integrity_service = user_calendar_data_integrity_service
        self.contact_service = contact_service

    async def fetch_contact_email(
        self,
        *,
        contact_email_id: UUID,
        organization_id: UUID,
    ) -> ContactEmail:
        contact_email = await self.contact_service.get_contact_email_by_id(
            organization_id=organization_id,
            contact_email_id=contact_email_id,
            exclude_deleted_or_archived=False,
        )

        if not contact_email:
            raise ValueError(f"contact email {contact_email_id} not found")

        return contact_email

    async def fetch_affected_calendar_event(
        self, contact_id: UUID, email: str, organization_id: UUID
    ) -> list[UUID]:
        return [
            calendar_event.id
            for calendar_event in await self.user_calendar_data_integrity_service.find_calendar_events_in_participants_by_contact_id(
                contact_id=contact_id,
                email=email,
                organization_id=organization_id,
            )
        ]

    async def process(
        self,
        *,
        param: DataMovementParam,
        user_choice: CRMIntegrityJobUserOption | None = None,
    ) -> list[CrmIdReplacementTrackParam]:
        track_params = []

        contact_email = await self.fetch_contact_email(
            contact_email_id=param.move_entity_id,
            organization_id=param.organization_id,
        )

        calendar_event_ids = await self.fetch_affected_calendar_event(
            contact_id=not_none(param.src_entity_id),
            email=contact_email.email,
            organization_id=param.organization_id,
        )

        for calendar_event_id in calendar_event_ids:
            (
                old_calendar_event,
                new_calendar_event,
            ) = await self.user_calendar_data_integrity_service.replace_contact_id_in_calendar_event(
                calendar_event_id=calendar_event_id,
                contact_id=not_none(param.src_entity_id),
                replaced_contact_id=param.dest_entity_id,
                organization_id=param.organization_id,
                email=contact_email.email,
            )

            if old_calendar_event.participants != new_calendar_event.participants:
                track_params.append(
                    CrmIdReplacementTrackParam(
                        entity_type=AssociatedEntityType.USER_CALENDAR_EVENT,
                        entity_id=calendar_event_id,
                        entity_field_name=AssociatedEntityField.PARTICIPANTS,
                        entity_field_type=AssociatedEntityFieldType.JSON,
                        entity_operation=AssociatedEntityOperation.UPDATE,
                        before_value=ProcessorUtils.stringify_json_list_to_string(
                            old_calendar_event.participants
                        ),
                        after_value=ProcessorUtils.stringify_json_list_to_string(
                            new_calendar_event.participants
                        ),
                    )
                )

        return track_params

    async def preview(
        self,
        *,
        param: DataMovementParam,
        user_choice: CRMIntegrityJobUserOption | None = None,
    ) -> list[IntegrityAssociatedDataOperationPreviewIdentifiers]:
        """
        Nothing to preview for Calendar domain.
        """
        return []
