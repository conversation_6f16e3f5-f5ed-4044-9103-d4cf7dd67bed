from uuid import UUID

from salestech_be.core.calendar.user_calendar_data_integrity_service import (
    UserCalendarDataIntegrityService,
)
from salestech_be.core.crm_integrity.event_processors.abstract_processor import (
    AbstractDataOperationProcessor,
)
from salestech_be.core.crm_integrity.event_processors.processor_utils import (
    ProcessorUtils,
)
from salestech_be.core.crm_integrity.types.activity_params import (
    CrmIdReplacementTrackParam,
    DataRemovalParam,
)
from salestech_be.core.crm_integrity.types.request_schema import (
    IntegrityAssociatedDataOperationPreviewIdentifiers,
)
from salestech_be.db.models.crm_integrity import (
    AssociatedEntityField,
    AssociatedEntityFieldType,
    AssociatedEntityOperation,
    AssociatedEntityType,
    CRMIntegrityJobUserOption,
)


class CalendarContactRemoveEventProcessor(
    AbstractDataOperationProcessor[DataRemovalParam, CrmIdReplacementTrackParam]
):
    """
    Event processor for Contact Removal within Calendar domain and associated activities.

    - unset account_id in all associated Calendar events

    """

    def __init__(
        self,
        user_calendar_data_integrity_service: UserCalendarDataIntegrityService,
    ) -> None:
        self.user_calendar_data_integrity_service = user_calendar_data_integrity_service

    async def fetch_affected_calendar_event(
        self, contact_id: UUID, account_id: UUID, organization_id: UUID
    ) -> list[UUID]:
        return [
            calendar_event.id
            for calendar_event in await self.user_calendar_data_integrity_service.find_calendar_events_in_participants_by_contact_id(
                contact_id=contact_id,
                account_id=account_id,
                organization_id=organization_id,
            )
        ]

    async def process(
        self,
        *,
        param: DataRemovalParam,
        user_choice: CRMIntegrityJobUserOption | None = None,
    ) -> list[CrmIdReplacementTrackParam]:
        track_params = []

        calendar_event_ids = await self.fetch_affected_calendar_event(
            contact_id=param.remove_entity_id,
            account_id=param.src_entity_id,
            organization_id=param.organization_id,
        )

        for calendar_event_id in calendar_event_ids:
            (
                old_calendar_event,
                new_calendar_event,
            ) = await self.user_calendar_data_integrity_service.replace_account_id_in_calendar_event(
                calendar_event_id=calendar_event_id,
                account_id=param.src_entity_id,
                contact_id=param.remove_entity_id,
                replaced_account_id=None,
                organization_id=param.organization_id,
            )

            if old_calendar_event.participants != new_calendar_event.participants:
                track_params.append(
                    CrmIdReplacementTrackParam(
                        entity_type=AssociatedEntityType.USER_CALENDAR_EVENT,
                        entity_id=calendar_event_id,
                        entity_field_name=AssociatedEntityField.PARTICIPANTS,
                        entity_field_type=AssociatedEntityFieldType.JSON,
                        entity_operation=AssociatedEntityOperation.UPDATE,
                        before_value=ProcessorUtils.stringify_json_list_to_string(
                            old_calendar_event.participants
                        ),
                        after_value=ProcessorUtils.stringify_json_list_to_string(
                            new_calendar_event.participants
                        ),
                    )
                )

        return track_params

    async def preview(
        self,
        *,
        param: DataRemovalParam,
        user_choice: CRMIntegrityJobUserOption | None = None,
    ) -> list[IntegrityAssociatedDataOperationPreviewIdentifiers]:
        """
        Nothing to preview for Calendar domain.
        """
        return []
