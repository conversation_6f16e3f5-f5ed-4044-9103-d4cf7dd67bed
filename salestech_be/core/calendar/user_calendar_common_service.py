from datetime import timed<PERSON><PERSON>
from typing import Any, assert_never
from uuid import UUID, uuid4

from asyncpg import UniqueViolationError
from fastapi import HTTPException, Request, status
from pydantic import EmailStr, ValidationError
from sqlalchemy.exc import IntegrityError

from salestech_be.common.exception import ConflictResourceError
from salestech_be.common.exception.exception import ResourceNotFoundError
from salestech_be.common.lifespan import get_db_engine
from salestech_be.common.singleton import Singleton
from salestech_be.common.type.formatted_string import EmailStrLower
from salestech_be.core.activity.service.activity_service import (
    ActivityService as ActivityServiceV2,
)
from salestech_be.core.activity.service.activity_service import (
    get_activity_service_general,
)
from salestech_be.core.activity.types import (
    ActivityRequest,
    ActivitySubReferenceRequest,
)
from salestech_be.core.calendar.types import MeetingSync
from salestech_be.core.calendar.user_calendar_crm_association_mapper import (
    _UserCalendarCrmAssociationMapper,
)
from salestech_be.core.common.email_calendar_mixin import EmailCalendarMixin
from salestech_be.core.contact.service.contact_query_service import (
    ContactQueryService,
    get_contact_query_service,
)
from salestech_be.core.contact.service.contact_resolve_service import (
    ContactResolveService,
    get_contact_resolve_service,
)
from salestech_be.core.contact.service.contact_service import (
    ContactService,
    get_contact_service,
)
from salestech_be.core.contact.types_v2 import ContactV2
from salestech_be.core.domain_crm_association.domain_crm_association_service import (
    DomainCRMAssociationService,
    get_domain_crm_association_service,
)
from salestech_be.core.ff.feature_flag_service import (
    FeatureFlagService,
    get_feature_flag_service,
)
from salestech_be.core.ff.types import FeatureFlagRequest
from salestech_be.core.meeting.conference_service import (
    ConferenceService,
    init_conference_service_by_db_engine,
)
from salestech_be.core.meeting.dto.meeting_dto import MeetingDto
from salestech_be.core.meeting.meeting_service import (
    MeetingService,
    meeting_service_factory_general,
)
from salestech_be.core.metadata.service.internal_select_list_service import (
    InternalSelectListService,
    get_select_list_service,
)
from salestech_be.core.organization.service.organization_preference_service import (
    OrganizationPreferenceService,
    organization_preference_service_from_engine,
)
from salestech_be.core.organization.service.organization_service import (
    OrganizationService,
    get_organization_service_general,
)
from salestech_be.core.user.service.user_service import (
    UserService,
    get_user_service_general,
)
from salestech_be.db.dao.event_schedule_repository import EventScheduleRepository
from salestech_be.db.dao.user_calendar_repository import UserCalendarRepository
from salestech_be.db.dao.user_integration_repository import UserIntegrationRepository
from salestech_be.db.dao.user_repository import UserRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.dto.user_calendar_event_dto import UserCalendarEventDto
from salestech_be.db.models.activity import (
    ActivityMetadataModel,
    ActivityPriority,
    ActivityReferenceIdType,
    ActivityStatus,
    ActivitySubReferenceType,
    ActivitySubType,
    ActivityType,
    CalendarEventRsvpActivityMetadata,
    CalendarEventRsvpStatus,
)
from salestech_be.db.models.contact import Contact
from salestech_be.db.models.event_schedule import EventSchedule
from salestech_be.db.models.meeting import (
    Meeting,
    MeetingProvider,
    MeetingReferenceIdType,
    MeetingStatus,
)
from salestech_be.db.models.organization_info import (
    MeetingRecordingLevel,
    MeetingSyncLevel,
)
from salestech_be.db.models.user import User
from salestech_be.db.models.user_calendar_event import (
    CalendarEventParticipant,
    UserCalendarEvent,
)
from salestech_be.db.models.user_integration import IntegrationType, UserIntegration
from salestech_be.integrations.google.client import GoogleClient
from salestech_be.integrations.google.schema import (
    EncryptedTokens,
    GoogleConferenceConferenceSolution,
    GoogleConferenceData,
    GoogleConferenceDataCreateRequest,
    GoogleConferenceDataEntryPoint,
    GoogleConferenceSolutionKey,
    GoogleEvent,
    GoogleEventRequest,
)
from salestech_be.integrations.nylas.async_nylas_client import AsyncNylasClient
from salestech_be.integrations.nylas.model import (
    NylasConferencing,
    NylasConferencingAutocreate,
    NylasConferencingDetails,
    NylasConferencingProvider,
    NylasUpdateEventRequest,
    NylasUpdateTimespan,
)
from salestech_be.integrations.zoom.type import (
    CreateMeetingRequest,
    ZoomMeeting,
    ZoomMeetingType,
)
from salestech_be.ree_logging import get_logger
from salestech_be.services.auth.encryptions import fernet_encryption_manager
from salestech_be.settings import settings
from salestech_be.util.pydantic_types.str import validate_e164
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime
from salestech_be.util.time import zoned_utc_now
from salestech_be.util.validation import not_none
from salestech_be.web.api.calendar.schema import (
    EventConferencingRequest,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.web.api.meeting.schema import (
    CreateMeetingRequest as CreateMeetingServiceRequest,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.web.api.meeting.schema import (
    Invitee,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    MeetingEventConferencing,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    PatchMeetingRequest,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.web.api.organization.schema import (
    ActivityCaptureMode,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    OrganizationProfile,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)

logger = get_logger(__name__)


class UserCalendarCommonService(EmailCalendarMixin):
    EVENT_DESCRIPTION_SEPARATOR = "\n\n\n====================\n"

    def __init__(
        self,
        google_client: GoogleClient,
        contact_service: ContactService,
        user_integration_repo: UserIntegrationRepository,
        user_cal_repo: UserCalendarRepository,
        user_repo: UserRepository,
        conference_service: ConferenceService,
        meeting_service: MeetingService,
        user_service: UserService,
        event_schedule_repo: EventScheduleRepository,
        select_list_service: InternalSelectListService,
        activity_service_v2: ActivityServiceV2,
        contact_query_service: ContactQueryService,
        contact_resolve_service: ContactResolveService,
        organization_service: OrganizationService,
        feature_flag_service: FeatureFlagService,
        organization_preference_service: OrganizationPreferenceService,
        domain_crm_association_service: DomainCRMAssociationService,
    ):
        super().__init__(
            contact_service=contact_service,
            user_service=user_service,
            select_list_service=select_list_service,
            organization_preference_service=organization_preference_service,
        )
        self.google_client = google_client
        self.user_integration_repo = user_integration_repo
        self.user_cal_repo = user_cal_repo
        self.user_repo = user_repo
        self.conference_service = conference_service
        self.meeting_service = meeting_service
        self.event_schedule_repo = event_schedule_repo
        self.activity_service_v2 = activity_service_v2
        self.contact_query_service = contact_query_service
        self.contact_resolve_service = contact_resolve_service
        self.organization_service = organization_service
        self.feature_flag_service = feature_flag_service
        self.domain_crm_association_service = domain_crm_association_service

    async def upsert_user_calendar_event_from_enriched_event(
        self,
        meeting: Meeting | None,
        enriched_cal_event: UserCalendarEvent,
    ) -> UserCalendarEvent:
        upserted_event = await self.user_cal_repo.upsert(
            enriched_cal_event.model_copy(
                update={
                    "meeting_id": meeting.id if meeting else None,
                }
            )
        )

        try:
            await self.domain_crm_association_service.bulk_create_domain_crm_associations_v2(
                domain_crm_associations=_UserCalendarCrmAssociationMapper.map_user_calendar_event_to_associations_create(
                    event=upserted_event,
                ),
            )
        except Exception as e:
            logger.bind(e=e, meeting=meeting, event=enriched_cal_event).error(
                "Failed to create crm association by upsert_user_calendar_event_from_enriched_event"
            )

        return upserted_event

    async def patch_user_calendar_event_from_enriched_event(
        self,
        user_id: UUID,
        meeting: Meeting | None,
        existing_event: UserCalendarEvent,
        updated_event: UserCalendarEvent,
    ) -> UserCalendarEvent:
        # Currently only meeting id could possibly update
        updated_event_with_new_meeting_id = not_none(
            await self.user_cal_repo.update_instance(
                updated_event.model_copy(
                    update={
                        "meeting_id": meeting.id if meeting else None,
                    }
                )
            )
        )

        try:
            associations_to_create, associations_to_delete = (
                _UserCalendarCrmAssociationMapper.map_user_calendar_event_to_associations_update(
                    user_id=user_id,
                    existing_event=existing_event,
                    updated_event=updated_event_with_new_meeting_id,
                )
            )

            await self.domain_crm_association_service.bulk_delete_domain_crm_associations_v2(
                domain_crm_associations=associations_to_delete,
                deleted_by_user_id=user_id,
            )

            await self.domain_crm_association_service.bulk_create_domain_crm_associations_v2(
                domain_crm_associations=associations_to_create,
            )
        except Exception as e:
            logger.bind(
                e=e, meeting=meeting, event=updated_event_with_new_meeting_id
            ).error(
                "Failed to update crm association by patch_user_calendar_event_from_enriched_event"
            )

        return updated_event

    async def hydrate_user_calendar_participant(  # noqa: C901
        self,
        calendar_event_participant: CalendarEventParticipant,
        organization_id: UUID,
        user_integration: UserIntegration,
        is_event_created_by_user: bool = False,
        has_bot_assistant: bool = False,
    ) -> CalendarEventParticipant:
        email = calendar_event_participant.email
        name: str | None = calendar_event_participant.name
        user_id: UUID | None = None
        contact_id: UUID | None = None
        person_or_integration: User | ContactV2 | UserIntegration | None = None

        # return without hydrate if it's a bot assistant email
        if email.startswith(settings.bot_assistant_email_prefix) and email.endswith(
            settings.bot_assistant_email_domain
        ):
            return calendar_event_participant

        if user_integration.connected_email == calendar_event_participant.email:
            person_or_integration = (
                await self.user_repo.find_user_by_id_and_organization_id(
                    user_id=user_integration.user_id,
                    organization_id=organization_id,
                    include_inactive=True,
                )
            )
        else:
            person_or_integration = await self.find_person_or_integration_by_email(
                email=email,
                organization_id=organization_id,
            )

        if isinstance(person_or_integration, UserIntegration):
            user = await self.user_repo.find_user_by_id_and_organization_id(
                user_id=person_or_integration.user_id,
                organization_id=organization_id,
                include_inactive=True,
            )
            if user:
                user_id = user.id
                name = name or user.display_name
        elif isinstance(person_or_integration, User):
            user_id = person_or_integration.id
            name = name or person_or_integration.display_name
        elif isinstance(person_or_integration, ContactV2):
            contact_id = person_or_integration.id
            name = name or person_or_integration.display_name
        else:
            try:
                logger.bind(
                    email=email,
                    user_id=user_integration.user_id,
                    organization_id=organization_id,
                    is_event_created_by_user=is_event_created_by_user,
                ).info("activity capture - contact and account creation")
                (
                    created_contacts,
                    _,
                ) = await self.try_create_account_and_contact_for_emails(
                    emails=[email],
                    organization_id=organization_id,
                    user_id=user_integration.user_id,
                    is_event_sent_by_user=is_event_created_by_user,
                    capture_mode_override=ActivityCaptureMode.ALL
                    if has_bot_assistant
                    else None,
                )
                if created_contacts:
                    contact_id = created_contacts[0].id
                    name = name or created_contacts[0].display_name

                created_hidden_users = (
                    await self.try_create_placeholder_user_for_emails(
                        emails=[email],
                        user_id=user_integration.user_id,
                        organization_id=organization_id,
                    )
                )
                if created_hidden_users:
                    user_id = created_hidden_users[0].id
            except ValidationError:
                logger.info(f"Skip creating contact for invalid email: {email}")
            except (UniqueViolationError, IntegrityError):
                logger.bind(
                    email=email,
                    user_id=user_integration.user_id,
                    organization_id=organization_id,
                ).info("Contact already exists")

        return CalendarEventParticipant(
            user_id=user_id,
            contact_id=contact_id,
            name=name,
            email=email,
            status=calendar_event_participant.status,
            comment=calendar_event_participant.comment,
            phone_number=calendar_event_participant.phone_number,
        )

    async def map_calendar_event_participants(
        self,
        cal_event: UserCalendarEvent,
        organization_id: UUID,
        user_integration: UserIntegration,
    ) -> list[CalendarEventParticipant]:
        is_event_created_by_user = False
        if cal_event.organizer_user_id:
            is_event_created_by_user = (
                cal_event.organizer_user_id == user_integration.user_id
            )
        if cal_event.organizer_email and user_integration.connected_email:
            is_event_created_by_user = (
                cal_event.organizer_email == user_integration.connected_email
            )
        organization = await self.organization_service.get_organization_by_id(
            organization_id=organization_id
        )
        organization_profile = (
            organization.organization_profile if organization else None
        )
        has_bot_assistant = self._has_bot_assistant(
            cal_event=cal_event,
            organization_profile=organization_profile,
        )
        # TODO: (Taiyan) revisit to call try_create_account_and_contact_for_emails only once
        return (
            [
                await self.hydrate_user_calendar_participant(
                    calendar_event_participant=participant,
                    organization_id=organization_id,
                    user_integration=user_integration,
                    is_event_created_by_user=is_event_created_by_user,
                    has_bot_assistant=has_bot_assistant,
                )
                for participant in cal_event.participants
            ]
            if cal_event.participants
            else []
        )

    async def find_person_or_integration_by_email(
        self,
        organization_id: UUID,
        email: EmailStr,
    ) -> ContactV2 | User | UserIntegration | None:
        # always honor connected email first for user, other than user.email
        # todo: constrain user to only able to add their verified email
        #  instead of any arbitrary emails
        user_integration = (
            await self.user_integration_repo.find_user_integration_by_connected_email(
                connected_email=email,
                organization_id=organization_id,
                integration_type=IntegrationType.CALENDAR,
            )
        )
        if user_integration:
            return user_integration

        # TODO restructure so that we look up once instead of for each find_person_or_integration_by_email call
        organization = await self.organization_service.get_organization_by_id(
            organization_id=organization_id
        )
        organization_profile = (
            organization.organization_profile if organization else None
        )

        # then try to find user
        email_identifier = email.split("@")[0]
        email_domain = email.split("@")[-1]
        alternate_domains = (
            organization_profile.alternate_domains
            if organization_profile and organization_profile.alternate_domains
            else []
        )
        domains_to_check = [email_domain, *alternate_domains]
        for domain in domains_to_check:
            user = await self.user_repo.find_user_by_organization_id_and_email(
                organization_id=organization_id,
                email=f"{email_identifier}@{domain}",
                include_inactive=True,
            )
            if user:
                return user

        # then try to find contact
        email_contact_map = (
            await self.contact_resolve_service.resolve_contact_by_emails(
                organization_id=organization_id,
                emails={email},
            )
        )
        return email_contact_map.get(email, None)

    async def create_conferencing(
        self,
        user: User,
        organization_id: UUID,
        conference_provider: NylasConferencingProvider,
        title: str,
        description: str | None,
        starts_at: ZoneRequiredDateTime | None,
        ends_at: ZoneRequiredDateTime | None,
    ) -> tuple[NylasConferencing | None, str | None]:
        conferencing: NylasConferencing | None = None
        location: str | None = None
        if conference_provider in {"Google Meet", "Microsoft Teams"}:
            conferencing = NylasConferencingAutocreate(
                provider=conference_provider, autocreate={}
            )
        elif conference_provider == "Zoom Meeting":
            zoom_meeting: ZoomMeeting = (
                await self.conference_service.create_zoom_meeting(
                    user_id=user.id,
                    organization_id=organization_id,
                    request=CreateMeetingRequest(
                        agenda=description,
                        default_password=False,
                        duration=int((ends_at - starts_at).total_seconds() / 60)
                        if starts_at and ends_at
                        else None,
                        password=None,
                        pre_schedule=False,
                        start_time=starts_at if starts_at else None,
                        topic=title,
                        type=ZoomMeetingType.SCHEDULED_MEETING.value,
                    ),
                )
            )
            conferencing = NylasConferencingDetails(
                provider=conference_provider,
                details={
                    "url": zoom_meeting.join_url,
                    "meeting_code": str(zoom_meeting.id),
                    "password": zoom_meeting.password
                    if zoom_meeting.password is not None
                    else "",
                },
            )
            location = zoom_meeting.join_url
        else:
            logger.info(f"Unsupported conferencing provider: {conference_provider}")
        return conferencing, location

    async def switch_conferencing(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self,
        user: User,
        organization_id: UUID,
        previous_conferencing_provider: str | None,
        previous_conferencing_details: dict[str, Any] | None,
        new_conferencing: EventConferencingRequest,
        title: str,
        starts_at: ZoneRequiredDateTime | None,
        ends_at: ZoneRequiredDateTime | None,
        description: str | None,
    ) -> tuple[NylasConferencing | None, str | None]:
        conferencing: NylasConferencing | None = None
        location: str | None = None

        if new_conferencing.conferencing_provider == "Zoom Meeting":
            zoom_meeting = await self.conference_service.create_zoom_meeting(
                user_id=user.id,
                organization_id=organization_id,
                request=CreateMeetingRequest(
                    agenda=description,
                    default_password=False,
                    duration=int((ends_at - starts_at).total_seconds() / 60)
                    if starts_at and ends_at
                    else None,
                    password=None,
                    pre_schedule=False,
                    start_time=starts_at if starts_at else None,
                    topic=title,
                    type=ZoomMeetingType.SCHEDULED_MEETING.value,
                ),
            )
            conferencing = NylasConferencingDetails(
                provider="Zoom Meeting",
                details={
                    "url": zoom_meeting.join_url,
                    "meeting_code": str(zoom_meeting.id),
                    "password": zoom_meeting.password
                    if zoom_meeting.password is not None
                    else "",
                },
            )

            location = zoom_meeting.join_url
        elif new_conferencing.conferencing_provider in {
            "Google Meet",
            "Microsoft Teams",
        }:
            conferencing = NylasConferencingAutocreate(
                provider=new_conferencing.conferencing_provider, autocreate={}
            )
            if (
                previous_conferencing_provider == "Zoom Meeting"
                and previous_conferencing_details
            ):
                # Delete Zoom conference room if switched from Zoom to others
                zoom_meeting_id = previous_conferencing_details["meeting_code"]
                await self.conference_service.delete_zoom_meeting(
                    user.id, organization_id, zoom_meeting_id
                )
                location = ""

        return conferencing, location

    async def create_conferencing_details(
        self,
        user: User,
        organization_id: UUID,
        use_consent_link: bool,
        conference_provider: NylasConferencingProvider,
        title: str,
        description: str | None,
        starts_at: ZoneRequiredDateTime | None,
        ends_at: ZoneRequiredDateTime | None,
    ) -> tuple[
        GoogleEventRequest | None,
        dict[str, str] | None,
        UUID | None,
        str | None,
    ]:
        google_conference_data = None
        conferencing_details = None
        consent_id = None
        consent_url = None
        additional_description = None

        if use_consent_link:
            consent_id = uuid4()
            consent_url = self.get_consent_url(consent_id)

        if conference_provider == "Zoom Meeting":
            zoom_meeting: ZoomMeeting = (
                await self.conference_service.create_zoom_meeting(
                    user_id=user.id,
                    organization_id=organization_id,
                    request=CreateMeetingRequest(
                        agenda=description,
                        default_password=False,
                        duration=int((ends_at - starts_at).total_seconds() / 60)
                        if starts_at and ends_at
                        else None,
                        password=None,
                        pre_schedule=False,
                        start_time=starts_at if starts_at else None,
                        topic=title,
                        type=ZoomMeetingType.SCHEDULED_MEETING.value,
                    ),
                )
            )

            conferencing_details = {
                "url": zoom_meeting.join_url,
                "meeting_code": str(zoom_meeting.id),
                "password": zoom_meeting.password
                if zoom_meeting.password is not None
                else "",
            }

            additional_description = self.format_zoom_meeting_description(
                user, zoom_meeting, consent_url
            )
            if use_consent_link:
                google_conference_data = self.generate_google_conference_data_manual(
                    uri=not_none(consent_url), meeting_name="Zoom Meeting"
                )
            else:
                google_conference_data = self.generate_google_conference_data_manual(
                    uri=zoom_meeting.join_url, meeting_name="Zoom Meeting"
                )

        elif conference_provider == "Google Meet":
            if use_consent_link:
                try:
                    google_meet = await self.conference_service.create_google_meet(
                        user_id=user.id, organization_id=organization_id
                    )
                    conferencing_details = {
                        "meeting_code": google_meet.meetingCode,
                        "url": google_meet.meetingUri,
                    }
                    additional_description = self.format_google_meet_description(
                        user, consent_url
                    )
                except HTTPException as e:
                    if e.status_code == status.HTTP_403_FORBIDDEN:
                        use_consent_link = False
                        consent_url = None
                        consent_id = None
                        additional_description = None
                        logger.error(
                            f"Manual create for Google Meet failed for user_id: {user.id}. Failover to autocreate now"
                        )

            if use_consent_link is False or consent_url is None or consent_id is None:
                google_conference_data = self.generate_google_conference_data_auto()
            else:
                google_conference_data = self.generate_google_conference_data_manual(
                    uri=consent_url, meeting_name="Google Meet"
                )
        elif conference_provider == "Microsoft Teams":
            # TODO
            pass

        return (
            google_conference_data,
            conferencing_details,
            consent_id,
            additional_description,
        )

    async def update_google_calendar_event_with_conference_data(
        self,
        user_id: UUID,
        organization_id: UUID,
        calendar_id: str,
        event_id: str,
        conference_request: GoogleEventRequest,
    ) -> GoogleEvent:
        # step 1: retrieve google access token
        (
            db_user_integration,
            db_user_auth,
        ) = await self.user_integration_repo.get_user_integration_and_user_auth_by_user_id(
            user_id=user_id,
            organization_id=organization_id,
            integration_type=IntegrationType.CALENDAR,
        )

        if db_user_integration is None or db_user_auth is None:
            logger.error(
                f"[create_google_meet] can't find user_integration. user_id={user_id}",
            )
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"can't find user_integration with user_id {user_id}",
            )
        if db_user_auth.access_token is None or db_user_auth.refresh_token is None:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"can't find access token or refresh token with user_id {user_id}",
            )
        # step 2: update google event
        access_token = db_user_auth.access_token
        refresh_token = db_user_auth.refresh_token
        google_event: GoogleEvent
        tries = 2
        for i in range(tries):
            try:
                google_event = await self.google_client.update_event(
                    calendar_id=calendar_id,
                    event_id=event_id,
                    request=conference_request,
                    encrypted_tokens=EncryptedTokens(
                        encrypted_access_token=access_token,
                        encrypted_refresh_token=refresh_token,
                    ),
                )
            except HTTPException as e:
                if i < (tries - 1):
                    if e.status_code == status.HTTP_401_UNAUTHORIZED:
                        logger.info("refresh google access token")
                        access_token = (
                            await self.conference_service.refresh_google_expired_token(
                                organization_id, db_user_auth
                            )
                        )
                        continue
                else:
                    raise
            break
        return google_event

    async def get_meeting_for_event_if_exists(
        self,
        *,
        organization_id: UUID,
        calendar_event: UserCalendarEvent,
    ) -> MeetingDto | None:
        try:
            return await self.meeting_service.get_meeting_by_reference_id(
                reference_id=calendar_event.group_key,
                reference_id_type=MeetingReferenceIdType.USER_CALENDAR_EVENT,
                organization_id=organization_id,
            )
        except ResourceNotFoundError:
            return None

    async def create_meeting_for_event_async(
        self,
        user_id: UUID,
        organization_id: UUID,
        calendar_event_id: UUID,
        schedule_bot: bool,
        event_schedule_id: UUID | None,
        meeting_title: str,
    ) -> UserCalendarEvent:
        calendar_event_dto = await self.user_cal_repo.find_user_calendar_event_or_error(
            calendar_event_id=calendar_event_id,
            organization_id=organization_id,
        )
        temp_user_calendar_event = calendar_event_dto.user_calendar_event
        meeting_dto = await self.create_meeting_for_event(
            user_id=user_id,
            organization_id=organization_id,
            new_calendar_event=temp_user_calendar_event,
            consent_id=None,
            participants=temp_user_calendar_event.participants,
            schedule_bot=schedule_bot,
            event_schedule_id=event_schedule_id,
            meeting_title=meeting_title,
        )
        meeting_id = None
        if not meeting_dto:
            # Unexpected case where meeting is missing
            # Log error but allow request to go through so user does not experience
            # any issues and we can resolve after.
            logger.bind(event_schedule_id=event_schedule_id).error(
                "Meeting not defined for booking event"
            )
        else:
            meeting_id = meeting_dto.meeting_id

        dirty_event = temp_user_calendar_event.copy(
            update={
                "meeting_id": meeting_id,
            }
        )
        # Atomically insert or update calendar event
        return await self.user_cal_repo.upsert_unique_target_columns(
            dirty_event,
            on_conflict_target_columns=["user_calendar_id", "external_id"],
            exclude_columns_from_update=["id"],
        )

    async def create_meeting_for_event(
        self,
        *,
        user_id: UUID,
        organization_id: UUID,
        new_calendar_event: UserCalendarEvent,
        consent_id: UUID | None,
        participants: list[CalendarEventParticipant],
        schedule_bot: bool,
        event_schedule_id: UUID | None,
        meeting_title: str | None = None,
        agenda: str | None = None,
        pipeline_id: UUID | None = None,
        pipeline_select_list_value_id: UUID | None = None,
        account_id: UUID | None = None,
        old_reference_id: str | None = None,  # for recurrent event case only
    ) -> MeetingDto | None:
        logger.bind(
            user_id=user_id,
            organization_id=organization_id,
            consent_id=consent_id,
            participants=participants,
            schedule_bot=schedule_bot,
            event_schedule_id=event_schedule_id,
            existing_meeting_id=new_calendar_event.meeting_id,
            group_key=new_calendar_event.group_key,
            pipeline_id=pipeline_id,
            pipeline_select_list_value_id=pipeline_select_list_value_id,
            account_id=account_id,
        ).info("Create meeting for calendar event")
        # resolve account_id
        contact_email_pairs: list[tuple[UUID | None, EmailStrLower | None]] = []
        for p in participants:
            contact_email_pairs.append(
                (
                    p.contact_id,
                    p.email,
                )
            )
        if not account_id:
            resolved_account_id_map = await self.contact_resolve_service.resolve_relevant_account_by_contact_and_email_pairs(
                organization_id=organization_id,
                contact_email_pairs=contact_email_pairs,
            )
            account_ids = list(set(resolved_account_id_map.values()))
            if account_ids and len(account_ids) == 1:
                account_id = account_ids[0]

        request_event_conferencing = MeetingEventConferencing(
            event_schedule_id=event_schedule_id,
            conferencing_details=new_calendar_event.conferencing_details or {},
            meeting_url=new_calendar_event.conferencing_details["url"]
            if new_calendar_event.conferencing_details
            else None,
            consent_id=consent_id,
            location=new_calendar_event.location,
            use_bot=(
                bool(new_calendar_event.conferencing_details)
                and "url" in not_none(new_calendar_event.conferencing_details)
                and bool(new_calendar_event.conferencing_provider)
                and schedule_bot
            ),
        )
        request_platform = (
            MeetingProvider.from_conferencing_provider(
                conferencing_provider=new_calendar_event.conferencing_provider
            )
            if new_calendar_event.conferencing_provider
            else None
        )

        # Check for phone call type meeting
        if (
            request_platform is None
            and new_calendar_event.conferencing_provider is None
        ):
            try:
                validate_e164(str(new_calendar_event.location))
                request_platform = MeetingProvider.PHONE
            except ValueError:
                pass

        invitees = [
            Invitee(
                email=p.email,
                user_id=p.user_id,
                contact_id=p.contact_id,
                is_organizer=(p.email == new_calendar_event.organizer_email),
                status=p.status,
                account_id=p.account_id,
            )
            for p in participants
            if p.user_id or p.contact_id or p.email
        ]

        if not participants or len(invitees) == 1:
            logger.bind(invitees=invitees).info(
                "No invited participants - skipping meeting creation"
            )
            return None

        create_meeting_request = CreateMeetingServiceRequest(
            platform=request_platform,
            reference_id=new_calendar_event.group_key,
            reference_id_type=MeetingReferenceIdType.USER_CALENDAR_EVENT,
            pipeline_id=pipeline_id,
            pipeline_select_list_value_id=pipeline_select_list_value_id,
            starts_at=new_calendar_event.starts_at,
            ends_at=new_calendar_event.ends_at,
            title=meeting_title if meeting_title else new_calendar_event.title,
            description=new_calendar_event.description,
            event_conferencing=request_event_conferencing,
            invitees=invitees,
            account_id=account_id,
            user_calendar_event_id=new_calendar_event.id,
        )

        # Handle recurrent meeting edge case
        if old_reference_id:
            meeting = await self.meeting_service.get_meeting_by_reference_id(
                reference_id=old_reference_id,
                reference_id_type=MeetingReferenceIdType.USER_CALENDAR_EVENT,
                organization_id=organization_id,
            )
            if meeting:
                return await self.meeting_service.patch_meeting(
                    user_id=user_id,
                    meeting_id=meeting.meeting_id,
                    organization_id=organization_id,
                    request=PatchMeetingRequest(
                        platform=request_platform,
                        title=new_calendar_event.title,
                        description=new_calendar_event.description,
                        starts_at=new_calendar_event.starts_at,
                        ends_at=new_calendar_event.ends_at,
                        event_conferencing=request_event_conferencing,
                        invitees=invitees,
                        agenda=agenda,
                        reference_id=new_calendar_event.group_key,
                        user_calendar_event_id=new_calendar_event.id,
                    ),
                )
            return await self.meeting_service.create_meeting(
                organization_id=organization_id,
                user_id=user_id,
                request=create_meeting_request,
            )

        # Handle new meeting creation
        try:
            return await self.meeting_service.create_meeting(
                organization_id=organization_id,
                user_id=user_id,
                request=create_meeting_request,
            )
        except (ConflictResourceError, UniqueViolationError, IntegrityError):
            logger.bind(group_key=new_calendar_event.group_key).info(
                "Meeting already exists, using existing meeting instead of creating"
            )
            return await self.meeting_service.get_meeting_by_reference_id(
                reference_id=new_calendar_event.group_key,
                reference_id_type=MeetingReferenceIdType.USER_CALENDAR_EVENT,
                organization_id=organization_id,
            )

    async def update_meeting_for_event(
        self,
        *,
        existing_meeting: MeetingDto,
        user_id: UUID,
        organization_id: UUID,
        new_calendar_event: UserCalendarEvent,
        consent_id: UUID | None,
        participants: list[CalendarEventParticipant],
        schedule_bot: bool,
        event_schedule_id: UUID | None,
        agenda: str | None = None,
        old_reference_id: str | None = None,  # for recurrent event case only
    ) -> MeetingDto | None:
        logger.bind(
            user_id=user_id,
            organization_id=organization_id,
            consent_id=consent_id,
            participants=participants,
            schedule_bot=schedule_bot,
            event_schedule_id=event_schedule_id,
            existing_meeting_id=new_calendar_event.meeting_id,
            group_key=new_calendar_event.group_key,
        ).info("Update meeting for calendar event")
        request_event_conferencing = MeetingEventConferencing(
            event_schedule_id=event_schedule_id,
            conferencing_details=new_calendar_event.conferencing_details or {},
            meeting_url=new_calendar_event.conferencing_details["url"]
            if new_calendar_event.conferencing_details
            else None,
            consent_id=consent_id,
            location=new_calendar_event.location,
            use_bot=(
                bool(new_calendar_event.conferencing_details)
                and "url" in not_none(new_calendar_event.conferencing_details)
                and bool(new_calendar_event.conferencing_provider)
                and schedule_bot
            ),
        )
        request_platform = (
            MeetingProvider.from_conferencing_provider(
                conferencing_provider=new_calendar_event.conferencing_provider
            )
            if new_calendar_event.conferencing_provider
            else None
        )

        # Check for phone call type meeting
        if (
            request_platform is None
            and new_calendar_event.conferencing_provider is None
        ):
            try:
                validate_e164(str(new_calendar_event.location))
                request_platform = MeetingProvider.PHONE
            except ValueError:
                pass

        invitees = [
            Invitee(
                user_id=p.user_id,
                contact_id=p.contact_id,
                is_organizer=(p.email == new_calendar_event.organizer_email),
                status=p.status,
            )
            for p in participants
        ]
        base_patch_request: dict[str, Any] = {  # type: ignore[explicit-any] # TODO: fix-any-annotation
            "platform": request_platform,
            "title": new_calendar_event.title,
            "description": new_calendar_event.description,
            "starts_at": new_calendar_event.starts_at,
            "ends_at": new_calendar_event.ends_at,
            "user_calendar_event_id": new_calendar_event.id,
            "event_conferencing": request_event_conferencing,
            "invitees": invitees,
            "status": MeetingStatus.SCHEDULED
            if new_calendar_event.starts_at > zoned_utc_now()
            else existing_meeting.meeting_status,
            "agenda": agenda,
        }
        if old_reference_id:
            logger.bind(
                old_reference_id=old_reference_id,
                new_reference_id=new_calendar_event.group_key,
            ).info("Updating recurrent meeting reference id")
            base_patch_request["reference_id"] = new_calendar_event.group_key
        patch_meeting_request = PatchMeetingRequest(**base_patch_request)

        # Update existing meeting
        # MeetingService will skip any update if nothing needs changing
        return await self.meeting_service.patch_meeting(
            user_id=user_id,
            meeting_id=existing_meeting.meeting_id,
            organization_id=organization_id,
            request=patch_meeting_request,
        )

    async def delete_event_by_event_dto(
        self,
        user_id: UUID,
        user_calendar_event_dto: UserCalendarEventDto,
    ) -> UserCalendarEvent:
        cal_event = not_none(
            await self.user_cal_repo.update_instance(
                user_calendar_event_dto.user_calendar_event.model_copy(
                    update={"deleted_at": zoned_utc_now()},
                ),
            ),
        )

        try:
            await self.domain_crm_association_service.bulk_delete_domain_crm_associations_v2(
                domain_crm_associations=_UserCalendarCrmAssociationMapper.map_user_calendar_event_to_associations_delete(
                    user_id=user_id,
                    organization_id=user_calendar_event_dto.user_calendar_event.organization_id,
                    event_id=user_calendar_event_dto.user_calendar_event.id,
                ),
                deleted_by_user_id=user_id,
            )
        except Exception as e:
            logger.bind(e=e, user_calendar_event_dto=user_calendar_event_dto).error(
                "Failed to delete meeting for event"
            )

        return cal_event

    async def is_organizer(
        self,
        *,
        user_id: UUID,
        organization_id: UUID,
        organizer_email: str,
    ) -> bool:
        """
        Check if a user is the organizer of a calendar event
        """
        # Look up the user in the organization
        user = await self.user_repo.find_user_by_id_and_organization_id(
            user_id=user_id,
            organization_id=organization_id,
        )

        # If user not found in org, they can't be the organizer
        if not user:
            return False

        # Compare user's email with organizer email to determine if they are the organizer
        return user.email == organizer_email

    def _get_organizer_user_participant(
        self, cal_event: UserCalendarEvent
    ) -> CalendarEventParticipant | None:
        return next(
            (
                p
                for p in cal_event.participants
                if p.user_id
                and cal_event.organizer_user_id
                and p.user_id == cal_event.organizer_user_id
            ),
            None,
        )

    def _is_for_test_reevo_org(
        self, cal_event: UserCalendarEvent, organization_id: UUID
    ) -> bool:
        if str(organization_id) not in settings.new_feature_org_ids:
            # First, try to extract the domain from the organizer_email
            organizer_domain = (
                UserCalendarCommonService.extract_domain_from_organizer_email(
                    cal_event.organizer_email
                )
                if cal_event.organizer_email
                else None
            )

            # If organizer_domain is not found from organizer_email, fall back to participants
            if not organizer_domain:
                user_participant = self._get_organizer_user_participant(cal_event)
                if user_participant:
                    organizer_domain = (
                        UserCalendarCommonService.extract_domain_from_organizer_email(
                            user_participant.email
                        )
                    )

            # Check if the domain is 'reevo.ai' and not in new_feature_org_ids
            return organizer_domain == "reevo.ai"

        return False

    async def get_meeting_sync_behavior_from_sync_setting(  # noqa: C901
        self,
        cal_event: UserCalendarEvent,
        organization_profile: OrganizationProfile | None,
        organization_id: UUID,
    ) -> MeetingSync:
        logger.bind(
            organization_id=organization_id,
            event_id=cal_event.id,
        ).info("Determining meeting sync behavior based on sync settings")

        meeting_recording_preference = await self.organization_preference_service.get_organization_meeting_recording_preference_or_default(
            organization_id=organization_id
        )
        is_sales_meeting = any(p.contact_id for p in cal_event.participants)
        record_meeting = False
        if (meeting_recording_preference.sales_meetings and is_sales_meeting) or (
            meeting_recording_preference.internal_meetings
            and (
                any(p.user_id for p in cal_event.participants)
                and len(
                    {
                        p.email.split("@")[-1].lower()
                        for p in cal_event.participants
                        if p.email
                    }
                )
                <= 1
            )
        ):
            logger.bind(
                organization_id=organization_id,
                event_id=cal_event.id,
            ).info("should record meeting based on settings")
            record_meeting = True

        # Override for recurring
        if (
            cal_event.master_event_id
            and not meeting_recording_preference.recurring_meetings
        ):
            logger.bind(
                organization_id=organization_id,
                event_id=cal_event.id,
            ).info("should not record recurring meeting")
            record_meeting = False

        if record_meeting:
            # Check for overrides - config says record but we should not
            # Case 1: Unsupported provided - would not be able to add bot
            meeting_provider = MeetingProvider.from_conferencing_provider(
                cal_event.conferencing_provider
            )
            if (
                record_meeting and not meeting_provider
            ) or not MeetingProvider.is_bot_eligible(
                meeting_provider=meeting_provider, organization_id=organization_id
            ):
                logger.bind(
                    organization_id=organization_id,
                    event_id=cal_event.id,
                    conferencing_details=cal_event.conferencing_details,
                    conferencing_provider=cal_event.conferencing_provider,
                ).info("Skipping bot creation for non-bot eligible meeting provider")
                record_meeting = False
            # Case 2: Internal testing orgs
            if await self.feature_flag_service.is_enabled(
                request=FeatureFlagRequest(
                    flag_key="enable_reevo_bot_check",
                    organization_id=organization_id,
                )
            ) and self._is_for_test_reevo_org(cal_event, organization_id):
                logger.bind(
                    organization_id=organization_id, event_id=cal_event.id
                ).info("Skipping bot creation for internal testing org")
                record_meeting = False
        # Check for overrides - config says don't record but we may need to
        elif self._has_bot_assistant(
            cal_event=cal_event, organization_profile=organization_profile
        ):
            logger.bind(
                group_key=cal_event.group_key, organization_id=organization_id
            ).info("Bot assistant is used, scheduling meeting and bot")
            record_meeting = True

        if record_meeting:
            return MeetingSync(
                schedule_meeting=True,
                schedule_bot=True,
            )

        # Recording not needed - check if we still need a meeting
        activity_capture_mode = await self.organization_preference_service.get_organization_activity_capture_mode(
            organization_id=organization_id
        )
        schedule_meeting = False
        match activity_capture_mode:
            case ActivityCaptureMode.ALL:
                schedule_meeting = True
            case ActivityCaptureMode.SENT:
                schedule_meeting = cal_event.organizer_user_id == cal_event.user_id
            case ActivityCaptureMode.DOMAIN:
                # Prior to meeting sync, calendar event enrichment ensures we have created contacts based on same setting
                schedule_meeting = is_sales_meeting
            case ActivityCaptureMode.NONE:
                schedule_meeting = False
            case _:
                assert_never(activity_capture_mode)

        return MeetingSync(
            schedule_meeting=schedule_meeting,
            schedule_bot=False,
        )

    async def get_meeting_sync_behavior_for_event(  # noqa: C901
        self,
        cal_event: UserCalendarEvent,
        organization_profile: OrganizationProfile | None,
        organization_id: UUID,
    ) -> MeetingSync:
        logger.bind(
            organization_id=organization_id,
            profile=organization_profile,
        ).info("Determining meeting sync behavior")

        if self._has_bot_assistant(
            cal_event=cal_event, organization_profile=organization_profile
        ):
            # If bot assistant is used, then always schedule the meeting
            logger.bind(
                group_key=cal_event.group_key, organization_id=organization_id
            ).info("Bot assistant is used, scheduling meeting and bot")
            return MeetingSync(
                schedule_meeting=True,
                schedule_bot=True,
            )

        meeting_recording_level = OrganizationService.get_recording_level(
            organization_profile=organization_profile
        )
        meeting_sync_level = OrganizationService.get_meeting_sync_level(
            organization_profile=organization_profile,
            meeting_recording_level=meeting_recording_level,
        )

        # Determine properties of the event and user
        has_external_participant = self._has_external_participant(
            cal_event=cal_event, organization_profile=organization_profile
        )
        contact_ids = [p.contact_id for p in cal_event.participants if p.contact_id]
        has_sales_contact = False
        contacts = (
            await self.contact_service.list_contacts_v2(
                organization_id=organization_id,
                only_include_contact_ids=set(contact_ids),
            )
            if contact_ids
            else []
        )
        has_sales_contact = any(c.primary_account_id for c in contacts)

        # Determine meeting sync
        match meeting_sync_level:
            case MeetingSyncLevel.ALL_MEETINGS:
                schedule_meeting = True
            case MeetingSyncLevel.EXTERNAL_MEETINGS:
                schedule_meeting = has_external_participant
            case MeetingSyncLevel.SALES_MEETINGS:
                schedule_meeting = has_sales_contact
            case _:
                assert_never(meeting_sync_level)

        # Determine recording/if bot is used
        if not schedule_meeting:
            # No meeting, then no bot - no need to check anything
            return MeetingSync(
                schedule_meeting=False,
                schedule_bot=False,
            )
        meeting_provider = MeetingProvider.from_conferencing_provider(
            cal_event.conferencing_provider
        )
        if not meeting_provider or not MeetingProvider.is_bot_eligible(
            meeting_provider=meeting_provider, organization_id=organization_id
        ):
            logger.bind(
                organization_id=organization_id,
                event_id=cal_event.id,
                conferencing_details=cal_event.conferencing_details,
                conferencing_provider=cal_event.conferencing_provider,
            ).info("Skipping bot creation for non-bot eligible meeting provider")
            return MeetingSync(
                schedule_meeting=schedule_meeting,
                schedule_bot=False,
            )

        if await self.feature_flag_service.is_enabled(
            request=FeatureFlagRequest(
                flag_key="enable_reevo_bot_check",
                organization_id=organization_id,
            )
        ) and self._is_for_test_reevo_org(cal_event, organization_id):
            logger.bind(organization_id=organization_id).info(
                "Skipping bot creation for internal testing org"
            )
            return MeetingSync(
                schedule_meeting=schedule_meeting,
                schedule_bot=False,
            )

        # Second match statement for meeting_recording_level
        match meeting_recording_level:
            case MeetingRecordingLevel.ALL_MEETINGS:
                schedule_bot = True
            case MeetingRecordingLevel.EXTERNAL_MEETINGS:
                schedule_bot = has_external_participant
            case MeetingRecordingLevel.SALES_MEETINGS:
                schedule_bot = has_sales_contact
            case _:
                assert_never(meeting_recording_level)

        logger.bind(
            schedule_meeting=schedule_meeting,
            schedule_bot=schedule_bot,
            has_external_participant=has_external_participant,
            has_sales_contact=has_sales_contact,
            meeting_sync_level=meeting_sync_level,
            meeting_recording_level=meeting_recording_level,
        ).info("Using sync settings for event")

        return MeetingSync(
            schedule_meeting=schedule_meeting,
            schedule_bot=schedule_bot,
        )

    def _has_external_participant(
        self,
        cal_event: UserCalendarEvent,
        organization_profile: OrganizationProfile | None,
    ) -> bool:
        organizer_domain = cal_event.organizer_email.split("@")[-1]
        alternate_organizer_domains = (
            organization_profile.alternate_domains
            if organization_profile and organization_profile.alternate_domains
            else []
        )

        participant_domains = [
            participant.email.split("@")[-1] for participant in cal_event.participants
        ]
        return any(
            domain != organizer_domain and domain not in alternate_organizer_domains
            for domain in participant_domains
        )

    def _has_bot_assistant(
        self,
        cal_event: UserCalendarEvent,
        organization_profile: OrganizationProfile | None,
    ) -> bool:
        bot_assistant_email = OrganizationService.get_bot_assistant(
            organization_profile=organization_profile
        )
        if not bot_assistant_email:
            return False
        participant_emails = [
            participant.email.lower() for participant in cal_event.participants
        ]
        return bot_assistant_email.lower() in participant_emails

    async def create_activity_v2_for_user_calendar_event_participant(
        self,
        user_integration: UserIntegration,
        existing_event: UserCalendarEvent,
        updated_event: UserCalendarEvent,
        meeting_dto: MeetingDto,
    ) -> None:
        rsvp_updates: dict[str, list[Contact | UserIntegration | User]] = {
            rsvp_status: [] for rsvp_status in ["yes", "no", "maybe", "noreply"]
        }

        for updated_participant in updated_event.participants:
            person = await self.find_person_or_integration_by_email(
                email=updated_participant.email,
                organization_id=updated_event.organization_id,
            )
            if isinstance(person, (Contact | UserIntegration | User)):
                existing_participant = next(
                    (
                        p
                        for p in existing_event.participants
                        if p.email == updated_participant.email
                    ),
                    None,
                )
                if (
                    existing_participant
                    and existing_participant.status != updated_participant.status
                ):
                    rsvp_updates[updated_participant.status].append(person)

        # Handle updated RSVPs by status, ideally we should only have 1 activity inserted since only 1 rsvp change per webhook message
        for rsvp_status, participants in rsvp_updates.items():
            if participants:
                await self._insert_activity_v2_by_rsvp_status(
                    organizer=user_integration,
                    cal_event=updated_event,
                    meeting_dto=meeting_dto,
                    filtered_participants=participants,
                    rsvp_status=rsvp_status,
                )

    async def _insert_activity_v2_by_rsvp_status(
        self,
        organizer: UserIntegration,
        cal_event: UserCalendarEvent,
        meeting_dto: MeetingDto,
        filtered_participants: list[Contact | UserIntegration | User],
        rsvp_status: str,
    ) -> None:
        if not filtered_participants:
            return

        contact_email_map = (
            await self.contact_resolve_service.resolve_email_by_contact_ids(
                organization_id=organizer.organization_id,
                contact_ids={
                    participant.id
                    for participant in [*filtered_participants, organizer]
                    if isinstance(participant, Contact)
                },
            )
        )

        sub_references = self._get_activity_sub_reference_lists(
            filtered_participant_list=filtered_participants,
            sub_reference_type=ActivitySubReferenceType.CALENDER_EVENT_PARTICIPANT,
            contact_email_map=contact_email_map,
        )

        organizer_metadata_activity_sub_reference = self._get_activity_sub_reference(
            person=organizer,
            sub_reference_type=ActivitySubReferenceType.CALENDER_EVENT_ORGANIZER,
            contact_email=contact_email_map.get(organizer.id)
            if isinstance(organizer, Contact)
            else None,
        )

        sub_references.append(organizer_metadata_activity_sub_reference)

        try:
            await self.activity_service_v2.insert_activity(
                organization_id=cal_event.organization_id,
                insert_activity_request=ActivityRequest(
                    type=ActivityType.MEETING,
                    sub_type=self._get_activity_sub_type(rsvp_status),
                    priority=ActivityPriority.MEDIUM,
                    status=ActivityStatus.READ,
                    owner_user_id=cal_event.user_id,
                    account_id=None,
                    sequence_id=None,
                    reference_id=str(cal_event.meeting_id),
                    reference_id_type=ActivityReferenceIdType.MEETING_ID,
                    display_name="Received RSVP for meeting invitation",
                    sub_references=sub_references,
                    metadata=CalendarEventRsvpActivityMetadata(
                        id=meeting_dto.meeting_id,
                        status=meeting_dto.meeting.status,
                        metadata_model=ActivityMetadataModel.CALENDAR_EVENT_RSVP,
                        rsvp_status=self._get_calendar_event_rsvp_status(rsvp_status),
                    ),
                    created_by_user_id=cal_event.user_id,
                    created_at=zoned_utc_now(),
                ),
            )
        except Exception as e:
            logger.error(
                f"Failed to insert activity for RSVP status {rsvp_status}: {e!s}",
                exc_info=e,
            )

    @staticmethod
    def _get_activity_sub_reference_lists(
        filtered_participant_list: list[Contact | UserIntegration | User],
        sub_reference_type: ActivitySubReferenceType,
        contact_email_map: dict[UUID, str] | None = None,
    ) -> list[ActivitySubReferenceRequest]:
        return [
            UserCalendarCommonService._get_activity_sub_reference(
                person=participant,
                sub_reference_type=sub_reference_type,
                contact_email=contact_email_map.get(participant.id)
                if contact_email_map and isinstance(participant, Contact)
                else None,
            )
            for participant in filtered_participant_list
        ]

    @staticmethod
    def _get_activity_sub_reference(
        person: Contact | UserIntegration | User,
        sub_reference_type: ActivitySubReferenceType,
        contact_email: str | None = None,
    ) -> ActivitySubReferenceRequest:
        contact_id = None
        user_id = None
        display_name = None
        if isinstance(person, Contact):
            contact_id = person.id
            email = contact_email
            display_name = person.display_name
        elif isinstance(person, User):
            user_id = person.id
            email = person.email
            display_name = person.display_name
        else:
            user_id = person.user_id
            email = person.connected_email

        return ActivitySubReferenceRequest(
            type=sub_reference_type,
            value=not_none(email),
            contact_id=contact_id,
            user_id=user_id,
            email_account_id=None,
            display_name=display_name,
        )

    @staticmethod
    def _get_activity_sub_type(rsvp_status: str) -> ActivitySubType:
        rsvp_mappings: dict[str, ActivitySubType] = {
            "yes": ActivitySubType.CALENDAR_EVENT_ACCEPTED,
            "no": ActivitySubType.CALENDAR_EVENT_REJECTED,
            "maybe": ActivitySubType.CALENDAR_EVENT_MAYBE,
            "noreply": ActivitySubType.CALENDAR_EVENT_MAYBE,
        }
        return rsvp_mappings[rsvp_status]

    @staticmethod
    def _get_calendar_event_rsvp_status(rsvp_status: str) -> CalendarEventRsvpStatus:
        rsvp_status_map: dict[str, CalendarEventRsvpStatus] = {
            "yes": CalendarEventRsvpStatus.ACCEPTED,
            "no": CalendarEventRsvpStatus.REJECTED,
            "maybe": CalendarEventRsvpStatus.MAYBE,
            "noreply": CalendarEventRsvpStatus.MAYBE,
        }
        return rsvp_status_map[rsvp_status]

    @staticmethod
    def format_zoom_meeting_description(
        user: User, zoom_meeting: ZoomMeeting, consent_url: str | None = None
    ) -> str:
        if consent_url:
            return (
                f"{UserCalendarCommonService.EVENT_DESCRIPTION_SEPARATOR}"
                f"{user.first_name} {user.last_name} is inviting you to a scheduled Zoom Meeting.\n"
                f"Join Zoom Meeting via Reevo\n"
                f"{consent_url}\n"
            )
        else:
            return (
                f"{UserCalendarCommonService.EVENT_DESCRIPTION_SEPARATOR}"
                f"{user.first_name} {user.last_name} is inviting you to a scheduled Zoom Meeting.\n"
                f"Join Zoom Meeting\n"
                f"{zoom_meeting.join_url}\n"
                f"\nMeeting ID: {zoom_meeting.id}\n"
                f"Passcode: {zoom_meeting.password}"
            )

    @staticmethod
    def get_consent_url(consent_id: UUID) -> str:
        domain = (
            "https://app-dev.reevo.ai"
            if settings.environment == "dev"
            else "https://app.reevo.ai"
        )
        return f"{domain}/public/meeting/join?consent_id={consent_id}"

    @staticmethod
    def generate_google_conference_data_manual(
        uri: str, meeting_name: str
    ) -> GoogleEventRequest:
        return GoogleEventRequest(
            conferenceData=GoogleConferenceData(
                entryPoints=[
                    GoogleConferenceDataEntryPoint(entryPointType="video", uri=uri)
                ],
                conferenceSolution=GoogleConferenceConferenceSolution(
                    key={"type": "addOn"}, name=meeting_name, iconUri=None
                ),
            )
        )

    @staticmethod
    def generate_google_conference_data_auto() -> GoogleEventRequest:
        return GoogleEventRequest(
            conferenceData=GoogleConferenceData(
                createRequest=GoogleConferenceDataCreateRequest(
                    conferenceSolutionKey=GoogleConferenceSolutionKey(
                        type="hangoutsMeet"
                    ),
                    requestId=str(uuid4()),
                )
            )
        )

    @staticmethod
    def format_google_meet_description(
        user: User, consent_url: str | None = None
    ) -> str:
        return (
            f"{UserCalendarCommonService.EVENT_DESCRIPTION_SEPARATOR}"
            f"{user.first_name} {user.last_name} is inviting you to a scheduled Google Meet meeting.\n"
            f"Join Google Meet via Reevo\n"
            f"{consent_url}\n"
        )

    @staticmethod
    def extract_domain_from_organizer_email(email: str | None) -> str | None:
        """Extract domain from email.

        Args:
            email: email in format '<EMAIL>'

        Returns:
            Domain string or None if invalid website format
        """
        if not email:
            return None

        domain = email.split("@")[-1]
        return domain if domain else None

    async def delete_buffer_events(
        self,
        user_calendar_event: UserCalendarEvent,
        calendar_external_id: str,
        grant_id: str,
        async_nylas_client: AsyncNylasClient,
    ) -> None:
        """
        Delete buffer events for given user_calendar_event.
        """
        # 1. query the buffer info
        buffer_info = not_none(user_calendar_event.buffer_info)
        external_ids = [
            buffer_info.pre_buffer_external_id,
            buffer_info.post_buffer_external_id,
        ]
        # exclude None values from the list
        external_ids_exclude_none = [eid for eid in external_ids if eid]
        logger.bind(
            user_calendar_id=user_calendar_event.user_calendar_id,
            organization_id=user_calendar_event.organization_id,
            event_count=len(external_ids),
        ).info("Found associated buffer events")

        for external_id in external_ids_exclude_none:
            try:
                # 2. delete the nylas event
                await async_nylas_client.delete_event(
                    grant_id=grant_id,
                    event_id=external_id,
                    calendar_id=calendar_external_id,
                    notify_all_participants=False,
                )
                logger.bind(
                    user_calendar_id=user_calendar_event.user_calendar_id,
                    organization_id=user_calendar_event.organization_id,
                ).info(f"Deleted buffer event: {external_id}")
            except Exception as e:
                logger.bind(
                    user_calendar_id=user_calendar_event.user_calendar_id,
                    organization_id=user_calendar_event.organization_id,
                ).error(f"Error deleting buffer events: {e}")

    async def update_buffer_event(
        self,
        user_calendar_event: UserCalendarEvent,
        calendar_external_id: str,
        grant_id: str,
        async_nylas_client: AsyncNylasClient,
    ) -> None:
        # 1. query the event schedule
        event_schedule = await self.event_schedule_repo.find_by_primary_key(
            table_model=EventSchedule,
            id=not_none(user_calendar_event.event_schedule_id),
        )
        if not event_schedule:
            logger.bind(
                event_schedule_id=user_calendar_event.id,
            ).error("Event schedule not found")
            return

        buffer_info = not_none(user_calendar_event.buffer_info)
        try:
            # 2. update buffer event before the meeting
            if buffer_info.pre_buffer_external_id and event_schedule.buffer_before:
                start = user_calendar_event.starts_at - timedelta(
                    minutes=int(event_schedule.buffer_before)
                )
                end = user_calendar_event.starts_at
                await async_nylas_client.update_event(
                    grant_id=grant_id,
                    calendar_id=calendar_external_id,
                    event_id=buffer_info.pre_buffer_external_id,
                    request=NylasUpdateEventRequest(
                        when=NylasUpdateTimespan(
                            start_time=int(start.timestamp()),
                            end_time=int(end.timestamp()),
                        )
                    ),
                    notify_all_participants=False,
                )
            # 3. update buffer event before the meeting
            if buffer_info.post_buffer_external_id and event_schedule.buffer_after:
                end = user_calendar_event.ends_at + timedelta(
                    minutes=int(event_schedule.buffer_after)
                )
                start = user_calendar_event.ends_at
                await async_nylas_client.update_event(
                    grant_id=grant_id,
                    calendar_id=calendar_external_id,
                    event_id=buffer_info.post_buffer_external_id,
                    request=NylasUpdateEventRequest(
                        when=NylasUpdateTimespan(
                            start_time=int(start.timestamp()),
                            end_time=int(end.timestamp()),
                        )
                    ),
                    notify_all_participants=False,
                )
        except Exception as e:
            logger.bind(
                user_calendar_id=user_calendar_event.user_calendar_id,
                organization_id=user_calendar_event.organization_id,
            ).error(f"Error updating buffer events: {e}")


class SingletonUserCalendarCommonService(Singleton, UserCalendarCommonService):
    pass


def get_user_calendar_common_service_by_db_engine(
    db_engine: DatabaseEngine,
) -> UserCalendarCommonService:
    if SingletonUserCalendarCommonService.has_instance():
        return SingletonUserCalendarCommonService.get_singleton_instance()

    google_client = GoogleClient(encryption_manager=fernet_encryption_manager)
    contact_service = get_contact_service(db_engine=db_engine)
    user_integration_repo = UserIntegrationRepository(engine=db_engine)
    user_cal_repo = UserCalendarRepository(engine=db_engine)
    user_repo = UserRepository(engine=db_engine)
    conference_service = init_conference_service_by_db_engine(db_engine=db_engine)
    meeting_service = meeting_service_factory_general(db_engine=db_engine)
    user_service = get_user_service_general(db_engine)
    event_schedule_repo = EventScheduleRepository(engine=db_engine)
    select_list_service = get_select_list_service(engine=db_engine)
    activity_service_v2 = get_activity_service_general(db_engine=db_engine)
    contact_query_service = get_contact_query_service(db_engine=db_engine)
    contact_resolve_service = get_contact_resolve_service(db_engine=db_engine)
    organization_service = get_organization_service_general(db_engine=db_engine)
    organization_preference_service = organization_preference_service_from_engine(
        db_engine=db_engine
    )
    domain_crm_association_service = get_domain_crm_association_service(
        db_engine=db_engine
    )

    return SingletonUserCalendarCommonService(
        google_client=google_client,
        contact_service=contact_service,
        user_integration_repo=user_integration_repo,
        user_cal_repo=user_cal_repo,
        user_repo=user_repo,
        conference_service=conference_service,
        meeting_service=meeting_service,
        user_service=user_service,
        event_schedule_repo=event_schedule_repo,
        select_list_service=select_list_service,
        contact_query_service=contact_query_service,
        activity_service_v2=activity_service_v2,
        organization_service=organization_service,
        feature_flag_service=get_feature_flag_service(),
        contact_resolve_service=contact_resolve_service,
        organization_preference_service=organization_preference_service,
        domain_crm_association_service=domain_crm_association_service,
    )


def get_user_calendar_common_service(request: Request) -> UserCalendarCommonService:
    db_engine = get_db_engine(request=request)
    return get_user_calendar_common_service_by_db_engine(db_engine=db_engine)
