from uuid import UUID

from salestech_be.common.schema_manager.std_object_field_identifier import (
    StdObjectIdentifiers,
)
from salestech_be.common.schema_manager.std_object_relationship import (
    CalendarEventRelationship,
)
from salestech_be.core.domain_crm_association.types import (
    CreateDomainCRMAssociationV2,
    DeleteDomainCRMAssociationV2,
)
from salestech_be.db.models.domain_crm_association import (
    AttributionInfo,
    AttributionSource,
    AttributionState,
)
from salestech_be.db.models.user_calendar_event import UserCalendarEvent
from salestech_be.util.time import zoned_utc_now


class _UserCalendarCrmAssociationMapper:
    @staticmethod
    def map_user_calendar_event_to_associations_create(
        event: UserCalendarEvent,
    ) -> list[CreateDomainCRMAssociationV2]:
        associations_to_create: list[CreateDomainCRMAssociationV2] = []

        # Associate the event to the creator user and meeting if the meeting exists
        # 1. Calendar event is guaranteed to have a user_id
        # 2. Calendar event can associate to participants, thus contacts
        # 3. Calendar event does not associate to a pipeline and account directly (only via meeting)
        organizer_user_id = event.organizer_user_id
        user_ids = set()
        created_at_time = zoned_utc_now()
        if organizer_user_id:
            user_ids.add(organizer_user_id)

        for participant in event.participants:
            if participant.user_id:
                user_ids.add(participant.user_id)

            if participant.contact_id:
                associations_to_create.append(
                    CreateDomainCRMAssociationV2(
                        organization_id=event.organization_id,
                        std_object_relationship=CalendarEventRelationship.calendar_event__to__contact,
                        domain_object_id=event.id,
                        domain_object_type=StdObjectIdentifiers.user_calendar_event,
                        crm_object_id=participant.contact_id,
                        crm_object_type=StdObjectIdentifiers.contact,
                        created_by_user_id=event.user_id,
                        attribution_state=AttributionState.PROCESSED_SELECTED,
                        attribution_info=AttributionInfo(
                            attribution_source=AttributionSource.DOMAIN_EVENT_CONTEXT,
                            attributed_at=created_at_time,
                            attributed_by_user_id=event.user_id,
                        ),
                    )
                )

        for user_id in user_ids:
            if user_id:
                associations_to_create.append(
                    CreateDomainCRMAssociationV2(
                        organization_id=event.organization_id,
                        std_object_relationship=CalendarEventRelationship.calendar_event__to__organizer_user
                        if user_id == organizer_user_id
                        else CalendarEventRelationship.calendar_event__to__participant_user,
                        domain_object_id=event.id,
                        domain_object_type=StdObjectIdentifiers.user_calendar_event,
                        crm_object_id=user_id,
                        crm_object_type=StdObjectIdentifiers.user,
                        created_by_user_id=event.user_id,
                        attribution_state=AttributionState.PROCESSED_SELECTED,
                        attribution_info=AttributionInfo(
                            attribution_source=AttributionSource.DOMAIN_EVENT_CONTEXT,
                            attributed_at=created_at_time,
                            attributed_by_user_id=event.user_id,
                        ),
                    )
                )

        return associations_to_create

    @staticmethod
    def map_user_calendar_event_to_associations_delete(
        user_id: UUID,
        organization_id: UUID,
        event_id: UUID,
    ) -> list[DeleteDomainCRMAssociationV2]:
        return [
            DeleteDomainCRMAssociationV2(
                organization_id=organization_id,
                domain_object_id=event_id,
                domain_object_type=StdObjectIdentifiers.user_calendar_event,
                deleted_by_user_id=user_id,
            )
        ]

    @staticmethod
    def map_user_calendar_event_to_associations_update(
        user_id: UUID,
        existing_event: UserCalendarEvent,
        updated_event: UserCalendarEvent,
    ) -> tuple[list[CreateDomainCRMAssociationV2], list[DeleteDomainCRMAssociationV2]]:
        associations_to_delete = _UserCalendarCrmAssociationMapper.map_user_calendar_event_to_associations_delete(
            user_id=user_id,
            organization_id=existing_event.organization_id,
            event_id=existing_event.id,
        )

        associations_to_create = _UserCalendarCrmAssociationMapper.map_user_calendar_event_to_associations_create(
            event=updated_event,
        )

        return associations_to_create, associations_to_delete
