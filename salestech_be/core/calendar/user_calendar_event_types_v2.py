from typing import Annotated
from uuid import UUID

from salestech_be.common.schema_manager.std_object_field_identifier import (
    StdObjectIdentifiers,
    UserCalendarEventField,
)
from salestech_be.common.schema_manager.std_object_relationship import (
    UserCalendarEventRelationship,
)
from salestech_be.common.type.metadata.field.field_indexable_config import (
    UniqueIndexableConfig,
)
from salestech_be.common.type.metadata.field.field_type_property import (
    BooleanCheckboxFieldProperty,
    TextFieldProperty,
    TimestampFieldProperty,
    UUIDFieldProperty,
)
from salestech_be.common.type.metadata.schema import OutboundRelationship
from salestech_be.core.common.types import CustomizableDomainModel, FieldMetadata
from salestech_be.core.meeting.types.meeting_types_v2 import Meeting<PERSON>ield
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime


class UserCalendarEventV2(CustomizableDomainModel):
    object_id = StdObjectIdentifiers.user_calendar_event.identifier
    object_display_name = "Calendar Event"
    field_name_provider = UserCalendarEventField
    inbound_relationships = ()
    outbound_relationships = (
        OutboundRelationship(
            relation_type=OutboundRelationship.RelationType.LOOKUP,
            id=UserCalendarEventRelationship.user_calendar_event__to__meeting,
            relationship_name="Meeting",
            self_object_identifier=StdObjectIdentifiers.user_calendar_event.identifier,
            related_object_identifier=StdObjectIdentifiers.meeting.identifier,
            self_cardinality=OutboundRelationship.Cardinality.ONE,
            related_object_cardinality=OutboundRelationship.Cardinality.ONE,
            ordered_self_field_identifiers=(
                UserCalendarEventField.meeting_id.identifier,
            ),
            ordered_related_field_identifiers=(MeetingField.id.identifier,),
        ),
    )

    # Basic information
    id: Annotated[
        UUID,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                index_config=UniqueIndexableConfig(is_indexed=True, is_unique=True),
                field_display_name="ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            )
        ),
    ]

    user_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="User ID",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ]

    organization_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Organization ID",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ]

    user_calendar_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="User Calendar ID",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ]

    group_key: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Group Key",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ]

    is_busy: Annotated[
        bool | None,
        FieldMetadata(
            type_property=BooleanCheckboxFieldProperty(
                field_display_name="Is Busy",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ]

    ical_uid: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="iCal UID",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ]

    external_id: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="External ID",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ]

    description: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Description",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ]

    title: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Title",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ]

    visibility: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Visibility",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ]

    location: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Location",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ]

    html_link: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="HTML Link",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ]

    status: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Status",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ]

    is_read_only: Annotated[
        bool | None,
        FieldMetadata(
            type_property=BooleanCheckboxFieldProperty(
                field_display_name="Is Read Only",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ]

    hide_participants: Annotated[
        bool | None,
        FieldMetadata(
            type_property=BooleanCheckboxFieldProperty(
                field_display_name="Hide Participants",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ]

    master_event_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Master Event ID",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ]

    original_master_event_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Original Master Event ID",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ]

    recurrence: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Recurrence",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ]

    organizer_name: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Organizer Name",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ]

    organizer_email: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Organizer Email",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ]

    organizer_user_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Organizer User ID",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ]

    participants: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Participants",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ]

    starts_at: Annotated[
        ZoneRequiredDateTime | None,
        FieldMetadata(
            type_property=TimestampFieldProperty(
                field_display_name="Starts At",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ]

    ends_at: Annotated[
        ZoneRequiredDateTime | None,
        FieldMetadata(
            type_property=TimestampFieldProperty(
                field_display_name="Ends At",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ]

    conferencing_provider: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Conferencing Provider",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ]

    conferencing_external_id: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Conferencing External ID",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ]

    conferencing_details: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Conferencing Details",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ]

    external_created_at: Annotated[
        ZoneRequiredDateTime | None,
        FieldMetadata(
            type_property=TimestampFieldProperty(
                field_display_name="External Created At",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ]

    external_updated_at: Annotated[
        ZoneRequiredDateTime | None,
        FieldMetadata(
            type_property=TimestampFieldProperty(
                field_display_name="External Updated At",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ]

    meeting_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Meeting ID",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ]

    event_schedule_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Event Schedule ID",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ]

    is_all_day_event: Annotated[
        bool | None,
        FieldMetadata(
            type_property=BooleanCheckboxFieldProperty(
                field_display_name="Is All Day Event",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ]

    buffer_info: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Buffer Info",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ]

    created_at: Annotated[
        ZoneRequiredDateTime | None,
        FieldMetadata(
            type_property=TimestampFieldProperty(
                field_display_name="Created At",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ]

    updated_at: Annotated[
        ZoneRequiredDateTime | None,
        FieldMetadata(
            type_property=TimestampFieldProperty(
                field_display_name="Updated At",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ]

    metadata: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Metadata",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ]

    deleted_at: Annotated[
        ZoneRequiredDateTime | None,
        FieldMetadata(
            type_property=TimestampFieldProperty(
                field_display_name="Deleted At",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ]
