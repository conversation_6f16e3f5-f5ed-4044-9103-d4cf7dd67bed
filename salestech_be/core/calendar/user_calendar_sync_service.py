from collections import defaultdict
from dataclasses import dataclass
from datetime import datetime, timedelta
from typing import Any
from uuid import UUID

import pytz
from asyncpg import UniqueViolationError
from dateutil.relativedelta import relativedelta
from fastapi import Request
from sqlalchemy.exc import IntegrityError
from temporalio.common import WorkflowIDReusePolicy

from salestech_be.common.exception import IllegalStateError
from salestech_be.common.exception.exception import (
    ExternalServiceError,
    ResourceNotFoundError,
)
from salestech_be.common.lifespan import get_db_engine
from salestech_be.common.singleton import Singleton
from salestech_be.core.calendar.user_calendar_common_service import (
    UserCalendarCommonService,
    get_user_calendar_common_service_by_db_engine,
)
from salestech_be.core.calendar.user_calendar_crm_association_mapper import (
    _UserCalendarCrmAssociationMapper,
)
from salestech_be.core.domain_crm_association.domain_crm_association_service import (
    DomainCRMAssociationService,
    get_domain_crm_association_service,
)
from salestech_be.core.ff.feature_flag_service import (
    FeatureFlagService,
    get_feature_flag_service,
)
from salestech_be.core.ff.types import FeatureFlagRequest
from salestech_be.core.meeting.dto.meeting_dto import MeetingDto
from salestech_be.core.meeting.meeting_service import (
    MeetingService,
    meeting_service_factory_general,
)
from salestech_be.core.organization.service.organization_service import (
    OrganizationService,
    get_organization_service_general,
)
from salestech_be.db.dao.calendar_account_repository import CalendarAccountRepository
from salestech_be.db.dao.user_calendar_repository import UserCalendarRepository
from salestech_be.db.dao.user_integration_repository import UserIntegrationRepository
from salestech_be.db.dao.user_repository import UserRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.dto.user_integration_dto import UserIntegrationDto
from salestech_be.db.models.calendar_account import CalendarAccount
from salestech_be.db.models.meeting import (
    MeetingCancelReason,
    MeetingStatus,
)
from salestech_be.db.models.user import User
from salestech_be.db.models.user_calendar import UserCalendar
from salestech_be.db.models.user_calendar_event import (
    CalendarEventParticipant,
    UserCalendarEvent,
)
from salestech_be.db.models.user_integration_connector import UserIntegrationConnector
from salestech_be.integrations.nylas.async_nylas_client import AsyncNylasClient
from salestech_be.integrations.nylas.model import NylasEvent, NylasListEventQueryParams
from salestech_be.integrations.temporal.client import get_temporal_client
from salestech_be.ree_logging import get_logger
from salestech_be.temporal.workflows.user_calendar.user_calendar_event_meeting_sync_workflow import (
    UserCalendarEventMeetingSyncWorkflow,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    UserCalendarEventMeetingSyncWorkflowInput,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime
from salestech_be.util.time import zoned_utc_from_timestamp, zoned_utc_now
from salestech_be.util.validation import not_none
from salestech_be.web.api.calendar.schema import (
    CalendarEventHistoricalSyncTimeFrame,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)

logger = get_logger("user_calendar_sync_service")

LIST_EVENT_PAGE_LIMIT = 200


@dataclass
class CalendarEventSyncResult:
    calendar_event: UserCalendarEvent | None
    meeting_id: UUID | None
    is_new: bool
    error: str | None = None


class UserCalendarSyncService:
    def __init__(
        self,
        async_nylas_client: AsyncNylasClient,
        user_repo: UserRepository,
        user_integration_repo: UserIntegrationRepository,
        user_cal_repo: UserCalendarRepository,
        calendar_account_repo: CalendarAccountRepository,
        user_calendar_common_service: UserCalendarCommonService,
        meeting_service: MeetingService,
        organization_service: OrganizationService,
        feature_flag_service: FeatureFlagService,
        domain_crm_association_service: DomainCRMAssociationService,
    ):
        self.async_nylas_client = async_nylas_client
        self.user_repo = user_repo
        self.user_integration_repo = user_integration_repo
        self.user_cal_repo = user_cal_repo
        self.user_calendar_common_service = user_calendar_common_service
        self.meeting_service = meeting_service
        self.calendar_account_repo = calendar_account_repo
        self.organization_service = organization_service
        self.feature_flag_service = feature_flag_service
        self.domain_crm_association_service = domain_crm_association_service

    async def _find_user_calendar_events_after_date(
        self,
        organization_id: UUID,
        after_date: ZoneRequiredDateTime,
    ) -> dict[str, list[UserCalendarEvent]]:
        calendar_events = (
            await self.user_cal_repo.find_all_user_calendar_events_starting_after(
                organization_id=organization_id,
                after_date=after_date,
            )
        )

        # Group events by group_key
        events_by_group_key: dict[str, list[UserCalendarEvent]] = defaultdict(list)
        for event in calendar_events:
            events_by_group_key[event.group_key].append(event)

        return events_by_group_key

    async def resync_user_calendar_events_for_meeting_settings(
        self,
        organization_id: UUID,
    ) -> list[UserCalendarEvent]:
        logger.bind(organization_id=organization_id).info(
            "Start resync user calendar events for meeting settings"
        )
        events_by_group_key = await self._find_user_calendar_events_after_date(
            organization_id=organization_id,
            after_date=zoned_utc_now(),
        )
        results = []
        for group_key, events in events_by_group_key.items():
            if not events:
                logger.bind(organization_id=organization_id, group_key=group_key).info(
                    "No events found for group key, skipping"
                )
                continue

            # Prefer to drive from organizer's event if there is any
            user_calendar_event = next(
                (event for event in events if event.organizer_user_id == event.user_id),
                events[0],
            )
            logger.bind(
                organization_id=organization_id,
                event_id=user_calendar_event.id,
                organizer_user_id=user_calendar_event.organizer_user_id,
                user_id=user_calendar_event.user_id,
            ).info("Syncing meeting for calendar event")

            meeting_dto = await self._handle_meeting_sync(
                enriched_event=user_calendar_event,
                existing_event=user_calendar_event,
                user_id=user_calendar_event.user_id,
                is_resync=True,
                retry_with_workflow=False,
            )
            meeting_id = meeting_dto.meeting_id if meeting_dto else None

            if meeting_id and not user_calendar_event.meeting_id:
                logger.bind(
                    organization_id=organization_id,
                    meeting_id=meeting_id,
                    event_meeting_id=user_calendar_event.meeting_id,
                ).info("Updating calendar event meeting id")
                updated_event = not_none(
                    await self.user_cal_repo.update_by_tenanted_primary_key(
                        table_model=UserCalendarEvent,
                        organization_id=organization_id,
                        primary_key_to_value={"id": user_calendar_event.id},
                        column_to_update={
                            "meeting_id": meeting_id,
                            "updated_at": zoned_utc_now(),
                        },
                    )
                )
                results.append(updated_event)
        return results

    @staticmethod
    def _is_organizer_in_participant_list(
        participants: list[CalendarEventParticipant], organizer_email: str
    ) -> bool:
        return any(participant.email == organizer_email for participant in participants)

    @staticmethod
    def _get_historical_sync_cutoff_datetime(
        email_connected_at: datetime,
        historical_sync_time_frame: CalendarEventHistoricalSyncTimeFrame,
    ) -> datetime:
        if (
            historical_sync_time_frame
            == CalendarEventHistoricalSyncTimeFrame.THREE_MONTHS
        ):
            return email_connected_at - relativedelta(months=3)
        elif (
            historical_sync_time_frame
            == CalendarEventHistoricalSyncTimeFrame.SIX_MONTHS
        ):
            return email_connected_at - relativedelta(months=6)
        elif (
            historical_sync_time_frame
            == CalendarEventHistoricalSyncTimeFrame.TWELVE_MONTHS
        ):
            return email_connected_at - relativedelta(months=12)
        elif (
            historical_sync_time_frame
            == CalendarEventHistoricalSyncTimeFrame.TWENTYFOUR_MONTHS
        ):
            return email_connected_at - relativedelta(months=24)
        else:
            return datetime.min.replace(tzinfo=pytz.UTC)

    # process user calendar including insert/updater/delete in temporal workflow
    async def process_user_calendar_task(  # type: ignore[explicit-any] # TODO: fix-any-annotation  # noqa: C901, PLR0911
        self,
        user_id: UUID,
        organization_id: UUID,
        calendar_account_id: UUID,
        sync_from: ZoneRequiredDateTime,
        sync_to: ZoneRequiredDateTime,
        only_sync_contact: bool = False,
    ) -> dict[str, Any]:
        with logger.contextualize(
            user_id=user_id,
            organization_id=organization_id,
            calendar_account_id=calendar_account_id,
            sync_from=sync_from,
            sync_to=sync_to,
        ):
            logger.info("start processing user calendar sync task in temporal workflow")

            synced_result: dict[str, Any] = {  # type: ignore[explicit-any] # TODO: fix-any-annotation
                "message": "Completed",
                "info": None,
            }

            # Fetch calendar account
            calendar_account: (
                CalendarAccount | None
            ) = await self.calendar_account_repo.find_by_tenanted_primary_key(
                CalendarAccount,
                id=calendar_account_id,
                organization_id=organization_id,
            )
            if not calendar_account:
                logger.bind(
                    calendar_account_id=calendar_account_id,
                    organization_id=organization_id,
                ).error("Calendar account not found")
                return synced_result
            elif not calendar_account.active:
                logger.bind(
                    calendar_account_id=calendar_account_id,
                    organization_id=organization_id,
                ).warning("Calendar account is inactive")
                return synced_result

            user_integration_dto: UserIntegrationDto = await self.user_integration_repo.find_user_integration_by_calendar_account_or_error(
                calendar_account_id=calendar_account.id,
                organization_id=organization_id,
            )

            if not user_integration_dto.user_integration_connector:
                raise IllegalStateError("No Nylas connector found for user integration")

            nylas_connector: UserIntegrationConnector = (
                user_integration_dto.user_integration_connector
            )

            # user calendar
            user_calendar: (
                UserCalendar | None
            ) = await self.user_cal_repo.find_user_primary_calendar_by_calendar_account(
                calendar_account_id=calendar_account.id,
                organization_id=organization_id,
            )
            if not user_calendar:
                nylas_cals = await self.async_nylas_client.list_calendars(
                    grant_id=nylas_connector.external_id,
                )
                primary_nylas_cal = next(
                    filter(lambda cal: cal.is_primary, nylas_cals.data), None
                )
                if not primary_nylas_cal:
                    raise ResourceNotFoundError(
                        f"no primary calendar found for user id {user_id}"
                    )

                if primary_nylas_cal.id != calendar_account.email:
                    # TODO: decide how should you handle this case going forward
                    logger.error(
                        f"primary calendar ({primary_nylas_cal.id}) does not match calendar account email ({calendar_account.email})"
                    )
                user_calendar = not_none(
                    await self.user_cal_repo.upsert_user_calendar(
                        UserCalendar.from_nylas_calendar(
                            user_id=user_id,
                            user_integration_id=user_integration_dto.integration_id,
                            calendar_account_id=calendar_account.id,
                            ncal=primary_nylas_cal,
                            organization_id=organization_id,
                        )
                    )
                )
                logger.bind(
                    user_id=user_id,
                    organization_id=organization_id,
                ).info(f"Create a new user_calendar({user_calendar.id}) for user")

            # Fetch events from Nylas
            try:
                nylas_events: list[NylasEvent] | None = await self._fetch_nylas_events(
                    grant_id=nylas_connector.external_id,
                    calendar_id=user_calendar.external_id,
                    sync_from=sync_from,
                    sync_to=sync_to,
                    show_cancelled=True,
                )
            except ExternalServiceError as e:
                logger.bind(
                    grant_id=nylas_connector.external_id,
                    calendar_id=user_calendar.external_id,
                ).error(f"[External] Error fetching events from Nylas: {e}")
                synced_result["message"] = "Error fetching events from Nylas"
                return synced_result
            except Exception as e:
                logger.bind(
                    grant_id=nylas_connector.external_id,
                    calendar_id=user_calendar.external_id,
                ).error(f"[Unknown] Error fetching events from Nylas: {e}")
                synced_result["message"] = "Error fetching events from Nylas"
                return synced_result

            if not nylas_events:
                synced_result["message"] = "No events fetched from Nylas"
                return synced_result
            logger.bind(
                user_id=user_id,
            ).info(f"Fetched {len(nylas_events)} events from Nylas for user")

            # only sync contact
            if only_sync_contact:
                logger.info("syncing contact from nylas events")
                contact_ids = await self._only_sync_contact_from_nylas(
                    nylas_events=nylas_events,
                    user_calendar=user_calendar,
                    user_integration_dto=user_integration_dto,
                    organization_id=organization_id,
                )
                synced_result["info"] = {
                    "synced_contact": len(contact_ids),
                }
                return synced_result

            # Process events
            synced_cal_event_info: dict[str, Any] = await self._process_events(  # type: ignore[explicit-any] # TODO: fix-any-annotation
                nylas_events=nylas_events,
                user_calendar=user_calendar,
                user_integration_dto=user_integration_dto,
                organization_id=organization_id,
            )

            current_event_ids = synced_cal_event_info["current_event_ids"]
            (
                cleaned_up_events_count,
                query_count_for_cleanup,
            ) = await self._cleanup_deleted_events(
                user_calendar=user_calendar,
                nylas_connector=nylas_connector,
                current_event_ids=current_event_ids,
                organization_id=organization_id,
                sync_from=sync_from,
                sync_to=sync_to,
            )

            synced_result["info"] = {
                "inserted": len(synced_cal_event_info["inserted_cal_event_ids"]),
                "updated": len(synced_cal_event_info["updated_nylas_events"]),
                "cleaned_up": cleaned_up_events_count,
                "query_count_for_cleanup": query_count_for_cleanup,
            }
            return synced_result

    async def _cleanup_deleted_events(
        self,
        *,
        user_calendar: UserCalendar,
        nylas_connector: UserIntegrationConnector,
        current_event_ids: list[str],
        organization_id: UUID,
        sync_from: ZoneRequiredDateTime,
        sync_to: ZoneRequiredDateTime,
    ) -> tuple[int, int]:
        """Handle events that exist in our database but were deleted from Nylas."""
        existing_events = await self.user_cal_repo.find_all_user_calendar_events_by_user_calendar_id_and_timerange(
            user_calendar_id=user_calendar.id,
            organization_id=organization_id,
            starts_at_inclusive=sync_from,
            ends_at_exclusive=sync_to,
        )
        cleaned_up_events_count = 0
        query_count = 0
        for existing_event in existing_events:
            if (
                existing_event.external_id not in current_event_ids
                and not existing_event.deleted_at
            ):
                # Verify event is actually deleted in Nylas
                actual_event = await self.async_nylas_client.get_event(
                    grant_id=nylas_connector.external_id,
                    calendar_id=user_calendar.external_id,
                    event_id=existing_event.external_id,
                )
                query_count += 1

                if (not actual_event) or (
                    (actual_event.title == "CANCELLED" or not actual_event.title)
                    and actual_event.status == "cancelled"
                ):
                    # Mark event as cancelled
                    await self.user_cal_repo.update_instance(
                        existing_event.model_copy(
                            update={
                                "title": "CANCELLED",
                                "status": "cancelled",
                                "deleted_at": zoned_utc_now(),
                            }
                        )
                    )
                    cleaned_up_events_count += 1
                    # Cancel associated meeting if it exists
                    meeting_id = existing_event.meeting_id
                    if meeting_id:
                        try:
                            await self.meeting_service.cancel_meeting(
                                meeting_id=meeting_id,
                                organization_id=organization_id,
                                cancel_reason=MeetingCancelReason.SCHEDULED_EVENT_CANCELED,
                            )
                        except Exception as e:
                            logger.bind(
                                meeting_id=meeting_id,
                            ).error(f"Error canceling meeting: {e}")
                            continue
        logger.info(f"Query count for cleanup: {query_count}")
        return cleaned_up_events_count, query_count

    async def _fetch_nylas_events(
        self,
        *,
        grant_id: str,
        calendar_id: str,
        sync_from: ZoneRequiredDateTime,
        sync_to: ZoneRequiredDateTime,
        show_cancelled: bool = False,
    ) -> list[NylasEvent] | None:
        list_nylas_event_response = await self.async_nylas_client.list_events(
            grant_id=grant_id,
            nylas_list_event_query=NylasListEventQueryParams(
                calendar_id=calendar_id,
                start=int(sync_from.timestamp()),
                end=int(sync_to.timestamp()),
                show_cancelled=show_cancelled,
            ),
        )
        if not list_nylas_event_response.data:
            logger.bind(
                grant_id=grant_id, calendar_id=calendar_id, start=sync_from, end=sync_to
            ).info("No events fetched from Nylas")
            return None
        return [
            NylasEvent.model_validate(nylas_event)
            for nylas_event in list_nylas_event_response.data
            if nylas_event.organizer and nylas_event.html_link
        ]

    async def _process_events(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self,
        *,
        nylas_events: list[NylasEvent],
        user_calendar: UserCalendar,
        user_integration_dto: UserIntegrationDto,
        organization_id: UUID,
    ) -> dict[str, Any]:
        logger.bind(
            user_calendar_id=user_calendar.id,
            organization_id=organization_id,
        ).info("Start processing nylas calendar events")
        results = []
        if await self.feature_flag_service.is_enabled(
            request=FeatureFlagRequest(
                organization_id=organization_id, flag_key="enable_recurring_event_sync"
            )
        ):
            sync_recurrent_event = True
        else:
            sync_recurrent_event = False
        for nylas_event in nylas_events:
            sync_result = await self.process_nylas_calendar_event(
                nylas_event=nylas_event,
                user_calendar=user_calendar,
                user_integration_dto=user_integration_dto,
                organization_id=organization_id,
                sync_recurrent_event=sync_recurrent_event,
            )
            results.append(sync_result)

        return {
            "inserted_cal_event_ids": [
                r.calendar_event.id
                for r in results
                if r.is_new and r.calendar_event is not None
            ],
            "updated_nylas_events": [
                r.calendar_event.id
                for r in results
                if not r.is_new and r.calendar_event is not None
            ],
            "total_nylas_events": len(nylas_events),
            "current_event_ids": [e.id for e in nylas_events],
        }

    async def _get_organizer_user_id(
        self,
        *,
        nylas_event: NylasEvent,
        organization_id: UUID,
    ) -> UUID | None:
        organizer_email: str | None = (
            nylas_event.organizer.email if nylas_event.organizer else None
        )
        if organizer_email:
            organizer_identifier = organizer_email.split("@")[0]
            organizer_domain = organizer_email.split("@")[-1]
            organization = await self.organization_service.get_organization_by_id(
                organization_id=organization_id
            )
            organization_profile = (
                organization.organization_profile if organization else None
            )
            alternate_domains = (
                organization_profile.alternate_domains
                if organization_profile and organization_profile.alternate_domains
                else []
            )

            domains_to_ceck = [organizer_domain, *alternate_domains]
            for domain in domains_to_ceck:
                organizer_user: (
                    User | None
                ) = await self.user_repo.find_user_by_organization_id_and_email(
                    organization_id=organization_id,
                    email=f"{organizer_identifier}@{domain}",
                )
                if organizer_user:
                    return organizer_user.id
        return None

    async def _enrich_event(
        self,
        *,
        dirty_event: UserCalendarEvent,
        user_integration_dto: UserIntegrationDto,
        organization_id: UUID,
    ) -> UserCalendarEvent:
        participants: list[
            CalendarEventParticipant
        ] = await self.user_calendar_common_service.map_calendar_event_participants(
            cal_event=dirty_event,
            organization_id=organization_id,
            user_integration=user_integration_dto.user_integration,
        )
        return dirty_event.model_copy(
            update={
                "participants": participants,
            }
        )

    async def resync_calendar_event_to_meeting(
        self, *, organization_id: UUID, group_key: str
    ) -> UUID | None:
        logger.bind(
            organization_id=organization_id,
            group_key=group_key,
        ).info("Resyncing calendar event to meeting")
        user_calendar_events = (
            await self.user_cal_repo.find_user_calendar_events_by_calendar_group_keys(
                group_keys=[group_key],
                organization_id=organization_id,
            )
        )
        if not user_calendar_events:
            logger.bind(
                organization_id=organization_id,
                group_key=group_key,
            ).error("No user calendar events found")
            return None

        calendar_event = user_calendar_events[0]
        meeting_dto = await self._handle_meeting_sync(
            enriched_event=calendar_event,
            existing_event=calendar_event,
            user_id=calendar_event.user_id,
            is_resync=True,
            retry_with_workflow=False,
        )
        return meeting_dto.meeting_id if meeting_dto else None

    async def process_nylas_calendar_event(
        self,
        nylas_event: NylasEvent,
        user_calendar: UserCalendar,
        user_integration_dto: UserIntegrationDto,
        organization_id: UUID,
        sync_recurrent_event: bool = False,
    ) -> CalendarEventSyncResult:
        # Find existing event
        existing_event = await self.user_cal_repo.find_user_calendar_event_by_user_calendar_id_and_external_id(
            user_calendar_id=user_calendar.id,
            external_id=nylas_event.id,
            organization_id=organization_id,
            exclude_deleted_or_archived=False,
        )
        # Skip syncing ghost events
        if not existing_event and nylas_event.title == "CANCELLED":
            logger.bind(
                nylas_event_id=nylas_event.id,
                user_calendar_id=user_calendar.id,
                organization_id=organization_id,
            ).debug("Skipping ghost event")
            return CalendarEventSyncResult(
                calendar_event=None, meeting_id=None, is_new=False
            )
        # Skip recurring events if not explicitly requested
        if (
            not existing_event
            and not sync_recurrent_event
            and nylas_event.master_event_id
        ):
            logger.bind(
                nylas_event_id=nylas_event.id,
                user_calendar_id=user_calendar.id,
                organization_id=organization_id,
            ).debug("Skipping recurring event")
            return CalendarEventSyncResult(
                calendar_event=None, meeting_id=None, is_new=False
            )
        else:
            logger.bind(
                nylas_event_id=nylas_event.id,
                user_calendar_id=user_calendar.id,
                organization_id=organization_id,
                master_event_id=nylas_event.master_event_id,
            ).info("Processing recuuring nylas event")

        # Skip syncing existing_event if event is not updated, to reducing db resource usage
        if (
            existing_event
            and existing_event.external_updated_at
            >= zoned_utc_from_timestamp(
                nylas_event.updated_at
                or nylas_event.created_at
                or int(zoned_utc_now().timestamp())
            )
        ):
            logger.bind(
                nylas_event_id=nylas_event.id,
                user_calendar_id=user_calendar.id,
                organization_id=organization_id,
            ).debug("Event is not updated, skip processing")
            return CalendarEventSyncResult(
                calendar_event=None, meeting_id=None, is_new=False
            )

        # Create base event from Nylas data
        organizer_user_id = await self._get_organizer_user_id(
            nylas_event=nylas_event,
            organization_id=organization_id,
        )

        dirty_event = UserCalendarEvent.from_async_nylas(
            user_calendar=user_calendar,
            organizer_user_id=organizer_user_id,
            user_id=user_calendar.user_id,
            organization_id=organization_id,
            evt=nylas_event,
        )

        # Enrich with participants
        enriched_event = await self._enrich_event(
            dirty_event=dirty_event,
            user_integration_dto=user_integration_dto,
            organization_id=organization_id,
        )
        # Handle meeting sync
        meeting_dto = await self._handle_meeting_sync(
            enriched_event=enriched_event,
            existing_event=existing_event,
            user_id=user_calendar.user_id,
            is_resync=False,
            retry_with_workflow=True,
        )

        # Create or update the calendar event
        try:
            if existing_event:
                result = await self._update_calendar_event(
                    existing_event=existing_event,
                    enriched_event=enriched_event,
                    user_integration_dto=user_integration_dto,
                    meeting_dto=meeting_dto,
                    user_calendar=user_calendar,
                )
            else:
                result = await self._create_calendar_event(
                    enriched_event=enriched_event,
                    user_integration_dto=user_integration_dto,
                    user_calendar=user_calendar,
                    meeting_dto=meeting_dto,
                )

            logger.bind(
                nylas_event_id=nylas_event.id,
                conferencing=nylas_event.conferencing,
            ).info("Raw Nylas event conferencing data")

            return CalendarEventSyncResult(
                calendar_event=result,
                meeting_id=meeting_dto.meeting_id if meeting_dto else None,
                is_new=existing_event is None,
            )
        except Exception as e:
            logger.error(f"Failed to sync calendar event: {e}", exc_info=e)
            return CalendarEventSyncResult(
                calendar_event=enriched_event,
                meeting_id=None,
                is_new=existing_event is None,
                error=str(e),
            )

    async def _handle_meeting_sync(  # noqa: C901, PLR0911, PLR0912
        self,
        enriched_event: UserCalendarEvent,
        existing_event: UserCalendarEvent | None,
        user_id: UUID,
        is_resync: bool,
        retry_with_workflow: bool,
    ) -> MeetingDto | None:
        # Get organization settings
        organization = await self.organization_service.get_organization_by_id(
            organization_id=enriched_event.organization_id
        )
        if not organization:
            logger.bind(
                organization_id=enriched_event.organization_id,
                event_id=enriched_event.id,
                user_id=user_id,
            ).error("Organization not found when handling meeting sync")
            return None

        # Check for existing meeting by reference first
        existing_meeting_dto = None
        if existing_event:
            existing_meeting_dto = (
                await self.user_calendar_common_service.get_meeting_for_event_if_exists(
                    organization_id=existing_event.organization_id,
                    calendar_event=existing_event,
                )
            )

            # Skip update time check if resyncing
            if (
                not is_resync
                and existing_meeting_dto
                and existing_meeting_dto.meeting.updated_at
                > enriched_event.external_updated_at
                and existing_meeting_dto.meeting.status == MeetingStatus.SCHEDULED
            ):
                logger.bind(
                    event_id=enriched_event.id,
                    event_updated_at=enriched_event.external_updated_at,
                    meeting_id=existing_meeting_dto.meeting_id,
                    meeting_status=existing_meeting_dto.meeting.status,
                    meeting_updated_at=existing_meeting_dto.meeting.updated_at,
                ).info("Meeting updated after event, skipping sync logic")
                return existing_meeting_dto

            if (
                existing_meeting_dto
                and existing_meeting_dto.meeting_id
                and existing_meeting_dto.meeting_id != existing_event.meeting_id
            ):
                logger.bind(
                    organization_id=enriched_event.organization_id,
                    calendar_event_id=enriched_event.id,
                    group_key=enriched_event.group_key,
                    calendar_event_meeting_id=existing_event.meeting_id,
                    reference_meeting_id=existing_meeting_dto.meeting_id,
                ).info(
                    "Skipping meeting update due to meeting being rescheduled already"
                )
                return existing_meeting_dto

        if (
            enriched_event.status == "cancelled"
            and existing_meeting_dto
            and existing_meeting_dto.meeting.status != MeetingStatus.CANCELED
        ):
            if (
                existing_event
                and existing_event.organizer_user_id == enriched_event.user_id
            ):
                await self.meeting_service.cancel_meeting(
                    meeting_id=existing_meeting_dto.meeting_id,
                    organization_id=enriched_event.organization_id,
                    cancel_reason=MeetingCancelReason.SCHEDULED_EVENT_CANCELED,
                )
                return None
            else:
                logger.bind(
                    meeting_id=existing_meeting_dto.meeting_id,
                    organization_id=enriched_event.organization_id,
                    user_calendar_event_id=enriched_event.id,
                    user_id=user_id,
                ).info(
                    "Meeting cancelled but owned by different user - skipping all updates"
                )
                return existing_meeting_dto

        # Determine sync behavior
        meeting_sync_behavior = await self.user_calendar_common_service.get_meeting_sync_behavior_from_sync_setting(
            cal_event=enriched_event,
            organization_profile=organization.organization_profile,
            organization_id=enriched_event.organization_id,
        )

        logger.bind(
            organization_id=enriched_event.organization_id,
            event_id=enriched_event.id,
            user_id=user_id,
            meeting_sync_behavior=meeting_sync_behavior,
        ).info("Proceed with meeting sync")

        # Config = do not create
        # No meeting: nothing to do
        # There is a meeting: allow for update path below (existing meetings maintained)
        if not meeting_sync_behavior.schedule_meeting and not existing_meeting_dto:
            return None

        # Config = create: Create or update meeting
        try:
            if not existing_event or not existing_meeting_dto:
                if (
                    enriched_event.visibility not in ["public", "default"]
                    or not enriched_event.participants
                    or len(enriched_event.participants) <= 1
                    or enriched_event.conferencing_provider is None
                ):
                    logger.bind(
                        organization_id=organization.id,
                        visibility=enriched_event.visibility,
                        participants=enriched_event.participants,
                        conferencing_provider=enriched_event.conferencing_provider,
                    ).info("Skip create meeting since basic conditions aren't met")
                    return None

                meeting_dto = (
                    await self.user_calendar_common_service.create_meeting_for_event(
                        user_id=user_id,
                        organization_id=enriched_event.organization_id,
                        new_calendar_event=enriched_event,
                        consent_id=None,
                        participants=enriched_event.participants,
                        schedule_bot=meeting_sync_behavior.schedule_bot,
                        event_schedule_id=enriched_event.event_schedule_id,
                        meeting_title=enriched_event.title,
                        old_reference_id=existing_event.group_key
                        if existing_event
                        and enriched_event.group_key != existing_event.group_key
                        else None,
                    )
                )
            else:
                meeting_dto = (
                    await self.user_calendar_common_service.update_meeting_for_event(
                        existing_meeting=existing_meeting_dto,
                        user_id=user_id,
                        organization_id=enriched_event.organization_id,
                        new_calendar_event=enriched_event,
                        participants=enriched_event.participants,
                        schedule_bot=meeting_sync_behavior.schedule_bot,
                        event_schedule_id=existing_event.event_schedule_id,
                        consent_id=None,
                        old_reference_id=existing_event.group_key
                        if existing_event
                        and enriched_event.group_key != existing_event.group_key
                        else None,
                    )
                )
        except UniqueViolationError:
            logger.bind(
                organization_id=enriched_event.organization_id,
                calendar_event_id=enriched_event.id,
                meeting_id=existing_event.meeting_id if existing_event else None,
            ).info(
                "Unique violation during meeting creation/update - meeting already set up"
            )
            return None
        except Exception as e:
            logger.bind(
                organization_id=enriched_event.organization_id,
                calendar_event_id=enriched_event.id,
                meeting_id=existing_event.meeting_id if existing_event else None,
                exc_info=e,
            ).error("Failed to create/update meeting for calendar event")

            if retry_with_workflow:
                try:
                    client = await get_temporal_client()
                    await client.start_workflow(
                        "UserCalendarEventMeetingSyncWorkflow",
                        args=[
                            UserCalendarEventMeetingSyncWorkflowInput(
                                group_key=enriched_event.group_key,
                                organization_id=enriched_event.organization_id,
                            )
                        ],
                        id=f"meeting-sync-{enriched_event.group_key}",
                        task_queue=UserCalendarEventMeetingSyncWorkflow.get_task_queue(),
                        id_reuse_policy=WorkflowIDReusePolicy.TERMINATE_IF_RUNNING,
                        execution_timeout=timedelta(minutes=3),
                    )

                    logger.bind(
                        organization_id=enriched_event.organization_id,
                        calendar_event_id=enriched_event.id,
                        group_key=enriched_event.group_key,
                    ).info("Triggered workflow for syncing meeting state")
                except Exception as e:
                    logger.bind(
                        organization_id=enriched_event.organization_id,
                        calendar_event_id=enriched_event.id,
                        group_key=enriched_event.group_key,
                        exc_info=e,
                    ).error("Unable to trigger workflow for syncing meeting state")

            return existing_meeting_dto
        return meeting_dto

    async def _update_calendar_event(
        self,
        existing_event: UserCalendarEvent,
        enriched_event: UserCalendarEvent,
        user_integration_dto: UserIntegrationDto,
        user_calendar: UserCalendar,
        meeting_dto: MeetingDto | None,
    ) -> UserCalendarEvent:
        """Update an existing calendar event."""

        # Log the update action with bound context
        logger.bind(
            organization_id=enriched_event.organization_id,
            event_id=existing_event.id,
            initial_status=existing_event.status,
            enriched_status=enriched_event.status,
        ).info("Updating calendar event")

        meeting_id = meeting_dto.meeting.id if meeting_dto else None

        # Preserve existing fields that shouldn't be overwritten
        cal_event = enriched_event.model_copy(
            update={
                "id": existing_event.id,
                "created_at": existing_event.created_at,
                "event_schedule_id": existing_event.event_schedule_id,
                "conferencing_provider": existing_event.conferencing_provider,
                "conferencing_external_id": existing_event.conferencing_external_id,
                "conferencing_details": existing_event.conferencing_details,
                "buffer_info": existing_event.buffer_info,
                "meeting_id": meeting_id,
                "status": enriched_event.status,
                "deleted_at": (
                    zoned_utc_now()
                    if enriched_event.status == "cancelled"
                    and existing_event.deleted_at is None
                    else None
                ),
            }
        )

        # Log the updated status of the event with bound context
        logger.bind(
            organization_id=enriched_event.organization_id,
            event_id=cal_event.id,
            updated_status=cal_event.status,
        ).info("Updated calendar event")

        # Update in database
        updated_event = await self.user_cal_repo.update_instance(
            cal_event,
            exclude_deleted_or_archived=False,
        )

        try:
            if updated_event:
                await self.domain_crm_association_service.bulk_delete_domain_crm_associations_v2(
                    domain_crm_associations=_UserCalendarCrmAssociationMapper.map_user_calendar_event_to_associations_delete(
                        user_id=updated_event.user_id,
                        organization_id=updated_event.organization_id,
                        event_id=updated_event.id,
                    ),
                    deleted_by_user_id=updated_event.user_id,
                )

                # recreate associations if the event is not cancelled
                if updated_event.status != "cancelled":
                    await self.domain_crm_association_service.bulk_create_domain_crm_associations_v2(
                        domain_crm_associations=_UserCalendarCrmAssociationMapper.map_user_calendar_event_to_associations_create(
                            event=updated_event,
                        ),
                    )
        except Exception as e:
            logger.bind(
                organization_id=enriched_event.organization_id,
                calendar_event_id=enriched_event.id,
                exc_info=e,
            ).error("Failed to create domain CRM associations")

        if not updated_event:
            raise ValueError("Failed to update calendar event")

        # Handle buffer events if needed
        if (
            updated_event.event_schedule_id
            and updated_event.buffer_info
            and (
                existing_event.starts_at != updated_event.starts_at
                or existing_event.ends_at != updated_event.ends_at
            )
        ):
            await self.user_calendar_common_service.update_buffer_event(
                user_calendar_event=updated_event,
                calendar_external_id=user_calendar.external_id,
                grant_id=not_none(
                    user_integration_dto.user_integration_connector
                ).external_id,
                async_nylas_client=self.async_nylas_client,
            )

        # Create rsvp activity for user calendar event participant
        if (
            meeting_dto
            and updated_event
            and updated_event.organizer_user_id == user_integration_dto.user_id
        ):
            await self.user_calendar_common_service.create_activity_v2_for_user_calendar_event_participant(
                user_integration=user_integration_dto.user_integration,
                existing_event=existing_event,
                updated_event=updated_event,
                meeting_dto=meeting_dto,
            )

        return updated_event

    async def _create_calendar_event(
        self,
        enriched_event: UserCalendarEvent,
        user_integration_dto: UserIntegrationDto,
        user_calendar: UserCalendar,
        meeting_dto: MeetingDto | None,
    ) -> UserCalendarEvent:
        """Create a new calendar event."""
        meeting_id = meeting_dto.meeting.id if meeting_dto else None
        try:
            new_event = await self.user_cal_repo.insert(
                enriched_event.model_copy(
                    update={
                        "meeting_id": meeting_id,
                    }
                )
            )

            try:
                await self.domain_crm_association_service.bulk_create_domain_crm_associations_v2(
                    domain_crm_associations=_UserCalendarCrmAssociationMapper.map_user_calendar_event_to_associations_create(
                        event=new_event,
                    ),
                )
            except Exception as e:
                logger.bind(
                    organization_id=enriched_event.organization_id,
                    calendar_event_id=enriched_event.id,
                    exc_info=e,
                ).error("Failed to create domain CRM associations")

            return new_event

        except (UniqueViolationError, IntegrityError):
            # Handle race condition where event was created between our check and insert
            logger.info(
                "Constraint violation during event creation - event exists, updating instead",
                event_external_id=enriched_event.external_id,
            )
            existing_event = await self.user_cal_repo.find_user_calendar_event_by_user_calendar_id_and_external_id(
                user_calendar_id=enriched_event.user_calendar_id,
                external_id=enriched_event.external_id,
                organization_id=enriched_event.organization_id,
                exclude_deleted_or_archived=False,
            )
            if not existing_event:
                raise ValueError(
                    "Failed to find existing event after constraint violation"
                )

            return await self._update_calendar_event(
                existing_event=existing_event,
                enriched_event=enriched_event,
                user_integration_dto=user_integration_dto,
                meeting_dto=meeting_dto,
                user_calendar=user_calendar,
            )

    async def _only_sync_contact_from_nylas(
        self,
        *,
        nylas_events: list[NylasEvent],
        user_calendar: UserCalendar,
        user_integration_dto: UserIntegrationDto,
        organization_id: UUID,
    ) -> set[UUID]:
        contact_ids = set()
        for nylas_event in nylas_events:
            dirty_event = UserCalendarEvent.from_async_nylas(
                user_calendar=user_calendar,
                organizer_user_id=None,
                user_id=user_calendar.user_id,
                organization_id=organization_id,
                evt=nylas_event,
            )
            participants = (
                await self.user_calendar_common_service.map_calendar_event_participants(
                    cal_event=dirty_event,
                    organization_id=organization_id,
                    user_integration=user_integration_dto.user_integration,
                )
            )
            contact_ids.update(
                [
                    participant.contact_id
                    for participant in participants
                    if participant.contact_id
                ]
            )
        return contact_ids

    async def sync_user_calendar(
        self, user_integration_dto: UserIntegrationDto
    ) -> UserCalendar | None:
        if not user_integration_dto.user_integration_connector:
            raise IllegalStateError("No Nylas connector found for user integration")

        organization_id = user_integration_dto.user_integration.organization_id
        nylas_connector: UserIntegrationConnector = (
            user_integration_dto.user_integration_connector
        )
        if not user_integration_dto.user_integration.calendar_account_id:
            return None
        user_calendar: (
            UserCalendar | None
        ) = await self.user_cal_repo.find_user_primary_calendar_by_calendar_account(
            calendar_account_id=user_integration_dto.user_integration.calendar_account_id,
            organization_id=organization_id,
        )
        if not user_calendar:
            user_id = user_integration_dto.user_id
            nylas_cals = await self.async_nylas_client.list_calendars(
                grant_id=nylas_connector.external_id,
            )
            primary_nylas_cal = next(
                filter(lambda cal: cal.is_primary, nylas_cals.data), None
            )
            if not primary_nylas_cal:
                raise ResourceNotFoundError(
                    f"no primary calendar found for user id {user_id}"
                )

            if (
                primary_nylas_cal.id
                != user_integration_dto.user_integration.connected_email
            ):
                logger.error(
                    f"primary calendar ({primary_nylas_cal.id}) does not match calendar account email ({user_integration_dto.user_integration.connected_email})"
                )
            user_calendar = not_none(
                await self.user_cal_repo.upsert_user_calendar(
                    UserCalendar.from_nylas_calendar(
                        user_id=user_id,
                        user_integration_id=user_integration_dto.integration_id,
                        calendar_account_id=user_integration_dto.user_integration.calendar_account_id,
                        ncal=primary_nylas_cal,
                        organization_id=organization_id,
                    )
                )
            )
            logger.bind(
                user_id=user_id,
                organization_id=organization_id,
            ).info(f"Create a new user_calendar({user_calendar.id}) for user")
        return user_calendar


class SingleUserCalendarSyncService(Singleton, UserCalendarSyncService):
    pass


def get_user_calendar_sync_service_by_db_engine(
    db_engine: DatabaseEngine,
) -> UserCalendarSyncService:
    if SingleUserCalendarSyncService.has_instance():
        return SingleUserCalendarSyncService.get_singleton_instance()

    user_repo = UserRepository(engine=db_engine)
    user_integration_repo = UserIntegrationRepository(engine=db_engine)
    user_cal_repo = UserCalendarRepository(engine=db_engine)
    user_calendar_common_service = get_user_calendar_common_service_by_db_engine(
        db_engine=db_engine
    )
    async_nylas_client = AsyncNylasClient()
    meeting_service = meeting_service_factory_general(db_engine=db_engine)
    calendar_account_repo = CalendarAccountRepository(engine=db_engine)
    organization_service = get_organization_service_general(db_engine=db_engine)
    feature_flag_service = get_feature_flag_service()
    domain_crm_association_service = get_domain_crm_association_service(
        db_engine=db_engine
    )

    return SingleUserCalendarSyncService(
        user_repo=user_repo,
        user_integration_repo=user_integration_repo,
        user_cal_repo=user_cal_repo,
        user_calendar_common_service=user_calendar_common_service,
        async_nylas_client=async_nylas_client,
        meeting_service=meeting_service,
        calendar_account_repo=calendar_account_repo,
        organization_service=organization_service,
        feature_flag_service=feature_flag_service,
        domain_crm_association_service=domain_crm_association_service,
    )


def get_user_calendar_sync_service(request: Request) -> UserCalendarSyncService:
    db_engine = get_db_engine(request=request)
    return get_user_calendar_sync_service_by_db_engine(db_engine=db_engine)
