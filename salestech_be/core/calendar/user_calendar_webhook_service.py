from datetime import timed<PERSON><PERSON>

from fastapi import Request
from temporalio.common import WorkflowIDReusePolicy

from salestech_be.common.exception import IllegalStateError
from salestech_be.common.lifespan import get_db_engine
from salestech_be.common.singleton import Singleton
from salestech_be.core.calendar.user_calendar_common_service import (
    UserCalendarCommonService,
    get_user_calendar_common_service_by_db_engine,
)
from salestech_be.core.calendar.user_calendar_crm_association_mapper import (
    _UserCalendarCrmAssociationMapper,
)
from salestech_be.core.calendar.user_calendar_sync_service import (
    UserCalendarSyncService,
    get_user_calendar_sync_service_by_db_engine,
)
from salestech_be.core.contact.service.contact_query_service import (
    ContactQueryService,
    get_contact_query_service,
)
from salestech_be.core.domain_crm_association.domain_crm_association_service import (
    DomainCRMAssociationService,
    get_domain_crm_association_service,
)
from salestech_be.core.meeting.meeting_service import (
    MeetingService,
    meeting_service_factory_general,
)
from salestech_be.core.organization.service.organization_service import (
    OrganizationService,
    get_organization_service_general,
)
from salestech_be.db.dao.user_calendar_repository import UserCalendarRepository
from salestech_be.db.dao.user_integration_repository import UserIntegrationRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.meeting import MeetingCancelReason
from salestech_be.db.models.user_calendar import UserCalendar
from salestech_be.db.models.user_calendar_event import UserCalendarEvent
from salestech_be.db.models.user_integration import (
    IntegrationType,
)
from salestech_be.db.models.user_integration_connector import (
    IntegrationConnectorProvider,
)
from salestech_be.integrations.nylas.async_nylas_client import AsyncNylasClient
from salestech_be.integrations.nylas.model import NylasEvent
from salestech_be.integrations.temporal.client import get_temporal_client
from salestech_be.ree_logging import get_logger
from salestech_be.temporal.workflows.user_calendar.user_calendar_webhook_workflow import (
    UserCalendarWebhookWorkflow,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    UserCalendarWebhookWorkflowInput,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.util.time import zoned_utc_now
from salestech_be.util.validation import not_none
from salestech_be.web.api.webhook.schema import (
    NylasWebhookEventData,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    NylasWebhookRequest,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    NylasWebhookType,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)

logger = get_logger("user_calendar_webhook_service")


class UserCalendarWebhookService:
    def __init__(
        self,
        async_nylas_client: AsyncNylasClient,
        user_integration_repo: UserIntegrationRepository,
        user_cal_repo: UserCalendarRepository,
        meeting_service: MeetingService,
        user_calendar_common_service: UserCalendarCommonService,
        user_calendar_sync_service: UserCalendarSyncService,
        organization_service: OrganizationService,
        contact_query_service: ContactQueryService,
        domain_crm_association_service: DomainCRMAssociationService,
    ):
        self.async_nylas_client = async_nylas_client
        self.user_integration_repo = user_integration_repo
        self.user_cal_repo = user_cal_repo
        self.meeting_service = meeting_service
        self.user_calendar_common_service = user_calendar_common_service
        self.user_calendar_sync_service = user_calendar_sync_service
        self.organization_service = organization_service
        self.contact_query_service = contact_query_service
        self.domain_crm_association_service = domain_crm_association_service

    async def upsert_calendar_event_from_external(
        self,
        event_external_id: str,
        calendar_external_id: str,
        connection_external_id: str,
    ) -> list[UserCalendarEvent]:
        # Use contextualize to set common logging context
        with logger.contextualize(
            external_event_id=event_external_id,
            calendar_id=calendar_external_id,
            grant_id=connection_external_id,
        ):
            logger.info("upserting calendar events from external")
            result: list[UserCalendarEvent] = []

            user_integration_dtos = await self.user_integration_repo.find_user_integrations_by_connector_external_id(
                connector_external_id=connection_external_id,
                connector_provider=IntegrationConnectorProvider.NYLAS,
                integration_type=IntegrationType.CALENDAR,
            )

            if not user_integration_dtos:
                logger.warning(
                    "no existing nylas calendar user integrations found "
                    f"for connection_external_id={connection_external_id}",
                )
                return result

            nylas_event = await self.async_nylas_client.get_event(
                grant_id=connection_external_id,
                calendar_id=calendar_external_id,
                event_id=event_external_id,
            )
            logger.info(f"nylas event retrieved: {nylas_event}")

            if not nylas_event or not self._valid_nylas_event(nylas_event=nylas_event):
                return result

            for user_int in user_integration_dtos:
                user_calendar = await self.user_cal_repo.find_user_calendar_by_user_integration_id_and_external_id(
                    user_integration_id=user_int.integration_id,
                    external_id=nylas_event.calendar_id,
                )
                if not user_calendar:
                    logger.warning(
                        f"no calendar has been added to user integration {user_int}",
                        user_integration_id=user_int.integration_id,
                        nylas_event_calendar_id=nylas_event.calendar_id,
                    )
                    continue
                with logger.contextualize(
                    user_calendar_id=user_calendar.id,
                    organization_id=user_calendar.organization_id,
                ):
                    logger.info("Processing nylas calendar event from webhook")
                    sync_result = await self.user_calendar_sync_service.process_nylas_calendar_event(
                        nylas_event=nylas_event,
                        user_calendar=user_calendar,
                        user_integration_dto=user_int,
                        organization_id=user_calendar.organization_id,
                    )
                    if sync_result.calendar_event:
                        logger.info("Successfully processed calendar event")
                        result.append(sync_result.calendar_event)
                    else:
                        logger.warning(
                            "No calendar event was returned after processing"
                        )
            return result

    async def delete_calendar_event_from_external(
        self,
        event_external_id: str,
        calendar_external_id: str,
        connection_external_id: str,
    ) -> list[UserCalendarEvent]:
        results: list[UserCalendarEvent] = []

        # means the deleted event is invoked from our side, record is deleted already.
        if not calendar_external_id:
            logger.warning(
                "no calendar_id pass from nylas through webhook event.deleted "
                f"for event_external_id={event_external_id}",
            )
            return results

        user_integration_dtos = await self.user_integration_repo.find_user_integrations_by_connector_external_id(
            connector_external_id=connection_external_id,
            connector_provider=IntegrationConnectorProvider.NYLAS,
            integration_type=IntegrationType.CALENDAR,
        )

        if not user_integration_dtos:
            logger.warning(
                "no existing nylas calendar user integrations found "
                f"for connection_external_id={connection_external_id}",
            )
            return results

        nylas_event = await self.async_nylas_client.get_event(
            grant_id=connection_external_id,
            calendar_id=calendar_external_id,
            event_id=event_external_id,
        )

        if nylas_event is not None and not self._valid_nylas_event(
            nylas_event=nylas_event
        ):
            return results

        for user_int in user_integration_dtos:
            user_calendar = await self.user_cal_repo.find_user_calendar_by_user_integration_id_and_external_id(
                user_integration_id=user_int.integration_id,
                external_id=nylas_event.calendar_id
                if nylas_event
                else calendar_external_id,
            )
            if not user_calendar:
                logger.warning(
                    "No calendar has been added to user integration, skipping delete event",
                    user_integration_id=user_int.integration_id,
                    event_external_id=event_external_id,
                    calendar_external_id=calendar_external_id,
                    grant_id=connection_external_id,
                )
                continue
            if nylas_event and nylas_event.recurrence:
                logger.bind(
                    original_master_event_id=nylas_event.id,
                    user_calendar_id=user_calendar.id,
                    event_external_id=event_external_id,
                ).info("Skipping recurring event handling")
            else:
                cal_event_or_none = await self._delete_calendar_event_from_external(
                    user_calendar=user_calendar,
                    external_id=nylas_event.id if nylas_event else event_external_id,
                    nylas_event=nylas_event,
                    grant_id=connection_external_id,
                    calendar_external_id=calendar_external_id,
                )
                if cal_event_or_none:
                    results.append(cal_event_or_none)
        return results

    async def _delete_calendar_event_from_external(
        self,
        user_calendar: UserCalendar,
        external_id: str,
        nylas_event: NylasEvent | None,
        grant_id: str,
        calendar_external_id: str,
    ) -> UserCalendarEvent | None:
        existing_event = await self.user_cal_repo.find_user_calendar_event_by_user_calendar_id_and_external_id(
            user_calendar_id=user_calendar.id,
            external_id=external_id,
            organization_id=user_calendar.organization_id,
            exclude_deleted_or_archived=True,
        )

        if existing_event:
            # delete buffer events before marking the user_calendar_event as deleted
            if existing_event.buffer_info:
                await self.user_calendar_common_service.delete_buffer_events(
                    calendar_external_id=calendar_external_id,
                    user_calendar_event=existing_event,
                    grant_id=grant_id,
                    async_nylas_client=self.async_nylas_client,
                )

            if nylas_event:
                dirty_updated = UserCalendarEvent.from_async_nylas(
                    user_calendar=user_calendar,
                    organizer_user_id=existing_event.organizer_user_id,
                    user_id=existing_event.user_id,
                    organization_id=existing_event.organization_id,
                    evt=nylas_event,
                ).model_copy(
                    update={
                        "id": existing_event.id,
                        "created_at": existing_event.created_at,
                        "deleted_at": zoned_utc_now(),
                        "meeting_id": existing_event.meeting_id,
                        "event_schedule_id": existing_event.event_schedule_id,
                        "participants": existing_event.participants,
                        "conferencing_provider": existing_event.conferencing_provider,
                        "conferencing_external_id": existing_event.conferencing_external_id,
                        "conferencing_details": existing_event.conferencing_details,
                        "buffer_info": existing_event.buffer_info,
                    },
                )
            else:
                dirty_updated = existing_event.model_copy(
                    update={
                        "deleted_at": zoned_utc_now(),
                    },
                )

            deleted_cal_event = await self.user_cal_repo.update_instance(dirty_updated)
            if deleted_cal_event is None:
                logger.warning(
                    f"calendar event is not successfully deleted. deleted_cal_event: {deleted_cal_event}, existing_event: {existing_event}"
                )
                return None

            try:
                await self.domain_crm_association_service.bulk_delete_domain_crm_associations_v2(
                    domain_crm_associations=_UserCalendarCrmAssociationMapper.map_user_calendar_event_to_associations_delete(
                        user_id=deleted_cal_event.user_id,
                        organization_id=deleted_cal_event.organization_id,
                        event_id=deleted_cal_event.id,
                    ),
                    deleted_by_user_id=deleted_cal_event.user_id,
                )
            except Exception as e:
                logger.bind(
                    organization_id=deleted_cal_event.organization_id,
                    calendar_event_id=deleted_cal_event.id,
                    exc_info=e,
                ).error("Failed to delete domain CRM associations")

            if (
                deleted_cal_event.organizer_user_id == deleted_cal_event.user_id
                and deleted_cal_event.meeting_id
            ):
                try:
                    await self.meeting_service.cancel_meeting(
                        meeting_id=not_none(deleted_cal_event.meeting_id),
                        organization_id=deleted_cal_event.organization_id,
                        cancel_reason=MeetingCancelReason.SCHEDULED_EVENT_CANCELED,
                    )
                except IllegalStateError as e:
                    logger.info(e.additional_error_details)

            return deleted_cal_event
        else:
            return None

    @staticmethod
    def _valid_nylas_event(nylas_event: NylasEvent) -> bool:
        if not nylas_event.organizer:
            logger.warning(f"skip event without organizer: (id={nylas_event.id})")
            return False
        if not nylas_event.html_link:
            logger.warning(f"skip event without html link: (id={nylas_event})")
            return False
        return True

    async def handle_nylas_event(
        self,
        webhook_request: NylasWebhookRequest,
    ) -> None:
        obj = NylasWebhookEventData.model_validate(webhook_request.data).object
        grant_id: str = obj.grant_id
        calendar_id: str = obj.calendar_id
        external_event_id: str = obj.id

        # Use contextualize to set common logging context
        with logger.contextualize(
            webhook_request_id=webhook_request.id,
            external_event_id=external_event_id,
            calendar_id=calendar_id,
            grant_id=grant_id,
        ):
            logger.info("Received Nylas webhook event")
            is_recurrence_event = obj.recurrence is not None and (
                webhook_request.type
                in {
                    NylasWebhookType.EVENT_CREATED,
                    NylasWebhookType.EVENT_UPDATED,
                }
            )
            if is_recurrence_event:
                logger.info("Skipping recurring event syncing from nylas webhook.")
                return
            if (
                webhook_request.type == NylasWebhookType.EVENT_CREATED
                and obj.metadata is not None
                and obj.metadata["source"] == "scheduler"
            ):
                logger.info("Skip syncing scheduler created event to avoid conflicts")
                return

            user_calendar_event_input = UserCalendarWebhookWorkflowInput(
                type=NylasWebhookType(webhook_request.type),
                grant_id=grant_id,
                external_event_id=external_event_id,
                calendar_id=calendar_id,
            )
            client = await get_temporal_client()
            try:
                await client.start_workflow(
                    UserCalendarWebhookWorkflow.run,
                    args=[user_calendar_event_input],
                    id=f"nylas_user_calendar_event:{grant_id}::{external_event_id}",
                    task_queue=UserCalendarWebhookWorkflow.get_task_queue(),
                    id_reuse_policy=WorkflowIDReusePolicy.TERMINATE_IF_RUNNING,
                    execution_timeout=timedelta(minutes=8),
                )
            except Exception as e:
                logger.error(
                    "Failed to syncing through Nylas calendar webhook workflow",
                    exc_info=e,
                )
                raise


class SingletonUserCalendarWebhookService(Singleton, UserCalendarWebhookService):
    pass


def get_user_calendar_webhook_service(request: Request) -> UserCalendarWebhookService:
    db_engine = get_db_engine(request=request)
    return get_user_calendar_webhook_service_by_db_engine(db_engine)


def get_user_calendar_webhook_service_by_db_engine(
    db_engine: DatabaseEngine,
) -> UserCalendarWebhookService:
    if SingletonUserCalendarWebhookService.has_instance():
        return SingletonUserCalendarWebhookService.get_singleton_instance()

    user_integration_repo = UserIntegrationRepository(engine=db_engine)
    user_cal_repo = UserCalendarRepository(engine=db_engine)
    user_calendar_common_service = get_user_calendar_common_service_by_db_engine(
        db_engine=db_engine
    )
    async_nylas_client = AsyncNylasClient()
    meeting_service = meeting_service_factory_general(db_engine=db_engine)
    organization_service = get_organization_service_general(db_engine=db_engine)
    contact_query_service = get_contact_query_service(db_engine=db_engine)
    user_calendar_sync_service = get_user_calendar_sync_service_by_db_engine(
        db_engine=db_engine
    )
    domain_crm_association_service = get_domain_crm_association_service(
        db_engine=db_engine
    )
    return SingletonUserCalendarWebhookService(
        user_integration_repo=user_integration_repo,
        user_cal_repo=user_cal_repo,
        async_nylas_client=async_nylas_client,
        meeting_service=meeting_service,
        user_calendar_common_service=user_calendar_common_service,
        organization_service=organization_service,
        contact_query_service=contact_query_service,
        user_calendar_sync_service=user_calendar_sync_service,
        domain_crm_association_service=domain_crm_association_service,
    )
