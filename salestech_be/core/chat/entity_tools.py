from __future__ import annotations

import time
from uuid import UUID

from pydantic import BaseModel
from pydantic_ai import ModelRetry, RunContext

from salestech_be.common.exception import ResourceNotFoundError
from salestech_be.core.account.types_v2 import AccountV2
from salestech_be.core.chat.agent_deps import Chat<PERSON>gentDeps
from salestech_be.core.contact.types_v2 import ContactV2
from salestech_be.ree_logging import get_logger
from salestech_be.search.common.type import (
    DocumentType,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.search.search.service_api_schema import (
    SearchRequest,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)

logger = get_logger(__name__)


class ContactResult(BaseModel):
    """Result of a contact search or fetch operation."""

    id: UUID
    display_name: str
    first_name: str | None = None
    last_name: str | None = None
    primary_email: str | None = None
    primary_phone_number: str | None = None
    title: str | None = None
    department: str | None = None
    primary_account_id: UUID | None = None
    primary_account_name: str | None = None
    # Related account information (only populated in get_contact_by_id)
    related_account: AccountResult | None = None

    @classmethod
    def from_contact_v2(cls, contact: ContactV2) -> ContactResult:
        return cls(
            id=contact.id,
            display_name=contact.display_name,
            first_name=contact.first_name,
            last_name=contact.last_name,
            primary_email=contact.primary_email,
            primary_phone_number=str(contact.primary_phone_number)
            if contact.primary_phone_number
            else None,
            title=contact.title,
            department=contact.department,
            primary_account_id=contact.primary_account_id,
            primary_account_name=None,  # ContactV2 doesn't have primary_account_name
        )


class AccountResult(BaseModel):
    """Result of an account search or fetch operation."""

    id: UUID
    display_name: str
    status: str | None = None
    domain_name: str | None = None
    official_website: str | None = None
    description: str | None = None
    estimated_annual_revenue: float | None = None
    estimated_employee_count: int | None = None
    # Related contacts information (only populated in get_account_by_id)
    related_contacts: list[ContactResult] = []

    @classmethod
    def from_account_v2(cls, account: AccountV2) -> AccountResult:
        return cls(
            id=account.id,
            display_name=account.display_name,
            status=account.status.value if account.status else None,
            domain_name=account.domain_name,
            official_website=account.official_website,
            description=account.description,
            estimated_annual_revenue=float(account.estimated_annual_revenue)
            if account.estimated_annual_revenue
            else None,
            estimated_employee_count=account.estimated_employee_count,
        )


class ContactSearchResult(BaseModel):
    """Result of a contact search operation."""

    contacts: list[ContactResult]
    total_count: int


class AccountSearchResult(BaseModel):
    """Result of an account search operation."""

    accounts: list[AccountResult]
    total_count: int


async def search_contacts(
    ctx: RunContext[ChatAgentDeps], query: str, limit: int = 5
) -> ContactSearchResult:
    """Search for contacts using a keyword query.

    Args:
        ctx: The run context containing dependencies.
        query: Keyword query to search for contacts. Can include name, email, or other identifying information.
           Examples: "John Smith", "<EMAIL>"
        limit: Maximum number of contacts to return (default: 5).

    Returns:
        A ContactSearchResult containing:
           - contacts: List of ContactResult objects with fields like id, display_name, first_name,
             last_name, primary_email, primary_phone_number, title, department, etc.
           - total_count: Total number of matches before limiting.
    """
    await ctx.deps.increment_tool_call_count()
    logger.bind(query=query).info("Performing search on contacts")

    request = SearchRequest(
        query=query,
        filters=[],
        indexes=[DocumentType.CONTACT],
        size=limit,
        timeout_secs=30,
    )

    search_start = time.perf_counter()
    hits = await ctx.deps.search_service.search_v0(
        organization_id=ctx.deps.organization_id,
        search_request=request,
    )
    logger.info(f"search_contacts took {time.perf_counter() - search_start:.2f}s")

    return ContactSearchResult(
        contacts=[ContactResult(**hit.source) for hit in hits.hits],
        total_count=hits.total,
    )


async def get_contact_by_id(
    ctx: RunContext[ChatAgentDeps], contact_id: UUID
) -> ContactResult:
    """Fetch a contact by its UUID.
    This provides more complete contact information than search_contacts,
    especially regarding the related account. If the contact_id is invalid,
    appropriate errors will be raised.

    Args:
        ctx: The run context containing dependencies.
        contact_id: The UUID of the contact to fetch

    Returns:
        A ContactResult object containing detailed contact information including:
           - Basic contact fields (name, email, phone, title, etc.)
           - Primary account reference information
           - related_account: Full AccountResult object if the contact has a primary_account_id
    """
    await ctx.deps.increment_tool_call_count()

    try:
        contact = await ctx.deps.contact_service.get_contact_v2(
            contact_id=contact_id,
            organization_id=ctx.deps.organization_id,
        )
    except ResourceNotFoundError as e:
        raise ModelRetry(f"contact_id {contact_id} not found") from e

    contact_result = ContactResult.from_contact_v2(contact)

    # Fetch related account if the contact has a primary account
    if contact.primary_account_id:
        try:
            account = await ctx.deps.account_service.get_account_v2(
                account_id=contact.primary_account_id,
                organization_id=ctx.deps.organization_id,
            )
            contact_result.related_account = AccountResult.from_account_v2(account)
        except Exception as e:
            logger.warning(f"Failed to fetch related account: {e}")

    return contact_result


async def search_accounts(
    ctx: RunContext[ChatAgentDeps], query: str, limit: int = 5
) -> AccountSearchResult:
    """Search for accounts using a keyword query.

    Args:
        ctx: The run context containing dependencies.
        query: Keyword query to search for accounts. Can include company name, domain, or other identifying information.
           Examples: "Acme Corporation", "acme.com"
        limit: Maximum number of accounts to return (default: 5).

    Returns:
        An AccountSearchResult containing:
           - accounts: List of AccountResult objects with fields like id, display_name, status,
             domain_name, official_website, description, estimated_annual_revenue, etc.
           - total_count: Total number of matches before limiting.
    """
    await ctx.deps.increment_tool_call_count()
    logger.bind(query=query).info("Performing search on accounts")

    request = SearchRequest(
        query=query,
        filters=[],
        indexes=[DocumentType.ACCOUNT],
        size=limit,
        timeout_secs=30,
    )

    search_start = time.perf_counter()
    hits = await ctx.deps.search_service.search_v0(
        organization_id=ctx.deps.organization_id,
        search_request=request,
    )
    logger.info(f"search_accounts took {time.perf_counter() - search_start:.2f}s")

    return AccountSearchResult(
        accounts=[AccountResult(**hit.source) for hit in hits.hits],
        total_count=hits.total,
    )


async def get_account_by_id(
    ctx: RunContext[ChatAgentDeps], account_id: UUID
) -> AccountResult:
    """Fetch an account which is a Company by its UUID.
    This provides more complete account information than search_accounts,
    especially regarding related contacts. If the account_id is invalid,
    appropriate errors will be raised.

    Args:
        ctx: The run context containing dependencies.
        account_id: The UUID of the account to fetch

    Returns:
        An AccountResult object containing detailed account information including:
           - Basic account fields (name, status, domain, website, etc.)
           - related_contacts: List of ContactResult objects for contacts associated with this account
    """
    await ctx.deps.increment_tool_call_count()

    try:
        account = await ctx.deps.account_service.get_account_v2(
            account_id=account_id,
            organization_id=ctx.deps.organization_id,
        )
    except ResourceNotFoundError as e:
        raise ModelRetry(f"account_id {account_id} not found") from e

    account_result = AccountResult.from_account_v2(account)

    # Fetch related contacts for this account
    try:
        contacts_map = (
            await ctx.deps.contact_service.map_contact_v2_by_primary_account_ids(
                organization_id=ctx.deps.organization_id,
                account_ids={account.id},
            )
        )

        # Get the contacts for this account
        contacts = contacts_map.get(account.id, [])

        # Convert contacts to ContactResult objects
        for contact in contacts:
            contact_result = ContactResult.from_contact_v2(contact)
            account_result.related_contacts.append(contact_result)
    except Exception as e:
        logger.warning(f"Failed to fetch related contacts: {e}")

    return account_result
