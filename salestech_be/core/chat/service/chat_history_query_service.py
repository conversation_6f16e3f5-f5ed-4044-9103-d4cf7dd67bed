from collections.abc import Sequence
from uuid import UUID

from salestech_be.common.singleton import Singleton
from salestech_be.core.chat.types import Chat, ChatMessage
from salestech_be.core.data.types import StandardRecord
from salestech_be.core.data.util import (
    ObjectRecordFetchConditions,
    filter_record_sequence,
    sort_record_sequence,
)
from salestech_be.db.dao.chat_repository import ChatRepository, get_chat_repository
from salestech_be.db.dbengine.core import DatabaseEngine


class ChatHistoryQueryService:
    def __init__(self, chat_repo: ChatRepository) -> None:
        self.chat_repo = chat_repo

    async def list_chats(
        self,
        user_id: UUID,
        organization_id: UUID,
        fetch_conditions: ObjectRecordFetchConditions | None = None,
    ) -> list[StandardRecord[Chat]]:
        db_chats = await self.chat_repo.list_chats(user_id, organization_id)
        chats = [Chat.from_db(db_chat) for db_chat in db_chats]

        result: Sequence[StandardRecord[Chat]] = [
            StandardRecord[Chat](
                object_id=Chat.object_id,
                data=chat,
                requested_relationships=set(),
                related_records={},
            )
            for chat in chats
        ]

        if fetch_conditions and fetch_conditions.filter_spec:
            result = await filter_record_sequence(
                filter_spec=fetch_conditions.filter_spec,
                records=result,
            )

        if fetch_conditions and fetch_conditions.sorting_spec:
            result = await sort_record_sequence(
                sorting_spec=fetch_conditions.sorting_spec,
                records=result,
            )

        return list(result)

    async def list_chat_messages(
        self,
        chat_id: UUID,
        organization_id: UUID,
        user_id: UUID,
        fetch_conditions: ObjectRecordFetchConditions | None = None,
    ) -> list[StandardRecord[ChatMessage]]:
        message_dtos = await self.chat_repo.list_chat_messages(
            chat_id=chat_id, organization_id=organization_id, user_id=user_id
        )

        chat_messages = [ChatMessage.from_dto(dto) for dto in message_dtos]

        result: Sequence[StandardRecord[ChatMessage]] = [
            StandardRecord[ChatMessage](
                object_id=ChatMessage.object_id,
                data=chat_message,
                requested_relationships=set(),
                related_records={},
            )
            for chat_message in chat_messages
        ]

        if fetch_conditions and fetch_conditions.filter_spec:
            result = await filter_record_sequence(
                filter_spec=fetch_conditions.filter_spec,
                records=result,
            )

        if fetch_conditions and fetch_conditions.sorting_spec:
            result = await sort_record_sequence(
                sorting_spec=fetch_conditions.sorting_spec,
                records=result,
            )

        return list(result)


class SingletonChatHistoryQueryService(ChatHistoryQueryService, Singleton):
    pass


def get_chat_history_query_service(
    db_engine: DatabaseEngine,
) -> ChatHistoryQueryService:
    return SingletonChatHistoryQueryService(get_chat_repository(db_engine))
