from collections.abc import Sequence
from typing import Annotated
from uuid import UUID

from fastapi import Depends

from salestech_be.common.singleton import Singleton
from salestech_be.common.type.patch_request import UnsetAware
from salestech_be.core.citation.types import Citation
from salestech_be.db.dao.citation_repository import CitationRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.ree_logging import get_logger

logger = get_logger(__name__)


class CitationQueryService:
    def __init__(
        self,
        citation_repository: Annotated[CitationRepository, Depends()],
    ):
        self.citation_repository = citation_repository

    # @log_timing(logger=logger)
    async def list_citations(
        self,
        organization_id: UUID,
        only_include_citation_ids: UnsetAware[set[UUID]],
    ) -> Sequence[Citation]:
        """List citations for an organization.

        Args:
            organization_id: The organization ID
            only_include_citation_ids: Optional set of citation IDs to filter by

        Returns:
            Sequence of Citation domain models
        """
        db_citations = await self.citation_repository.list_citations(
            organization_id=organization_id,
            only_include_citation_ids=only_include_citation_ids,
        )
        return [Citation(**citation.model_dump()) for citation in db_citations]


class SingletonCitationQueryService(Singleton, CitationQueryService):
    pass


def get_citation_query_service(*, db_engine: DatabaseEngine) -> CitationQueryService:
    if SingletonCitationQueryService.has_instance():
        return SingletonCitationQueryService.get_singleton_instance()
    return SingletonCitationQueryService(
        citation_repository=CitationRepository(engine=db_engine),
    )
