from collections import defaultdict
from collections.abc import Sequence
from datetime import UTC, datetime
from uuid import UUID, uuid4

from salestech_be.common.singleton import Singleton
from salestech_be.db.dao.citation_repository import CitationRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.citation import (
    Citation,
    CitationForObjectType,
    CitationMetadata,
    CitationSourceType,
    EmailCitationMetadata,
)


class CitationService:
    """Service for managing citations."""

    def __init__(self, engine: DatabaseEngine):
        self.engine = engine
        self.citation_repository = CitationRepository(engine=engine)

    async def create_citation(
        self,
        organization_id: UUID,
        for_object_id: UUID,
        for_object_type: CitationForObjectType,
        source_type: CitationSourceType,
        source_id: UUID,
        created_by_user_id: UUID,
        metadata: CitationMetadata | None = None,
        field_name: str | None = None,
        field_item_id: UUID | None = None,
    ) -> Citation:
        """Create a new citation."""
        citation = Citation(
            id=uuid4(),
            organization_id=organization_id,
            for_object_id=for_object_id,
            for_object_type=for_object_type,
            source_type=source_type,
            source_id=source_id,
            metadata=metadata,
            created_at=datetime.now(UTC),
            created_by_user_id=created_by_user_id,
            field_name=field_name,
            field_item_id=field_item_id,
        )

        await self.citation_repository.insert(citation)
        return citation

    async def update_citation(
        self,
        citation_id: UUID,
        metadata: CitationMetadata,
    ) -> Citation:
        """Update a citation."""
        citation = await self.citation_repository.get_citation_by_pk(
            citation_id=citation_id,
        )
        # Create a new citation object with updated metadata
        updated_citation = Citation(
            id=citation.id,
            organization_id=citation.organization_id,
            for_object_id=citation.for_object_id,
            for_object_type=citation.for_object_type,
            source_type=citation.source_type,
            source_id=citation.source_id,
            metadata=metadata,
            created_at=citation.created_at,
            created_by_user_id=citation.created_by_user_id,
            field_name=citation.field_name,
            field_item_id=citation.field_item_id,
        )
        # Global Message ID is not updated
        if isinstance(citation.metadata, EmailCitationMetadata):
            updated_citation.metadata.global_message_id = (  # type: ignore
                citation.metadata.global_message_id
            )
        await self.citation_repository.upsert_pk(updated_citation)
        return updated_citation

    async def get_citations_for_object(
        self,
        organization_id: UUID,
        for_object_id: UUID,
        for_object_type: CitationForObjectType,
    ) -> Sequence[Citation]:
        """Get all citations for a given object."""
        return await self.citation_repository.list_citations_by_object(
            for_object_id=for_object_id,
            for_object_type=for_object_type,
            organization_id=organization_id,
        )

    async def get_citation(
        self,
        citation_id: UUID,
        organization_id: UUID,
    ) -> Citation:
        """Get a citation by its ID."""
        return await self.citation_repository.get_citation_by_id(
            citation_id=citation_id,
            organization_id=organization_id,
        )

    async def find_all_citations_by_task_ids(
        self,
        task_ids: list[UUID],
        organization_id: UUID,
    ) -> dict[UUID, list[Citation]]:
        """Get all citations for a list of task IDs.

        Args:
            task_ids: List of task IDs to get citations for
            organization_id: Organization ID

        Returns:
            Dict mapping task ID to list of citations
        """
        citations = await self.citation_repository.list_citations_by_object_ids(
            for_object_ids=task_ids,
            for_object_type=CitationForObjectType.TASK,
            organization_id=organization_id,
        )

        # Group citations by task ID
        task_citations: dict[UUID, list[Citation]] = defaultdict(list)
        for citation in citations:
            task_citations[citation.for_object_id].append(citation)

        return task_citations


class SingletonCitationService(Singleton, CitationService):
    pass


def get_citation_service(engine: DatabaseEngine) -> CitationService:
    """Get an instance of the citation service."""
    if SingletonCitationService.has_instance():
        return SingletonCitationService.get_singleton_instance()
    return SingletonCitationService(engine=engine)
