from typing import Annotated
from uuid import UUID

from salestech_be.common.schema_manager.std_object_field_identifier import (
    Citation<PERSON>ield,
    CitationForGiantTaskField,
    StdObjectIdentifiers,
)
from salestech_be.common.type.metadata.common import StandardObjectIdentifier
from salestech_be.common.type.metadata.field.field_indexable_config import (
    UniqueIndexableConfig,
)
from salestech_be.common.type.metadata.field.field_type_property import (
    DefaultEnumFieldProperty,
    NestedObjectFieldProperty,
    TimestampFieldProperty,
    UUIDFieldProperty,
)
from salestech_be.core.common.types import DomainModel, FieldMetadata
from salestech_be.db.models.citation import (
    CitationForObjectType,
    CitationMetadata,
    CitationSourceType,
)
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime


class Citation(DomainModel):
    """Domain model for citations"""

    object_id = StandardObjectIdentifier(object_name="citation")
    object_display_name = "Citation"
    field_name_provider = <PERSON>Field

    id: Annotated[
        UUID,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                index_config=UniqueIndexableConfig(is_indexed=True, is_unique=True),
                field_display_name="ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            )
        ),
    ]
    for_object_id: Annotated[
        UUID,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="For Object ID",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ]
    for_object_type: Annotated[
        CitationForObjectType,
        FieldMetadata(
            type_property=DefaultEnumFieldProperty(
                enum_class=CitationForObjectType,
                field_display_name="For Object Type",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ]
    source_type: Annotated[
        CitationSourceType,
        FieldMetadata(
            type_property=DefaultEnumFieldProperty(
                enum_class=CitationSourceType,
                field_display_name="Source Type",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ]
    source_id: Annotated[
        UUID,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Source ID",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ]
    metadata: Annotated[
        CitationMetadata | None,
        FieldMetadata(
            type_property=NestedObjectFieldProperty(
                object_identifier=object_id,
                field_display_name="Metadata",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ] = None
    organization_id: Annotated[
        UUID,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Organization ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            )
        ),
    ]
    created_at: Annotated[
        ZoneRequiredDateTime,
        FieldMetadata(
            type_property=TimestampFieldProperty(
                field_display_name="Created At",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ]
    created_by_user_id: Annotated[
        UUID,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Created By User ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            )
        ),
    ]
    deleted_at: Annotated[
        ZoneRequiredDateTime | None,
        FieldMetadata(
            type_property=TimestampFieldProperty(
                field_display_name="Deleted At",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ] = None
    deleted_by_user_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Deleted By User ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            )
        ),
    ] = None


class CitationForGiantTask(DomainModel):
    """Domain model for Citation references in Giant Tasks."""

    object_id = StdObjectIdentifiers.citation_for_giant_task.identifier
    object_display_name = "Citation For Giant Task"
    field_name_provider = CitationForGiantTaskField

    inbound_relationships = ()
    outbound_relationships = ()

    id: Annotated[
        UUID,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            )
        ),
    ]

    organization_id: Annotated[
        UUID,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Organization ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            )
        ),
    ]

    source_id: Annotated[
        UUID,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Source ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            )
        ),
    ]

    source_type: Annotated[
        CitationSourceType,
        FieldMetadata(
            type_property=DefaultEnumFieldProperty(
                enum_class=CitationSourceType,
                field_display_name="Source Type",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ]

    metadata: Annotated[
        CitationMetadata | None,
        FieldMetadata(
            type_property=NestedObjectFieldProperty(
                object_identifier=StdObjectIdentifiers.citation.identifier,
                field_display_name="Metadata",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ] = None
