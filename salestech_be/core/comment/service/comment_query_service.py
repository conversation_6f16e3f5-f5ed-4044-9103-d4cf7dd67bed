from uuid import UUID

from fastapi import Request

from salestech_be.common.lifespan import get_db_engine
from salestech_be.common.singleton import Singleton
from salestech_be.common.type.patch_request import UNSET, UnsetAware, specified
from salestech_be.core.comment.types import Comment
from salestech_be.db.dao.comment_repository import CommentRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.comment import Comment as DbComment
from salestech_be.ree_logging import get_logger

logger = get_logger(__name__)


class CommentQueryService:
    def __init__(
        self,
        comment_repository: CommentRepository,
    ):
        self.comment_repository = comment_repository

    async def get_comment_by_id(
        self, organization_id: UUID, comment_id: UUID
    ) -> Comment | None:
        db_comment = await self.comment_repository.find_by_tenanted_primary_key(
            table_model=DbComment,
            organization_id=organization_id,
            id=comment_id,
        )
        if not db_comment:
            return None
        return Comment.map_from_db(db_comment=db_comment)

    async def list_comments(
        self,
        organization_id: UUID,
        only_include_comment_ids: UnsetAware[set[UUID]] = UNSET,
    ) -> list[Comment]:
        if specified(only_include_comment_ids):
            db_comments = await self.comment_repository.find_all_comments_by_ids_and_organization_id(
                comment_ids=only_include_comment_ids,
                organization_id=organization_id,
            )
        else:
            db_comments = (
                await self.comment_repository.find_all_comments_by_organization_id(
                    organization_id=organization_id,
                )
            )

        return [
            Comment.map_from_db(db_comment=db_comment) for db_comment in db_comments
        ]

    async def find_all_comments_by_reference_ids(
        self, organization_id: UUID, reference_ids: list[UUID], reference_id_type: str
    ) -> dict[UUID, list[Comment]]:
        db_comments = await self.comment_repository.find_all_comments_by_reference_ids(
            organization_id=organization_id,
            reference_ids=reference_ids,
            reference_id_type=reference_id_type,
        )
        return {
            reference_id: [
                Comment.map_from_db(db_comment=db_comment) for db_comment in db_comments
            ]
            for reference_id, db_comments in db_comments.items()
        }


class SingletonCommentQueryService(Singleton, CommentQueryService):
    pass


def get_comment_query_service_from_engine(
    db_engine: DatabaseEngine,
) -> CommentQueryService:
    if SingletonCommentQueryService.has_instance():
        return SingletonCommentQueryService.get_singleton_instance()
    return SingletonCommentQueryService(
        comment_repository=CommentRepository(
            engine=db_engine,
        )
    )


def get_comment_query_service(request: Request) -> CommentQueryService:
    return get_comment_query_service_from_engine(db_engine=get_db_engine(request))
