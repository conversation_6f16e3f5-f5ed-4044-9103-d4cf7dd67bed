from uuid import UUID, uuid4

from fastapi import Request

from salestech_be.common.exception.exception import (
    ResourceNotFoundError,
)
from salestech_be.common.lifespan import get_db_engine
from salestech_be.common.singleton import Singleton
from salestech_be.core.comment.service.comment_query_service import (
    CommentQueryService,
    get_comment_query_service_from_engine,
)
from salestech_be.core.comment.types import Comment
from salestech_be.core.meeting.service.meeting_query_service import (
    MeetingQueryService,
    get_meeting_query_service,
)
from salestech_be.core.notification.service.notification_service import (
    NotificationService,
    get_notification_service_by_db_engine,
)
from salestech_be.core.notification.types import SendNotificationRequest
from salestech_be.core.task.service.task_query_service import (
    TaskQueryService,
    get_task_query_service_from_engine,
)
from salestech_be.db.dao.comment_repository import CommentRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.comment import Comment as DbComment
from salestech_be.db.models.comment import (
    CommentReferenceIdType,
    MeetingCommentMetadata,
)
from salestech_be.db.models.notification import (
    NotificationDataType,
    NotificationMeetingCommentData,
    NotificationReferenceIdType,
    NotificationTaskCommentData,
)
from salestech_be.ree_logging import get_logger
from salestech_be.util.time import zoned_utc_now
from salestech_be.util.validation import not_none
from salestech_be.web.api.comment.schema import (
    CreateCommentRequest,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    PatchCommentRequest,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.web.api.common.container import (
    DeleteEntityResponse,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)

logger = get_logger(__name__)


class CommentService:
    def __init__(
        self,
        comment_query_service: CommentQueryService,
        comment_repository: CommentRepository,
        task_query_service: TaskQueryService,
        meeting_query_service: MeetingQueryService,
        notification_service: NotificationService,
    ):
        self.comment_query_service = comment_query_service
        self.comment_repository = comment_repository
        self.task_query_service = task_query_service
        self.meeting_query_service = meeting_query_service
        self.notification_service = notification_service

    async def create_comment(
        self,
        organization_id: UUID,
        user_id: UUID,
        request: CreateCommentRequest,
    ) -> Comment:
        logger.bind(
            organization_id=organization_id,
            user_id=user_id,
            request=request,
        ).info("Create comment request")

        db_comment = await self.comment_repository.insert(
            instance=DbComment(
                id=uuid4(),
                reference_id=request.reference_id,
                reference_id_type=request.reference_id_type.name,
                comment_html=request.comment_html,
                organization_id=organization_id,
                created_at=zoned_utc_now(),
                created_by_user_id=user_id,
                updated_at=None,
                updated_by_user_id=None,
                deleted_at=None,
                deleted_by_user_id=None,
                attachment_ids=request.attachment_ids,
                mentioned_user_ids=request.mentioned_user_ids,
                metadata=MeetingCommentMetadata(
                    start_time=request.metadata.start_time,
                    end_time=request.metadata.end_time,
                )
                if request.metadata
                else None,
                parent_comment_id=request.parent_comment_id,
            )
        )

        comment = Comment.map_from_db(db_comment=db_comment)

        # Send notifications for mentioned users
        if request.mentioned_user_ids:
            await self.send_mention_notifications(
                organization_id=organization_id,
                user_id=user_id,
                comment=comment,
            )

        return comment

    async def patch_comment(
        self,
        organization_id: UUID,
        user_id: UUID,
        comment_id: UUID,
        request: PatchCommentRequest,
    ) -> Comment:
        logger.bind(
            organization_id=organization_id,
            user_id=user_id,
            request=request,
            comment_id=comment_id,
        ).info("Patch comment request")

        comment_fields_to_update = request.model_dump(
            include={
                "comment_html",
                "attachment_ids",
                "mentioned_user_ids",
            }
        )
        if comment_fields_to_update:
            comment_fields_to_update["updated_at"] = zoned_utc_now()
            comment_fields_to_update["updated_by_user_id"] = user_id

        found_comment = await self.comment_repository.find_by_tenanted_primary_key(
            table_model=DbComment,
            organization_id=organization_id,
            id=comment_id,
        )
        if not found_comment:
            raise ResourceNotFoundError

        db_comment = await self.comment_repository.update_by_tenanted_primary_key(
            table_model=DbComment,
            organization_id=organization_id,
            primary_key_to_value={"id": comment_id},
            column_to_update=comment_fields_to_update,
        )

        if not db_comment:
            raise ResourceNotFoundError

        patched_comment = Comment.map_from_db(db_comment=db_comment)

        # Send notifications for mentioned users
        if request.mentioned_user_ids:
            await self.send_mention_notifications(
                organization_id=organization_id,
                user_id=user_id,
                comment=patched_comment,
            )
        return patched_comment

    async def delete_comment(
        self,
        organization_id: UUID,
        user_id: UUID,
        comment_id: UUID,
    ) -> DeleteEntityResponse:
        logger.bind(
            organization_id=organization_id,
            comment_id=comment_id,
        ).info(f"Delete comment {comment_id}")

        found_comment = await self.comment_repository.find_by_tenanted_primary_key(
            table_model=DbComment,
            id=comment_id,
            organization_id=organization_id,
        )

        if not found_comment:
            raise ResourceNotFoundError

        deleted_comment = await self.comment_repository.update_by_tenanted_primary_key(
            table_model=DbComment,
            organization_id=organization_id,
            primary_key_to_value={"id": comment_id},
            column_to_update={
                "deleted_by_user_id": user_id,
                "deleted_at": zoned_utc_now(),
            },
        )

        if not deleted_comment:
            raise ResourceNotFoundError

        logger.bind(
            comment_id=comment_id,
            organization_id=organization_id,
        ).info(f"Deleted comment {comment_id}")
        return DeleteEntityResponse(
            id=comment_id,
            deleted_at=not_none(deleted_comment.deleted_at),
            deleted_by_user_id=deleted_comment.deleted_by_user_id,
        )

    async def send_mention_notifications(
        self,
        organization_id: UUID,
        user_id: UUID,
        comment: Comment,
    ) -> None:
        """Send notifications to users mentioned in a comment."""
        if not comment.mentioned_user_ids:
            return

        logger.bind(
            organization_id=organization_id,
            actor_user_id=user_id,
            comment_id=comment.id,
            mentioned_user_ids=comment.mentioned_user_ids,
        ).info("Sending mention notifications")

        try:
            notification_data: NotificationDataType | None = None
            if comment.reference_id_type == CommentReferenceIdType.TASK:
                task = await self.task_query_service.get_by_id_v2(
                    comment.reference_id, organization_id
                )
                notification_data = NotificationTaskCommentData(
                    task_name=task.title, task_page=f"/tasks?taskId={task.id}"
                )
                reference_id = task.id
                reference_id_type = NotificationReferenceIdType.TASK
            elif comment.reference_id_type == CommentReferenceIdType.MEETING:
                meeting = await self.meeting_query_service.get_meeting_v2(
                    comment.reference_id, organization_id
                )
                if not meeting:
                    return
                notification_data = NotificationMeetingCommentData(
                    meeting_title=meeting.title, meeting_page=f"/meetings/{meeting.id}"
                )
                reference_id = meeting.id
                reference_id_type = NotificationReferenceIdType.MEETING
            else:
                return
            await self.notification_service.send_notification(
                send_notification_request=SendNotificationRequest(
                    data=notification_data,
                    reference_id=str(reference_id),
                    reference_id_type=reference_id_type,
                    activity_id=None,
                    actor_user_id=user_id,
                    recipient_user_ids=comment.mentioned_user_ids,
                    idempotency_key=f"comment-mention-{comment.id}-{comment.updated_at}",
                ),
                organization_id=organization_id,
            )

        except Exception as e:
            logger.error(
                "Failed to send mention notifications",
                exc_info=e,
                organization_id=organization_id,
                actor_user_id=user_id,
                comment_id=comment.id,
                mentioned_user_ids=comment.mentioned_user_ids,
            )


class SingletonCommentService(Singleton, CommentService):
    pass


def get_comment_service_from_engine(db_engine: DatabaseEngine) -> CommentService:
    if SingletonCommentService.has_instance():
        return SingletonCommentService.get_singleton_instance()
    return SingletonCommentService(
        comment_query_service=get_comment_query_service_from_engine(
            db_engine=db_engine
        ),
        comment_repository=CommentRepository(engine=db_engine),
        task_query_service=get_task_query_service_from_engine(db_engine),
        meeting_query_service=get_meeting_query_service(db_engine),
        notification_service=get_notification_service_by_db_engine(db_engine),
    )


def get_comment_service(request: Request) -> CommentService:
    return get_comment_service_from_engine(db_engine=get_db_engine(request))
