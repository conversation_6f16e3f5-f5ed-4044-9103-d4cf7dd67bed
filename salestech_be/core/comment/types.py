from __future__ import annotations

from typing import Annotated
from uuid import UUID

from salestech_be.common.schema_manager.std_object_field_identifier import (
    CommentField,
    MeetingField,
    OrganizationUserField,
    StdObjectIdentifiers,
    TaskField,
)
from salestech_be.common.schema_manager.std_object_relationship import (
    CommentRelationship,
)
from salestech_be.common.type.metadata.field.field_indexable_config import (
    UniqueIndexableConfig,
)
from salestech_be.common.type.metadata.field.field_type_property import (
    DefaultEnumFieldProperty,
    Dict<PERSON>ieldProperty,
    ListFieldProperty,
    LongTextAreaFieldProperty,
    TimestampFieldProperty,
    UUIDFieldProperty,
)
from salestech_be.common.type.metadata.schema import OutboundRelationship
from salestech_be.core.common.types import DomainModel, FieldMetadata
from salestech_be.db.models.comment import Comment as DbComment
from salestech_be.db.models.comment import (
    CommentReferenceIdType,
    MeetingCommentMetadata,
)
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime


class Comment(DomainModel):
    object_id = StdObjectIdentifiers.comment.identifier
    object_display_name = "Comment"
    field_name_provider = CommentField

    outbound_relationships = (
        OutboundRelationship(
            relation_type=OutboundRelationship.RelationType.LOOKUP,
            id=CommentRelationship.comment__to__created_by_user,
            relationship_name="Created By User",
            self_object_identifier=StdObjectIdentifiers.comment.identifier,
            related_object_identifier=StdObjectIdentifiers.user.identifier,
            self_cardinality=OutboundRelationship.Cardinality.MANY,
            related_object_cardinality=OutboundRelationship.Cardinality.ONE,
            ordered_self_field_identifiers=(
                CommentField.created_by_user_id.identifier,
            ),
            ordered_related_field_identifiers=(OrganizationUserField.id.identifier,),
        ),
        OutboundRelationship(
            relation_type=OutboundRelationship.RelationType.LOOKUP,
            id=CommentRelationship.comment__to__task,
            relationship_name="Task",
            self_object_identifier=StdObjectIdentifiers.comment.identifier,
            related_object_identifier=StdObjectIdentifiers.task.identifier,
            self_cardinality=OutboundRelationship.Cardinality.MANY,
            related_object_cardinality=OutboundRelationship.Cardinality.ONE,
            ordered_self_field_identifiers=(CommentField.reference_id.identifier,),
            ordered_related_field_identifiers=(TaskField.id.identifier,),
        ),
        OutboundRelationship(
            relation_type=OutboundRelationship.RelationType.LOOKUP,
            id=CommentRelationship.comment__to__meeting,
            relationship_name="Meeting",
            self_object_identifier=StdObjectIdentifiers.comment.identifier,
            related_object_identifier=StdObjectIdentifiers.meeting.identifier,
            self_cardinality=OutboundRelationship.Cardinality.MANY,
            related_object_cardinality=OutboundRelationship.Cardinality.ONE,
            ordered_self_field_identifiers=(CommentField.reference_id.identifier,),
            ordered_related_field_identifiers=(MeetingField.id.identifier,),
        ),
    )

    id: Annotated[
        UUID,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="ID",
                is_ui_displayable=False,
                is_ui_editable=False,
                index_config=UniqueIndexableConfig(is_indexed=True, is_unique=True),
            ),
        ),
    ]

    organization_id: Annotated[
        UUID,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Organization ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            ),
        ),
    ]

    comment_html: Annotated[
        str,
        FieldMetadata(
            type_property=LongTextAreaFieldProperty(
                field_display_name="Comment",
                is_ui_displayable=True,
                is_ui_editable=True,
            ),
        ),
    ]

    attachment_ids: Annotated[
        list[UUID] | None,
        FieldMetadata(
            type_property=ListFieldProperty(
                element_field_type_property=UUIDFieldProperty(
                    field_display_name="Attachment IDs",
                ),
                field_display_name="Attachments",
                is_ui_displayable=False,
                is_ui_editable=True,
            ),
        ),
    ] = None

    created_at: Annotated[
        ZoneRequiredDateTime,
        FieldMetadata(
            type_property=TimestampFieldProperty(
                field_display_name="Created At",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ]

    created_by_user_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Created By User ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            ),
        ),
    ]

    updated_at: Annotated[
        ZoneRequiredDateTime | None,
        FieldMetadata(
            type_property=TimestampFieldProperty(
                field_display_name="Updated At",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ] = None

    updated_by_user_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Updated By User ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            ),
        ),
    ] = None

    deleted_at: Annotated[
        ZoneRequiredDateTime | None,
        FieldMetadata(
            type_property=TimestampFieldProperty(
                field_display_name="Deleted At",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ] = None

    deleted_by_user_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Deleted By User ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            ),
        ),
    ] = None

    mentioned_user_ids: Annotated[
        list[UUID] | None,
        FieldMetadata(
            type_property=ListFieldProperty(
                element_field_type_property=UUIDFieldProperty(
                    field_display_name="Mentioned User IDs",
                ),
                field_display_name="Mentioned Users",
                is_ui_displayable=False,
                is_ui_editable=True,
            ),
        ),
    ] = None

    reference_id: Annotated[
        UUID,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Reference ID",
                is_ui_displayable=False,
                is_ui_editable=False,
                index_config=UniqueIndexableConfig(is_indexed=True, is_unique=False),
            ),
        ),
    ]

    reference_id_type: Annotated[
        CommentReferenceIdType,
        FieldMetadata(
            type_property=DefaultEnumFieldProperty(
                field_display_name="Reference Type",
                enum_class=CommentReferenceIdType,
                is_ui_displayable=False,
                is_ui_editable=False,
            ),
        ),
    ]

    metadata: Annotated[
        MeetingCommentMetadata | None,
        FieldMetadata(
            type_property=DictFieldProperty(
                field_display_name="Metadata",
                is_ui_displayable=False,
                is_ui_editable=False,
                value_field_type_property=TimestampFieldProperty(
                    field_display_name="Value",
                    is_ui_displayable=False,
                    is_ui_editable=False,
                ),
            ),
        ),
    ]

    parent_comment_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Parent ID",
            ),
        ),
    ]

    @classmethod
    def map_from_db(
        cls,
        db_comment: DbComment,
    ) -> Comment:
        return Comment(
            id=db_comment.id,
            reference_id=db_comment.reference_id,
            reference_id_type=CommentReferenceIdType(db_comment.reference_id_type),
            comment_html=db_comment.comment_html,
            organization_id=db_comment.organization_id,
            created_at=db_comment.created_at,
            created_by_user_id=db_comment.created_by_user_id,
            updated_at=db_comment.updated_at,
            updated_by_user_id=db_comment.updated_by_user_id,
            deleted_at=db_comment.deleted_at,
            deleted_by_user_id=db_comment.deleted_by_user_id,
            attachment_ids=db_comment.attachment_ids,
            mentioned_user_ids=db_comment.mentioned_user_ids,
            metadata=db_comment.metadata,
            parent_comment_id=db_comment.parent_comment_id,
        )
