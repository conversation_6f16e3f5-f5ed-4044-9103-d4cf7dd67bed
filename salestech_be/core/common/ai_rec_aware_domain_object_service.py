from abc import ABC, abstractmethod
from asyncio import TaskGroup
from collections.abc import Mapping
from typing import Annotated, Any
from uuid import UUID, uuid4

from fastapi import Depends
from pydantic import BaseModel

from salestech_be.common.exception import InvalidArgumentError, ResourceNotFoundError
from salestech_be.common.schema_manager.std_object_field_identifier import (
    StdObjectIdentifiers,
)
from salestech_be.common.type.metadata.field.field_type_property import (
    Dict<PERSON>ieldProperty,
    ListFieldProperty,
)
from salestech_be.common.type.metadata.field.field_value import (
    BaseSingularFieldValue,
    DictFieldValue,
    FieldValue,
    ListFieldValue,
)
from salestech_be.common.type.shape_constrained_model import ShapeConstrainedModel
from salestech_be.core.common.property_metadata import AIRec, PropertyMetadata
from salestech_be.core.common.service_api_patch_spec import (
    BaseAcceptAIRecPatchSpec,
    PatchStandardObjectBySourceRequest,
    RejectCurrentAIRecPatchSpec,
)
from salestech_be.core.common.types import DomainModel
from salestech_be.core.opportunity_stage_criteria.criteria_types import CriteriaCitation
from salestech_be.db.dao.crm_ai_rec_repository import CrmAIRecRepository
from salestech_be.db.models.crm_ai_rec import (
    CrmAIRecType,
    CrmObjectAiRec,
    CrmObjectAiRecCreate,
    CrmPropertyAiRec,
    CrmPropertyAiRecCreate,
    CrmPropertyMetadata,
    ParentRecordIds,
)
from salestech_be.ree_logging import get_logger
from salestech_be.util.time import zoned_utc_now
from salestech_be.util.validation import not_none

logger = get_logger()


class BaseAIRecAwareDomainObjectService[DT: DomainModel](ABC):
    def __init__(self, crm_ai_rec_repository: Annotated[CrmAIRecRepository, Depends()]):
        self.crm_ai_rec_repository = crm_ai_rec_repository

    @classmethod
    @abstractmethod
    def domain_model_type(cls) -> type[DT]:
        raise NotImplementedError

    @classmethod
    @abstractmethod
    def record_id(cls, record: DT) -> UUID:
        raise NotImplementedError

    @classmethod
    def to_field_value_by_field_name[DomainModelT: DomainModel](
        cls, shaped_constrained_model: ShapeConstrainedModel[DomainModelT]
    ) -> Mapping[str, FieldValue]:
        constraint_model_type: type[DomainModelT] = (
            shaped_constrained_model.constraint()
        )
        specified_fields = shaped_constrained_model.flatten_specified_values()
        result: dict[str, FieldValue] = {}
        for field_name, field_value in specified_fields.items():
            if field_name not in constraint_model_type.field_metadata_by_name:
                continue
            field_metadata = constraint_model_type.field_metadata_by_name[
                constraint_model_type.field_name_provider(field_name)
            ]
            field_type_property = field_metadata.type_property
            field_value_binding = field_type_property.field_value_type()
            field_value_type = field_value_binding.field_value_type
            if issubclass(field_value_type, BaseSingularFieldValue):
                result[field_name] = field_value_type.from_generic_value(field_value)
            elif issubclass(field_value_type, ListFieldValue) and isinstance(
                field_type_property, ListFieldProperty
            ):
                result[field_name] = field_value_type.from_generic_value(
                    field_value,
                    value_field_type=field_type_property.element_field_type_property.field_type,
                )
            elif issubclass(field_value_type, DictFieldValue) and isinstance(
                field_type_property, DictFieldProperty
            ):
                result[field_name] = field_value_type.from_generic_value(
                    field_value,
                    value_field_type=field_type_property.value_field_type_property.field_type,
                )
            else:
                raise NotImplementedError(
                    f"Field type {field_type_property} not supported"
                )
        return result

    @classmethod
    def std_object_identifier(cls) -> StdObjectIdentifiers:
        return StdObjectIdentifiers(cls.domain_model_type().object_id.object_name)

    async def reject_property_ai_recs(
        self,
        *,
        organization_id: UUID,
        user_id: UUID,
        ai_rec_ids: set[UUID],
        record_id: UUID,
    ) -> Mapping[UUID, CrmPropertyAiRec]:
        """
        Reject AI recs for a given record.
        """
        return await self.crm_ai_rec_repository.reject_crm_property_ai_rec(
            organization_id=organization_id,
            user_id=user_id,
            ai_rec_ids=ai_rec_ids,
            sobject_name=self.std_object_identifier(),
            record_id=record_id,
        )

    async def reject_object_ai_recs(
        self,
        *,
        organization_id: UUID,
        user_id: UUID,
        ai_rec_ids: set[UUID],
    ) -> Mapping[UUID, CrmObjectAiRec]:
        """
        Reject AI recs for a given record.
        """
        return await self.crm_ai_rec_repository.reject_crm_object_ai_rec(
            organization_id=organization_id,
            user_id=user_id,
            ai_rec_ids=ai_rec_ids,
            sobject_name=self.std_object_identifier(),
        )

    async def find_property_metadata_for_record(
        self,
        sobject_name: StdObjectIdentifiers,
        record_ids: set[UUID],
        organization_id: UUID,
    ) -> Mapping[UUID, list[PropertyMetadata]]:
        """
        Finds the property metadata for given record IDs.
        Return a mapping of record ID to list of property metadata.
        """
        # todo: build a bulk interface to fetch across different sobject names
        async with TaskGroup() as tg:
            db_prop_metadata_map_task = tg.create_task(
                self.crm_ai_rec_repository.map_crm_property_metadata_by_record_and_field_path(
                    organization_id=organization_id,
                    sobject_name=sobject_name,
                    record_ids=record_ids,
                )
            )
            db_ai_recs_map_task = tg.create_task(
                self.crm_ai_rec_repository.map_latest_crm_property_ai_rec_by_record_and_field_path(
                    organization_id=organization_id,
                    sobject_name=sobject_name,
                    record_ids=record_ids,
                )
            )

        db_prop_metadata_map: Mapping[UUID, Mapping[str, CrmPropertyMetadata]] = (
            db_prop_metadata_map_task.result()
        )

        db_ai_recs_map: Mapping[UUID, Mapping[str, CrmPropertyAiRec]] = (
            db_ai_recs_map_task.result()
        )

        result: dict[UUID, list[PropertyMetadata]] = {}

        for record_id in record_ids:
            db_prop_metadata = db_prop_metadata_map.get(record_id, {})
            db_ai_recs = db_ai_recs_map.get(record_id, {})
            field_paths = set(db_prop_metadata.keys()).union(db_ai_recs.keys())
            if not field_paths:
                continue
            ordered_field_paths = sorted(field_paths)
            property_metadata: list[PropertyMetadata] = []
            for field_path in ordered_field_paths:
                db_prop_metadata_for_field_path: CrmPropertyMetadata | None = (
                    db_prop_metadata.get(field_path)
                )
                db_ai_rec_for_field_path: CrmPropertyAiRec | None = db_ai_recs.get(
                    field_path
                )
                if not db_prop_metadata_for_field_path and (
                    not db_ai_rec_for_field_path
                ):
                    continue
                field_path_tuple = (
                    db_ai_rec_for_field_path.sobject_field_path_tuple
                    if db_ai_rec_for_field_path
                    else not_none(
                        db_prop_metadata_for_field_path
                    ).sobject_field_path_tuple
                )
                property_metadata.append(
                    PropertyMetadata(
                        field_path=field_path_tuple,
                        last_updated_source_ai_rec_id=db_prop_metadata_for_field_path.field_last_updated_by_ai_rec_id
                        if db_prop_metadata_for_field_path
                        else None,
                        citation_ids=db_prop_metadata_for_field_path.citation_ids
                        if db_prop_metadata_for_field_path
                        else None,
                        ai_rec=AIRec(
                            id=db_ai_rec_for_field_path.id,
                            rec_value=db_ai_rec_for_field_path.rec_value.to_generic_value()
                            if db_ai_rec_for_field_path.rec_value
                            else None,
                            rec_type=db_ai_rec_for_field_path.rec_type,
                            citation_ids=db_ai_rec_for_field_path.rec_citation_ids,
                            created_at=db_ai_rec_for_field_path.created_at,
                        )
                        if db_ai_rec_for_field_path
                        and (not db_ai_rec_for_field_path.accepted_at)
                        else None,
                        field_last_updated_source=db_prop_metadata_for_field_path.field_last_updated_source
                        if db_prop_metadata_for_field_path
                        else None,
                    )
                )
            result[record_id] = property_metadata
        return result

    async def reset_property_metadata_for_record(
        self,
        *,
        organization_id: UUID,
        record_id: UUID,
        user_id: UUID,
        patch_req: ShapeConstrainedModel[DT],
    ) -> None:
        """
        Resets the property metadata for a given record.
        This is used when a record is patched by a user, and we want to reset the property metadata
        for the patched fields, so the fields is not considered patched from AI recommendations anymore.
        """
        specified_fields = patch_req.specified_fields()
        constraint_domain_model_type = patch_req.constraint()
        # todo: use enum here instead
        sobject_name = constraint_domain_model_type.object_id.object_name
        for field in specified_fields:
            if field in constraint_domain_model_type.field_name_provider:
                await self.crm_ai_rec_repository.upsert_crm_property_metadata(
                    organization_id=organization_id,
                    sobject_name=sobject_name,
                    record_id=record_id,
                    sobject_field_path=field,
                    user_id=user_id,
                    field_last_updated_by_user_id=user_id,
                    field_last_updated_at=zoned_utc_now(),
                    field_last_updated_source="user",
                )


class BaseAIRecAwarePatchService[
    DT: DomainModel,
    PatchRequestT: ShapeConstrainedModel[Any],
](BaseAIRecAwareDomainObjectService[DT], ABC):
    @classmethod
    @abstractmethod
    def patch_request_type(cls) -> type[PatchRequestT]:  # type: ignore[explicit-any]  # model dynamic typing for ShapedConstrainedModel
        raise NotImplementedError

    @abstractmethod
    async def _patch_record(  # type: ignore[explicit-any]
        self,
        *,
        organization_id: UUID,
        user_id: UUID,
        record_id: UUID,
        patch_request: PatchRequestT,
    ) -> None:
        raise NotImplementedError

    async def patch_record_by_source(  # type: ignore[explicit-any]
        self,
        *,
        organization_id: UUID,
        user_id: UUID,
        record_id: UUID,
        req: PatchStandardObjectBySourceRequest,
    ) -> None:
        ai_rec_ids = {
            spec.ai_rec_id
            for spec in req.specs
            if isinstance(spec, BaseAcceptAIRecPatchSpec)
        }
        ai_rec_ids_to_reject = {
            spec.ai_rec_id
            for spec in req.specs
            if isinstance(spec, RejectCurrentAIRecPatchSpec)
        }
        await self.patch_record_from_property_ai_rec(
            organization_id=organization_id,
            user_id=user_id,
            record_id=record_id,
            ai_rec_ids=ai_rec_ids,
        )
        if ai_rec_ids_to_reject:
            await self.reject_property_ai_recs(
                organization_id=organization_id,
                user_id=user_id,
                ai_rec_ids=ai_rec_ids_to_reject,
                record_id=record_id,
            )

    async def patch_record_from_property_ai_rec(  # type: ignore[explicit-any]
        self,
        *,
        organization_id: UUID,
        user_id: UUID,
        record_id: UUID,
        ai_rec_ids: set[UUID],
    ) -> None:
        """
        Patches an object with the specified request based on AI recommendations.
        Upon patching, accepts the AI recommendations.
        The corresponding property metadata is also updated, such that we know which AI recs
        were used to patch the object at field property level.
        """
        ai_recs = await self.crm_ai_rec_repository.find_crm_property_ai_recs_by_id(
            organization_id=organization_id,
            ai_rec_ids=ai_rec_ids,
            sobject_name=self.std_object_identifier(),
            record_id=record_id,
        )
        found_ids = {ai_rec.id for ai_rec in ai_recs}
        if missing := ai_rec_ids - found_ids:
            raise ResourceNotFoundError(
                f"AI Recs with ids {missing} not found in organization {organization_id}"
            )
        patch_request = await self._build_patch_request_from_property_ai_recs(
            organization_id=organization_id, user_id=user_id, ai_recs=ai_recs
        )
        await self._patch_record(
            organization_id=organization_id,
            user_id=user_id,
            record_id=record_id,
            patch_request=patch_request,
        )
        await self.crm_ai_rec_repository.accept_crm_property_ai_rec(
            organization_id=organization_id,
            user_id=user_id,
            ai_rec_ids=ai_rec_ids,
            sobject_name=self.std_object_identifier(),
            record_id=record_id,
        )

    async def patch_record(  # type: ignore[explicit-any]
        self,
        *,
        organization_id: UUID,
        user_id: UUID,
        record_id: UUID,
        patch_request: PatchRequestT,
    ) -> None:
        """
        Patches an object with the specified request based on End user (non-AI) request.
        Upon patching, resets the property metadata for the record, so the patched properties
        are not considered patched from any AI recommendations anymore.
        """
        await self._patch_record(
            organization_id=organization_id,
            user_id=user_id,
            record_id=record_id,
            patch_request=patch_request,
        )
        await self.reset_property_metadata_for_record(
            organization_id=organization_id,
            record_id=record_id,
            user_id=user_id,
            patch_req=patch_request,
        )

    async def create_property_ai_recs_from_patch_request(  # type: ignore[explicit-any]
        self,
        *,
        organization_id: UUID,
        user_id: UUID,
        record_id: UUID,
        patch_request: PatchRequestT,
        citations: list[CriteriaCitation] | None = None,
        ai_rec_types: dict[str, CrmAIRecType] | None = None,
    ) -> list[CrmPropertyAiRec]:
        """
        Creates AI recs for patching an object based on the specified request.
        The created AI recs can be later used to actually patch the object once user accepts.
        """
        rec_creates = await self._build_property_ai_rec_requests_from_patch_request(
            organization_id=organization_id,
            user_id=user_id,
            record_id=record_id,
            patch_request=patch_request,
            citations=citations,
            ai_rec_types=ai_rec_types,
        )
        if not rec_creates:
            return []
        return [
            await self.crm_ai_rec_repository.insert_crm_property_ai_rec(
                rec_create,
            )
            for rec_create in rec_creates
        ]

    async def _build_property_ai_rec_requests_from_patch_request(  # type: ignore[explicit-any]
        self,
        *,
        organization_id: UUID,
        user_id: UUID,
        record_id: UUID,
        patch_request: PatchRequestT,
        citations: list[CriteriaCitation] | None = None,
        ai_rec_types: dict[str, CrmAIRecType] | None = None,
    ) -> list[CrmPropertyAiRecCreate]:
        if not issubclass(patch_request.constraint(), DomainModel):
            raise NotImplementedError(
                f"Patch request {patch_request} must be a subclass of {DomainModel}"
            )
        field_value_by_name = self.to_field_value_by_field_name(patch_request)
        return [
            CrmPropertyAiRecCreate(
                sobject_name=self.std_object_identifier(),
                sobject_field_path=field_name,
                rec_value=field_value,
                rec_type=(ai_rec_types or {}).get(field_name, CrmAIRecType.enhancement),
                created_at=zoned_utc_now(),
                created_by_user_id=user_id,
                organization_id=organization_id,
                record_id=record_id,
                rec_citation_ids=[
                    citation.id
                    for citation in citations
                    if citation.field_name == field_name
                ]
                if citations
                else None,
            )
            for field_name, field_value in field_value_by_name.items()
        ]

    async def _build_patch_request_from_property_ai_recs(  # type: ignore[explicit-any]
        self,
        *,
        organization_id: UUID,
        user_id: UUID,
        ai_recs: list[CrmPropertyAiRec],
    ) -> PatchRequestT:
        # todo xw: this isn't fully correct, we need to properly handle nested fields
        recs_by_field_name = {
            ai_rec.sobject_field_path: ai_rec.rec_value.to_generic_value()
            if ai_rec.rec_value
            else None
            for ai_rec in ai_recs
        }
        return self.patch_request_type().model_validate(recs_by_field_name)


class BaseAIRecAwareCreateService[
    DomainModelT: DomainModel,
    DomainModelPreviewT: ShapeConstrainedModel[Any],
    CreateReqT: BaseModel,
](BaseAIRecAwareDomainObjectService[DomainModelT], ABC):
    @classmethod
    @abstractmethod
    def create_request_type(cls) -> type[CreateReqT]:  # type: ignore[explicit-any] # workaround higher order type inference issue
        raise NotImplementedError

    @classmethod
    @abstractmethod
    def domain_model_preview_type(cls) -> type[DomainModelPreviewT]:  # type: ignore[explicit-any] # workaround higher order type inference issue
        raise NotImplementedError

    @classmethod
    @abstractmethod
    def convert_create_req_to_domain_model_preview(  # type: ignore[explicit-any]
        cls, create_req: CreateReqT
    ) -> DomainModelPreviewT:
        raise NotImplementedError

    @abstractmethod
    async def create_record(  # type: ignore[explicit-any]
        self,
        *,
        organization_id: UUID,
        user_id: UUID,
        parent_record_ids: ParentRecordIds | None,
        create_request: CreateReqT,
        citation_ids: list[UUID] | None = None,
        ai_rec_id: UUID | None = None,
    ) -> DomainModelT:
        raise NotImplementedError

    @classmethod
    def creation_rec_dedup_key(  # type: ignore[explicit-any]
        cls, create_request: CreateReqT, parent_record_ids: ParentRecordIds | None
    ) -> str | None:
        return None

    async def create_record_from_ai_rec(  # type: ignore[explicit-any]
        self, *, organization_id: UUID, user_id: UUID, ai_rec_id: UUID
    ) -> DomainModelT:
        """
        Creates an object based on the specified AI rec.
        Upon creation, accepts the AI rec.
        """

        object_ai_rec = (
            await self.crm_ai_rec_repository.get_unrejected_crm_object_ai_rec(
                organization_id=organization_id,
                sobject_name=self.std_object_identifier(),
                rec_id=ai_rec_id,
            )
        )
        create_request = self._build_create_request_from_object_ai_rec(
            organization_id=organization_id, user_id=user_id, ai_rec=object_ai_rec
        )
        created = await self.create_record(
            organization_id=organization_id,
            user_id=user_id,
            create_request=create_request,
            citation_ids=object_ai_rec.rec_citation_ids,
            ai_rec_id=ai_rec_id,
            parent_record_ids=object_ai_rec.parent_record_ids,
        )
        await self.crm_ai_rec_repository.accept_crm_object_ai_rec(
            organization_id=organization_id,
            user_id=user_id,
            ai_rec_id=ai_rec_id,
            record_id=self.record_id(created),
            sobject_name=self.std_object_identifier(),
        )
        return created

    async def create_object_ai_rec_from_create_request(  # type: ignore[explicit-any]
        self,
        *,
        organization_id: UUID,
        user_id: UUID,
        create_request: CreateReqT,
        citation_ids: list[UUID] | None = None,
        parent_record_ids: ParentRecordIds | None,
    ) -> CrmObjectAiRec:
        """
        Creates an AI rec for creating an object based on the specified request.
        The created object AI rec can be later used to actually created the object once user accepts.
        """
        db_object_to_insert = CrmObjectAiRecCreate(
            id=uuid4(),
            organization_id=organization_id,
            sobject_name=self.std_object_identifier(),
            rec_value=create_request.model_dump(mode="json"),
            created_at=zoned_utc_now(),
            created_by_user_id=user_id,
            rec_citation_ids=citation_ids,
            parent_record_ids=parent_record_ids,
            dedupe_key=self.creation_rec_dedup_key(
                create_request=create_request, parent_record_ids=parent_record_ids
            ),
        )
        return await self.crm_ai_rec_repository.create_crm_object_ai_rec(
            create=db_object_to_insert
        )

    @classmethod
    def _build_create_request_from_object_ai_rec(  # type: ignore[explicit-any]
        cls, *, organization_id: UUID, user_id: UUID, ai_rec: CrmObjectAiRec
    ) -> CreateReqT:
        if ai_rec.sobject_name != cls.std_object_identifier():
            logger.warning(
                "AI rec is for a different object type",
                object_ai_rec_id=ai_rec.id,
                object_ai_rec_sobject_name=ai_rec.sobject_name,
                expected_sobject_name=cls.std_object_identifier(),
            )
            raise InvalidArgumentError("AI rec is for a different object type")
        return cls.create_request_type().model_validate(ai_rec.rec_value)
