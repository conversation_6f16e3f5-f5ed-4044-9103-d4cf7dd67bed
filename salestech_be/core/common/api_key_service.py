import secrets
from uuid import UUID, uuid4

from fastapi import Request
from pydantic import BaseModel

from salestech_be.common.exception.exception import UnauthorizedError
from salestech_be.common.lifespan import get_db_engine
from salestech_be.common.singleton import Singleton
from salestech_be.db.dao.api_key_repository import ApiKeyRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.api_key import <PERSON>pi<PERSON>ey, ApiKeyScope
from salestech_be.util.time import zoned_utc_now


class ApiKeyResponse(BaseModel):
    id: UUID
    name: str
    is_expired: bool

    class Config:
        from_attributes = True


class ApiKeyService:
    def __init__(self, repository: ApiKeyRepository):
        self.repository = repository

    async def list_api_keys(
        self,
        organization_id: UUID,
        user_id: UUID,
        scope: ApiKeyScope,
    ) -> list[ApiKeyResponse]:
        """Get all API keys for the given org, user, and scope"""
        api_keys = await self.repository.find_all_by_organization_and_user_and_scope(
            organization_id, user_id, scope
        )
        return [
            ApiKeyResponse(
                id=api_key.id,
                name=api_key.name,
                is_expired=api_key.is_expired,
            )
            for api_key in api_keys
        ]

    async def create_api_key(
        self,
        organization_id: UUID,
        user_id: UUID,
        scope: ApiKeyScope,
        key_name: str,
    ) -> ApiKey:
        """Create a new API key for a specific scope"""
        api_key = ApiKey(
            id=uuid4(),
            name=key_name,
            secret_key=self._generate_key(),
            organization_id=organization_id,
            user_id=user_id,
            scope=scope,
            created_at=zoned_utc_now(),
            is_expired=False,
        )

        return await self.repository.insert(api_key)

    async def validate_api_key(
        self, secret_key: str, required_scope: ApiKeyScope
    ) -> ApiKey:
        """Validate an API key and ensure it has the required scope"""
        api_key = await self.repository.find_by_secret_key(secret_key)

        if not api_key:
            raise UnauthorizedError("Invalid API key")

        if api_key.is_expired:
            raise UnauthorizedError("API key has expired")

        if api_key.scope != required_scope:
            raise UnauthorizedError(
                f"API key not authorized for {required_scope.value}"
            )

        return api_key

    async def expire_api_key(
        self, key_id: UUID, organization_id: UUID, user_id: UUID
    ) -> None:
        """Expire an API key"""
        await self.repository.expire_api_key(key_id, organization_id, user_id)

    def _generate_key(self) -> str:
        """Generate a random, URL-safe API key"""
        return secrets.token_urlsafe(32)


class SingletonApiKeyService(Singleton, ApiKeyService):
    pass


def api_key_service_from_engine(
    db_engine: DatabaseEngine,
) -> ApiKeyService:
    return SingletonApiKeyService(repository=ApiKeyRepository(engine=db_engine))


def api_key_service_factory(
    request: Request,
) -> ApiKeyService:
    return api_key_service_from_engine(db_engine=get_db_engine(request=request))
