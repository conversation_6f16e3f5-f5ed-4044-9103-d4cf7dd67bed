from abc import ABC
from collections.abc import Mapping
from typing import Self

from frozendict import frozendict
from pydantic import BaseModel, Field, PrivateAttr, model_validator

from salestech_be.common.query_util.domain_fetch_hints import DomainFetchHints
from salestech_be.common.schema_manager.std_object_field_identifier import (
    StdFieldIdentifierEnum,
)
from salestech_be.core.common.types import DomainModel
from salestech_be.db.dao.generic_repository import SelectionSpec
from salestech_be.db.models.core.base import TableModel
from salestech_be.db.models.core.utils import (
    SortRankMappingProvider,
    SqlSelection,
    SqlSort,
    sort_to_sql_sort_clause,
    value_filter_to_sql_selection,
)
from salestech_be.ree_logging import get_logger

logger = get_logger()


class DomainToTableModelFieldMapping[DT: DomainModel, TT: TableModel](BaseModel, ABC):
    """
    A field path to table column mapping between a domain model and a table model.
    e.g. for a domain model field path of ("organization", "name"), the table model column name is "organization_name".
    >>> account_mapping = DomainToTableModelFieldMapping[AccountV2, Account](
    ...     domain_model_tp=AccountV2,
    ...     table_model_tp=Account,
    ...     field_path_mapping={
    ...         ("organization", "name"): "organization_name",
    ...     },
    ... )
    ... account_column = account_mapping(("organization", "name"))
    """

    domain_model_tp: type[DT]
    table_model_tp: type[TT]

    field_path_mapping_override: Mapping[tuple[str, ...], str] = Field(
        default_factory=dict
    )

    _default_field_path_mapping: Mapping[tuple[str, ...], str] = PrivateAttr(
        default_factory=dict
    )

    @model_validator(mode="after")
    def validate_field_path_mapping(self) -> Self:
        # validate the provided field_path_mapping is valid
        domain_model_field_name_provider_enum: type[StdFieldIdentifierEnum] = (
            self.domain_model_tp.field_name_provider
        )
        table_model_column_name = self.table_model_tp.column_fields.keys()

        _invalid_field_path_mapping: dict[tuple[str, ...], str] = {}

        for field_path, table_column in self.field_path_mapping_override.items():
            # check if the field_path is valid
            if not field_path:
                _invalid_field_path_mapping[field_path] = table_column
                continue
            if field_path[0] not in domain_model_field_name_provider_enum:
                _invalid_field_path_mapping[field_path] = table_column
                continue
            if table_column not in table_model_column_name:
                _invalid_field_path_mapping[field_path] = table_column
                continue
        if _invalid_field_path_mapping:
            raise ValueError(
                f"Invalid field path mappings found for domain model {self.domain_model_tp} and table model {self.table_model_tp}: {_invalid_field_path_mapping}"
            )

        # todo(xw): I'm very iffy about this. on one hand, we can leverage this auto matching to save user from specifying the mapping.
        # on the other hand, this might be a source of bugs or unintentional behavior.
        # i will keep this for now, but need to revisit this once the push down is released.
        _default_field_path_mapping: dict[tuple[str, ...], str] = {}
        for domain_model_field_name in domain_model_field_name_provider_enum:
            if domain_model_field_name in table_model_column_name:
                # dm_pt = parse_pydantic_field_type(
                #     self.domain_model_tp, field_name=domain_model_field_name
                # )
                # tb_pt = parse_pydantic_field_type(
                #     self.table_model_tp, field_name=domain_model_field_name
                # )
                # todo(xw): find a better way to bring the strict check back
                # if (
                #     dm_pt.type_if_singular_or_nullable_singular
                #     == tb_pt.type_if_singular_or_nullable_singular
                # ):
                #     _default_field_path_mapping[(domain_model_field_name,)] = str(
                #         domain_model_field_name
                #     )
                _default_field_path_mapping[(domain_model_field_name,)] = str(
                    domain_model_field_name
                )

        self._default_field_path_mapping = frozendict(_default_field_path_mapping)
        return self

    def __call__(self, domain_field_path: tuple[str, ...]) -> str | None:
        return self.field_path_mapping_override.get(
            domain_field_path,
            self._default_field_path_mapping.get(domain_field_path, None),
        )


def to_sql_selection_spec[DT: DomainModel, TT: TableModel](  # noqa: C901
    *,
    domain_fetch_hints: DomainFetchHints | None = None,
    domain_to_table_model_field_mapping: DomainToTableModelFieldMapping[DT, TT],
    sort_rank_mapping_provider: SortRankMappingProvider | None = None,
    enable_json_spec: bool = False,
) -> SelectionSpec:
    """
    Convert a DomainFetchHints object into a SelectionSpec object.

    The SelectionSpec object can be used to fetch data from the database.

    Arguments:
        domain_fetch_hint: The domain fetch hint to convert.
        domain_to_table_model_field_mapping: The domain to table model field mapping to use.
    """
    if not domain_fetch_hints:
        return SelectionSpec(
            must=(),
            must_not=(),
            any=(),
            sorting=(),
            exclude_invisible=True,
            exclude_locked_by_integrity_jobs=True,
        )

    if (
        domain_fetch_hints.object_identifier
        != domain_to_table_model_field_mapping.domain_model_tp.object_id
    ):
        raise ValueError(
            f"The object identifier of the domain fetch hint ({domain_fetch_hints.object_identifier}) does not match the object identifier of the domain model ({domain_to_table_model_field_mapping.domain_model_tp.object_id})"
        )

    domain_filters = domain_fetch_hints.baseline_filters
    domain_sorting_spec = domain_fetch_hints.non_relational_sorting_spec
    must_selections: list[SqlSelection] = []
    must_not_selections: list[SqlSelection] = []
    any_selections: list[SqlSelection] = []
    sortings: list[SqlSort] = []

    if domain_filters:
        for filter_ in domain_filters.must_filters:
            if sql_selection := value_filter_to_sql_selection(
                value_filter=filter_,
                table_model_tp=domain_to_table_model_field_mapping.table_model_tp,
                param_name_prefix="",
                field_path_db_column_mapping_provider=domain_to_table_model_field_mapping,
                enable_json_spec=enable_json_spec,
            ):
                must_selections.append(sql_selection)
        for filter_ in domain_filters.must_not_filters:
            if sql_selection := value_filter_to_sql_selection(
                value_filter=filter_,
                table_model_tp=domain_to_table_model_field_mapping.table_model_tp,
                param_name_prefix="",
                field_path_db_column_mapping_provider=domain_to_table_model_field_mapping,
                enable_json_spec=enable_json_spec,
            ):
                must_not_selections.append(sql_selection)
        for filter_ in domain_filters.at_least_one_filters:
            if sql_selection := value_filter_to_sql_selection(
                value_filter=filter_,
                table_model_tp=domain_to_table_model_field_mapping.table_model_tp,
                param_name_prefix="",
                field_path_db_column_mapping_provider=domain_to_table_model_field_mapping,
                enable_json_spec=enable_json_spec,
            ):
                any_selections.append(sql_selection)
            else:
                # clear any_selections if any one of the at_least_one_filters is not translated to a sql selection
                # this is to avoid returning empty any_selections due to only passing down a subset of at_least_one_filters
                logger.info(
                    "one of the at_least_one_filters is not translated to a sql selection, clear any_selections",
                    dropped_filter=filter_,
                )
                any_selections.clear()
                break
    for sorter in domain_sorting_spec.ordered_sorters:
        if sql_sort := sort_to_sql_sort_clause(
            sorter=sorter,
            table_model_tp=domain_to_table_model_field_mapping.table_model_tp,
            field_path_db_column_mapping_provider=domain_to_table_model_field_mapping,
            sort_rank_mapping_provider=sort_rank_mapping_provider,
        ):
            sortings.append(sql_sort)

    return SelectionSpec(
        must=tuple(must_selections),
        must_not=tuple(must_not_selections),
        any=tuple(any_selections),
        sorting=tuple(sortings),
        limit=domain_fetch_hints.limit,
        exclude_invisible=domain_fetch_hints.exclude_invisible,
        exclude_locked_by_integrity_jobs=domain_fetch_hints.exclude_locked_by_integrity_jobs,
        offset=domain_fetch_hints.offset,
    )
