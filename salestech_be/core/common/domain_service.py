from abc import ABC, abstractmethod
from collections.abc import Awaitable, Callable
from typing import Any, Generic, TypeVar, cast
from uuid import UUID

from pydantic import BaseModel, ValidationError
from sqlalchemy.exc import IntegrityError

from salestech_be.common.exception.exception import (
    ConflictResourceError,
    CRMSyncConflictError,
    ForbiddenError,
    IllegalStateError,
    ReferentialViolationError,
    ResourceNotFoundError,
)
from salestech_be.common.type.patch_request import (
    BaseBulkPatchRequest,
    BasePatchRequest,
    BulkPatchEntityResponse,
)
from salestech_be.core.common.types import UserAuthContext
from salestech_be.core.ff.feature_flag_service import FeatureFlagService
from salestech_be.ree_logging import get_logger
from salestech_be.util.validation import not_none
from salestech_be.web.api.common.container import (
    DeleteEntityResponse,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)

logger = get_logger(__name__)

T = TypeVar("T", bound=BaseModel)


class AllowedUsers(BaseModel):
    owner_user_id: UUID | None
    participant_user_ids: list[UUID]

    def is_owner_or_participant(self, user_id: UUID) -> bool:
        return user_id == self.owner_user_id or user_id in self.participant_user_ids

    def is_owner(self, user_id: UUID) -> bool:
        return user_id == self.owner_user_id


class _ReeService(Generic[T]):
    async def get_allowed_users_from_entity(self, domain_entity: T) -> AllowedUsers:
        """
        Returns AllowedUsers for a domain entity object.

        Sub classes may override this, in most cases we have standard naming fields that are used here.
        """

        model_as_dict = domain_entity.model_dump(
            include={"owner_user_id", "participant_user_id_list"}
        )
        owner_user_id = UUID(str(model_as_dict.get("owner_user_id")))
        participant_user_ids = cast(
            list[UUID], model_as_dict.get("participant_user_id_list")
        )
        return AllowedUsers(
            owner_user_id=owner_user_id, participant_user_ids=participant_user_ids
        )


class DomainService(_ReeService[T], Generic[T], ABC):
    """
    Abstract class that defines common methods and helper functions for all domain services.
    """

    def __init__(self, feature_flag_service: FeatureFlagService) -> None:
        self.feature_flag_service = feature_flag_service

    async def can_access_entity_for_patch(
        self,
        user_auth_context: UserAuthContext,
        entity: T,
        patch_request: BasePatchRequest | None = None,
    ) -> bool:
        """
        Returns a bool indicating if the provided user can access the entity for updating.

        Basic definition in this class checks if the user is the owner or a participant; sub classes
        may override this to implement more complex access control logic if needed.
        """
        # If not admin, check if user is owner/participant
        if not (user_auth_context.is_admin or user_auth_context.is_super_admin):
            allowed_users = await self.get_allowed_users_from_entity(entity)
            if not allowed_users.is_owner_or_participant(
                user_id=user_auth_context.user_id
            ):
                return False

        # Always run domain-specific access checks (even for admins)
        # This allows business rules to apply to all users including admins
        return await self.can_access_entity_for_patch_domain_specific(
            user_auth_context=user_auth_context,
            entity=entity,
            patch_request=patch_request,
        )

    async def can_access_entity_for_patch_domain_specific(
        self,
        user_auth_context: UserAuthContext,
        entity: T,
        patch_request: BasePatchRequest | None = None,
    ) -> bool:
        """
        Domain-specific access control for patch operations.

        Override this method in subclasses to implement domain-specific logic.
        Returns True by default.
        """
        return True

    @staticmethod
    async def validate_patch_request_before_auth(
        entity: T, request: BasePatchRequest
    ) -> None:
        """
        Validate patch request values before authorization.

        This method validates business rules that require access to the current entity state.
        It runs before authorization to ensure validation errors (400) are thrown before
        authorization errors (403).

        This is a good place to validate business rules that require access to the current
        entity state if not done in the Frontend.

        Override this method in subclasses to implement domain-specific validation.
        Default implementation does nothing.

        Raises InvalidArgumentError or other validation exceptions if validation fails.
        """

    @abstractmethod
    async def update_entity(
        self, organization_id: UUID, user_id: UUID, entity: T, request: BasePatchRequest
    ) -> T:
        """
        Update domain entity based on the specified request.
        """
        ...

    async def authed_bulk_patch_entity(  # noqa: C901
        self, user_auth_context: UserAuthContext, request: BaseBulkPatchRequest
    ) -> BulkPatchEntityResponse:
        """
        Patch entities with ids and a specified request.  Includes enforcing access control.
        """
        logger.bind(
            organization_id=user_auth_context.organization_id,
            user_id=user_auth_context.user_id,
            request=request,
        ).info("Bulk Patch Entities request")

        total_requested_count = len(request.entity_ids)
        patch_success_count = 0
        patch_failed_count = 0
        failure_details: dict[UUID, str] = {}

        # Process each entity ID individually
        for entity_id in request.entity_ids:
            try:
                # Use the authed_patch_entity method to handle the entity
                await self.authed_patch_entity(
                    user_auth_context=user_auth_context,
                    entity_id=entity_id,
                    request=request.patch_request,
                )
                patch_success_count += 1

            except ResourceNotFoundError as e:
                patch_failed_count += 1
                failure_details[entity_id] = f"Resource not found: {e!s}"
                logger.error(
                    "Entity not found in bulk patch operation",
                    entity_id=entity_id,
                    error=str(e),
                )
            except ConflictResourceError as e:
                patch_failed_count += 1
                failure_details[entity_id] = (
                    f"Conflict found: {not_none(e.additional_error_details).reference_id}"
                )
                logger.error(
                    "Conflict Entity found in bulk patch operation",
                    entity_id=entity_id,
                    error=str(e),
                )
            except ForbiddenError as e:
                patch_failed_count += 1
                failure_details[entity_id] = f"Permission denied: {e!s}"
                logger.error(
                    "Permission denied in bulk patch operation",
                    entity_id=entity_id,
                    error=str(e),
                )
            except IllegalStateError as e:
                patch_failed_count += 1
                failure_details[entity_id] = f"Invalid state: {e!s}"
                logger.error(
                    "Invalid state error in bulk patch operation",
                    entity_id=entity_id,
                    error=str(e),
                )
            except ValidationError as e:
                patch_failed_count += 1
                failure_details[entity_id] = f"Validation error: {e!s}"
                logger.error(
                    "Validation error in bulk patch operation",
                    entity_id=entity_id,
                    error=str(e),
                )
            except IntegrityError as e:
                patch_failed_count += 1
                failure_details[entity_id] = "Conflict with existing data"
                logger.error(
                    "Database integrity error in bulk patch operation",
                    entity_id=entity_id,
                    error=str(e),
                )
            except ReferentialViolationError as e:
                patch_failed_count += 1
                failure_details[entity_id] = f"Referential violation: {e!s}"
                logger.error(
                    "Referential violation in bulk patch operation",
                    entity_id=entity_id,
                    error=str(e),
                )
            except CRMSyncConflictError as e:
                patch_failed_count += 1
                failure_details[entity_id] = f"CRM sync conflict: {e!s}"
                logger.error(
                    "CRM sync conflict in bulk patch operation",
                    entity_id=entity_id,
                    error=str(e),
                )
            except Exception as e:
                patch_failed_count += 1
                # For generic exceptions, provide a more user-friendly message
                error_type = type(e).__name__
                error_msg = str(e) if str(e) else "Unknown error occurred"
                failure_details[entity_id] = f"{error_type}: {error_msg}"
                logger.error(
                    "Failed to patch entity in bulk operation",
                    entity_id=entity_id,
                    error_type=error_type,
                    error=error_msg,
                )

        return BulkPatchEntityResponse(
            total_requested_count=total_requested_count,
            patch_success_count=patch_success_count,
            patch_failed_count=patch_failed_count,
            failure_details=failure_details,
        )

    async def authed_patch_entity(
        self,
        user_auth_context: UserAuthContext,
        entity_id: UUID,
        request: BasePatchRequest,
    ) -> T:
        """
        Patches an entity with the specified request.  Includes enforcing access control.

        This method follows the validation-before-authorization pattern:
        1. Fetch entity
        2. Validate request (throws 400 errors)
        3. Check authorization (throws 403 errors)
        4. Update entity
        """
        logger.bind(
            organization_id=user_auth_context.organization_id,
            user_id=user_auth_context.user_id,
            entity_id=entity_id,
            request=request,
        ).info("Patch entity request")

        entity = await self.get_entity(
            entity_id=entity_id,
            organization_id=user_auth_context.organization_id,
            user_id=user_auth_context.user_id,
        )
        if not entity:
            raise ResourceNotFoundError("Entity not found")

        # Optional validation before authorization- throws 400 errors
        await self.validate_patch_request_before_auth(entity, request)

        # Now do authorization - throws 403 errors
        if not await self.can_access_entity_for_patch(
            user_auth_context=user_auth_context, entity=entity, patch_request=request
        ):
            raise ForbiddenError("No permission to access the requested resource")

        # Finally update
        return await self.update_entity(
            organization_id=user_auth_context.organization_id,
            user_id=user_auth_context.user_id,
            entity=entity,
            request=request,
        )

    async def can_access_entity_for_delete(
        self, user_auth_context: UserAuthContext, entity: T
    ) -> bool:
        if user_auth_context.is_admin or user_auth_context.is_super_admin:
            return True

        allowed_users = await self.get_allowed_users_from_entity(entity)
        return allowed_users.is_owner_or_participant(user_id=user_auth_context.user_id)

    async def can_access_entity_for_read_participants_or_owner(
        self, user_auth_context: UserAuthContext, entity: T
    ) -> bool:
        if user_auth_context.is_admin or user_auth_context.is_super_admin:
            return True

        allowed_users = await self.get_allowed_users_from_entity(entity)
        return allowed_users.is_owner_or_participant(user_id=user_auth_context.user_id)

    async def can_access_entity_for_read(
        self, user_auth_context: UserAuthContext, entity: T
    ) -> bool:
        """
        Default function for checking if a user can read an entity.

        We currently allow read access across org, so we skip checks for now
        """
        return True

    @abstractmethod
    async def remove_entity(
        self, organization_id: UUID, user_id: UUID, entity_id: UUID
    ) -> DeleteEntityResponse:
        """
        Remove domain entity based on the specified request.
        """
        ...

    async def authed_delete_entity(
        self,
        user_auth_context: UserAuthContext,
        entity_id: UUID,
        error_if_not_found: bool = True,
    ) -> DeleteEntityResponse:
        """
        Deletes an entity identified by the given ID.  Enforces access control.
        """
        logger.bind(
            organization_id=user_auth_context.organization_id,
            user_id=user_auth_context.user_id,
            entity_id=entity_id,
        ).info("Delete entity request")

        entity = await self.get_entity(
            entity_id=entity_id,
            organization_id=user_auth_context.organization_id,
            user_id=user_auth_context.user_id,
        )
        if not entity:
            if error_if_not_found:
                raise ResourceNotFoundError("Entity not found")
            else:
                return DeleteEntityResponse(id=entity_id)

        if not await self.can_access_entity_for_delete(
            user_auth_context=user_auth_context, entity=entity
        ):
            raise ForbiddenError("No permission to access the requested resource")

        return await self.remove_entity(
            organization_id=user_auth_context.organization_id,
            user_id=user_auth_context.user_id,
            entity_id=entity_id,
        )

    async def can_create_entity(self, user_auth_context: UserAuthContext) -> bool:
        return True

    @abstractmethod
    async def create_entity(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self, organization_id: UUID, user_id: UUID, request: Any
    ) -> T:
        """
        Create domain entity based on the specified request.
        """
        ...

    async def authed_create_entity(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self,
        user_auth_context: UserAuthContext,
        request: Any,
    ) -> T:
        """
        Patches an entity with the specified request.  Includes enforcing access control.
        """
        logger.bind(
            organization_id=user_auth_context.organization_id,
            user_id=user_auth_context.user_id,
            request=request,
        ).info("Create entity request")
        if not await self.can_create_entity(
            user_auth_context=user_auth_context,
        ):
            raise ForbiddenError("No permission to access the requested resource")

        return await self.create_entity(
            organization_id=user_auth_context.organization_id,
            user_id=user_auth_context.user_id,
            request=request,
        )

    @abstractmethod
    async def get_entity(
        self, entity_id: UUID, organization_id: UUID, user_id: UUID
    ) -> T | None:
        """
        Returns domain model with the specified entity id.
        """
        ...

    async def error_if_no_entity_access(
        self,
        user_auth_context: UserAuthContext,
        entity_id: UUID,
        access_check_function: Callable[[UserAuthContext, T], Awaitable[bool]],
    ) -> None:
        entity = await self.get_entity(
            entity_id=entity_id,
            organization_id=user_auth_context.organization_id,
            user_id=user_auth_context.user_id,
        )
        if not entity:
            raise ResourceNotFoundError("Entity not found")

        if not await access_check_function(user_auth_context, entity):
            raise ForbiddenError("No permission to access the requested resource")


class AllowedModifications(BaseModel):
    """Describes the edit permissions for a domain object"""

    is_editable: bool
    restricted_fields: list[str]


class DomainQueryService(_ReeService[T], Generic[T], ABC):
    """
    Abstract class that defines common methods and helper functions for all domain query services.
    """

    def __init__(self, feature_flag_service: FeatureFlagService) -> None:
        self.feature_flag_service = feature_flag_service

    async def is_entity_viewable_by_user(
        self, user_auth_context: UserAuthContext, domain_object: T
    ) -> bool:
        """
        Returns a bool indicating if the provided user can view the domain object.
        """
        # Placeholder implementation - we don't restrict things yet
        return True

    # @log_timing(logger=logger)
    async def filter_viewable_records(
        self, records: list[T], user_auth_context: UserAuthContext
    ) -> list[T]:
        filtered_records: list[T] = []
        for record in records:
            if await self.is_entity_viewable_by_user(
                user_auth_context=user_auth_context,
                domain_object=record,
            ):
                filtered_records.append(record)
        return filtered_records

    async def get_allowed_modifications_for_user(
        self, user_auth_context: UserAuthContext, domain_object: T
    ) -> AllowedModifications:
        """
        Returns an AllowedModifications object describing if and how the domain object can be edited by the user.
        """
        allowed_users = await self.get_allowed_users_from_entity(domain_object)
        return AllowedModifications(
            is_editable=allowed_users.is_owner_or_participant(
                user_id=user_auth_context.user_id
            ),
            restricted_fields=[],  # No use case yet where we restrict fields
        )

    # @log_timing(logger=logger)
    async def is_editable(
        self, user_auth_context: UserAuthContext | None, domain_object: T
    ) -> bool:
        """
        Convenience wrapper for checking if the domain object is editable by the user.  Uses get_allowed_modifications_for_user
        to determine result.
        """
        if not user_auth_context:
            return True

        if user_auth_context.is_admin or user_auth_context.is_super_admin:
            return True

        allowed_modifications = await self.get_allowed_modifications_for_user(
            user_auth_context=user_auth_context,
            domain_object=domain_object,
        )
        return allowed_modifications.is_editable
