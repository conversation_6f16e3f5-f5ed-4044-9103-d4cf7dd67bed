from datetime import date, datetime
from decimal import Decimal
from typing import Annotated, Any, Literal, TypeAlias
from uuid import UUID

from pydantic import BaseModel, Field

from salestech_be.common.type.metadata.schema import FieldPath
from salestech_be.db.models.crm_ai_rec import CrmAIRecType
from salestech_be.util.enum_util import NameValueStrEnum
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime

__all__ = [
    "AIRec",
    "AIRecType",
    "AIRecValue",
    "PropertyMetadata",
]


AIRecType = CrmAIRecType


AIRecValue: TypeAlias = (  # type: ignore[explicit-any] # need to use Any to represent free-form json
    int
    | float
    | UUID
    | str
    | bool
    | datetime
    | date
    | Decimal
    | list[int]
    | list[float]
    | list[UUID]
    | list[str]
    | list[bool]
    | list[datetime]
    | list[date]
    | list[Decimal]
    | dict[str, Any]
)


class AIAnalysisState(NameValueStrEnum):
    """
    The state of the AI analysis.
    """

    pending = "pending"
    """
    The AI analysis is pending.
    """
    done = "done"
    """
    The AI analysis is done.
    """
    not_applicable = "not_applicable"
    """
    The AI analysis is not applicable.
    """


class AIRec(BaseModel):
    """
    A property value level recommendation made by AI.
    """

    id: Annotated[UUID, Field(description="The ID of the recommendation")]
    rec_value: Annotated[
        AIRecValue | None, Field(description="The value recommended by AI")
    ] = None
    rec_type: Annotated[AIRecType, Field(description="The type of recommendation")]
    citation_ids: Annotated[
        list[UUID] | None,
        Field(
            description="The IDs of the citations that support the recommendation",
        ),
    ] = None
    created_at: Annotated[
        ZoneRequiredDateTime,
        Field(
            description="The date and time the recommendation was created",
        ),
    ]


class PropertyMetadata(BaseModel):
    """
    Metadata about a property.
    """

    field_path: Annotated[
        FieldPath,
        Field(description="The path to the property"),
    ]

    citation_ids: Annotated[
        list[UUID] | None,
        Field(
            description="The IDs of the citations that support the property, provided by either the user or AI",
        ),
    ] = None
    field_last_updated_source: Annotated[
        Literal["user", "ai"] | None,
        Field(description="The source of the last update to the property"),
    ] = None
    ai_analysis_state: Annotated[
        AIAnalysisState,
        Field(description="The state of the AI analysis"),
    ] = AIAnalysisState.not_applicable
    ai_rec: Annotated[
        AIRec | None,
        Field(description="The AI recommendation for the property"),
    ] = None
    last_updated_source_ai_rec_id: Annotated[
        UUID | None,
        Field(
            description="The ID of the AI recommendation that last updated the property"
        ),
    ] = None
