from typing import Annotated, Literal, <PERSON><PERSON><PERSON><PERSON>
from uuid import UUID

from pydantic import BaseModel, Discriminator

from salestech_be.common.type.metadata.common import ObjectIdentifier
from salestech_be.common.type.metadata.schema import FieldPath
from salestech_be.util.enum_util import NameValueStrEnum

__all__ = [
    "AcceptAnyAIRecPatchSpec",
    "AcceptCurrentAIRecPatchSpec",
    "ManualEntryPatchSpec",
    "ManualSource",
    "PatchSpec",
    "PatchSpecType",
    "RejectCurrentAIRecPatchSpec",
]


class PatchSpecType(NameValueStrEnum):
    """
    The type of patch operation.
    """

    accept_current_ai_rec = "accept_current_ai_rec"
    accept_any_ai_rec = "accept_any_ai_rec"
    reject_current_ai_rec = "reject_current_ai_rec"
    manual_entry = "manual_entry"


class BasePatchSpec(BaseModel):
    """
    A specification for a patch operation.
    """

    field_path: FieldPath


class BaseAcceptAIRecPatchSpec(BasePatchSpec):
    """
    A specification for an accept AI recommendation patch operation.
    """

    ai_rec_id: UUID


class AcceptCurrentAIRecPatchSpec(BaseAcceptAIRecPatchSpec):
    """
    A specification for an accept AI recommendation patch operation.
    """

    spec_type: Literal[PatchSpecType.accept_current_ai_rec] = (
        PatchSpecType.accept_current_ai_rec
    )


class AcceptAnyAIRecPatchSpec(BaseAcceptAIRecPatchSpec):
    """
    A specification for an accept AI recommendation patch operation.
    """

    spec_type: Literal[PatchSpecType.accept_any_ai_rec] = (
        PatchSpecType.accept_any_ai_rec
    )


class RejectCurrentAIRecPatchSpec(BasePatchSpec):
    """
    A specification for a reject AI recommendation patch operation.
    """

    spec_type: Literal[PatchSpecType.reject_current_ai_rec] = (
        PatchSpecType.reject_current_ai_rec
    )
    ai_rec_id: UUID
    reason: str | None = None


class ManualSource(BaseModel):
    """
    The source of a manual entry.
    """

    object_identifier: ObjectIdentifier
    record_id: UUID


class ManualEntryPatchSpec(BasePatchSpec):
    """
    A specification for a manual entry patch operation.
    """

    spec_type: Literal[PatchSpecType.manual_entry] = PatchSpecType.manual_entry
    sources: list[ManualSource]


PatchSpec: TypeAlias = Annotated[
    AcceptCurrentAIRecPatchSpec
    | RejectCurrentAIRecPatchSpec
    # | AcceptAnyAIRecPatchSpec |
    | ManualEntryPatchSpec,
    Discriminator(discriminator="spec_type"),
]


class PatchStandardObjectBySourceRequest(BaseModel):
    specs: list[PatchSpec]


class CreateStandardObjectBySourceRequest(BaseModel):
    ai_rec_id: UUID
