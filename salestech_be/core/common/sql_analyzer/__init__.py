"""SQL Analyzer module for extracting table references from SQL statements."""

from salestech_be.core.common.sql_analyzer.dto.sql_analysis_dto import (
    SqlTableAnalysisResult,
    TableReference,
)
from salestech_be.core.common.sql_analyzer.service.sql_table_analyzer_service import (
    SqlTableAnalyzer,
    get_sql_table_analyzer_service,
)

__all__ = [
    "SqlTableAnalysisResult",
    "SqlTableAnalyzer",
    "TableReference",
    "get_sql_table_analyzer_service",
]
