"""Data Transfer Objects for SQL analysis results."""

from typing import Annotated

from pydantic import BaseModel, ConfigDict, Field


class TableReference(BaseModel):
    """Represents a physical table reference found in SQL statement."""

    model_config = ConfigDict(frozen=True)

    schema_name: Annotated[
        str, Field(description="PostgreSQL schema name (e.g., 'public')")
    ]
    table_name: Annotated[str, Field(description="Table name")]
    alias: Annotated[str | None, Field(description="Table alias used in the query")] = (
        None
    )
    fully_qualified_name: Annotated[str, Field(description="Full schema.table name")]


class SqlTableAnalysisResult(BaseModel):
    """Complete analysis result of SQL table extraction."""

    model_config = ConfigDict(frozen=True)

    distinct_tables: Annotated[
        list[TableReference],
        Field(description="All unique physical tables referenced"),
    ]
    sql_statement: Annotated[str, Field(description="Original SQL statement analyzed")]
    parsed_successfully: Annotated[
        bool, <PERSON>(description="Whether parsing succeeded without errors")
    ]
    errors: Annotated[
        list[str] | None, Field(description="Parse or analysis errors if any")
    ] = None
    total_table_count: Annotated[
        int, Field(description="Count of distinct physical tables")
    ]

    @property
    def all_table_references(self) -> list[TableReference]:
        """Get all table references (both physical tables and CTEs) for backward compatibility."""
        return [*self.distinct_tables]
