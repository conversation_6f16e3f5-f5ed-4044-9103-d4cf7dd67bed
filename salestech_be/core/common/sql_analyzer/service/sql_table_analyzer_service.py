"""PostgreSQL SQL table analyzer service with comprehensive schema integration."""

import sqlglot
import sqlglot.expressions as exp

from salestech_be.core.common.sql_analyzer.dto.sql_analysis_dto import (
    SqlTableAnalysisResult,
    TableReference,
)
from salestech_be.ree_logging import get_logger

logger = get_logger(__name__)

_dialect = "postgres"


class SqlTableAnalyzer:
    """
    Analyze SQL statement and extract all table references with comprehensive CTE handling.
    """

    async def analyze_sql_tables(
        self,
        sql_statement: str,
    ) -> SqlTableAnalysisResult:
        """
        Analyze SQL statement and extract all table references with comprehensive CTE handling.

        Args:
            sql_statement: PostgreSQL SQL statement to analyze

        Returns:
            Complete analysis result with table references and metadata
        """
        try:
            # Parse SQL statement into AST (syntactic only)
            parsed = sqlglot.parse_one(sql_statement, read=_dialect)

            # Extract all table references and CTEs separately
            physical_table_refs = self._extract_all_table_references(parsed)

            # Deduplicate physical tables
            distinct_tables = self._deduplicate_tables(physical_table_refs)

            return SqlTableAnalysisResult(
                distinct_tables=distinct_tables,
                sql_statement=sql_statement,
                parsed_successfully=True,
                errors=None,
                total_table_count=len(distinct_tables),
            )
        except Exception as e:
            logger.opt(exception=e).error("Unexpected error during SQL analysis")
            raise

    def _extract_all_table_references(
        self, parsed_sql: exp.Expression
    ) -> list[TableReference]:
        """
        Extract all table references using SQLGlot's optimized find_all traversal.

        This implementation correctly handles:
        - Physical tables in all contexts (main query, CTEs, subqueries, joins)
        - CTE references and their proper categorization
        - Tables with same names as CTEs (correctly distinguished by context)
        - Multiple references to the same table with different aliases
        - Nested CTEs and complex query structures
        - Schema-qualified table names
        - Recursive CTEs and self-referencing tables

        Uses SQLGlot's find_all() which is optimized and automatically traverses
        the entire AST including all nested structures.

        Returns:
            list[TableReference]: Physical tables
        """
        physical_table_refs: list[TableReference] = []

        # First, collect all CTE definitions and their internal expressions
        # Use normalized names for comparison (handles quoted vs unquoted identifiers)
        cte_definitions = {}
        for cte in parsed_sql.find_all(exp.CTE):
            if cte.alias_or_name:
                normalized_name = self._normalize_identifier_for_comparison(
                    cte.alias_or_name
                )
                cte_definitions[normalized_name] = cte
        cte_names = set(cte_definitions.keys())

        # Process all table references found in the entire SQL AST
        for table_node in parsed_sql.find_all(exp.Table):
            if not table_node.name:
                continue

            table_name = table_node.name
            schema_name = table_node.db or "public"
            alias = table_node.alias

            # Determine if this table reference is actually a CTE reference
            # A table is a CTE reference if:
            # 1. Its name matches a defined CTE, AND
            # 2. It's NOT inside the definition of that same CTE
            is_cte_reference = False
            normalized_table_name = self._normalize_identifier_for_comparison(
                table_name
            )
            if normalized_table_name in cte_names:
                # Check if this table reference is inside the definition of the CTE with the same name
                is_inside_own_cte_definition = self._is_table_inside_cte_definition(
                    table_node, cte_definitions[normalized_table_name]
                )
                is_cte_reference = not is_inside_own_cte_definition

            if not is_cte_reference:
                # This is a reference to a physical database table
                fully_qualified_name = f"{schema_name}.{table_name}"

                table_ref = TableReference(
                    schema_name=schema_name,
                    table_name=table_name,
                    alias=alias,
                    fully_qualified_name=fully_qualified_name,
                )
                physical_table_refs.append(table_ref)

        return physical_table_refs

    def _is_table_inside_cte_definition(
        self, table_node: exp.Table, cte_definition: exp.CTE
    ) -> bool:
        """
        Check if a table node is inside the definition of a specific CTE AND represents
        a source table (not a recursive reference).

        This helps distinguish between:
        - A table reference that is a source for the CTE (should be physical table)
        - A table reference that is a recursive CTE reference (should be CTE reference)
        - A table reference outside the CTE definition (should be CTE reference if name matches)
        """
        # Walk up the AST from the table node to see if we encounter the CTE definition
        current = table_node.parent

        while current:
            if current is cte_definition:
                # We are inside the CTE definition, but we need to check if this is a
                # recursive reference or a source table reference
                return self._is_source_table_in_cte(table_node, cte_definition)
            current = current.parent
        return False

    def _is_source_table_in_cte(  # noqa: C901, PLR0911
        self, table_node: exp.Table, cte_definition: exp.CTE
    ) -> bool:
        """
        Determine if a table reference within a CTE is a source table (physical)
        or a recursive/cross-CTE reference (should be CTE).

        For recursive CTEs, only base case references should be treated as physical.
        For regular CTEs, references to tables with the same name as the CTE are physical.
        """
        # Get normalized names for comparison (handle quoted vs unquoted identifiers)
        cte_name = self._normalize_identifier_for_comparison(
            cte_definition.alias_or_name
        )
        table_name = self._normalize_identifier_for_comparison(table_node.name)

        # If the table name doesn't match the CTE name, it's definitely a source table
        if table_name != cte_name:
            return True

        # If names match, check if this is a recursive CTE by looking for WITH RECURSIVE
        # Walk up the AST to find the WITH clause
        current = cte_definition.parent
        while current:
            if isinstance(current, exp.With):
                # Check if this is a recursive WITH by looking for the RECURSIVE keyword
                is_recursive = getattr(current, "recursive", False)
                if not is_recursive:
                    # Non-recursive CTE with same name - treat as physical table
                    return True
                break
            current = current.parent

        # For recursive CTEs, determine if we're in base case or recursive case
        cte_query = cte_definition.this if hasattr(cte_definition, "this") else None
        if not cte_query:
            return True

        # Look for UNION patterns to distinguish base case from recursive case
        unions = list(cte_query.find_all(exp.Union))
        if not unions:
            # No UNION in recursive CTE - treat as physical (base case)
            return True

        # Walk up to find which part of the UNION we're in
        current = table_node.parent
        while current and current != cte_query:
            # If we hit a Union node, check which side we're on
            for union in unions:
                if current == union.left:
                    # We're in the left side (base case) - treat as physical
                    return True
                elif current == union.right:
                    # We're in the right side (recursive case) - treat as CTE reference
                    return False
            current = current.parent

        # Default: if we can't determine, treat as physical (conservative approach)
        return True

    def _normalize_identifier_for_comparison(self, identifier: str | None) -> str:
        """
        Normalize identifier names for comparison following PostgreSQL rules.

        PostgreSQL identifier rules:
        - Unquoted identifiers are case-insensitive (folded to lowercase)
        - Quoted identifiers preserve case and special characters
        - Empty/None identifiers return empty string
        """
        if not identifier:
            return ""

        # Check if identifier is quoted (starts and ends with double quotes)
        if (
            identifier.startswith('"')
            and identifier.endswith('"')
            and len(identifier) > 1
        ):
            # Quoted identifier - preserve case, remove quotes
            return identifier[1:-1]
        else:
            # Unquoted identifier - fold to lowercase following PostgreSQL rules
            return identifier.lower()

    def _deduplicate_tables(
        self, table_refs: list[TableReference]
    ) -> list[TableReference]:
        """
        Remove duplicate table references based on fully qualified name.

        Preserves the first occurrence of each unique table.
        """
        seen = set()
        distinct_tables = []

        for table_ref in table_refs:
            if table_ref.fully_qualified_name not in seen:
                seen.add(table_ref.fully_qualified_name)
                distinct_tables.append(table_ref)

        return distinct_tables


def get_sql_table_analyzer_service() -> SqlTableAnalyzer:
    """Dependency injection factory for SqlTableAnalyzerService."""
    return SqlTableAnalyzer()
