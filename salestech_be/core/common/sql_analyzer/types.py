"""Type definitions and utilities for SQL analysis."""

import typing
from decimal import Decimal
from uuid import UUID

from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime


def python_type_to_postgres_type(python_type: typing.Any) -> str:  # type: ignore[explicit-any]
    """
    Convert Python/Pydantic types to PostgreSQL type strings.

    Args:
        python_type: The Python type to convert

    Returns:
        PostgreSQL type string compatible with SQLGlot
    """
    # Handle Union types (e.g., str | None)
    if hasattr(python_type, "__origin__"):
        if python_type.__origin__ is typing.Union:
            # Get the non-None type from Union
            args = python_type.__args__
            non_none_types = [arg for arg in args if arg is not type(None)]
            if non_none_types:
                # Use the first non-None type
                python_type = non_none_types[0]
        elif python_type.__origin__ is list:
            # Handle list types (e.g., list[str] -> TEXT[])
            inner_type = python_type.__args__[0] if python_type.__args__ else str
            base_type = python_type_to_postgres_type(inner_type)
            return f"{base_type}[]"

    # Direct type mappings
    type_mapping = {
        str: "TEXT",
        int: "INTEGER",
        float: "DOUBLE PRECISION",
        bool: "BOOLEAN",
        UUID: "UUID",
        Decimal: "NUMERIC",
        ZoneRequiredDateTime: "TIMESTAMPTZ",
        bytes: "BYTEA",
        dict: "JSONB",
        list: "JSONB",  # Default for generic lists
    }

    # Handle string representations of types
    if isinstance(python_type, str):
        string_mappings = {
            "str": "TEXT",
            "int": "INTEGER",
            "float": "DOUBLE PRECISION",
            "bool": "BOOLEAN",
            "UUID": "UUID",
            "Decimal": "NUMERIC",
            "ZoneRequiredDateTime": "TIMESTAMPTZ",
            "bytes": "BYTEA",
            "dict": "JSONB",
            "list": "JSONB",
        }
        return string_mappings.get(python_type, "TEXT")

    # Get the mapped type or default to TEXT
    return type_mapping.get(python_type, "TEXT")


def extract_type_from_annotation(annotation: typing.Any) -> type:  # type: ignore[explicit-any]
    """
    Extract the actual type from a complex type annotation.

    Args:
        annotation: The type annotation to analyze

    Returns:
        The extracted Python type
    """
    # Handle typing constructs
    if hasattr(annotation, "__origin__"):
        origin = annotation.__origin__

        # Handle Union types
        if origin is typing.Union:
            args = annotation.__args__
            # Return the first non-None type
            for arg in args:
                if arg is not type(None):
                    return extract_type_from_annotation(arg)

        # Handle generic types like list[str], dict[str, int]
        elif origin in (list, dict, tuple, set):
            return origin  # type: ignore[no-any-return]

    # Handle string type hints
    if isinstance(annotation, str):
        # This would require eval() which is unsafe, so return str as fallback
        return str

    # Return the annotation if it's already a type
    if isinstance(annotation, type):
        return annotation

    # Fallback to str for unknown annotations
    return str
