"""
This file contains the default deal funnel and deal stages for the application.
It's a temp location to make sure v1 and v2 default deal stages are compatible until
v1 is deprecated.
"""

from typing import Final, TypedDict

__all__ = [
    "display_name",
    "v1_default_deal_stages",
    "v2_default_deal_stages",
]
display_name: Final[str] = "Default Deal Funnel"


class DealStageValue(TypedDict):
    display_name: str
    probability: int
    is_closed: bool
    is_won: bool


default_deal_stages: Final[tuple[DealStageValue, ...]] = (
    {
        "display_name": "Prospecting",
        "probability": 5,
        "is_closed": False,
        "is_won": False,
    },
    {
        "display_name": "Meeting Set",
        "probability": 10,
        "is_closed": False,
        "is_won": False,
    },
    {
        "display_name": "Meeting Held",
        "probability": 20,
        "is_closed": False,
        "is_won": False,
    },
    {
        "display_name": "Negotiation",
        "probability": 45,
        "is_closed": False,
        "is_won": False,
    },
    {
        "display_name": "Contract Out",
        "probability": 85,
        "is_closed": False,
        "is_won": False,
    },
)

v1_default_deal_stages: Final[tuple[DealStageValue, ...]] = (*default_deal_stages,)
v2_default_deal_stages: Final[tuple[DealStageValue, ...]] = (
    *default_deal_stages,
    {
        "display_name": "Closed Lost",
        "probability": 0,
        "is_closed": True,
        "is_won": False,
    },
    {
        "display_name": "Closed Won",
        "probability": 100,
        "is_closed": True,
        "is_won": True,
    },
)
