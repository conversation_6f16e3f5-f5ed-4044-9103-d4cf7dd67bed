"""
This file contains the default lead and lead stages for the application.
It's a temp location to make sure v1 and v2 default lead stages are compatible until
v1 is deprecated.
"""

from typing import Final, TypedDict

__all__ = [
    "display_name",
    "v2_default_lead_stages",
]
display_name: Final[str] = "Default Lead Stage"


class LeadStageValue(TypedDict):
    display_name: str
    is_converted: bool


v2_default_lead_stages: Final[tuple[LeadStageValue, ...]] = (
    {
        "display_name": "New",
        "is_converted": False,
    },
    {
        "display_name": "Contacted",
        "is_converted": False,
    },
    {
        "display_name": "Nurturing",
        "is_converted": False,
    },
    {
        "display_name": "Unqualified",
        "is_converted": False,
    },
    {
        "display_name": "Converted",
        "is_converted": True,
    },
)
