from collections import defaultdict
from dataclasses import dataclass
from datetime import UTC, datetime
from uuid import UUID

from salestech_be.core.common.converter import address_from_db
from salestech_be.core.common.types import BaseAISuggestedValue
from salestech_be.core.contact.types_v2 import (
    ContactAISuggestedDisplayName,
    ContactAISuggestedLinkedinUrl,
    ContactAISuggestedTitle,
)
from salestech_be.core.contact.types_v2 import (
    ContactV2 as DomainContactV2,
)
from salestech_be.core.metadata.converter import (
    contact_account_association_lite_from_db,
    contact_account_email_from_db,
    contact_email_lite_from_db,
    contact_phone_number_from_db,
    select_list_value_lite_from_db,
)
from salestech_be.db.dto.custom_object_data_dto import ExtensionCustomObjectDataGroupDto
from salestech_be.db.models.address import Address as DbAddress
from salestech_be.db.models.contact import (
    Contact as DbContact,
)
from salestech_be.db.models.contact_account_association import ContactAccountAssociation
from salestech_be.db.models.contact_email import (
    ContactEmail,
    ContactEmailAccountAssociation,
)
from salestech_be.db.models.contact_phone_number import (
    ContactPhoneNumber as DbContactPhoneNumber,
)
from salestech_be.db.models.core.types import CreatedSource
from salestech_be.db.models.select_list import SelectListValue
from salestech_be.util.validation import not_none


@dataclass
class ContactSelectListValueContainer:
    stage: SelectListValue


def _ai_value_is_more_recent(
    db_contact_updated_at: datetime | None,
    ai_suggested_value_updated_at: datetime | None,
) -> bool:
    if not ai_suggested_value_updated_at:
        return False
    db_contact_updated_at = db_contact_updated_at or datetime.min.replace(tzinfo=UTC)
    return ai_suggested_value_updated_at > db_contact_updated_at


def _is_using_ai_suggested_value(
    db_contact_value: str | int | float | None,
    db_contact_created_source: CreatedSource | None,
    db_contact_updated_at: datetime | None,
    ai_suggested_value: BaseAISuggestedValue | None = None,
) -> bool:
    if not ai_suggested_value or not ai_suggested_value.value:
        return False
    if db_contact_value is None:
        return True
    if (
        db_contact_created_source
        and db_contact_created_source == CreatedSource.CSV_IMPORT
    ):
        return db_contact_value is None
    if isinstance(ai_suggested_value, ContactAISuggestedDisplayName):
        # Note: do not override display name since it is used by FE for multiple purposes
        return False
    return _ai_value_is_more_recent(
        db_contact_updated_at, ai_suggested_value.updated_at
    )


def contact_v2_from_db(
    *,
    db_contact: DbContact,
    contact_emails: list[ContactEmail],
    contact_phone_numbers: list[DbContactPhoneNumber],
    db_address: DbAddress | None = None,
    extension_custom_object_data_group_dto: ExtensionCustomObjectDataGroupDto
    | None = None,
    contact_select_list_value_container: ContactSelectListValueContainer,
    contact_account_associations: list[ContactAccountAssociation] | None = None,
    contact_email_account_associations: list[ContactEmailAccountAssociation]
    | None = None,
    primary_contact_email: str | None = None,
    ai_suggested_linkedin_url: ContactAISuggestedLinkedinUrl | None = None,
    ai_suggested_display_name: ContactAISuggestedDisplayName | None = None,
    ai_suggested_title: ContactAISuggestedTitle | None = None,
) -> DomainContactV2:
    effective_stage_value = contact_select_list_value_container.stage
    primary_association = next(
        (
            association
            for association in contact_account_associations or []
            if association.is_primary
        ),
        None,
    )
    primary_account_id = primary_association.account_id if primary_association else None

    contact_email_account_associations_map: dict[
        UUID, list[ContactEmailAccountAssociation]
    ] = defaultdict(list)
    for contact_email_account_association in contact_email_account_associations or []:
        contact_email_account_associations_map[
            contact_email_account_association.contact_email_id
        ].append(contact_email_account_association)
    contact_emails_lite = []
    for contact_email in contact_emails:
        contact_email_account_association_list = (
            contact_email_account_associations_map.get(contact_email.id) or []
        )
        contact_account_emails = [
            contact_account_email_from_db(
                email=contact_email.email,
                contact_email_account_association=eaa,
            )
            for eaa in contact_email_account_association_list
        ]
        contact_emails_lite.append(
            contact_email_lite_from_db(
                contact_email=contact_email,
                contact_account_emails=contact_account_emails,
            )
        )

    contact_phone_numbers_lite = []
    for contact_phone_number in contact_phone_numbers:
        contact_phone_numbers_lite.append(
            contact_phone_number_from_db(db_contact_phone_number=contact_phone_number)
        )

    contact_account_associations_lite = []
    for contact_account_association in contact_account_associations or []:
        contact_account_associations_lite.append(
            contact_account_association_lite_from_db(
                contact_account_association=contact_account_association,
            )
        )
    return DomainContactV2(
        id=db_contact.id,
        display_name=not_none(not_none(ai_suggested_display_name).value)
        if _is_using_ai_suggested_value(
            db_contact.display_name,
            db_contact.created_source,
            db_contact.updated_at,
            ai_suggested_display_name,
        )
        else db_contact.display_name,
        stage_id=effective_stage_value.id,
        stage=select_list_value_lite_from_db(select_list_value=effective_stage_value),
        created_at=db_contact.created_at,
        owner_user_id=db_contact.owner_user_id,
        organization_id=db_contact.organization_id,
        access_status=db_contact.access_status,
        first_name=not_none(ai_suggested_display_name).get_first_name(
            not_none(ai_suggested_display_name).value
        )
        if _is_using_ai_suggested_value(
            db_contact.display_name,  # decide by the full display name
            db_contact.created_source,
            db_contact.updated_at,
            ai_suggested_display_name,
        )
        else db_contact.first_name,
        last_name=not_none(ai_suggested_display_name).get_last_name(
            not_none(ai_suggested_display_name).value
        )
        if _is_using_ai_suggested_value(
            db_contact.display_name,  # decide by the full display name
            db_contact.created_source,
            db_contact.updated_at,
            ai_suggested_display_name,
        )
        else db_contact.last_name,
        middle_name=not_none(ai_suggested_display_name).get_middle_name(
            not_none(ai_suggested_display_name).value
        )
        if _is_using_ai_suggested_value(
            db_contact.display_name,  # decide by the full display name
            db_contact.created_source,
            db_contact.updated_at,
            ai_suggested_display_name,
        )
        else db_contact.middle_name,
        primary_email=primary_contact_email,
        primary_phone_number=db_contact.primary_phone_number,
        linkedin_url=not_none(not_none(ai_suggested_linkedin_url).value)
        if _is_using_ai_suggested_value(
            db_contact.linkedin_url,
            db_contact.created_source,
            db_contact.updated_at,
            ai_suggested_linkedin_url,
        )
        else db_contact.linkedin_url,
        zoominfo_url=db_contact.zoominfo_url,
        facebook_url=db_contact.facebook_url,
        x_url=db_contact.x_url,
        address=address_from_db(db_address=db_address) if db_address else None,
        title=not_none(not_none(ai_suggested_title).value)
        if _is_using_ai_suggested_value(
            db_contact.title,
            db_contact.created_source,
            db_contact.updated_at,
            ai_suggested_title,
        )
        else db_contact.title,
        department=db_contact.department,
        updated_at=db_contact.updated_at or db_contact.created_at,
        primary_account_id=primary_account_id,
        # additional_account_ids=additional_account_ids,
        created_source=db_contact.created_source,
        contact_emails=contact_emails_lite,
        contact_phone_numbers=contact_phone_numbers_lite,
        contact_account_associations=contact_account_associations_lite,
        # created_source=db_contact.created_source,
        created_by_user_id=db_contact.created_by_user_id,
        updated_by_user_id=db_contact.updated_by_user_id,
        archived_at=db_contact.archived_at,
        archived_by_user_id=db_contact.archived_by_user_id,
        integrity_job_started_at=db_contact.integrity_job_started_at,
        integrity_job_started_by_user_id=db_contact.integrity_job_started_by_user_id,
        integrity_job_started_by_job_ids=db_contact.integrity_job_started_by_job_ids,
        integrity_job_finished_at=db_contact.integrity_job_finished_at,
        integrity_job_finished_by_user_id=db_contact.integrity_job_finished_by_user_id,
        email_enriched_at=db_contact.email_enriched_at,
        phone_enriched_at=db_contact.phone_enriched_at,
        custom_field_data=extension_custom_object_data_group_dto.to_generic_custom_field_value(
            extension_id=db_contact.id
        )
        if extension_custom_object_data_group_dto
        else None,
        person_id=db_contact.person_id,
        custom_field_data_v2=extension_custom_object_data_group_dto.to_typed_custom_field_data(
            extension_id=db_contact.id
        )
        if extension_custom_object_data_group_dto
        else None,
        participant_user_id_list=[
            participant.user_id for participant in db_contact.participants or []
        ],
        ai_suggested_linkedin_url=ai_suggested_linkedin_url,
        ai_suggested_display_name=ai_suggested_display_name,
        ai_suggested_title=ai_suggested_title,
    )
