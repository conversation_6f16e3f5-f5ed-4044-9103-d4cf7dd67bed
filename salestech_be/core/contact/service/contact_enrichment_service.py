from dataclasses import dataclass
from typing import Annotated
from uuid import UUID

from fastapi import Depends

from salestech_be.common.singleton import Singleton
from salestech_be.core.contact.service.contact_service import (
    ContactService,
    get_contact_service,
)
from salestech_be.core.contact.service_api_schema import (
    BulkEnrichContactsResponse,
    EnrichContactResultLite,
    PatchContactRequest,
)
from salestech_be.core.contact.types_v2 import ContactV2
from salestech_be.core.prospecting.prospecting_enrichment_service import (
    ProspectingEnrichmentService,
    get_prospecting_enrichment_service,
    get_prospecting_enrichment_service_by_db_engine,
)
from salestech_be.core.prospecting.type.person_type_v2 import BulkEnrichPersonRequest
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.dto.person_dto import PersonDto
from salestech_be.db.models.contact import (
    CreateDbContactEmailRequest,
    CreateDbContactPhoneNumberRequest,
)
from salestech_be.db.models.person import ProspectingEnrichStatus
from salestech_be.ree_logging import get_logger

logger = get_logger()


@dataclass
class EnrichmentRequestInfo:
    """Information needed for contact enrichment requests."""

    enrich_requests: list[BulkEnrichPersonRequest]
    contact_to_account_map: dict[UUID, UUID]


class ContactEnrichmentService:
    def __init__(
        self,
        contact_service: Annotated[ContactService, Depends()],
        prospecting_enrichment_service: Annotated[
            ProspectingEnrichmentService, Depends(get_prospecting_enrichment_service)
        ],
    ):
        super().__init__()
        self.contact_service = contact_service
        self.prospecting_enrichment_service = prospecting_enrichment_service
        self.logger = logger

    async def _prepare_enrichment_requests(
        self, contacts: list[ContactV2], organization_id: UUID
    ) -> EnrichmentRequestInfo:
        """Prepare enrichment requests by gathering company information from accounts."""
        contact_to_account_map = {}
        account_ids = set()

        # Extract primary account information for context
        for contact in contacts:
            if not contact.contact_account_associations:
                continue

            primary_associations = [
                assoc
                for assoc in contact.contact_account_associations
                if assoc.is_primary
            ]

            if primary_associations:
                primary_account_id = primary_associations[0].account_id
                contact_to_account_map[contact.id] = primary_account_id
                account_ids.add(primary_account_id)

        # Load account data to get company names
        db_accounts = (
            await self.contact_service.account_repository.list_by_ids(
                list(account_ids), organization_id=organization_id
            )
            if account_ids
            else []
        )

        account_lookup = {account.id: account for account in db_accounts}

        # Prepare enrichment requests for all contacts
        enrich_requests = []
        for contact in contacts:
            # Get company name from primary account if available
            company_name = None
            if contact.id in contact_to_account_map:
                account_id = contact_to_account_map[contact.id]
                account = account_lookup.get(account_id)
                if account:
                    company_name = account.display_name

            # Create enrichment request with all available information
            enrich_requests.append(
                BulkEnrichPersonRequest(
                    contact_id=contact.id,
                    first_name=contact.first_name,
                    last_name=contact.last_name,
                    display_name=contact.display_name,
                    email=contact.primary_email,
                    linkedin_url=contact.linkedin_url,
                    company_name=company_name,
                    person_id=contact.person_id,
                    has_email=bool(contact.primary_email or contact.contact_emails),
                    has_phone_number=bool(
                        contact.primary_phone_number or contact.contact_phone_numbers
                    ),
                )
            )

        return EnrichmentRequestInfo(
            enrich_requests=enrich_requests,
            contact_to_account_map=contact_to_account_map,
        )

    def _calculate_prospection_cost(
        self, contacts: list[ContactV2], enrich_phone_numbers: bool
    ) -> tuple[list[ContactV2], int, int]:
        """
        Calculate the cost of enriching contacts with prospection data.

        Args:
            contacts: List of contacts to enrich
            enrich_phone_numbers: Whether to enrich phone numbers

        Returns:
            Tuple containing:
                - list of contacts that need enrichment
                - estimate_email_credits: Estimated email credits needed
                - estimate_phone_credits: Estimated phone credits needed
        """
        total_enrichable_contact_ids: list[ContactV2] = []
        estimate_email_credits = 0
        estimate_phone_credits = 0

        for contact in contacts:
            has_email = bool(contact.primary_email or contact.contact_emails)
            has_phone = bool(
                contact.primary_phone_number or contact.contact_phone_numbers
            )

            # If both email and phone exist, this contact doesn't need enrichment
            if has_email and has_phone:
                continue

            # If email exists but phone doesn't
            if has_email and not has_phone:
                if enrich_phone_numbers:
                    total_enrichable_contact_ids.append(contact)
                    estimate_phone_credits += 3
                # If not enriching phone numbers, skip this contact
                continue

            # If email doesn't exist
            if not has_email:
                total_enrichable_contact_ids.append(contact)
                estimate_email_credits += 1

                # Additionally add phone credits if enriching phones
                if enrich_phone_numbers:
                    estimate_phone_credits += 3

        return (
            total_enrichable_contact_ids,
            estimate_email_credits,
            estimate_phone_credits,
        )

    async def process_enriched_results(
        self,
        organization_id: UUID,
        user_id: UUID,
        total_contact_ids: list[UUID],
        enrichable_contact_ids: list[UUID],
        enriched_person_map: dict[UUID, PersonDto | None],
        enrich_phone_numbers: bool = False,
    ) -> BulkEnrichContactsResponse:
        total_enrichable_contacts = await self.contact_service.list_contacts_v2(
            organization_id=organization_id,
            only_include_contact_ids=set(enrichable_contact_ids),
        )
        # Create lookup map for enrichable contacts by ID for quick access later
        contact_map = {contact.id: contact for contact in total_enrichable_contacts}

        # Process results and update contacts
        success_contact_ids = await self._process_enrichment_results(
            enriched_person_map=enriched_person_map,
            contact_map=contact_map,
            organization_id=organization_id,
            user_id=user_id,
        )
        self.logger.bind(
            total_contact_ids=total_contact_ids,
            enrichable_contact_ids=enrichable_contact_ids,
            enrich_phone_numbers=enrich_phone_numbers,
            success_contact_ids=success_contact_ids,
        ).info("Contact Enrichment service, process_enriched_results")
        # Update enrichment timestamp for contacts that successfully enriched phone or email
        if success_contact_ids:
            await self.contact_service.contact_repository.update_contacts_enrichment_timestamp(
                organization_id=organization_id,
                contact_ids=list(success_contact_ids),
                phone_enriched=enrich_phone_numbers,
            )
        # Prepare result data for each requested contact
        return self._prepare_enrichment_response(
            contact_ids=total_contact_ids,
            enriched_person_map=enriched_person_map,
            success_contact_ids=success_contact_ids,
        )

    def _prepare_enrichment_response(
        self,
        contact_ids: list[UUID],
        enriched_person_map: dict[UUID, PersonDto | None],
        success_contact_ids: set[UUID],
    ) -> BulkEnrichContactsResponse:
        """Prepare the final enrichment response for all requested contacts."""
        result_data = {}

        for contact_id in contact_ids:
            person_dto = enriched_person_map.get(contact_id)

            if person_dto:
                # Extract email and phone from the PersonDto
                db_person = person_dto.db_person
                email = (
                    db_person.work_email
                    if db_person.work_email_enrich_status
                    == ProspectingEnrichStatus.ENRICHED
                    else None
                )
                phone = (
                    db_person.phone_numbers[0].number
                    if (
                        db_person.phone_number_enrich_status
                        == ProspectingEnrichStatus.ENRICHED
                        and db_person.phone_numbers
                        and len(db_person.phone_numbers) > 0
                    )
                    else None
                )

                # Set error message if enrichment succeeded but provided no data
                error = (
                    "enrich success but no result"
                    if email is None and phone is None
                    else None
                )

                result_data[contact_id] = EnrichContactResultLite(
                    email=email, phone_number=phone, error=error
                )
            else:
                # If contact wasn't enriched
                result_data[contact_id] = EnrichContactResultLite(
                    email=None, phone_number=None, error="Failed to enrich contact"
                )

        return BulkEnrichContactsResponse(data=result_data)

    async def _add_enriched_phone(
        self,
        contact_id: UUID,
        organization_id: UUID,
        user_id: UUID,
        phone_number: str,
        has_primary_phone: bool,
    ) -> bool:
        """Add an enriched phone number to a contact."""
        try:
            # Set as primary if contact doesn't have a primary phone yet
            set_as_primary = not has_primary_phone

            phone_req = CreateDbContactPhoneNumberRequest(
                phone_number=phone_number,
                is_contact_primary=set_as_primary,
            )

            await self.contact_service.batch_upsert_contact_phone_number(
                organization_id=organization_id,
                user_id=user_id,
                contact_id=contact_id,
                create_contact_phone_number_requests=[phone_req],
            )

            self.logger.bind(
                contact_id=contact_id,
                organization_id=organization_id,
                phone=phone_number,
                set_as_primary=set_as_primary,
            ).info(
                f"Added enriched phone number to contact{' as primary' if set_as_primary else ''}"
            )
            return True
        except Exception as e:
            self.logger.bind(
                contact_id=contact_id,
                organization_id=organization_id,
                phone=str(phone_number),
                error=str(e),
            ).error("Failed to add enriched phone number to contact")
            return False

    async def _add_enriched_email(
        self,
        contact_id: UUID,
        organization_id: UUID,
        user_id: UUID,
        work_email: str,
        has_primary_email: bool,
    ) -> bool:
        """Add an enriched email to a contact."""
        try:
            # Set as primary if contact doesn't have a primary email yet
            set_as_primary = not has_primary_email

            email_req = CreateDbContactEmailRequest(
                email=work_email.lower(),
                is_contact_primary=set_as_primary,
            )

            await self.contact_service.batch_upsert_contact_email(
                organization_id=organization_id,
                user_id=user_id,
                contact_id=contact_id,
                create_contact_email_requests=[email_req],
                overwrite_archived=True,
            )

            self.logger.bind(
                contact_id=contact_id,
                organization_id=organization_id,
                email=work_email,
                set_as_primary=set_as_primary,
            ).info(
                f"Added enriched email to contact{' as primary' if set_as_primary else ''}"
            )
            return True
        except Exception as e:
            self.logger.bind(
                contact_id=contact_id,
                organization_id=organization_id,
                email=work_email,
                error=str(e),
            ).error("Failed to add enriched email to contact")
            return False

    async def _process_enrichment_results(
        self,
        enriched_person_map: dict[UUID, PersonDto | None],
        contact_map: dict[UUID, ContactV2],
        organization_id: UUID,
        user_id: UUID,
    ) -> set[UUID]:
        """Process enrichment results and update contacts with new data."""
        success_contact_ids = set()

        for contact_id, person_dto in enriched_person_map.items():
            if not person_dto:
                continue

            db_person = person_dto.db_person
            email_enriched = (
                db_person.work_email_enrich_status == ProspectingEnrichStatus.ENRICHED
            )
            phone_enriched = (
                db_person.phone_number_enrich_status == ProspectingEnrichStatus.ENRICHED
            )

            # Skip if nothing was actually enriched
            if not (email_enriched or phone_enriched):
                continue

            # Only update contacts that needed enrichment (were in contact_map)
            if not (contact := contact_map.get(contact_id)):
                self.logger.bind(
                    contact_id=contact_id, organization_id=organization_id
                ).warning("Contact not found in loaded contacts map during enrichment")
                continue

            has_primary_email = bool(contact.primary_email)
            has_primary_phone = bool(contact.primary_phone_number)

            # Add enriched email if available
            if email_enriched and db_person.work_email:  # noqa: SIM102
                if await self._add_enriched_email(
                    contact_id=contact_id,
                    organization_id=organization_id,
                    user_id=user_id,
                    work_email=db_person.work_email,
                    has_primary_email=has_primary_email,
                ):
                    success_contact_ids.add(contact_id)

            # Add enriched phone number if available
            if (  # noqa: SIM102
                phone_enriched
                and db_person.phone_numbers
                and len(db_person.phone_numbers) > 0
            ):
                if await self._add_enriched_phone(
                    contact_id=contact_id,
                    organization_id=organization_id,
                    user_id=user_id,
                    phone_number=db_person.phone_numbers[0].number,
                    has_primary_phone=has_primary_phone,
                ):
                    success_contact_ids.add(contact_id)

            # If the contact doesn't have a person_id, set it to the person_id from the enrichment result
            if not contact.person_id:
                await self.contact_service.update_entity(
                    organization_id=organization_id,
                    user_id=user_id,
                    entity=contact,
                    request=PatchContactRequest(
                        person_id=db_person.id,
                    ),
                )

        return success_contact_ids

    async def enrich_contact_by_ids(
        self,
        contact_ids: list[UUID],
        user_id: UUID,
        organization_id: UUID,
        enrich_phone_numbers: bool = False,
        preview: bool = False,
        async_run: bool = False,
    ) -> BulkEnrichContactsResponse:
        """Enrich contacts with additional data like emails and phone numbers.

        This function handles enrichment of contact data by finding and adding missing
        email addresses and phone numbers. It can operate in two modes:
        - Preview mode: Returns cost estimate without performing actual enrichment
        - Execution mode: Performs actual enrichment and returns results

        The function will:
        1. Calculate the contacts that need enrichment and estimate cost
        2. If in preview mode, return just the cost estimate
        3. If in execution mode:
           - Gather company information from primary accounts
           - Create enrichment requests for all eligible contacts
           - Send enrichment requests to prospecting service
           - Process results and add new emails/phone numbers to contacts
           - Return the enrichment results for all contacts

        Args:
            contact_ids: List of contact IDs to enrich
            user_id: ID of the user requesting enrichment
            organization_id: ID of the organization owning the contacts
            enrich_phone_numbers: Whether to enrich phone numbers in addition to emails
            preview: If True, only calculate and return cost estimates without performing enrichment
            async_run: if this function should be run async

        Returns:
            BulkEnrichContactsResponse: Contains enrichment results or cost estimates
            - In preview mode: contains cost estimates only
            - In execution mode: contains enrichment results for each contact

        Raises:
            Various exceptions caught and logged during processing of individual contacts
        """
        # If no contacts provided or none found, return empty response
        if not contact_ids or not (
            contacts
            := await self.contact_service.contact_query_service.list_contacts_v2(
                organization_id=organization_id,
                only_include_contact_ids=set(contact_ids),
            )
        ):
            self.logger.bind(
                organization_id=organization_id, contact_ids=contact_ids
            ).info("No contacts found to enrich")
            return BulkEnrichContactsResponse(data={})

        # Calculate which contacts need enrichment and the associated costs
        total_enrichable_contacts, estimate_email_credits, estimate_phone_credits = (
            self._calculate_prospection_cost(
                contacts=contacts, enrich_phone_numbers=enrich_phone_numbers
            )
        )

        # If preview mode, just return the cost estimates without performing enrichment
        if preview:
            return BulkEnrichContactsResponse(
                total_enrichable_contacts_count=len(total_enrichable_contacts),
                estimate_email_credits=estimate_email_credits,
                estimate_phone_credits=estimate_phone_credits,
            )

        # If no contacts need enrichment, return empty response
        if not total_enrichable_contacts:
            self.logger.bind(
                organization_id=organization_id, contact_count=len(contacts)
            ).info("No contacts need enrichment")
            return BulkEnrichContactsResponse(
                total_enrichable_contacts_count=0,
                estimate_email_credits=0,
                estimate_phone_credits=0,
            )

        # Prepare enrichment requests
        enrichment_info = await self._prepare_enrichment_requests(
            contacts=total_enrichable_contacts, organization_id=organization_id
        )

        self.logger.info(
            f"Starting contact enrichment, org:{organization_id}, enrich_phone:{enrich_phone_numbers}, enrich_size:{len(enrichment_info.enrich_requests)}"
        )

        if not async_run:
            result = await self.prospecting_enrichment_service.bulk_enrich_contact(
                organization_id=organization_id,
                user_id=user_id,
                enrich_requests=enrichment_info.enrich_requests,
                enrich_phone_numbers=enrich_phone_numbers,
            )
            return await self.process_enriched_results(
                organization_id=organization_id,
                user_id=user_id,
                total_contact_ids=contact_ids,
                enrichable_contact_ids=[c.id for c in total_enrichable_contacts],
                enriched_person_map=result,
                enrich_phone_numbers=enrich_phone_numbers,
            )

        # Send enrichment requests to prospecting service
        run_id = await self.prospecting_enrichment_service.bulk_enrich_contact_async(
            organization_id=organization_id,
            user_id=user_id,
            enrich_requests=enrichment_info.enrich_requests,
            enrich_phone_numbers=enrich_phone_numbers,
        )
        return BulkEnrichContactsResponse(
            total_enrichable_contacts_count=len(total_enrichable_contacts),
            estimate_email_credits=estimate_email_credits,
            estimate_phone_credits=estimate_phone_credits,
            prospecting_job_id=run_id,
        )


class SingletonContactEnrichmentService(Singleton, ContactEnrichmentService):
    pass


def get_contact_enrichment_service(
    db_engine: DatabaseEngine,
) -> ContactEnrichmentService:
    if SingletonContactEnrichmentService.has_instance():
        return SingletonContactEnrichmentService.get_singleton_instance()
    return SingletonContactEnrichmentService(
        contact_service=get_contact_service(db_engine=db_engine),
        prospecting_enrichment_service=get_prospecting_enrichment_service_by_db_engine(
            db_engine=db_engine
        ),
    )
