from collections import defaultdict
from typing import Annotated, Self, cast
from uuid import UUID

from fastapi import Depends
from pydantic import BaseModel, model_validator

from salestech_be.common.singleton import Singleton
from salestech_be.common.type.contact import ContactChannelType
from salestech_be.common.type.formatted_string import EmailStrLower
from salestech_be.core.contact.service.contact_query_service import (
    ContactQueryService,
    get_contact_query_service,
)
from salestech_be.core.contact.service_api_schema import ContactIdDisplayNameResult
from salestech_be.core.contact.types_v2 import ContactV2
from salestech_be.db.dao.contact_repository import ContactRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.contact_email import ContactEmail
from salestech_be.ree_logging import get_logger
from salestech_be.util.validation import not_none

logger = get_logger()


class ResolveContactInfos(BaseModel):
    contact_ids: set[UUID] | None = None
    contact_email_map: dict[U<PERSON><PERSON>, EmailStrLower | None] | None = None
    contact_account_map: dict[UUID, UUID | None] | None = None

    @model_validator(mode="after")
    def _validate_only_one_map(self) -> Self:
        map_cnt = 0
        for info_map in [
            self.contact_ids,
            self.contact_email_map,
            self.contact_account_map,
        ]:
            if info_map:
                map_cnt += 1
        if map_cnt > 1:
            raise ValueError("Only one parameter is allowed.")
        if not map_cnt:
            raise ValueError("At least one param should be provided.")
        return self


class ResolveEmailResult(BaseModel):
    contact_id: UUID | None
    account_id: UUID | None


class ContactResolveService:
    def __init__(
        self,
        contact_repository: Annotated[ContactRepository, Depends()],
        contact_query_service: Annotated[ContactQueryService, Depends()],
    ):
        super().__init__()
        self.contact_repository = contact_repository
        self.contact_query_service = contact_query_service

    async def resolve_contact_by_emails(
        self,
        *,
        organization_id: UUID,
        emails: set[EmailStrLower],
    ) -> dict[EmailStrLower, ContactV2 | None]:
        email_contact_map: dict[EmailStrLower, ContactV2 | None] = dict.fromkeys(
            emails, None
        )
        contact_emails = await self.contact_query_service.list_contact_emails_by_emails(
            organization_id=organization_id,
            emails=emails,
        )
        contacts = await self.contact_query_service.list_contacts_v2(
            organization_id=organization_id,
            only_include_contact_ids={ce.contact_id for ce in contact_emails},
        )
        contact_map = {c.id: c for c in contacts}
        for contact_email in contact_emails:
            email = contact_email.email
            contact_id = contact_email.contact_id
            email_contact_map[email] = contact_map.get(contact_id)
        return email_contact_map

    async def resolve_contact_id_by_emails(
        self,
        *,
        organization_id: UUID,
        emails: set[EmailStrLower],
    ) -> dict[EmailStrLower, UUID | None]:
        email_contact_id_map: dict[EmailStrLower, UUID | None] = dict.fromkeys(
            emails, None
        )
        contact_emails = await self.contact_query_service.list_contact_emails_by_emails(
            organization_id=organization_id,
            emails=emails,
        )
        for contact_email in contact_emails:
            email = contact_email.email
            contact_id = contact_email.contact_id
            email_contact_id_map[email] = contact_id
        return email_contact_id_map

    async def resolve_contact_id_display_name_by_emails(
        self,
        *,
        organization_id: UUID,
        emails: set[EmailStrLower],
    ) -> dict[EmailStrLower, ContactIdDisplayNameResult | None]:
        resolved_contact_id_display_name_map: dict[
            EmailStrLower, ContactIdDisplayNameResult | None
        ] = dict.fromkeys(emails, None)
        contact_id_display_name_result = (
            await self.contact_repository.find_contact_id_display_name_by_emails(
                organization_id=organization_id,
                emails=emails,
            )
        )
        for r in contact_id_display_name_result:
            resolved_contact_id_display_name_map[r.from_email] = (
                ContactIdDisplayNameResult(
                    contact_id=r.id,
                    display_name=r.display_name,
                )
            )
        return resolved_contact_id_display_name_map

    async def resolve_email_by_contact_ids(
        self,
        *,
        organization_id: UUID,
        contact_ids: set[UUID],
    ) -> dict[UUID, EmailStrLower]:
        resolved_contact_id_email_map: dict[UUID, EmailStrLower] = defaultdict()
        contact_id_contact_emails_map = (
            await self.contact_query_service.list_contact_emails_by_contact_ids(
                organization_id=organization_id,
                contact_ids=contact_ids,
            )
        )
        for contact_id, contact_emails in contact_id_contact_emails_map.items():
            for contact_email in contact_emails:
                if (
                    contact_id not in resolved_contact_id_email_map
                    or contact_email.is_contact_primary
                ):
                    resolved_contact_id_email_map[contact_id] = contact_email.email

        return resolved_contact_id_email_map

    async def get_pipeline_ids_by_contact_ids(
        self, contact_ids: list[UUID], organization_id: UUID
    ) -> UUID | None:
        pipeline_ids: set[UUID] = set()
        for contact_id in contact_ids:
            pipelines = (
                await self.contact_query_service.list_active_pipelines_by_contact_id(
                    organization_id=organization_id,
                    contact_id=contact_id,
                )
            )
            for pipeline in pipelines:
                pipeline_ids.add(pipeline.pipeline_id)

        if len(pipeline_ids) == 1:
            return next(iter(pipeline_ids))

        if len(pipeline_ids) > 1:
            logger.bind(contact_ids=contact_ids, pipeline_ids=pipeline_ids).warning(
                "multiple pipelines found across contacts"
            )
            return next(
                iter(pipeline_ids)
            )  # TODO: This is a temporary fix. Need to handle multiple pipelines
        else:
            logger.info("No active pipelines found for contact ids")
        return None

    async def batch_resolve_relevant_email_account_by_contact_info(
        self,
        organization_id: UUID,
        resolve_contact_infos: ResolveContactInfos,
    ) -> dict[UUID, tuple[EmailStrLower | None, UUID | None]]:
        contact_account_pairs: list[tuple[UUID, UUID | None]] = []
        if resolve_contact_infos.contact_ids:
            contact_account_pairs = [
                (contact_id, None) for contact_id in resolve_contact_infos.contact_ids
            ]
        if resolve_contact_infos.contact_account_map:
            contact_account_pairs = list(
                resolve_contact_infos.contact_account_map.items()
            )
        if contact_account_pairs:
            return (
                await self.resolve_relevant_contact_email_by_contact_and_account_pairs(
                    organization_id=organization_id,
                    contact_account_pairs=contact_account_pairs,
                )
            )

        contact_email_pairs: list[tuple[UUID | None, str | None]] = list(
            not_none(resolve_contact_infos.contact_email_map).items()
        )
        return await self._batch_resolve_relevant_account_by_contact_and_email_pairs(
            organization_id=organization_id,
            contact_email_pairs=contact_email_pairs,
        )

    async def batch_resolve_relevant_contact_info_by_email(
        self,
        organization_id: UUID,
        emails: list[EmailStrLower],
    ) -> dict[EmailStrLower, ResolveEmailResult]:
        contact_emails = await self.contact_query_service.list_contact_emails_by_emails(
            organization_id=organization_id,
            emails=set(emails),
        )
        email_contact_map = {
            contact_email.email: contact_email.contact_id
            for contact_email in contact_emails
        }

        contact_email_account_association_map = await self.contact_query_service.get_primary_contact_email_account_associations_by_emails(
            organization_id=organization_id,
            emails=list(email_contact_map.keys()),
        )

        email_account_map: dict[EmailStrLower, UUID | None] = defaultdict()
        for (
            email,
            contact_email_account_associations,
        ) in contact_email_account_association_map.items():
            if not contact_email_account_associations:
                continue
            contact_email_account_associations.sort(
                key=lambda info: info.created_at, reverse=True
            )
            email_account_map[email] = contact_email_account_associations[0].account_id

        miss_emails = {email for email in emails if email not in email_account_map}
        if miss_emails:
            primary_account_id_map = await self._get_primary_account_ids_by_contact_ids(
                organization_id=organization_id,
                contact_ids=set(email_contact_map.values()),
            )
            for email in miss_emails:
                contact_id = email_contact_map.get(email)
                if not contact_id:
                    continue
                email_account_map[email] = primary_account_id_map.get(contact_id)

        return {
            email: ResolveEmailResult(
                contact_id=email_contact_map.get(email),
                account_id=email_account_map.get(email),
            )
            for email in emails
        }

    async def resolve_relevant_account_by_contact_and_email(
        self, organization_id: UUID, contact_id: UUID, email: EmailStrLower
    ) -> UUID | None:
        """
        - If email is passed in
            - find accounts that is directly associated with the email
                - return the account where the email is the preferred email
                - if multiple account qualify → return the most recently created one
            - if non account qualify
                - fallback → primary_account of the contact
        - if email is not passed in
            - fallback → primary_account of the contact
        """
        contact_email_account_association_map = await self.contact_query_service.get_contact_email_account_associations_by_emails(
            organization_id=organization_id,
            emails=[email],
        )
        contact_email_account_associations = (
            contact_email_account_association_map.get(email) or []
        )

        contact_email_account_associations.sort(
            key=lambda info: info.created_at, reverse=True
        )
        for contact_email_account_association in contact_email_account_associations:
            if (
                contact_email_account_association.contact_id == contact_id
                and contact_email_account_association.is_contact_account_primary
            ):
                return contact_email_account_association.account_id

        return await self._get_primary_account_id_by_contact_id(
            organization_id=organization_id,
            contact_id=contact_id,
        )

    async def resolve_relevant_account_by_contact_and_email_pairs(
        self,
        organization_id: UUID,
        contact_email_pairs: list[tuple[UUID | None, str | None]],
    ) -> dict[UUID, UUID | None]:
        res_pair_map = (
            await self._batch_resolve_relevant_account_by_contact_and_email_pairs(
                organization_id=organization_id,
                contact_email_pairs=contact_email_pairs,
            )
        )
        return {
            contact_id: account_id
            for contact_id, (email, account_id) in res_pair_map.items()
        }

    async def _batch_resolve_relevant_account_by_contact_and_email_pairs(
        self,
        organization_id: UUID,
        contact_email_pairs: list[tuple[UUID | None, str | None]],
    ) -> dict[UUID, tuple[EmailStrLower | None, UUID | None]]:
        """
        - If email is passed in
            - find accounts that is directly associated with the email, and the email is the preferred one
                - return the account where the email is the preferred email
                - if multiple account qualify → return the most recently created one
            - find accounts that is directly associated with the email, and the email is not the preferred one
                - return the account where the email is not the preferred email
                - if multiple account qualify → return the most recently created one
            - if non account qualify
                - fallback → primary_account of the contact
        - if email is not passed in
            - fallback → primary_account of the contact
        """
        email_contact_map: dict[EmailStrLower, UUID] = {
            email: contact_id
            for contact_id, email in contact_email_pairs
            if email and contact_id
        }
        contact_email_account_association_map = await self.contact_query_service.get_contact_email_account_associations_by_emails(
            organization_id=organization_id,
            emails=list(email_contact_map.keys()),
        )

        resolved_account_id_map: dict[UUID, tuple[str | None, UUID | None]] = (
            defaultdict()
        )
        for (
            email,
            contact_email_account_associations,
        ) in contact_email_account_association_map.items():
            if not contact_email_account_associations:
                continue
            # order by:
            # 1. the association where the email is the preferred email, sort by most recently created records first
            # 1. the association where the email is not the preferred email, sort by most recently created records first
            contact_email_account_associations.sort(
                key=lambda info: (
                    info.is_contact_account_primary,
                    info.created_at,
                    info.id,
                ),
                reverse=True,
            )
            for contact_email_account_association in contact_email_account_associations:
                contact_id = email_contact_map.get(email)
                if (
                    contact_id
                    and contact_email_account_association.contact_id == contact_id
                ):
                    resolved_account_id_map[contact_id] = (
                        email,
                        contact_email_account_association.account_id,
                    )
                    break

        miss_contacts = {
            contact_id for contact_id, _ in contact_email_pairs if contact_id
        } - set(resolved_account_id_map.keys())
        if miss_contacts:
            primary_account_id_map = await self._get_primary_account_ids_by_contact_ids(
                organization_id=organization_id,
                contact_ids=miss_contacts,
            )
            for contact_id in miss_contacts:
                resolved_account_id_map.update(
                    {contact_id: (None, primary_account_id_map.get(contact_id))}
                )
        return resolved_account_id_map

    async def resolve_account_by_contacts(
        self,
        organization_id: UUID,
        contact_ids: list[UUID],
    ) -> dict[UUID, UUID | None]:
        return await self.resolve_relevant_account_by_contact_and_email_pairs(
            organization_id=organization_id,
            contact_email_pairs=[(contact_id, None) for contact_id in contact_ids],
        )

    async def resolve_relevant_contact_email_by_contact_and_account_pairs(
        self,
        organization_id: UUID,
        contact_account_pairs: list[tuple[UUID, UUID | None]],
    ) -> dict[UUID, tuple[EmailStrLower | None, UUID | None]]:
        return await self.map_the_most_relevant_contact_emails(
            organization_id=organization_id,
            contact_account_pairs=contact_account_pairs,
        )

    async def resolve_relevant_contact_and_account_by_emails(
        self,
        organization_id: UUID,
        emails: list[EmailStrLower],
    ) -> dict[EmailStrLower, tuple[UUID | None, UUID | None]]:
        contact_emails = await self.contact_query_service.list_contact_emails_by_emails(
            organization_id=organization_id,
            emails=set(emails),
        )
        email_contact_map = {
            contact_email.email: contact_email.contact_id
            for contact_email in contact_emails
        }

        contact_email_account_association_map = await self.contact_query_service.get_contact_email_account_associations_by_emails(
            organization_id=organization_id,
            emails=list(email_contact_map.keys()),
        )

        email_account_map: dict[EmailStrLower, UUID | None] = defaultdict()
        for (
            email,
            contact_email_account_associations,
        ) in contact_email_account_association_map.items():
            if not contact_email_account_associations:
                continue
            contact_email_account_associations.sort(
                key=lambda info: info.created_at, reverse=True
            )
            for contact_email_account_association in contact_email_account_associations:
                if contact_email_account_association.is_contact_account_primary:
                    email_account_map[email] = (
                        contact_email_account_association.account_id
                    )
                    break

        miss_emails = {email for email in emails if email not in email_account_map}
        if miss_emails:
            primary_account_id_map = await self._get_primary_account_ids_by_contact_ids(
                organization_id=organization_id,
                contact_ids=set(email_contact_map.values()),
            )
            for email in miss_emails:
                contact_id = email_contact_map.get(email)
                if not contact_id:
                    continue
                email_account_map[email] = primary_account_id_map.get(contact_id)

        return {
            email: (email_contact_map.get(email), email_account_map.get(email))
            for email in emails
        }

    async def _get_primary_account_ids_by_contact_ids(
        self,
        organization_id: UUID,
        contact_ids: set[UUID],
    ) -> dict[UUID, UUID]:
        return await self.contact_repository.get_primary_account_ids_by_contact_ids(
            organization_id=organization_id,
            contact_ids=contact_ids,
        )

    async def _get_primary_account_id_by_contact_id(
        self,
        organization_id: UUID,
        contact_id: UUID,
    ) -> UUID | None:
        return await self.contact_repository.get_primary_account_id_by_contact_id(
            organization_id=organization_id,
            contact_id=contact_id,
        )

    async def resolve_email_by_contact_and_account(
        self,
        *,
        organization_id: UUID,
        contact_id: UUID,
        account_id: UUID | None,
    ) -> str | None:
        email: str | None = None
        # if account_id presented
        if account_id:
            email = await self.contact_query_service.get_preferred_email_of_account(
                organization_id=organization_id,
                contact_id=contact_id,
                account_id=account_id,
            )
        # default: fallback to primary_email
        if not email:
            email = await self.contact_query_service.get_primary_email_by_contact_id(
                organization_id=organization_id, contact_id=contact_id
            )
        return email

    async def map_the_most_relevant_contact_emails(
        self,
        *,
        organization_id: UUID,
        contact_account_pairs: list[tuple[UUID, UUID | None]],
    ) -> dict[UUID, tuple[EmailStrLower | None, UUID | None]]:
        """
        Batch Find the most relevant contact email based on the given context.
        Args:
            organization_id: Organization ID
            contact_id: Contact ID to find email for
            account_id: Optional account ID to provide context for email selection
        Returns:
            EmailStrLower | None: The most relevant email address or None if no email found

        Selection Priority Order:
        1. When account_id is specified:
            a. First try: Primary email associated with the specified account
            b. If not found: Primary email associated with contact's primary account
            c. If not found: Contact's primary email
            d. If none found: Return None
        2. When account_id is NOT specified:
            a. First try: Primary email associated with contact's primary account
            b. If not found: Contact's primary email
            c. If none found: Return None

        Example:
            Contact has:
            - Primary account A with primary email "<EMAIL>"
            - Associated account B with primary email "<EMAIL>"
            - Contact primary email "<EMAIL>"
            - If account_id=B is specified:
                Returns "<EMAIL>"
            - If account_id=C is specified (C is not associated):
                Falls back to primary account A's email "<EMAIL>"
            - If no account_id specified:
                Returns primary account A's email "<EMAIL>"

        Note:
            For backward compatibility, will use contact's primary email as fallback
            if no relevant contact-account emails are found.
        """
        result: dict[UUID, tuple[EmailStrLower | None, UUID | None]] = defaultdict()
        not_associated_with_or_not_given_account_contact_ids: set[UUID] = set()
        with_account: list[tuple[UUID, UUID | None]] = []
        for contact_id, account_id in contact_account_pairs:
            if account_id:
                with_account.append((contact_id, account_id))
            else:
                not_associated_with_or_not_given_account_contact_ids.add(contact_id)

        # Process contacts with account context
        if with_account:
            account_specific_emails = await self._get_account_specific_emails(
                organization_id=organization_id, contact_account_pairs=with_account
            )
            result.update(account_specific_emails)

            # Add contacts without found emails to no-account group for fallback
            not_associated_with_or_not_given_account_contact_ids.update(
                contact_id
                for contact_id, _ in with_account
                if contact_id not in account_specific_emails
            )

        # Process remaining contacts without account context
        if not_associated_with_or_not_given_account_contact_ids:
            primary_account_emails = await self._get_primary_account_emails(
                organization_id=organization_id,
                contact_ids=not_associated_with_or_not_given_account_contact_ids,
            )
            result.update(primary_account_emails)

            # Get primary emails for contacts with no account-associated emails
            contacts_needing_primary = {
                contact_id
                for contact_id in not_associated_with_or_not_given_account_contact_ids
                if contact_id not in primary_account_emails
            }
            if contacts_needing_primary:
                primary_emails = await self._get_contact_primary_emails(
                    organization_id=organization_id,
                    contact_ids=contacts_needing_primary,
                )
                result.update(primary_emails)

        return result

    async def _get_account_specific_emails(
        self,
        *,
        organization_id: UUID,
        contact_account_pairs: list[tuple[UUID, UUID | None]],
    ) -> dict[UUID, tuple[EmailStrLower | None, UUID | None]]:
        """Get primary emails associated with specific accounts."""
        email_map = await self.contact_repository.map_prefer_contact_email_by_contact_id_and_account_id(
            organization_id=organization_id,
            contact_account_pairs=contact_account_pairs,
        )
        return {
            contact_id: (email.email, association.account_id)
            for contact_id, (email, association) in email_map.items()
        }

    async def _get_primary_account_emails(
        self,
        *,
        organization_id: UUID,
        contact_ids: set[UUID],
    ) -> dict[UUID, tuple[EmailStrLower | None, UUID | None]]:
        """Get primary emails associated with contacts' primary accounts."""
        email_map = await self.contact_repository.map_primary_account_prefer_contact_email_by_contact_id(
            organization_id=organization_id,
            contact_ids=list(contact_ids),
        )
        return {
            contact_id: (email.email, association.account_id)
            for contact_id, (email, association) in email_map.items()
        }

    async def _get_contact_primary_emails(
        self,
        *,
        organization_id: UUID,
        contact_ids: set[UUID],
    ) -> dict[UUID, tuple[EmailStrLower | None, UUID | None]]:
        """Get contacts' primary emails (not associated with any account)."""
        contact_emails = cast(
            list[ContactEmail],
            await self.contact_repository.list_primary_contact_channel_infos_by_contact_ids(
                contact_channel_type=ContactChannelType.EMAIL,
                organization_id=organization_id,
                contact_ids=list(contact_ids),
            ),
        )
        return {email.contact_id: (email.email, None) for email in contact_emails}


class SingletonContactResolveService(Singleton, ContactResolveService):
    pass


def get_contact_resolve_service(db_engine: DatabaseEngine) -> ContactResolveService:
    if SingletonContactResolveService.has_instance():
        return SingletonContactResolveService.get_singleton_instance()
    return SingletonContactResolveService(
        contact_repository=ContactRepository(engine=db_engine),
        contact_query_service=get_contact_query_service(db_engine=db_engine),
    )
