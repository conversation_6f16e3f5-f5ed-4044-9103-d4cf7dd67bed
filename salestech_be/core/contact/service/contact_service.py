import uuid
from collections.abc import Mapping
from typing import Annotated, cast
from uuid import UUID

from fastapi import Depends
from frozendict import frozendict
from phonenumbers import NumberParseException
from pydantic import EmailStr

from salestech_be.common.exception.exception import (
    ConflictErrorDetails,
    ConflictResourceError,
    InvalidArgumentError,
)
from salestech_be.common.schema_manager.std_object_field_identifier import (
    StdObjectIdentifiers,
    StdSelectListIdentifier,
)
from salestech_be.common.singleton import Singleton
from salestech_be.common.type.contact import ContactChannelType, ParsePhoneNumberResult
from salestech_be.common.type.formatted_string import EmailStrLower
from salestech_be.common.type.metadata.field.field_value import (
    FieldValueOrAny,
)
from salestech_be.common.type.patch_request import (
    UNSET,
    BasePatchRequest,
    UnsetAware,
    specified,
)
from salestech_be.common.type.pipeline import PipelineIdentifiers
from salestech_be.core.approval_request.service.approval_service import (
    ApprovalService,
    get_approval_service_with_engine,
)
from salestech_be.core.common.domain_service import (
    DomainService,
)
from salestech_be.core.common.types import UserAuthContext
from salestech_be.core.contact.service.contact_query_service import (
    ContactQueryService,
    get_contact_query_service,
)
from salestech_be.core.contact.service_api_schema import (
    ArchiveContactAccountAssociationResult,
    PatchContactAccountRoleRequest,
    PatchContactRequest,
    ShiftContactStageRequest,
    ShiftContactStageResponse,
    UpsertContactAccountAssociationResult,
    UpsertContactAccountRoleRequest,
)
from salestech_be.core.contact.types_v2 import ContactV2
from salestech_be.core.custom_object.service.custom_object_service import (
    CustomObjectService,
)
from salestech_be.core.custom_object.type.extendable_standard_object import (
    ExtendableStandardObject,
)
from salestech_be.core.data.service.query_service import get_domain_object_query_service
from salestech_be.core.domain_object_list.service.domain_object_list_query_service import (
    DomainObjectListQueryService,
    get_domain_object_list_query_service_by_db_engine,
)
from salestech_be.core.ff.feature_flag_service import (
    FeatureFlagService,
    get_feature_flag_service,
)
from salestech_be.core.imports.service.crm_sync_push_service import (
    CrmSyncPushService,
    get_crm_sync_push_service,
)
from salestech_be.core.job.service.job_service import JobService
from salestech_be.core.metadata.service.internal_select_list_service import (
    InternalSelectListService,
    get_select_list_service,
)
from salestech_be.core.metadata.service.stage_criteria_service import (
    StageCriteriaService,
    get_stage_criteria_service,
)
from salestech_be.core.metadata.types import (
    ContactPhoneNumber,
    PhoneNumberParsingResult,
    StageCriteriaEvaluateResult,
)
from salestech_be.core.notification.service.notification_service import (
    NotificationService,
    get_notification_service_by_db_engine,
)
from salestech_be.core.notification.types import SendNotificationRequest
from salestech_be.core.research_agent.research_agent_service import (
    ResearchAgentService,
    get_research_agent_service,
)
from salestech_be.core.research_agent.research_metric import (
    ResearchMetric,
    ResearchMetricsParams,
)
from salestech_be.core.research_agent.types import IntelProviderTypeEnum, ResearchView
from salestech_be.db.dao.account_repository import AccountRepository
from salestech_be.db.dao.address_repository import AddressRepository
from salestech_be.db.dao.contact_repository import (
    ContactRepository,
    DeleteContactEmailAccountAssociationResult,
    DeleteContactEmailResult,
    DeleteContactPhoneNumberResult,
    PatchContactAccountAssociationResult,
    UpdateContactEmailAccountAssociationResult,
    UpdateContactEmailResult,
    UpdateContactPhoneNumberResult,
    UpsertContactEmailResult,
    UpsertContactPhoneNumberResult,
)
from salestech_be.db.dao.custom_object_repository import CustomObjectRepository
from salestech_be.db.dao.job_repository import JobRepository
from salestech_be.db.dao.person_repository import PersonRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.dto.research_dto import ContactResearchDto
from salestech_be.db.models.account import Account
from salestech_be.db.models.address import Address
from salestech_be.db.models.approval_request import (
    ApprovalRequestStatus,
    ApprovalRequestType,
    ContactStageBackwardTransitApprovalRequestReference,
)
from salestech_be.db.models.contact import (
    Contact,
    ContactUpdate,
    CreateContactRequest,
    CreateDbContactEmailRequest,
    CreateDbContactPhoneNumberRequest,
    UpdateContactEmailAccountAssociationRequest,
    UpdateContactEmailRequest,
    UpdateContactPhoneNumberRequest,
    UpdateContactRequest,
)
from salestech_be.db.models.contact import Contact as DbContact
from salestech_be.db.models.contact_account_association import (
    ContactAccountAssociation,
    ContactAccountAssociationUpdate,
)
from salestech_be.db.models.contact_email import (
    ContactEmail,
    ContactEmailAccountAssociation,
)
from salestech_be.db.models.contact_phone_number import (
    ContactPhoneNumber as DbContactPhoneNumber,
)
from salestech_be.db.models.contact_phone_number import (
    ContactPhoneNumberAccountAssociation,
)
from salestech_be.db.models.notification import (
    NotificationCRMChangeData,
    NotificationReferenceIdType,
)
from salestech_be.ree_logging import get_logger
from salestech_be.settings import settings
from salestech_be.util.pydantic_types.str import (
    PhoneNumberWithExtension,
    parse_phone_number,
)
from salestech_be.util.time import zoned_utc_now
from salestech_be.util.validation import cast_or_error, one_row_or_none
from salestech_be.web.api.common.container import (
    DeleteEntityResponse,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.web.api.contact.schema import (
    CreateContactRequest as CreateContactApiRequest,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)

logger = get_logger()


class ContactService(DomainService[ContactV2]):
    def __init__(
        self,
        contact_repository: Annotated[ContactRepository, Depends()],
        account_repository: Annotated[AccountRepository, Depends()],
        address_repository: Annotated[AddressRepository, Depends()],
        custom_object_service: Annotated[CustomObjectService, Depends()],
        job_service: Annotated[JobService, Depends()],
        person_repository: Annotated[PersonRepository, Depends()],
        contact_query_service: Annotated[ContactQueryService, Depends()],
        domain_object_list_query_service: Annotated[
            DomainObjectListQueryService, Depends()
        ],
        select_list_service: Annotated[InternalSelectListService, Depends()],
        research_agent_service: Annotated[ResearchAgentService, Depends()],
        approval_service: Annotated[ApprovalService, Depends()],
        stage_criteria_service: Annotated[StageCriteriaService, Depends()],
        notification_service: Annotated[NotificationService, Depends()],
        feature_flag_service: Annotated[
            FeatureFlagService, Depends(get_feature_flag_service)
        ],
        crm_sync_push_service: Annotated[
            CrmSyncPushService, Depends(get_crm_sync_push_service)
        ],
    ):
        super().__init__(feature_flag_service=feature_flag_service)
        self.contact_repository = contact_repository
        self.account_repository = account_repository
        self.address_repository = address_repository
        self.custom_object_service = custom_object_service
        self.job_service = job_service
        self.person_repository = person_repository
        self.contact_query_service = contact_query_service
        self.logger = get_logger()
        self.select_list_service = select_list_service
        self.research_agent_service = research_agent_service
        self.approval_service = approval_service
        self.stage_criteria_service = stage_criteria_service
        self.notification_service = notification_service
        self.crm_sync_push_service = crm_sync_push_service
        self.domain_object_list_query_service = domain_object_list_query_service

    def get_relative_redirection_url(self, contact_id: UUID) -> str:
        return f"/contacts/{contact_id}"

    async def list_contacts_v2_paginated(
        self,
        organization_id: UUID,
        include_custom_object: bool | None = False,
        offset: int = 0,
        limit: int | None = None,
    ) -> list[ContactV2]:
        """List contacts with offset-based pagination."""
        contacts = await self.contact_query_service.list_contacts_v2(
            organization_id=organization_id,
            include_custom_object=include_custom_object,
        )
        if limit:
            return contacts[offset : offset + limit]
        return contacts[offset:]

    async def list_contacts_v2(
        self,
        organization_id: UUID,
        only_include_contact_ids: UnsetAware[set[UUID]] = UNSET,
        include_custom_object: bool | None = False,
        contextual_account_id: UUID | None = None,
    ) -> list[ContactV2]:
        return await self.contact_query_service.list_contacts_v2(
            organization_id=organization_id,
            only_include_contact_ids=only_include_contact_ids,
            include_custom_object=include_custom_object,
            contextual_account_id=contextual_account_id,
        )

    async def get_entity(
        self, entity_id: UUID, organization_id: UUID, user_id: UUID
    ) -> ContactV2 | None:
        return await self.get_contact_v2(
            contact_id=entity_id,
            organization_id=organization_id,
            include_custom_object=False,
        )

    async def get_contact_v2(
        self,
        contact_id: UUID,
        organization_id: UUID,
        include_custom_object: bool | None = False,
    ) -> ContactV2:
        logger.bind(
            contact_id=contact_id,
            organization_id=organization_id,
            include_custom_object=include_custom_object,
        ).info("Get contact request")
        return await self.contact_query_service.get_contact_v2(
            contact_id=contact_id,
            organization_id=organization_id,
            include_custom_object=include_custom_object,
        )

    async def list_active_contact_account_associations(
        self,
        *,
        organization_id: UUID,
        contact_id: UUID,
        account_id: UUID | None = None,
    ) -> list[ContactAccountAssociation]:
        """
        List active contact account associations for a contact.
        """
        return (
            await self.contact_query_service.list_active_contact_account_associations(
                organization_id=organization_id,
                contact_id=contact_id,
                account_id=account_id,
            )
        )

    async def list_latest_contact_account_associations(
        self,
        *,
        organization_id: UUID,
        contact_id: UUID,
        account_id: UUID | None = None,
    ) -> list[ContactAccountAssociation]:
        """
        List latest contact account associations for a contact.
        Returned associations are latest associations for each account, may not be active.
        """
        return (
            await self.contact_query_service.list_latest_contact_account_associations(
                organization_id=organization_id,
                contact_id=contact_id,
                account_id=account_id,
            )
        )

    async def list_active_pipelines_by_contact_id(
        self,
        *,
        organization_id: UUID,
        contact_id: UUID,
        account_id: UUID | None = None,
    ) -> list[PipelineIdentifiers]:
        return await self.contact_query_service.list_active_pipelines_by_contact_id(
            organization_id=organization_id,
            contact_id=contact_id,
            account_id=account_id,
        )

    async def map_contact_v2_by_primary_account_ids(
        self,
        organization_id: UUID,
        account_ids: set[UUID],
        include_custom_object: bool = False,
    ) -> Mapping[UUID, list[ContactV2]]:
        return await self.contact_query_service.map_contact_v2_by_primary_account_ids(
            organization_id=organization_id,
            account_ids=account_ids,
            include_custom_object=include_custom_object,
        )

    async def list_by_person_ids(
        self,
        person_ids: list[UUID],
        organization_id: UUID | None,
    ) -> list[ContactV2]:
        return await self.contact_query_service.list_by_person_ids(
            person_ids=person_ids,
            organization_id=organization_id,
        )

    async def list_contact_ids_by_person_ids(
        self, person_ids: list[UUID], organization_id: UUID
    ) -> list[UUID]:
        db_contacts = await self.contact_query_service.list_by_person_ids(
            person_ids=person_ids,
            organization_id=organization_id,
        )
        return [db_contact.id for db_contact in db_contacts]

    async def list_active_contact_associations_for_account(
        self, *, organization_id: UUID, account_id: UUID
    ) -> list[ContactAccountAssociation]:
        return await self.contact_query_service.list_active_contact_associations_for_account(
            organization_id=organization_id,
            account_id=account_id,
        )

    async def list_active_contact_associations_for_contact(
        self, *, organization_id: UUID, contact_id: UUID
    ) -> list[ContactAccountAssociation]:
        return await self.contact_query_service.list_active_contact_associations_for_contact(
            organization_id=organization_id,
            contact_id=contact_id,
        )

    async def create_contact_with_custom_field(
        self,
        create_contact_api_request: CreateContactApiRequest,
        organization_id: UUID,
        user_id: UUID,
        custom_field_data: Mapping[UUID, FieldValueOrAny] | None = None,
        import_record_id: UUID | None = None,
    ) -> ContactV2:
        logger.bind(
            create_contact_request=create_contact_api_request,
            custom_field_data=custom_field_data,
        ).info("Create contact request")

        stage_id: UUID | None = None
        if create_contact_api_request.stage_id:
            await self.select_list_service.validate_select_list_value_reference(
                organization_id=organization_id,
                select_list_value_id=create_contact_api_request.stage_id,
                application_code_name=StdSelectListIdentifier.contact_stage,
            )
            stage_id = create_contact_api_request.stage_id
        elif (
            default_stage_list
            := await self.select_list_service.find_default_select_list_dto_by_application_code_name(
                organization_id=organization_id,
                application_code_name=StdSelectListIdentifier.contact_stage,
            )
        ) and (default_value := default_stage_list.default_or_initial_active_value):
            stage_id = default_value.id

        if not stage_id:
            raise InvalidArgumentError(
                "Stage is required for creating a contact, however "
                "the organization does not have a default stage set."
            )

        create_db_contact_request = (
            create_contact_api_request.to_create_contact_request(
                user_id=user_id,
                organization_id=organization_id,
                stage_id=stage_id,
            )
        )
        (
            db_contact,
            db_contact_address,
            db_contact_emails,
            db_contact_phone_numbers,
            db_contact_account_associations,
        ) = self._generate_stable_db_models(
            create_contact_request=create_db_contact_request,
            organization_id=organization_id,
        )
        if (
            not create_contact_api_request.created_source
            or create_contact_api_request.created_source.is_not_crm()
        ):
            await self.crm_sync_push_service.sync_push_obj_contact_create(
                organization_id=organization_id,
                db_contact=db_contact,
                db_contact_address=db_contact_address,
                db_contact_emails=db_contact_emails,
                db_contact_account_associations=db_contact_account_associations,
                import_record_id=import_record_id,
            )
        created_contact = await self.contact_repository.create_or_unarchive_contact(
            organization_id=organization_id,
            db_contact=db_contact,
            db_contact_address=db_contact_address,
            db_contact_emails=db_contact_emails,
            db_contact_phone_numbers=db_contact_phone_numbers,
            db_contact_account_associations=db_contact_account_associations,
        )

        if custom_field_data:
            await self.custom_object_service.create_custom_object_data_by_extension_id(
                organization_id=created_contact.organization_id,
                user_id=created_contact.created_by_user_id,
                parent_object_name=ExtendableStandardObject.contact,
                extension_id=created_contact.id,
                custom_field_data_by_field_id=custom_field_data,
            )

        return await self.get_contact_v2(
            organization_id=created_contact.organization_id,
            contact_id=created_contact.id,
            include_custom_object=True,
        )

    def _generate_stable_db_models(
        self,
        create_contact_request: CreateContactRequest,
        organization_id: UUID,
    ) -> tuple[
        Contact,
        Address | None,
        list[tuple[ContactEmail, list[ContactEmailAccountAssociation]]],
        list[tuple[DbContactPhoneNumber, list[ContactPhoneNumberAccountAssociation]]],
        list[ContactAccountAssociation],
    ]:
        """Generate stable DB models with pre-assigned IDs before any DB operations."""
        contact = create_contact_request.contact
        db_contact_address = None
        if contact.address:
            db_contact_address = contact.address.to_db_insert(
                created_at=zoned_utc_now(),
            )
        db_contact = contact.to_db_inserts(
            organization_id=organization_id,
            address_id_override=db_contact_address.id if db_contact_address else None,
        )
        # At most one contact email is supported for now in the legacy create_contact method
        db_contact_emails: list[
            tuple[ContactEmail, list[ContactEmailAccountAssociation]]
        ] = []
        if create_contact_request.contact_emails:
            # Only returning 1 contact email for now.
            db_contact_email_to_add, associations = (
                create_contact_request.contact_emails[0].to_db_inserts(
                    organization_id=organization_id,
                    user_id=create_contact_request.contact.created_by_user_id,
                    contact_id=db_contact.id,
                )
            )
            db_contact_emails.append((db_contact_email_to_add, associations))
        db_contact_phone_numbers: list[
            tuple[DbContactPhoneNumber, list[ContactPhoneNumberAccountAssociation]]
        ] = []
        if create_contact_request.contact_phone_numbers:
            db_contact_phone_number_to_add, phone_number_associations = (
                create_contact_request.contact_phone_numbers[0].to_db_inserts(
                    organization_id=organization_id,
                    user_id=create_contact_request.contact.created_by_user_id,
                    contact_id=db_contact.id,
                )
            )
            db_contact_phone_numbers.append(
                (db_contact_phone_number_to_add, phone_number_associations)
            )
        db_contact_account_associations: list[ContactAccountAssociation] = []
        if create_contact_request.contact_account_roles:
            # Only returning 1 contact account association for legacy.
            association = create_contact_request.contact_account_roles[0].to_db_inserts(
                organization_id=organization_id,
                user_id=create_contact_request.contact.created_by_user_id,
                contact_id=db_contact.id,
            )
            db_contact_account_associations.append(association)

        return (
            db_contact,
            db_contact_address,
            db_contact_emails,
            db_contact_phone_numbers,
            db_contact_account_associations,
        )

    async def get_default_stage_id_for_contact(
        self,
        organization_id: UUID,
    ) -> UUID:
        default_stage_list = await self.select_list_service.find_default_select_list_dto_by_application_code_name(
            organization_id=organization_id,
            application_code_name=StdSelectListIdentifier.contact_stage,
        )
        if (
            not default_stage_list
            or not default_stage_list.default_or_initial_active_value
        ):
            raise InvalidArgumentError(
                "Stage is required for creating a contact, however "
                "the organization does not have a default stage set."
            )
        default_value = default_stage_list.default_or_initial_active_value
        return default_value.id

    async def create_entity(
        self, organization_id: UUID, user_id: UUID, request: CreateContactRequest
    ) -> ContactV2:
        return await self.create_contact_with_contact_channels(
            user_id=user_id,
            organization_id=organization_id,
            create_contact_with_contact_channel_request=request,
        )

    async def create_contact_with_contact_channels(
        self,
        user_id: UUID,
        organization_id: UUID,
        create_contact_with_contact_channel_request: CreateContactRequest,
    ) -> ContactV2:
        logger.bind(
            create_contact_with_contact_channel_request=create_contact_with_contact_channel_request,
        ).info("Create contact request")

        create_db_contact_request = create_contact_with_contact_channel_request.contact
        await self.select_list_service.validate_select_list_value_reference(
            organization_id=organization_id,
            select_list_value_id=create_db_contact_request.stage_id,
            application_code_name=StdSelectListIdentifier.contact_stage,
        )
        contact_phone_numbers_req = (
            create_contact_with_contact_channel_request.contact_phone_numbers
        )
        if contact_phone_numbers_req:
            await self._check_duplicated_phone_number(contact_phone_numbers_req)

        created_contact_dto = await self.contact_repository.create_contact(
            user_id=user_id,
            organization_id=organization_id,
            req=create_contact_with_contact_channel_request,
            overwrite_archived_emails=create_contact_with_contact_channel_request.overwrite_archived_emails,
        )
        created_contact = created_contact_dto.contact

        # Note: The CSV type must be "contact" to create custom object data
        if create_db_contact_request.custom_field_data and (
            create_db_contact_request.csv_type == "contact"
            or create_db_contact_request.csv_type is None
        ):
            await self.custom_object_service.create_custom_object_data_by_extension_id(
                user_id=user_id,
                organization_id=organization_id,
                parent_object_name=ExtendableStandardObject.contact,
                extension_id=created_contact.id,
                custom_field_data_by_field_id=create_db_contact_request.custom_field_data,
            )

        return await self.get_contact_v2(
            organization_id=created_contact.organization_id,
            contact_id=created_contact.id,
            include_custom_object=True,
        )

    async def find_active_contact_account_association(
        self,
        organization_id: UUID,
        contact_id: UUID,
        account_id: UUID,
    ) -> ContactAccountAssociation | None:
        return await self.contact_repository.find_active_contact_account_association(
            organization_id=organization_id,
            contact_id=contact_id,
            account_id=account_id,
        )

    async def upsert_contact_account_association(
        self,
        *,
        organization_id: UUID,
        user_id: UUID,
        contact_id: UUID,
        req: UpsertContactAccountRoleRequest,
        exclude_locked_by_integrity_jobs: bool = True,
    ) -> UpsertContactAccountAssociationResult:
        db_result = await self.contact_repository.upsert_contact_account_association(
            association=ContactAccountAssociation(
                created_by_user_id=user_id,
                updated_by_user_id=user_id,
                organization_id=organization_id,
                contact_id=contact_id,
                account_id=req.account_id,
                is_primary=req.is_primary,
                title=req.title,
                department=req.department,
            ),
            exclude_locked_by_integrity_jobs=exclude_locked_by_integrity_jobs,
        )
        updated_contact_v2 = await self.get_contact_v2(
            organization_id=organization_id,
            contact_id=contact_id,
        )
        return UpsertContactAccountAssociationResult(
            updated_contact=updated_contact_v2,
            upserted=db_result.upserted,
            demoted_sibling=db_result.demoted_sibling,
            promoted_sibling=db_result.promoted_sibling,
        )

    async def patch_contact_account_association(
        self,
        *,
        organization_id: UUID,
        user_id: UUID,
        contact_id: UUID,
        req: PatchContactAccountRoleRequest,
    ) -> PatchContactAccountAssociationResult:
        return await self.contact_repository.patch_contact_account_association(
            organization_id=organization_id,
            contact_id=contact_id,
            account_id=req.account_id,
            update=ContactAccountAssociationUpdate(
                updated_by_user_id=user_id,
                is_primary=req.is_primary,
                title=req.title,
                department=req.department,
            ),
        )

    async def authed_upsert_contact_account_association(
        self,
        user_auth_context: UserAuthContext,
        contact_id: UUID,
        req: UpsertContactAccountRoleRequest,
    ) -> UpsertContactAccountAssociationResult:
        await self.error_if_no_entity_access(
            user_auth_context=user_auth_context,
            entity_id=contact_id,
            access_check_function=self.can_access_entity_for_patch,
        )
        return await self.upsert_contact_account_association(
            organization_id=user_auth_context.organization_id,
            user_id=user_auth_context.user_id,
            contact_id=contact_id,
            req=req,
        )

    async def authed_patch_contact_account_association(
        self,
        user_auth_context: UserAuthContext,
        contact_id: UUID,
        req: PatchContactAccountRoleRequest,
    ) -> PatchContactAccountAssociationResult:
        await self.error_if_no_entity_access(
            user_auth_context=user_auth_context,
            entity_id=contact_id,
            access_check_function=self.can_access_entity_for_patch,
        )
        return await self.patch_contact_account_association(
            organization_id=user_auth_context.organization_id,
            user_id=user_auth_context.user_id,
            contact_id=contact_id,
            req=req,
        )

    async def archive_contact_account_association(
        self,
        *,
        user_id: UUID,
        organization_id: UUID,
        contact_id: UUID,
        account_id: UUID,
    ) -> ArchiveContactAccountAssociationResult:
        db_result = await self.contact_repository.archive_contact_account_association(
            organization_id=organization_id,
            contact_id=contact_id,
            account_id=account_id,
            user_id=user_id,
        )

        await self.contact_repository.archive_contact_email_account_associations_by_account_id(
            organization_id=organization_id,
            contact_id=contact_id,
            account_id=account_id,
            user_id=user_id,
        )

        updated_contact_v2 = await self.get_contact_v2(
            organization_id=organization_id,
            contact_id=contact_id,
        )
        return ArchiveContactAccountAssociationResult(
            promoted_sibling=db_result.promoted_sibling,
            archived=db_result.archived,
            updated_contact=updated_contact_v2,
        )

    async def authed_archive_contact_account_association(
        self,
        user_auth_context: UserAuthContext,
        contact_id: UUID,
        account_id: UUID,
    ) -> ArchiveContactAccountAssociationResult:
        await self.error_if_no_entity_access(
            user_auth_context=user_auth_context,
            entity_id=contact_id,
            access_check_function=self.can_access_entity_for_patch,
        )
        return await self.archive_contact_account_association(
            user_id=user_auth_context.user_id,
            organization_id=user_auth_context.organization_id,
            contact_id=contact_id,
            account_id=account_id,
        )

    async def remove_entity(
        self, organization_id: UUID, user_id: UUID, entity_id: UUID
    ) -> DeleteEntityResponse:
        archived_contact = await self.archive_contact(
            contact_id=entity_id,
            organization_id=organization_id,
            archived_by_user_id=user_id,
        )
        return DeleteEntityResponse(
            id=archived_contact.id,
            deleted_by_user_id=archived_contact.archived_by_user_id,
            deleted_at=archived_contact.archived_at,
        )

    async def archive_contact(
        self,
        contact_id: UUID,
        organization_id: UUID,
        archived_by_user_id: UUID,
        exclude_locked_by_integrity_jobs: bool = True,
    ) -> DbContact:
        logger.bind(
            contact_id=contact_id,
            organization_id=organization_id,
            archived_by_user_id=archived_by_user_id,
        ).info("Archive contact request")
        return await self.contact_repository.archive_contact(
            contact_id=contact_id,
            organization_id=organization_id,
            archived_by_user_id=archived_by_user_id,
            exclude_locked_by_integrity_jobs=exclude_locked_by_integrity_jobs,
        )

    async def unarchive_contact(
        self, organization_id: UUID, contact_id: UUID, unarchived_by_user_id: UUID
    ) -> ContactV2:
        db_contact = await self.contact_repository.unarchive_contact(
            organization_id=organization_id,
            contact_id=contact_id,
            unarchived_by_user_id=unarchived_by_user_id,
        )
        return await self.contact_query_service.get_contact_v2(
            organization_id=organization_id,
            contact_id=db_contact.id,
        )

    async def authed_unarchive_contact(
        self,
        user_auth_context: UserAuthContext,
        contact_id: UUID,
    ) -> ContactV2:
        await self.error_if_no_entity_access(
            user_auth_context=user_auth_context,
            entity_id=contact_id,
            access_check_function=self.can_access_entity_for_patch,
        )

        return await self.unarchive_contact(
            organization_id=user_auth_context.organization_id,
            contact_id=contact_id,
            unarchived_by_user_id=user_auth_context.user_id,
        )

    #  todo: this is to support finding similar contacts.
    # # migrate this to search use case.
    async def list_all_db_contact_no_associated_data(
        self,
        organization_id: UUID,
        include_archived: bool | None = False,
    ) -> list[DbContact]:
        return await self.contact_query_service.list_all_db_contact_no_associated_data(
            organization_id=organization_id,
            include_archived=include_archived,
        )

    async def shift_contact_stage(  # noqa: C901
        self,
        *,
        organization_id: UUID,
        user_id: UUID,
        contact_id: UUID,
        req: ShiftContactStageRequest,
    ) -> ShiftContactStageResponse:
        # 00 fetch non-archived contact
        contact = await self.contact_query_service.get_contact_v2(
            organization_id=organization_id,
            contact_id=contact_id,
        )
        if contact.archived_at:
            raise InvalidArgumentError("Cannot shift stage of archived contact")

        if not contact.primary_account_id:
            raise InvalidArgumentError(
                "Cannot shift contact stage if no account assigned. "
            )

        # 01 get target and current pipeline stages
        target_stage_slv = (
            await self.select_list_service.validate_select_list_value_reference(
                organization_id=organization_id,
                select_list_value_id=req.target_stage_id,
                application_code_name=StdSelectListIdentifier.contact_stage,
            )
        )
        current_stage_slv = (
            await self.select_list_service.validate_select_list_value_reference(
                organization_id=organization_id,
                select_list_value_id=contact.stage_id,
                application_code_name=StdSelectListIdentifier.contact_stage,
            )
            if contact.stage_id
            else None
        )

        # 02 no jump across lists allowed for contacts, not even with approval
        if (
            current_stage_slv
            and current_stage_slv.select_list_id != target_stage_slv.select_list_id
        ):
            raise InvalidArgumentError(
                f"Cannot shift stage of contact {contact_id} from {current_stage_slv.display_value}"
                f" to {target_stage_slv.display_value} because a contact's stage must remain in the same select list"
            )

        # 03 no-op if target and current stage are the same
        if current_stage_slv and current_stage_slv.id == target_stage_slv.id:
            return ShiftContactStageResponse(
                contact=await self.contact_query_service.get_contact_v2(
                    organization_id=organization_id,
                    contact_id=contact_id,
                ),
            )

        # 04 check stage shift criteria
        # 04a of target, if shifting backward
        # 04b of stages (current, target], if shifting forward
        # 04c of stages [beginning of sl, target], if current is missing
        shifting_backward = current_stage_slv and (
            target_stage_slv.rank < current_stage_slv.rank
        )

        if shifting_backward:
            target_stage_list_slvs_in_range = [target_stage_slv]
        else:
            target_stage_list_slvs_in_range = (
                await self.select_list_service.list_select_list_value_within_rank_range(
                    organization_id=organization_id,
                    select_list_id=target_stage_slv.select_list_id,
                    rank_le=target_stage_slv.rank,
                    rank_gt=current_stage_slv.rank if current_stage_slv else None,
                    active_only=True,
                )
            )
        for sl_slv in target_stage_list_slvs_in_range:
            eval_result = await self.stage_criteria_service.evaluate_stage_criteria(
                organization_id=organization_id,
                record_id=contact_id,
                stage_selector=sl_slv.id,
                primary_object_identifier=ContactV2.object_id,
            )
            # fail and return if any stage in range fails criteria
            if not eval_result.is_success:
                return ShiftContactStageResponse(
                    contact=await self.contact_query_service.get_contact_v2(
                        organization_id=organization_id,
                        contact_id=contact_id,
                    ),
                    stage_criteria_evaluation_result=eval_result,
                )

        # 05 check for approval needed in case of backward shift
        approval_all_clear = True
        approval_request = None
        if shifting_backward:  # approval needed for backward shift
            (
                approval_all_clear,
                approval_request,
            ) = await self.approval_service.check_approval_for_shift(
                organization_id=organization_id,
                user_id=user_id,
                approval_request_type=ApprovalRequestType.CONTACT_STAGE_BACKWARD,
                approval_request_display_name="Backward Shift Approval",
                approval_request_description="Backward shift approval request",
                approval_request_ref=ContactStageBackwardTransitApprovalRequestReference(
                    reference_model=ApprovalRequestType.CONTACT_STAGE_BACKWARD,
                    contact_id=contact.id,
                    target_stage_select_list_value_id=target_stage_slv.id,
                ),
                existing_approval_request_id=req.approval_request_id,
                existing_approval_type=ApprovalRequestType.CONTACT_STAGE_BACKWARD,
            )

        # 06 update contact stage, and update approval if needed
        shifted = False
        if approval_all_clear:
            await self.contact_repository.update_by_tenanted_primary_key(
                table_model=Contact,
                organization_id=organization_id,
                primary_key_to_value={
                    "id": contact.id,
                },
                column_to_update=ContactUpdate(
                    stage_id=req.target_stage_id,
                    updated_by_user_id=user_id,
                    updated_at=zoned_utc_now(),
                ),
            )
            if approval_request:
                await self.approval_service.update_approval_state(
                    organization_id=organization_id,
                    user_id=user_id,
                    approval_request=approval_request,
                    approval_request_status=ApprovalRequestStatus.EXECUTED,
                )
            shifted = True
        return ShiftContactStageResponse(
            contact=await self.contact_query_service.get_contact_v2(
                organization_id=organization_id,
                contact_id=contact_id,
            ),
            shifted=shifted,
            stage_criteria_evaluation_result=StageCriteriaEvaluateResult(
                is_success=True,
                stage_selector=target_stage_slv.id,
                evaluate_details=[],
            ),
        )

    async def authed_shift_contact_stage(
        self,
        user_auth_context: UserAuthContext,
        contact_id: UUID,
        req: ShiftContactStageRequest,
    ) -> ShiftContactStageResponse:
        await self.error_if_no_entity_access(
            user_auth_context=user_auth_context,
            entity_id=contact_id,
            access_check_function=self.can_access_entity_for_patch,
        )
        return await self.shift_contact_stage(
            organization_id=user_auth_context.organization_id,
            user_id=user_auth_context.user_id,
            contact_id=contact_id,
            req=req,
        )

    async def update_entity(
        self,
        organization_id: UUID,
        user_id: UUID,
        entity: ContactV2,
        request: BasePatchRequest,
    ) -> ContactV2:
        contact_request = cast(PatchContactRequest, request)
        return await self.patch_by_id(
            organization_id=organization_id,
            user_id=user_id,
            contact_id=entity.id,
            request=contact_request,
        )

    async def patch_by_id(
        self,
        *,
        organization_id: UUID,
        user_id: UUID,
        contact_id: UUID,
        request: PatchContactRequest,
        import_record_id: UUID | None = None,
    ) -> ContactV2:
        logger.bind(
            contact_id=contact_id,
            organization_id=organization_id,
            user_id=user_id,
            request=request,
        ).info("Patch contact request")

        update_db_contact_request = request.to_update_db_contact_request(
            organization_id=organization_id,
            user_id=user_id,
            contact_id=contact_id,
        )

        if (
            not request.created_source_meta
            or not request.created_source_meta.is_not_crm()
        ):
            # Add CRM sync
            await self.crm_sync_push_service.sync_push_obj_contact_update(
                organization_id=organization_id,
                update_db_contact_request=update_db_contact_request,
                import_record_id=import_record_id,
            )

        await self._update_db_contact(
            request.to_update_db_contact_request(
                organization_id=organization_id,
                user_id=user_id,
                contact_id=contact_id,
            )
        )
        logger.bind(
            contact_id=contact_id,
            organization_id=organization_id,
            user_id=user_id,
            request=request,
        ).info("Patch contact request")

        updated_contact = await self.get_contact_v2(
            contact_id=contact_id, organization_id=organization_id
        )

        if specified(request.custom_field_data) and request.custom_field_data:
            await (
                self.custom_object_service.update_custom_object_data_by_extension_id_v2(
                    organization_id=organization_id,
                    user_id=user_id,
                    parent_object_name=ExtendableStandardObject.contact,
                    extension_id=contact_id,
                    custom_field_data_by_field_id=request.custom_field_data,
                )
            )
        if specified(request.linkedin_url):
            await self.research_agent_service.delete_intel_person_association_by_contact_id(
                contact_id=contact_id,
                user_id=user_id,
            )
        return updated_contact

    async def _update_db_contact(
        self, update_contact_request: UpdateContactRequest
    ) -> Contact:
        return await self.contact_repository.patch_contact(
            request=update_contact_request
        )

    async def update_primary_account_id(
        self,
        user_id: UUID,
        organization_id: UUID,
        contact_id: UUID,
        new_account_id: UUID,
    ) -> Contact | None:
        result = await self.contact_repository.upsert_contact_account_association(
            association=ContactAccountAssociation(
                created_by_user_id=user_id,
                updated_by_user_id=user_id,
                organization_id=organization_id,
                contact_id=contact_id,
                account_id=new_account_id,
                is_primary=True,
            ),
        )
        return result.updated_contact

    async def find_by_primary_emails(
        self, organization_id: UUID, primary_emails: list[str]
    ) -> list[Contact]:
        return await self.contact_query_service.find_by_primary_emails(
            organization_id=organization_id,
            primary_emails=primary_emails,
        )

    async def find_contact_ids_by_primary_emails(
        self, *, organization_id: UUID, primary_emails: set[str]
    ) -> frozendict[EmailStr, UUID]:
        return await self.contact_query_service.find_contact_ids_by_primary_emails(
            organization_id=organization_id,
            primary_emails=primary_emails,
        )

    async def find_contacts_by_primary_emails(
        self, organization_id: UUID, primary_emails: set[str]
    ) -> frozendict[EmailStr, ContactV2]:
        return await self.contact_query_service.find_contacts_by_primary_emails(
            organization_id=organization_id,
            primary_emails=primary_emails,
        )

    async def list_by_emails(
        self,
        organization_id: UUID | None,
        emails: list[EmailStr],
    ) -> list[ContactV2]:
        return await self.contact_query_service.list_by_emails(
            organization_id=organization_id,
            emails=emails,
        )

    async def list_by_linkedin_urls(
        self, organization_id: UUID | None, linkedin_urls: list[str]
    ) -> list[ContactV2]:
        return await self.contact_query_service.list_by_linkedin_urls(
            organization_id=organization_id,
            linkedin_urls=linkedin_urls,
        )

    async def list_contact_emails_by_contact_id(
        self,
        organization_id: UUID,
        contact_id: UUID,
    ) -> list[ContactEmail]:
        return await self.contact_query_service.list_contact_emails_by_contact_id(
            organization_id=organization_id,
            contact_id=contact_id,
        )

    async def list_contacts_by_ids_untenanted(
        self,
        contact_ids: list[UUID],
        exclude_deleted_or_archived: bool = True,
    ) -> list[Contact]:
        return await self.contact_repository.list_contacts_by_ids_untenanted(
            ids=list(contact_ids),
            exclude_deleted_or_archived=exclude_deleted_or_archived,
        )

    async def find_contact_email_by_emails(
        self,
        *,
        organization_id: UUID,
        emails: list[EmailStrLower],
    ) -> list[ContactEmail]:
        return await self.contact_query_service.list_contact_emails_by_emails(
            organization_id=organization_id,
            emails=set(emails),
        )

    async def find_contact_email_by_email(
        self,
        organization_id: UUID,
        email: EmailStrLower,
    ) -> ContactEmail | None:
        contact_emails = await self.contact_query_service.list_contact_emails_by_emails(
            organization_id=organization_id,
            emails={email},
        )
        return one_row_or_none(contact_emails)

    async def get_contact_email_by_id(
        self,
        organization_id: UUID,
        contact_email_id: UUID,
        exclude_deleted_or_archived: bool = True,
    ) -> ContactEmail | None:
        return cast(
            ContactEmail,
            await self.contact_repository.get_contact_channel_info_by_id(
                contact_channel_type=ContactChannelType.EMAIL,
                organization_id=organization_id,
                contact_channel_info_id=contact_email_id,
                exclude_deleted_or_archived=exclude_deleted_or_archived,
            ),
        )

    async def list_contact_emails_by_ids(
        self,
        organization_id: UUID,
        contact_email_ids: set[UUID],
        exclude_deleted_or_archived: bool = True,
    ) -> list[ContactEmail]:
        return [
            cast_or_error(contact_channel_info, ContactEmail)
            for contact_channel_info in await self.contact_repository.get_contact_channel_info_by_ids(
                contact_channel_type=ContactChannelType.EMAIL,
                organization_id=organization_id,
                contact_channel_info_ids=contact_email_ids,
                exclude_deleted_or_archived=exclude_deleted_or_archived,
            )
        ]

    async def list_contact_email_account_associationss(
        self,
        organization_id: UUID,
        contact_id: UUID,
        account_id: UUID,
    ) -> list[ContactEmailAccountAssociation]:
        return cast(
            list[ContactEmailAccountAssociation],
            await self.contact_repository.list_contact_channel_info_account_associations(
                contact_channel_type=ContactChannelType.EMAIL,
                organization_id=organization_id,
                contact_id=contact_id,
                account_id=account_id,
            ),
        )

    async def list_contact_email_account_associations_by_contact_id(
        self,
        organization_id: UUID,
        contact_id: UUID,
    ) -> list[ContactEmailAccountAssociation]:
        return cast(
            list[ContactEmailAccountAssociation],
            await self.contact_repository.list_contact_channel_info_account_associations_by_contact_id(
                contact_channel_type=ContactChannelType.EMAIL,
                organization_id=organization_id,
                contact_id=contact_id,
            ),
        )

    async def list_contact_email_account_associations_by_account_id(
        self,
        organization_id: UUID,
        account_id: UUID,
    ) -> list[ContactEmailAccountAssociation]:
        return cast(
            list[ContactEmailAccountAssociation],
            await self.contact_repository.list_contact_channel_info_account_associations_by_account_id(
                contact_channel_type=ContactChannelType.EMAIL,
                organization_id=organization_id,
                account_id=account_id,
            ),
        )

    async def list_contact_email_account_associations_by_contact_email_id(
        self,
        organization_id: UUID,
        contact_email_id: UUID,
    ) -> list[ContactEmailAccountAssociation]:
        return cast(
            list[ContactEmailAccountAssociation],
            await self.contact_repository.list_contact_channel_info_account_associations_by_contact_channel_info_id(
                contact_channel_type=ContactChannelType.EMAIL,
                organization_id=organization_id,
                contact_channel_info_id=contact_email_id,
            ),
        )

    async def list_contact_email_account_associations_by_contact_email_ids(
        self,
        organization_id: UUID,
        contact_email_ids: set[UUID],
        exclude_deleted_or_archived: bool = True,
    ) -> list[ContactEmailAccountAssociation]:
        return cast(
            list[ContactEmailAccountAssociation],
            await self.contact_repository.list_contact_channel_info_account_associations_by_contact_channel_info_ids(
                contact_channel_type=ContactChannelType.EMAIL,
                organization_id=organization_id,
                contact_channel_info_ids=contact_email_ids,
                exclude_deleted_or_archived=exclude_deleted_or_archived,
            ),
        )

    async def get_contact_research(
        self,
        contact_id: UUID,
        organization_id: UUID,
        referer: str | None,
        user_id: UUID | None = None,
    ) -> ContactResearchDto | None:
        contact = await self.contact_repository.get_by_id(
            contact_id=contact_id,
            organization_id=organization_id,
        )
        if not contact:
            return None
        if (
            not contact.linkedin_url
            and not settings.research_agent_enable_find_linkedin_url_by_email
        ):
            return None
        result = await self.research_agent_service.get_research_for_contact_or_none(
            contact_id=contact_id,
            contact_linkedin_url=contact.linkedin_url,
            request_user_id=user_id,
            organization_id=organization_id,
        )
        provider = (
            IntelProviderTypeEnum.from_str(result.latest_provider_status.name)
            if result and result.latest_provider_status
            else None
        )
        ResearchMetric.track_person_research_availability(
            contact_id=contact_id,
            is_available=result is not None,
            research_metrics_params=ResearchMetricsParams(
                view=ResearchView.from_referer(referer=referer),
                provider=provider,
                latest_provider_status=result.latest_provider_status
                if result
                else None,
            ),
        )

        if result is None:
            return None
        contact_research_dto = ContactResearchDto.from_person_research(
            contact_id=contact_id,
            organization_id=organization_id,
            person_research=result,
            is_feedback_positive=result.is_feedback_positive,
        )
        ResearchMetric.track_person_research_fields(
            contact_id=contact_id,
            contact_research_dto=contact_research_dto,
            research_metrics_params=ResearchMetricsParams(
                view=ResearchView.from_referer(referer=referer),
                provider=provider,
                latest_provider_status=result.latest_provider_status,
            ),
        )
        return contact_research_dto

    async def list_contact_researches(
        self,
        contact_ids: list[UUID],
        organization_id: UUID,
        user_id: UUID | None = None,
    ) -> list[ContactResearchDto]:
        if not contact_ids:
            return []
        contact_research_map = (
            await self.research_agent_service.get_research_or_none_for_contacts(
                contact_ids=set(contact_ids),
                request_user_id=user_id,
                organization_id=organization_id,
            )
        )
        # TODO: handle errors on missing researches
        return [
            ContactResearchDto.from_person_research(
                contact_id=contact_id,
                organization_id=organization_id,
                person_research=person_research,
                is_feedback_positive=person_research.is_feedback_positive,
            )
            for contact_id, person_research in contact_research_map.items()
            if person_research is not None
        ]

    async def send_contact_change_notification(
        self, contact_v2_before: ContactV2, contact_v2_after: ContactV2
    ) -> None:
        before_stage_display_value = (
            contact_v2_before.stage.display_value if contact_v2_before.stage else None
        )
        after_stage_display_value = (
            contact_v2_after.stage.display_value if contact_v2_after.stage else None
        )

        if before_stage_display_value != after_stage_display_value:
            await self.notification_service.send_notification(
                send_notification_request=SendNotificationRequest(
                    data=NotificationCRMChangeData(
                        message=f"Contact {contact_v2_after.display_name} stage changed from {before_stage_display_value} to {after_stage_display_value}",
                        action_url=f"/contacts/{contact_v2_after.id}",
                    ),
                    reference_id=str(contact_v2_after.id),
                    reference_id_type=NotificationReferenceIdType.CONTACT,
                    activity_id=None,
                    actor_user_id=contact_v2_after.updated_by_user_id,
                    recipient_user_ids=[contact_v2_after.owner_user_id],
                    idempotency_key=str(uuid.uuid4()),
                ),
                organization_id=contact_v2_after.organization_id,
            )

    async def batch_upsert_contact_email(
        self,
        organization_id: UUID,
        user_id: UUID,
        contact_id: UUID,
        create_contact_email_requests: list[CreateDbContactEmailRequest],
        overwrite_archived: bool = False,
        exclude_locked_by_integrity_jobs: bool = True,
    ) -> list[UpsertContactEmailResult]:
        primary_contact_email = cast(
            ContactEmail,
            await self.contact_repository.get_primary_contact_channel_info_by_contact_id(
                contact_channel_type=ContactChannelType.EMAIL,
                organization_id=organization_id,
                contact_id=contact_id,
            ),
        )
        upsert_primary = [
            req.email for req in create_contact_email_requests if req.is_contact_primary
        ]
        if len(upsert_primary) > 1:
            raise InvalidArgumentError(
                f"Duplicate primary contact emails, contact: {contact_id}, emails: {upsert_primary}"
            )
        if not primary_contact_email and not upsert_primary:
            raise InvalidArgumentError(
                f"required primary contact email, contact: {contact_id}"
            )
        result_list: list[UpsertContactEmailResult] = []
        for req in create_contact_email_requests:
            contact_email, contact_email_account_associations = req.to_db_inserts(
                organization_id=organization_id,
                user_id=user_id,
                contact_id=contact_id,
            )
            try:
                result_list.append(
                    await self.contact_repository.upsert_contact_email(
                        _in_nested_transaction=False,
                        overwrite_archived=overwrite_archived,
                        contact_email=contact_email,
                        contact_email_account_associations=contact_email_account_associations,
                        exclude_locked_by_integrity_jobs=exclude_locked_by_integrity_jobs,
                    )
                )
            except ConflictResourceError as e:
                additional_error_details = e.additional_error_details
                if (
                    not additional_error_details
                    or not additional_error_details.reference_id
                ):
                    raise e
                conflicted_existing_object_attrs = (
                    additional_error_details.conflicted_existing_object_attrs
                )
                # archived contact
                if conflicted_existing_object_attrs.get("archived_at"):
                    code = "CONTACT_EMAIL_ALREADY_EXISTS_ARCHIVED_CONTACT"
                    detail = "try to unarchive the existing contact or overwrite contact email"
                # active contact & archived email
                elif conflicted_existing_object_attrs.get("existing_email_archive_at"):
                    code = "CONTACT_EMAIL_ALREADY_EXISTS_ACTIVE_CONTACT_ARCHIVED_EMAIL"
                    detail = "contact email conflict, try to merge or overwrite contact email"
                # active contact & active email
                else:
                    code = "CONTACT_EMAIL_ALREADY_EXISTS_ACTIVE_CONTACT_ACTIVE_EMAIL"
                    detail = "contact email conflict, try to merge the existing contact"
                raise ConflictResourceError(
                    additional_error_details=ConflictErrorDetails(
                        code=code,
                        details=detail,
                        conflicted_existing_object=StdObjectIdentifiers.contact.identifier,
                        reference_id=additional_error_details.reference_id,
                        conflicted_existing_object_attrs=conflicted_existing_object_attrs,
                    )
                )
        return result_list

    async def authed_batch_upsert_contact_email(
        self,
        user_auth_context: UserAuthContext,
        contact_id: UUID,
        create_contact_email_requests: list[CreateDbContactEmailRequest],
        overwrite_archived: bool = False,
    ) -> list[UpsertContactEmailResult]:
        await self.error_if_no_entity_access(
            user_auth_context=user_auth_context,
            entity_id=contact_id,
            access_check_function=self.can_access_entity_for_patch,
        )
        return await self.batch_upsert_contact_email(
            organization_id=user_auth_context.organization_id,
            user_id=user_auth_context.user_id,
            contact_id=contact_id,
            create_contact_email_requests=create_contact_email_requests,
            overwrite_archived=overwrite_archived,
        )

    async def authed_batch_upsert_contact_phone_number(
        self,
        user_auth_context: UserAuthContext,
        contact_id: UUID,
        create_contact_phone_number_requests: list[CreateDbContactPhoneNumberRequest],
    ) -> list[UpsertContactPhoneNumberResult]:
        await self.error_if_no_entity_access(
            user_auth_context=user_auth_context,
            entity_id=contact_id,
            access_check_function=self.can_access_entity_for_patch,
        )
        return await self.batch_upsert_contact_phone_number(
            organization_id=user_auth_context.organization_id,
            user_id=user_auth_context.user_id,
            contact_id=contact_id,
            create_contact_phone_number_requests=create_contact_phone_number_requests,
        )

    async def patch_contact_email_by_email(
        self, *, user_id: UUID, organization_id: UUID, req: UpdateContactEmailRequest
    ) -> UpdateContactEmailResult:
        return await self.contact_repository.patch_contact_email_by_id(
            user_id=user_id,
            organization_id=organization_id,
            req=req,
        )

    async def patch_contact_phone_number_by_phone_number(
        self,
        *,
        user_id: UUID,
        organization_id: UUID,
        req: UpdateContactPhoneNumberRequest,
    ) -> UpdateContactPhoneNumberResult:
        return await self.contact_repository.patch_contact_phone_number_by_id(
            user_id=user_id,
            organization_id=organization_id,
            req=req,
        )

    async def authed_patch_contact_email_by_email(
        self,
        user_auth_context: UserAuthContext,
        contact_id: UUID,
        req: UpdateContactEmailRequest,
    ) -> UpdateContactEmailResult:
        await self.error_if_no_entity_access(
            user_auth_context=user_auth_context,
            entity_id=contact_id,
            access_check_function=self.can_access_entity_for_patch,
        )
        return await self.patch_contact_email_by_email(
            user_id=user_auth_context.user_id,
            organization_id=user_auth_context.organization_id,
            req=req,
        )

    async def authed_patch_contact_phone_number_by_phone_number(
        self,
        user_auth_context: UserAuthContext,
        contact_id: UUID,
        req: UpdateContactPhoneNumberRequest,
    ) -> UpdateContactPhoneNumberResult:
        await self.error_if_no_entity_access(
            user_auth_context=user_auth_context,
            entity_id=contact_id,
            access_check_function=self.can_access_entity_for_patch,
        )
        return await self.patch_contact_phone_number_by_phone_number(
            user_id=user_auth_context.user_id,
            organization_id=user_auth_context.organization_id,
            req=req,
        )

    async def patch_contact_email_account_association(
        self,
        organization_id: UUID,
        user_id: UUID,
        req: UpdateContactEmailAccountAssociationRequest,
    ) -> UpdateContactEmailAccountAssociationResult:
        return await self.contact_repository.patch_contact_email_account_association(
            user_id=user_id,
            organization_id=organization_id,
            req=req,
        )

    async def authed_patch_contact_email_account_association(
        self,
        user_auth_context: UserAuthContext,
        contact_id: UUID,
        req: UpdateContactEmailAccountAssociationRequest,
    ) -> UpdateContactEmailAccountAssociationResult:
        await self.error_if_no_entity_access(
            user_auth_context=user_auth_context,
            entity_id=contact_id,
            access_check_function=self.can_access_entity_for_patch,
        )
        return await self.patch_contact_email_account_association(
            organization_id=user_auth_context.organization_id,
            user_id=user_auth_context.user_id,
            req=req,
        )

    async def delete_contact_email(
        self,
        organization_id: UUID,
        user_id: UUID,
        contact_id: UUID,
        email: EmailStr,
        exclude_locked_by_integrity_jobs: bool = True,
    ) -> DeleteContactEmailResult:
        return await self.contact_repository.delete_contact_email(
            organization_id=organization_id,
            contact_id=contact_id,
            user_id=user_id,
            email=email,
            exclude_locked_by_integrity_jobs=exclude_locked_by_integrity_jobs,
        )

    async def delete_contact_email_account_associations(
        self,
        *,
        organization_id: UUID,
        user_id: UUID,
        contact_id: UUID,
        account_id: UUID,
        contact_email_id: UUID,
        exclude_locked_by_integrity_jobs: bool = True,
    ) -> DeleteContactEmailAccountAssociationResult:
        # validate both contact and account exist and not archived
        await self.contact_repository.find_by_tenanted_primary_key_or_fail(
            Contact,
            organization_id=organization_id,
            exclude_locked_by_integrity_jobs=exclude_locked_by_integrity_jobs,
            id=contact_id,
        )
        await self.account_repository.find_by_tenanted_primary_key_or_fail(
            Account,
            organization_id=organization_id,
            exclude_locked_by_integrity_jobs=exclude_locked_by_integrity_jobs,
            id=account_id,
        )

        # validate contact account association, expect to be active
        contact_account_associations = (
            await self.list_active_contact_account_associations(
                organization_id=organization_id,
                contact_id=contact_id,
                account_id=account_id,
            )
        )
        if not contact_account_associations:
            raise InvalidArgumentError(
                f"contact account association not found, contact: {contact_id}, account: {account_id}"
            )

        # delete the association with the given email, and promote the next primary association if necessary
        # validate contact email exists and is active
        contact_email = cast(
            ContactEmail,
            await self.contact_repository.get_contact_channel_info_by_id(
                contact_channel_type=ContactChannelType.EMAIL,
                organization_id=organization_id,
                contact_channel_info_id=contact_email_id,
            ),
        )

        if not contact_email:
            raise InvalidArgumentError(
                f"contact email not found or not in active status, contact: {contact_id}, contact_email_id: {contact_email_id}"
            )

        return await self.contact_repository.delete_contact_email_account_association(
            organization_id=organization_id,
            user_id=user_id,
            contact_id=contact_id,
            account_id=account_id,
            contact_email_id=contact_email.id,
        )

    async def authed_delete_contact_email_account_associations(
        self,
        user_auth_context: UserAuthContext,
        contact_id: UUID,
        account_id: UUID,
        contact_email_id: UUID,
    ) -> DeleteContactEmailAccountAssociationResult:
        await self.error_if_no_entity_access(
            user_auth_context=user_auth_context,
            entity_id=contact_id,
            access_check_function=self.can_access_entity_for_patch,
        )
        return await self.delete_contact_email_account_associations(
            organization_id=user_auth_context.organization_id,
            user_id=user_auth_context.user_id,
            contact_id=contact_id,
            account_id=account_id,
            contact_email_id=contact_email_id,
        )

    async def delete_contact_phone_number(
        self,
        organization_id: UUID,
        user_id: UUID,
        contact_id: UUID,
        phone_number: PhoneNumberWithExtension,
        exclude_locked_by_integrity_jobs: bool = True,
    ) -> DeleteContactPhoneNumberResult:
        return await self.contact_repository.delete_contact_phone_number(
            organization_id=organization_id,
            user_id=user_id,
            contact_id=contact_id,
            phone_number=phone_number,
            exclude_locked_by_integrity_jobs=exclude_locked_by_integrity_jobs,
        )

    async def batch_upsert_contact_phone_number(
        self,
        organization_id: UUID,
        user_id: UUID,
        contact_id: UUID,
        create_contact_phone_number_requests: list[CreateDbContactPhoneNumberRequest],
        exclude_locked_by_integrity_jobs: bool = True,
    ) -> list[UpsertContactPhoneNumberResult]:
        if create_contact_phone_number_requests:
            await self._check_duplicated_phone_number(
                create_contact_phone_number_requests
            )
        contact_phone_numbers = cast(
            list[DbContactPhoneNumber],
            await self.contact_repository.list_contact_channel_infos_by_contact_id(
                contact_channel_type=ContactChannelType.PHONE_NUMBER,
                organization_id=organization_id,
                contact_id=contact_id,
            ),
        )

        original_phone_numbers = {cpn.phone_number for cpn in contact_phone_numbers}
        primary_contact_phone_number = next(
            (cpn for cpn in contact_phone_numbers if cpn.is_contact_primary), None
        )
        upsert_primary = [
            req.phone_number
            for req in create_contact_phone_number_requests
            if req.is_contact_primary
        ]
        if len(upsert_primary) > 1:
            raise InvalidArgumentError(
                f"Duplicate primary contact phone numbers, contact: {contact_id}, phone number: {upsert_primary}"
            )
        if not primary_contact_phone_number and not upsert_primary:
            raise InvalidArgumentError(
                f"required primary contact phone number, contact: {contact_id}"
            )
        result_list: list[UpsertContactPhoneNumberResult] = []
        for req in create_contact_phone_number_requests:
            if req.phone_number in original_phone_numbers:
                raise ConflictResourceError(
                    f"The phone number: {req.phone_number} is duplicated with existing phone number"
                )
            contact_phone_number, contact_phone_number_account_associations = (
                req.to_db_inserts(
                    organization_id=organization_id,
                    user_id=user_id,
                    contact_id=contact_id,
                )
            )
            result_list.append(
                await self.contact_repository.upsert_contact_phone_number(
                    _in_nested_transaction=False,
                    contact_phone_number=contact_phone_number,
                    contact_phone_number_account_associations=contact_phone_number_account_associations,
                    exclude_locked_by_integrity_jobs=exclude_locked_by_integrity_jobs,
                )
            )
        return result_list

    async def _check_duplicated_phone_number(
        self,
        reqs: list[CreateDbContactPhoneNumberRequest],
    ) -> None:
        visited: set[PhoneNumberWithExtension] = set()
        for req in reqs:
            if req.phone_number in visited:
                raise InvalidArgumentError(
                    f"the phone number({req.phone_number}) is duplicated, check you request."
                )
            visited.add(req.phone_number)


def try_parse_phone_number(phone_number_raw: str) -> PhoneNumberParsingResult:
    try:
        country_code, number_e164, extension, _ = parse_phone_number(phone_number_raw)
        phone_number_view_object = ContactPhoneNumber(
            country_code=country_code,
            number_e164=number_e164,
            extension=extension,
        )
        return PhoneNumberParsingResult(
            parsed_phone_number=phone_number_view_object,
            parse_result=ParsePhoneNumberResult.SUCCESS,
        )
    except ValueError as value_ex:
        return PhoneNumberParsingResult(
            parse_result=ParsePhoneNumberResult.FAILED,
            message=f"phone number {phone_number_raw} value error: {value_ex}",
        )
    except NumberParseException as number_ex:
        return PhoneNumberParsingResult(
            parse_result=ParsePhoneNumberResult.FAILED,
            message=f"phone number {phone_number_raw} could not be parsed, error: {number_ex}",
        )
    except Exception as ex:
        return PhoneNumberParsingResult(
            parse_result=ParsePhoneNumberResult.FAILED,
            message=f"parse phone number {phone_number_raw} with unknown error{ex}",
        )


class SingletonContactService(Singleton, ContactService):
    pass


def get_contact_service(db_engine: DatabaseEngine) -> ContactService:
    if SingletonContactService.has_instance():
        return SingletonContactService.get_singleton_instance()
    return SingletonContactService(
        address_repository=AddressRepository(
            engine=db_engine,
        ),
        contact_repository=ContactRepository(
            engine=db_engine,
        ),
        custom_object_service=CustomObjectService(
            custom_object_repo=CustomObjectRepository(engine=db_engine),
            select_list_service=get_select_list_service(engine=db_engine),
        ),
        account_repository=AccountRepository(
            engine=db_engine,
        ),
        job_service=JobService(
            job_repository=JobRepository(
                engine=db_engine,
            )
        ),
        person_repository=PersonRepository(engine=db_engine),
        contact_query_service=get_contact_query_service(db_engine=db_engine),
        domain_object_list_query_service=get_domain_object_list_query_service_by_db_engine(
            db_engine=db_engine
        ),
        select_list_service=get_select_list_service(engine=db_engine),
        research_agent_service=get_research_agent_service(
            db_engine=db_engine,
        ),
        approval_service=get_approval_service_with_engine(db_engine=db_engine),
        stage_criteria_service=get_stage_criteria_service(
            engine=db_engine,
            domain_object_query_service=get_domain_object_query_service(
                db_engine=db_engine
            ),
        ),
        notification_service=get_notification_service_by_db_engine(db_engine=db_engine),
        feature_flag_service=get_feature_flag_service(),
        crm_sync_push_service=get_crm_sync_push_service(db_engine=db_engine),
    )
