from __future__ import annotations

from collections.abc import Mapping
from uuid import UUID

from pydantic import BaseModel

from salestech_be.common.query_util.filter_schema import FilterSpec
from salestech_be.common.type.formatted_string import EmailStrLower
from salestech_be.common.type.metadata.field.field_value import FieldValueOrAny
from salestech_be.common.type.patch_request import (
    UNSET,
    BaseBulkPatchRequest,
    BasePatchRequest,
    UnsetAware,
    specified,
)
from salestech_be.core.approval_request.types import (
    ApprovalRequest,
    IApprovableActionResponse,
)
from salestech_be.core.common.types import Address
from salestech_be.core.contact.types_v2 import ContactV2
from salestech_be.core.metadata.types import StageCriteriaEvaluateResult
from salestech_be.db.models.contact import (
    ContactUpdate,
)
from salestech_be.db.models.contact import (
    UpdateContactRequest as UpdateDbContactRequest,
)
from salestech_be.db.models.contact_account_association import ContactAccountAssociation
from salestech_be.db.models.core.types import CreatedSource, EntityParticipant
from salestech_be.util.pydantic_types.str import PhoneNumberWithExtension

# todo(xw): Move all ContactService request models here.


class ShiftContactStageRequest(BaseModel):
    target_stage_id: UUID
    approval_request_id: UUID | None = None


class ShiftContactStageResponse(BaseModel, IApprovableActionResponse):
    contact: ContactV2
    shifted: bool = False
    stage_criteria_evaluation_result: StageCriteriaEvaluateResult | None = None
    approval_request: ApprovalRequest | None = None

    @property
    def is_successful(self) -> bool:
        if not self.stage_criteria_evaluation_result:
            return True
        return self.stage_criteria_evaluation_result.is_success


class UpsertContactAccountRoleRequest(BaseModel):
    account_id: UUID
    is_primary: bool
    title: str | None = None
    department: str | None = None


class TouchContactAccountRoleRequest(BaseModel):
    contact_id: UUID
    account_id: UUID
    is_primary: bool
    title: str | None = None
    department: str | None = None


class PatchContactAccountRoleRequest(BasePatchRequest):
    account_id: UUID
    is_primary: UnsetAware[bool] = UNSET
    title: UnsetAware[str | None] = UNSET
    department: UnsetAware[str | None] = UNSET


class ArchiveContactAccountAssociationRequest(BaseModel):
    account_id: UUID


class UpsertContactAccountAssociationResult(BaseModel):
    updated_contact: ContactV2 | None
    upserted: ContactAccountAssociation
    demoted_sibling: ContactAccountAssociation | None
    promoted_sibling: ContactAccountAssociation | None


class ArchiveContactAccountAssociationResult(BaseModel):
    updated_contact: ContactV2 | None
    archived: ContactAccountAssociation
    promoted_sibling: ContactAccountAssociation | None


class MergeContactAccountAssociationPreviewResult(BaseModel):
    """
    Result of previewing association updates when merging two accounts/contacts.
    - to_archive is the contact account associations that will be archived.
    - to_create is the contact account associations that will be created.

    Notes: ids in to_create might have no actual meaning, do NOT use them directly as reference.
    """

    to_archive: list[ContactAccountAssociation]
    to_create: list[ContactAccountAssociation]


class ContactIdDisplayNameResult(BaseModel):
    contact_id: UUID
    display_name: str | None


class PatchContactRequest(BasePatchRequest):
    require_at_least_one_specified_field = True

    first_name: UnsetAware[str | None] = UNSET
    last_name: UnsetAware[str | None] = UNSET
    display_name: UnsetAware[str] = UNSET
    middle_name: UnsetAware[str | None] = UNSET
    primary_email: UnsetAware[EmailStrLower | None] = UNSET
    primary_phone_number: UnsetAware[PhoneNumberWithExtension | None] = UNSET
    linkedin_url: UnsetAware[str | None] = UNSET
    zoominfo_url: UnsetAware[str | None] = UNSET
    owner_user_id: UnsetAware[UUID] = UNSET
    address: UnsetAware[Address | None] = UNSET
    title: UnsetAware[str | None] = UNSET
    department: UnsetAware[str | None] = UNSET
    person_id: UnsetAware[UUID | None] = UNSET
    facebook_url: UnsetAware[str | None] = UNSET
    created_source: UnsetAware[str | None] = UNSET
    created_source_meta: CreatedSource | None = (
        None  # This created_source is not a DB record and is system-generated
    )
    participant_user_id_list: UnsetAware[list[UUID] | None] = UNSET

    custom_field_data: UnsetAware[Mapping[UUID, FieldValueOrAny] | None] = UNSET

    def to_update_db_contact_request(
        self, organization_id: UUID, user_id: UUID, contact_id: UUID
    ) -> UpdateDbContactRequest:
        address_id: UnsetAware[UUID | None] = UNSET
        if specified(self.address) and not self.address:
            address_id = None
        contact_update = ContactUpdate(
            first_name=self.first_name,
            last_name=self.last_name,
            display_name=self.display_name,
            middle_name=self.middle_name,
            primary_phone_number=self.primary_phone_number,
            linkedin_url=self.linkedin_url,
            zoominfo_url=self.zoominfo_url,
            owner_user_id=self.owner_user_id,
            title=self.title,
            department=self.department,
            person_id=self.person_id,
            facebook_url=self.facebook_url,
            updated_by_user_id=user_id,
            address_id=address_id,
            participants=EntityParticipant.list_from_unset_aware_request_field(
                self.participant_user_id_list
            ),
        )
        remove_current_primary_email = False
        if specified(self.primary_email) and not self.primary_email:
            remove_current_primary_email = True
        return UpdateDbContactRequest(
            contact_update=contact_update,
            organization_id=organization_id,
            contact_id=contact_id,
            primary_email=self.primary_email if specified(self.primary_email) else None,
            remove_current_primary_email=remove_current_primary_email,
            address_request=self.address.to_db_address_create_request(
                created_by_user_id=user_id, organization_id=organization_id
            )
            if specified(self.address) and self.address
            else None,
        )


class BulkPatchContactsRequest(BaseBulkPatchRequest):
    entity_ids: list[UUID]
    patch_request: PatchContactRequest


class EnrichContactResultLite(BaseModel):
    phone_number: str | None = None
    email: str | None = None
    error: str | None = None


class BulkEnrichContactsResponse(BaseModel):
    data: dict[UUID, EnrichContactResultLite] = {}
    total_enrichable_contacts_count: int | None = None
    estimate_email_credits: int | None = None
    estimate_phone_credits: int | None = None
    prospecting_job_id: UUID | None = None


class EnrichContactsRequest(BaseModel):
    contact_ids: list[UUID] | None = None
    list_id: UUID | None = None
    filter_spec: FilterSpec | None = None
    enrich_phone_number: bool = True
