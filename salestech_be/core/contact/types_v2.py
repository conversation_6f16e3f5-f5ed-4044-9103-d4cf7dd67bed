from __future__ import annotations

from typing import Annotated
from uuid import UUID

from nameparser import HumanName
from pydantic import BaseModel, EmailStr, Field

from salestech_be.common.schema_manager.std_object_field_identifier import (
    A<PERSON>untField,
    ContactAccountRoleField,
    ContactField,
    ContactForGiantTaskField,
    ContactPipelineRoleField,
    DomainObjectListField,
    OrganizationUserField,
    StdObjectIdentifiers,
)
from salestech_be.common.schema_manager.std_object_relationship import (
    ContactRelationship,
)
from salestech_be.common.type.metadata.common import (
    ObjectAccessStatus,
    StandardObjectIdentifier,
)
from salestech_be.common.type.metadata.field.field_indexable_config import (
    UniqueIndexableConfig,
)
from salestech_be.common.type.metadata.field.field_type_property import (
    DefaultEnumFieldProperty,
    EmailFieldProperty,
    ListFieldProperty,
    NestedObjectFieldProperty,
    PhoneFieldProperty,
    TextFieldProperty,
    TimestampFieldProperty,
    UrlFieldProperty,
    UUIDFieldProperty,
)
from salestech_be.common.type.metadata.schema import (
    InboundRelationship,
    OutboundRelationship,
)
from salestech_be.core.common.types import (
    Address,
    BaseAISuggestedValue,
    CustomizableDomainModel,
    DomainModel,
    FieldMetadata,
)
from salestech_be.core.metadata.types import (
    ContactAccountAssociationLite,
    ContactEmailLite,
    ContactPhoneNumber,
    SelectListValueLite,
)
from salestech_be.core.research_agent.types import DBContactAISuggestedValues
from salestech_be.db.models.core.types import CreatedSource
from salestech_be.db.models.task import ContactForGiantTask as DbContactForGiantTask
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime
from salestech_be.util.validation import not_none


class ContactAISuggestedLinkedinUrl(BaseAISuggestedValue):
    object_id = StandardObjectIdentifier(object_name="ai_suggested_linkedin_url")
    object_display_name = "AI Suggested LinkedIn URL"

    value: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="LinkedIn URL",
                is_ui_displayable=False,
                is_ui_editable=False,
            )
        ),
    ] = None

    @classmethod
    def from_db_ai_suggested_values(
        cls, db_ai_suggested_values: DBContactAISuggestedValues | None
    ) -> ContactAISuggestedLinkedinUrl | None:
        if not db_ai_suggested_values:
            return None

        return cls(
            value=db_ai_suggested_values.linkedin_profile_url,
            updated_at=db_ai_suggested_values.updated_at,
        )


class ContactAISuggestedDisplayName(BaseAISuggestedValue):
    object_id = StandardObjectIdentifier(object_name="ai_suggested_display_name")
    object_display_name = "AI Suggested Display Name"

    value: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Name",
                is_ui_displayable=False,
                is_ui_editable=False,
            )
        ),
    ] = None

    @classmethod
    def from_db_ai_suggested_values(
        cls, db_ai_suggested_values: DBContactAISuggestedValues | None
    ) -> ContactAISuggestedDisplayName | None:
        if not db_ai_suggested_values:
            return None

        return cls(
            value=db_ai_suggested_values.name,
            updated_at=db_ai_suggested_values.updated_at,
        )

    @classmethod
    def get_first_name(cls, value: str | None) -> str | None:
        if not value:
            return None
        name = HumanName(value)
        if not name:
            return None
        return str(name.first)

    @classmethod
    def get_last_name(cls, value: str | None) -> str | None:
        if not value:
            return None
        name = HumanName(value)
        if not name:
            return None
        return str(name.last)

    @classmethod
    def get_middle_name(cls, value: str | None) -> str | None:
        if not value:
            return None
        name = HumanName(value)
        if not name:
            return None
        return str(name.middle)


class ContactAISuggestedTitle(BaseAISuggestedValue):
    object_id = StandardObjectIdentifier(object_name="ai_suggested_title")
    object_display_name = "AI Suggested Title"

    value: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Title",
                is_ui_displayable=False,
                is_ui_editable=False,
            )
        ),
    ] = None

    @classmethod
    def from_db_ai_suggested_values(
        cls, db_ai_suggested_values: DBContactAISuggestedValues | None
    ) -> ContactAISuggestedTitle | None:
        if not db_ai_suggested_values:
            return None

        return cls(
            value=db_ai_suggested_values.title,
            updated_at=db_ai_suggested_values.updated_at,
        )


class ContactAccountIdPair(BaseModel):
    contact_id: UUID
    account_id: UUID


class ContactEmailInfo(BaseModel):
    email: EmailStr | None = None
    is_contact_primary: bool


class ContactAccountAssociationInfo(BaseModel):
    account_id: UUID
    display_name: str | None = None
    is_primary: bool


class ContactSearchResult(BaseModel):
    contact_id: UUID
    display_name: str | None = None
    emails: list[ContactEmailInfo]
    account_associations: list[ContactAccountAssociationInfo]


class ContactForGiantTask(DomainModel):
    object_id = StdObjectIdentifiers.contact_for_giant_task.identifier
    object_display_name = "Contact for Giant Task"
    field_name_provider = ContactForGiantTaskField

    id: Annotated[
        UUID,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                index_config=UniqueIndexableConfig(is_indexed=True, is_unique=True),
                field_display_name="ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            )
        ),
    ]

    organization_id: Annotated[
        UUID,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Organization ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            )
        ),
    ]

    display_name: Annotated[
        str,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Name",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ]

    @classmethod
    def from_db_contact_for_giant_task(
        cls, db_contact_for_giant_task: DbContactForGiantTask
    ) -> ContactForGiantTask:
        return cls(
            id=not_none(db_contact_for_giant_task.contact_id),
            organization_id=not_none(db_contact_for_giant_task.contact_organization_id),
            display_name=not_none(db_contact_for_giant_task.contact_display_name),
        )


class ContactV2(CustomizableDomainModel):
    object_id = StdObjectIdentifiers.contact.identifier
    object_display_name = "Contact"
    field_name_provider = ContactField
    inbound_relationships = (
        InboundRelationship(
            id=ContactRelationship.domain_object_lists__from__contact,
            relation_type=OutboundRelationship.RelationType.LOOKUP,
            relationship_name="Domain Object Lists",
            self_object_identifier=StdObjectIdentifiers.contact.identifier,
            related_object_identifier=StdObjectIdentifiers.domain_object_list.identifier,
            self_cardinality=OutboundRelationship.Cardinality.MANY,
            related_object_cardinality=OutboundRelationship.Cardinality.MANY,
            ordered_self_field_identifiers=(ContactField.id.identifier,),
            ordered_related_field_identifiers=(DomainObjectListField.id.identifier,),
        ),
        InboundRelationship(
            id=ContactRelationship.contact__from__contact_account_role,
            relation_type=InboundRelationship.RelationType.LOOKUP,
            relationship_name="Contact Account Role",
            self_object_identifier=StdObjectIdentifiers.contact.identifier,
            related_object_identifier=StdObjectIdentifiers.contact_account_role.identifier,
            self_cardinality=InboundRelationship.Cardinality.ONE,
            related_object_cardinality=InboundRelationship.Cardinality.MANY,
            ordered_self_field_identifiers=(ContactField.id.identifier,),
            ordered_related_field_identifiers=(
                ContactAccountRoleField.contact_id.identifier,
            ),
            is_related_object_junction=True,
        ),
        InboundRelationship(
            id=ContactRelationship.contact__from__contact_pipeline_role,
            relation_type=InboundRelationship.RelationType.LOOKUP,
            relationship_name="Contact Opportunity Role",
            self_object_identifier=StdObjectIdentifiers.contact.identifier,
            related_object_identifier=StdObjectIdentifiers.contact_pipeline_role.identifier,
            self_cardinality=InboundRelationship.Cardinality.ONE,
            related_object_cardinality=InboundRelationship.Cardinality.MANY,
            ordered_self_field_identifiers=(ContactField.id.identifier,),
            ordered_related_field_identifiers=(
                ContactPipelineRoleField.contact_id.identifier,
            ),
            is_related_object_junction=True,
        ),
    )
    outbound_relationships = (
        OutboundRelationship(
            id=ContactRelationship.contact__to__primary_account,
            relation_type=OutboundRelationship.RelationType.LOOKUP,
            relationship_name="Primary Account",
            self_object_identifier=StdObjectIdentifiers.contact.identifier,
            related_object_identifier=StdObjectIdentifiers.account.identifier,
            self_cardinality=OutboundRelationship.Cardinality.MANY,
            related_object_cardinality=OutboundRelationship.Cardinality.ONE,
            ordered_self_field_identifiers=(
                ContactField.primary_account_id.identifier,
            ),
            ordered_related_field_identifiers=(AccountField.id.identifier,),
        ),
        OutboundRelationship(
            id=ContactRelationship.contact__to__created_by_user,
            relation_type=OutboundRelationship.RelationType.LOOKUP,
            relationship_name="Created By User",
            self_object_identifier=StdObjectIdentifiers.contact.identifier,
            related_object_identifier=StdObjectIdentifiers.user.identifier,
            self_cardinality=OutboundRelationship.Cardinality.MANY,
            related_object_cardinality=OutboundRelationship.Cardinality.ONE,
            ordered_self_field_identifiers=(
                ContactField.created_by_user_id.identifier,
            ),
            ordered_related_field_identifiers=(OrganizationUserField.id.identifier,),
        ),
        OutboundRelationship(
            id=ContactRelationship.contact__to__updated_by_user,
            relation_type=OutboundRelationship.RelationType.LOOKUP,
            relationship_name="Updated By User",
            self_object_identifier=StdObjectIdentifiers.contact.identifier,
            related_object_identifier=StdObjectIdentifiers.user.identifier,
            self_cardinality=OutboundRelationship.Cardinality.MANY,
            related_object_cardinality=OutboundRelationship.Cardinality.ONE,
            ordered_self_field_identifiers=(
                ContactField.updated_by_user_id.identifier,
            ),
            ordered_related_field_identifiers=(OrganizationUserField.id.identifier,),
        ),
        OutboundRelationship(
            id=ContactRelationship.contact__to__owner_user,
            relation_type=OutboundRelationship.RelationType.LOOKUP,
            relationship_name="Assignee",
            self_object_identifier=StdObjectIdentifiers.contact.identifier,
            related_object_identifier=StdObjectIdentifiers.user.identifier,
            self_cardinality=OutboundRelationship.Cardinality.MANY,
            related_object_cardinality=OutboundRelationship.Cardinality.ONE,
            ordered_self_field_identifiers=(ContactField.owner_user_id.identifier,),
            ordered_related_field_identifiers=(OrganizationUserField.id.identifier,),
        ),
        OutboundRelationship(
            id=ContactRelationship.contact__to__archived_by_user,
            relation_type=OutboundRelationship.RelationType.LOOKUP,
            relationship_name="Archived By User",
            self_object_identifier=StdObjectIdentifiers.contact.identifier,
            related_object_identifier=StdObjectIdentifiers.user.identifier,
            self_cardinality=OutboundRelationship.Cardinality.MANY,
            related_object_cardinality=OutboundRelationship.Cardinality.ONE,
            ordered_self_field_identifiers=(
                ContactField.archived_by_user_id.identifier,
            ),
            ordered_related_field_identifiers=(OrganizationUserField.id.identifier,),
        ),
        OutboundRelationship(
            id=ContactRelationship.contact__to__participant_users,
            relation_type=OutboundRelationship.RelationType.LOOKUP,
            relationship_name="Participant Users",
            self_object_identifier=StdObjectIdentifiers.contact.identifier,
            related_object_identifier=StdObjectIdentifiers.user.identifier,
            self_cardinality=OutboundRelationship.Cardinality.ONE,
            related_object_cardinality=OutboundRelationship.Cardinality.MANY,
            ordered_self_field_identifiers=(
                ContactField.participant_user_id_list.identifier,
            ),
            ordered_related_field_identifiers=(OrganizationUserField.id.identifier,),
        ),
    )

    # Basic information
    id: Annotated[
        UUID,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                index_config=UniqueIndexableConfig(is_indexed=True, is_unique=True),
                field_display_name="ID",
                is_ui_displayable=False,
                is_ui_editable=False,
                is_import_field_mappable=False,
            )
        ),
    ]
    display_name: Annotated[
        str,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Contact Name",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ]
    stage_id: Annotated[
        UUID,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Stage ID",
                is_ui_displayable=True,
                is_ui_editable=True,
                is_import_field_mappable=False,
            )
        ),
    ]
    stage: Annotated[
        SelectListValueLite,
        FieldMetadata(
            type_property=NestedObjectFieldProperty(
                object_identifier=StdObjectIdentifiers.select_list_value.identifier,
                field_display_name="Stage",
                is_ui_displayable=True,
                is_ui_editable=False,
                is_import_field_mappable=True,
            )
        ),
    ]
    first_name: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="First Name",
                is_ui_displayable=True,
                is_ui_editable=True,
                is_import_field_mappable=True,
            )
        ),
    ] = None
    last_name: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Last Name",
                is_ui_displayable=True,
                is_ui_editable=True,
                is_import_field_mappable=True,
            )
        ),
    ] = None
    middle_name: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Middle Name",
                is_ui_displayable=True,
                is_ui_editable=True,
                is_import_field_mappable=False,
            )
        ),
    ] = None
    title: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Title",
                is_ui_displayable=True,
                is_ui_editable=True,
                is_import_field_mappable=True,
            )
        ),
    ] = None
    department: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Department",
                is_ui_displayable=True,
                is_ui_editable=True,
                is_import_field_mappable=False,
            )
        ),
    ] = None
    access_status: Annotated[
        ObjectAccessStatus,
        FieldMetadata(
            type_property=DefaultEnumFieldProperty(
                enum_class=ObjectAccessStatus,
                field_display_name="Access Status",
                is_ui_displayable=False,
                is_ui_editable=False,
                is_import_field_mappable=False,
            )
        ),
    ]

    # Contact information
    primary_email: Annotated[
        str | None,
        FieldMetadata(
            type_property=EmailFieldProperty(
                field_display_name="Primary Email",
                is_ui_displayable=True,
                is_ui_editable=True,
                is_import_field_mappable=True,
            )
        ),
    ] = None
    primary_phone_number: Annotated[
        str | None,
        FieldMetadata(
            type_property=PhoneFieldProperty(
                field_display_name="Primary Phone Number",
                is_ui_displayable=True,
                is_ui_editable=True,
                is_import_field_mappable=True,
            )
        ),
    ] = None
    address: Annotated[
        Address | None,
        FieldMetadata(
            type_property=NestedObjectFieldProperty(
                object_identifier=StdObjectIdentifiers.address.identifier,
                field_display_name="Address",
                is_ui_displayable=True,
                is_import_field_mappable=False,
            )
        ),
    ] = None
    linkedin_url: Annotated[
        str | None,
        FieldMetadata(
            type_property=UrlFieldProperty(
                field_display_name="LinkedIn URL",
                is_ui_displayable=True,
                is_ui_editable=True,
                is_import_field_mappable=True,
            )
        ),
    ] = None
    zoominfo_url: Annotated[
        str | None,
        FieldMetadata(
            type_property=UrlFieldProperty(
                field_display_name="Zoominfo URL",
                is_ui_displayable=True,
                is_ui_editable=True,
                is_import_field_mappable=True,
            )
        ),
    ] = None
    x_url: Annotated[
        str | None,
        FieldMetadata(
            type_property=UrlFieldProperty(
                field_display_name="X URL",
                is_ui_displayable=True,
                is_ui_editable=True,
                is_import_field_mappable=True,
            )
        ),
    ] = None
    facebook_url: Annotated[
        str | None,
        FieldMetadata(
            type_property=UrlFieldProperty(
                field_display_name="Facebook URL",
                is_ui_displayable=True,
                is_ui_editable=True,
                is_import_field_mappable=True,
            )
        ),
    ] = None

    # AI Suggested Values
    ai_suggested_linkedin_url: Annotated[
        ContactAISuggestedLinkedinUrl | None,
        FieldMetadata(
            type_property=NestedObjectFieldProperty(
                field_display_name="AI Suggested LinkedIn URL",
                object_identifier=StandardObjectIdentifier(
                    object_name="ai_suggested_linkedin_url"
                ),
                is_ui_displayable=False,
                is_ui_editable=False,
                is_import_field_mappable=False,
            ),
            is_enriched_field=True,
        ),
    ] = None
    ai_suggested_display_name: Annotated[
        ContactAISuggestedDisplayName | None,
        FieldMetadata(
            type_property=NestedObjectFieldProperty(
                object_identifier=StandardObjectIdentifier(
                    object_name="ai_suggested_display_name"
                ),
                field_display_name="AI Suggested Display Name",
                is_ui_displayable=False,
                is_ui_editable=False,
                is_import_field_mappable=False,
            ),
            is_enriched_field=True,
        ),
    ] = None
    ai_suggested_title: Annotated[
        ContactAISuggestedTitle | None,
        FieldMetadata(
            type_property=NestedObjectFieldProperty(
                object_identifier=StandardObjectIdentifier(
                    object_name="ai_suggested_title"
                ),
                field_display_name="AI Suggested Title",
                is_ui_displayable=False,
                is_ui_editable=False,
                is_import_field_mappable=False,
            ),
            is_enriched_field=True,
        ),
    ] = None

    # Ownership and organization
    organization_id: Annotated[
        UUID,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Organization ID",
                is_ui_displayable=False,
                is_ui_editable=False,
                is_import_field_mappable=False,
            )
        ),
    ]
    owner_user_id: Annotated[
        UUID,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Owner ID",
                is_ui_displayable=False,
                is_ui_editable=True,
                is_import_field_mappable=True,
            )
        ),
    ]
    created_by_user_id: Annotated[
        UUID,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Created By User ID",
                is_ui_displayable=False,
                is_ui_editable=False,
                is_import_field_mappable=False,
            )
        ),
    ]
    updated_by_user_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Updated By User ID",
                is_ui_displayable=False,
                is_ui_editable=False,
                is_import_field_mappable=False,
            )
        ),
    ] = None
    archived_by_user_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Archived By User ID",
                is_ui_displayable=False,
                is_ui_editable=False,
                is_import_field_mappable=False,
            )
        ),
    ] = None
    integrity_job_started_by_user_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Integrity Job Started By User ID",
                is_ui_displayable=False,
                is_ui_editable=False,
                is_import_field_mappable=False,
            )
        ),
    ] = None
    integrity_job_started_by_job_ids: Annotated[
        list[UUID],
        FieldMetadata(
            type_property=ListFieldProperty(
                element_field_type_property=UUIDFieldProperty(
                    field_display_name="Integrity Job Started By Job ID",
                ),
                field_display_name="Integrity Job Started By Job IDs",
                is_ui_displayable=False,
                is_ui_editable=False,
                is_import_field_mappable=False,
            ),
        ),
    ] = Field(default_factory=list)
    integrity_job_finished_by_user_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Integrity Job Finished By User ID",
                is_ui_displayable=False,
                is_ui_editable=False,
                is_import_field_mappable=False,
            )
        ),
    ] = None

    primary_account_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Primary Account ID",
                is_ui_displayable=False,
                is_ui_editable=True,
                is_import_field_mappable=False,
            )
        ),
    ] = None
    contact_emails: Annotated[
        list[ContactEmailLite],
        FieldMetadata(
            type_property=ListFieldProperty(
                element_field_type_property=NestedObjectFieldProperty(
                    object_identifier=StdObjectIdentifiers.contact_email.identifier,
                    field_display_name="Contact Email",
                ),
                field_display_name="Contact Emails",
                is_ui_displayable=True,
                is_ui_editable=True,
                is_import_field_mappable=False,
            )
        ),
    ] = Field(default_factory=list)
    contact_phone_numbers: Annotated[
        list[ContactPhoneNumber],
        FieldMetadata(
            type_property=ListFieldProperty(
                element_field_type_property=NestedObjectFieldProperty(
                    object_identifier=StdObjectIdentifiers.contact_phone_number.identifier,
                    field_display_name="Contact Phone Number",
                ),
                field_display_name="Contact Phone Numbers",
                is_ui_displayable=False,
                is_ui_editable=False,
                is_import_field_mappable=False,
            )
        ),
    ] = Field(default_factory=list)
    contact_account_associations: Annotated[
        list[ContactAccountAssociationLite],
        FieldMetadata(
            type_property=ListFieldProperty(
                element_field_type_property=NestedObjectFieldProperty(
                    object_identifier=StdObjectIdentifiers.contact_account_association.identifier,
                    field_display_name="Contact Account Associations",
                ),
                field_display_name="Contact Account Associations",
                is_ui_displayable=False,
                is_ui_editable=False,
                is_import_field_mappable=False,
            )
        ),
    ] = Field(default_factory=list)
    created_source: Annotated[
        CreatedSource | None,
        FieldMetadata(
            type_property=DefaultEnumFieldProperty(
                enum_class=CreatedSource,
                field_display_name="Created Source",
                is_ui_displayable=True,
                is_ui_editable=False,
                is_import_field_mappable=False,
            )
        ),
    ] = None
    merge_to_contact_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Merge To Contact ID",
                is_ui_displayable=False,
                is_ui_editable=False,
                is_import_field_mappable=False,
            )
        ),
    ] = None  # to be deprecated

    # Timestamps
    created_at: Annotated[
        ZoneRequiredDateTime,
        FieldMetadata(
            type_property=TimestampFieldProperty(
                field_display_name="Created At",
                is_ui_displayable=True,
                is_ui_editable=False,
                is_import_field_mappable=True,
            )
        ),
    ]
    updated_at: Annotated[
        ZoneRequiredDateTime,
        FieldMetadata(
            type_property=TimestampFieldProperty(
                field_display_name="Last Updated At",
                is_ui_displayable=True,
                is_ui_editable=False,
                is_import_field_mappable=False,
            )
        ),
    ]
    archived_at: Annotated[
        ZoneRequiredDateTime | None,
        FieldMetadata(
            type_property=TimestampFieldProperty(
                field_display_name="Archived At",
                is_ui_displayable=True,
                is_ui_editable=False,
                is_import_field_mappable=False,
            )
        ),
    ] = None
    integrity_job_started_at: Annotated[
        ZoneRequiredDateTime | None,
        FieldMetadata(
            type_property=TimestampFieldProperty(
                field_display_name="Integrity Job Started At",
                is_ui_displayable=False,
                is_ui_editable=False,
                is_import_field_mappable=False,
            )
        ),
    ] = None
    integrity_job_finished_at: Annotated[
        ZoneRequiredDateTime | None,
        FieldMetadata(
            type_property=TimestampFieldProperty(
                field_display_name="Integrity Job Finished At",
                is_ui_displayable=False,
                is_ui_editable=False,
                is_import_field_mappable=False,
            )
        ),
    ] = None

    person_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Person ID",
                is_ui_displayable=False,
                is_ui_editable=False,
                is_import_field_mappable=False,
            )
        ),
    ] = None

    participant_user_id_list: Annotated[
        list[UUID],
        FieldMetadata(
            type_property=ListFieldProperty(
                element_field_type_property=UUIDFieldProperty(
                    field_display_name="Participant User ID",
                ),
                field_display_name="Participant User IDs",
                is_ui_displayable=True,
                is_ui_editable=True,
                is_import_field_mappable=False,
            ),
        ),
    ] = Field(default_factory=list)

    phone_enriched_at: Annotated[
        ZoneRequiredDateTime | None,
        FieldMetadata(
            type_property=TimestampFieldProperty(
                field_display_name="Phone Number Last Enrichment At",
                is_ui_displayable=True,
                is_ui_editable=False,
                is_import_field_mappable=False,
            )
        ),
    ] = None

    email_enriched_at: Annotated[
        ZoneRequiredDateTime | None,
        FieldMetadata(
            type_property=TimestampFieldProperty(
                field_display_name="Email Last Enrichment At",
                is_ui_displayable=True,
                is_ui_editable=False,
                is_import_field_mappable=False,
            )
        ),
    ] = None
