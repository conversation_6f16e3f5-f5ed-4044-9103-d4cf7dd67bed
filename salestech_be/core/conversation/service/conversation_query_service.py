from typing import Annotated
from uuid import UUID

from fastapi import Depends
from fastapi.requests import Request

from salestech_be.common.lifespan import get_db_engine
from salestech_be.common.singleton import Singleton
from salestech_be.core.common.domain_service import (
    DomainQueryService,
)
from salestech_be.core.conversation.types.conversation_types import Conversation
from salestech_be.core.ff.feature_flag_service import (
    FeatureFlagService,
    get_feature_flag_service,
)
from salestech_be.db.dao.conversation_repository import ConversationRepository
from salestech_be.db.dao.meeting_repository import MeetingRepository
from salestech_be.db.dao.voice_call_repository import VoiceCallRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.conversation import (
    Conversation as DBConversation,
)
from salestech_be.db.models.conversation import (
    ConversationReferenceType,
)


class ConversationQueryService(DomainQueryService[Conversation]):
    def __init__(
        self,
        conversation_repository: Annotated[ConversationRepository, Depends()],
        meeting_repository: Annotated[MeetingRepository, Depends()],
        voice_call_repository: Annotated[VoiceCallRepository, Depends()],
        feature_flag_service: Annotated[
            FeatureFlagService, Depends(get_feature_flag_service)
        ],
    ):
        super().__init__(feature_flag_service=feature_flag_service)
        self.conversation_repository = conversation_repository
        self.meeting_repository = meeting_repository
        self.voice_call_repository = voice_call_repository

    async def _enrich_conversation(
        self,
        *,
        db_conversations: list[DBConversation],
        organization_id: UUID,
        include_custom_object: bool | None = False,
    ) -> list[Conversation]:
        """
        Enrich a list of conversations (from DB objects) with additional information
        to form a list of domain-level conversations.
        """

        # TODO: additional associated obj lookups?
        return [
            Conversation(
                id=it.id,
                organization_id=it.organization_id,
                created_at=it.created_at,
                participant_user_id_list=[
                    p.user_id for p in it.participants if p.user_id is not None
                ]
                if it.participants
                else [],
                participant_contact_id_list=[
                    p.contact_id for p in it.participants if p.contact_id is not None
                ]
                if it.participants
                else [],
                attendee_user_id_list=[
                    p.user_id for p in it.attendees if p.user_id is not None
                ]
                if it.attendees
                else [],
                attendee_contact_id_list=[
                    p.contact_id for p in it.attendees if p.contact_id is not None
                ]
                if it.attendees
                else [],
                created_by_user_id=it.created_by_user_id,
                reference_id=it.reference_id,
                reference_type=it.reference_type,
                pipeline_id=it.pipeline_id,
                account_id=it.account_id,
            )
            for it in db_conversations
        ]

    async def list_conversation(
        self,
        *,
        organization_id: UUID,
        include_custom_object: bool | None = False,
    ) -> list[Conversation]:
        """
        List all conversations for an organization.
        """

        # TODO: list all might be bad/slow. Check with FE if we an do time ranged query.

        db_conversations: list[
            DBConversation
        ] = await self.conversation_repository.find_all_conversations_tenanted(
            organization_id=organization_id,
        )

        return await self._enrich_conversation(
            db_conversations=db_conversations,
            organization_id=organization_id,
            include_custom_object=include_custom_object,
        )

    async def list_conversation_by_reference_type(
        self,
        *,
        organization_id: UUID,
        reference_type: ConversationReferenceType,
        include_custom_object: bool | None = False,
    ) -> list[Conversation]:
        """
        List all conversations for an organization for a specific conversation reference type (e.g., meeting, call).
        """

        # TODO: list all might be bad/slow. Check with FE if we an do time ranged query.

        db_conversations: list[
            DBConversation
        ] = await self.conversation_repository.find_all_conversations_by_reference_type_tenanted(
            organization_id=organization_id,
            reference_type=reference_type,
        )

        return await self._enrich_conversation(
            db_conversations=db_conversations,
            organization_id=organization_id,
            include_custom_object=include_custom_object,
        )


class SingletonConversationQueryService(Singleton, ConversationQueryService):
    pass


def get_conversation_query_service_from_engine(
    db_engine: DatabaseEngine,
) -> ConversationQueryService:
    return SingletonConversationQueryService(
        conversation_repository=ConversationRepository(
            engine=db_engine,
        ),
        meeting_repository=MeetingRepository(
            engine=db_engine,
        ),
        voice_call_repository=VoiceCallRepository(
            engine=db_engine,
        ),
        feature_flag_service=get_feature_flag_service(),
    )


def get_conversation_query_service(
    request: Request,
) -> ConversationQueryService:
    if SingletonConversationQueryService.has_instance():
        return SingletonConversationQueryService.get_singleton_instance()
    return get_conversation_query_service_from_engine(
        db_engine=get_db_engine(request),
    )
