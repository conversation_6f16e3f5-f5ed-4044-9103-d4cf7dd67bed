from typing import Annotated
from uuid import UUID

from pydantic import Field

from salestech_be.common.schema_manager.std_object_field_identifier import (
    AccountField,
    ContactField,
    ConversationField,
    OrganizationUserField,
    PipelineField,
    StdObjectIdentifiers,
)
from salestech_be.common.schema_manager.std_object_relationship import (
    ConversationRelationship,
)
from salestech_be.common.type.metadata.field.field_indexable_config import (
    UniqueIndexableConfig,
)
from salestech_be.common.type.metadata.field.field_type_property import (
    Default<PERSON>numFieldProperty,
    ListFieldProperty,
    TimestampFieldProperty,
    UUIDFieldProperty,
)
from salestech_be.common.type.metadata.schema import (
    OutboundRelationship,
)
from salestech_be.core.common.types import CustomizableDomainModel, FieldMetadata
from salestech_be.db.models.conversation import ConversationReferenceType
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime


class Conversation(CustomizableDomainModel):
    object_id = StdObjectIdentifiers.conversation.identifier
    object_display_name = "Conversation"
    field_name_provider = ConversationField
    inbound_relationships = ()
    outbound_relationships = (
        OutboundRelationship(
            relation_type=OutboundRelationship.RelationType.LOOKUP,
            id=ConversationRelationship.conversation__to__created_by_user,
            relationship_name="Created By User",
            self_object_identifier=StdObjectIdentifiers.conversation.identifier,
            related_object_identifier=StdObjectIdentifiers.user.identifier,
            self_cardinality=OutboundRelationship.Cardinality.MANY,
            related_object_cardinality=OutboundRelationship.Cardinality.MANY,
            ordered_self_field_identifiers=(
                ConversationField.created_by_user_id.identifier,
            ),
            ordered_related_field_identifiers=(OrganizationUserField.id.identifier,),
        ),
        OutboundRelationship(
            relation_type=OutboundRelationship.RelationType.LOOKUP,
            id=ConversationRelationship.conversation__to__participant_user,
            relationship_name="Participant User",
            self_object_identifier=StdObjectIdentifiers.conversation.identifier,
            related_object_identifier=StdObjectIdentifiers.user.identifier,
            self_cardinality=OutboundRelationship.Cardinality.MANY,
            related_object_cardinality=OutboundRelationship.Cardinality.MANY,
            ordered_self_field_identifiers=(
                ConversationField.participant_user_id_list.identifier,
            ),
            ordered_related_field_identifiers=(OrganizationUserField.id.identifier,),
        ),
        OutboundRelationship(
            relation_type=OutboundRelationship.RelationType.LOOKUP,
            id=ConversationRelationship.conversation__to__participant_contact,
            relationship_name="Participant Contact",
            self_object_identifier=StdObjectIdentifiers.conversation.identifier,
            related_object_identifier=StdObjectIdentifiers.contact.identifier,
            self_cardinality=OutboundRelationship.Cardinality.MANY,
            related_object_cardinality=OutboundRelationship.Cardinality.MANY,
            ordered_self_field_identifiers=(
                ConversationField.participant_contact_id_list.identifier,
            ),
            ordered_related_field_identifiers=(ContactField.id.identifier,),
        ),
        OutboundRelationship(
            relation_type=OutboundRelationship.RelationType.LOOKUP,
            id=ConversationRelationship.conversation__to__attendee_user,
            relationship_name="Attendee User",
            self_object_identifier=StdObjectIdentifiers.conversation.identifier,
            related_object_identifier=StdObjectIdentifiers.user.identifier,
            self_cardinality=OutboundRelationship.Cardinality.MANY,
            related_object_cardinality=OutboundRelationship.Cardinality.MANY,
            ordered_self_field_identifiers=(
                ConversationField.attendee_user_id_list.identifier,
            ),
            ordered_related_field_identifiers=(OrganizationUserField.id.identifier,),
        ),
        OutboundRelationship(
            relation_type=OutboundRelationship.RelationType.LOOKUP,
            id=ConversationRelationship.conversation__to__attendee_contact,
            relationship_name="Attendee Contact",
            self_object_identifier=StdObjectIdentifiers.conversation.identifier,
            related_object_identifier=StdObjectIdentifiers.contact.identifier,
            self_cardinality=OutboundRelationship.Cardinality.MANY,
            related_object_cardinality=OutboundRelationship.Cardinality.MANY,
            ordered_self_field_identifiers=(
                ConversationField.attendee_contact_id_list.identifier,
            ),
            ordered_related_field_identifiers=(ContactField.id.identifier,),
        ),
        OutboundRelationship(
            relation_type=OutboundRelationship.RelationType.LOOKUP,
            id=ConversationRelationship.conversation__to__account,
            relationship_name="Account",
            self_object_identifier=StdObjectIdentifiers.conversation.identifier,
            related_object_identifier=StdObjectIdentifiers.account.identifier,
            self_cardinality=OutboundRelationship.Cardinality.MANY,
            related_object_cardinality=OutboundRelationship.Cardinality.MANY,
            ordered_self_field_identifiers=(ConversationField.account_id.identifier,),
            ordered_related_field_identifiers=(AccountField.id.identifier,),
        ),
        OutboundRelationship(
            relation_type=OutboundRelationship.RelationType.LOOKUP,
            id=ConversationRelationship.conversation__to__pipeline,
            relationship_name="Opportunity",
            self_object_identifier=StdObjectIdentifiers.conversation.identifier,
            related_object_identifier=StdObjectIdentifiers.pipeline.identifier,
            self_cardinality=OutboundRelationship.Cardinality.MANY,
            related_object_cardinality=OutboundRelationship.Cardinality.ONE,
            ordered_self_field_identifiers=(ConversationField.pipeline_id.identifier,),
            ordered_related_field_identifiers=(PipelineField.id.identifier,),
        ),
    )

    # Basic information
    id: Annotated[
        UUID,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                index_config=UniqueIndexableConfig(is_indexed=True, is_unique=True),
                field_display_name="ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            )
        ),
    ]

    organization_id: Annotated[
        UUID,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Organization ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            )
        ),
    ]

    created_at: Annotated[
        ZoneRequiredDateTime,
        FieldMetadata(
            type_property=TimestampFieldProperty(
                field_display_name="Created At",
                is_ui_displayable=False,
                is_ui_editable=False,
            )
        ),
    ]

    participant_user_id_list: Annotated[
        list[UUID],
        FieldMetadata(
            type_property=ListFieldProperty(
                element_field_type_property=UUIDFieldProperty(
                    field_display_name="Participant User ID",
                ),
                field_display_name="Participant User IDs",
                is_ui_displayable=False,
                is_ui_editable=False,
            ),
        ),
        Field(default_factory=list),
    ]

    participant_contact_id_list: Annotated[
        list[UUID],
        FieldMetadata(
            type_property=ListFieldProperty(
                element_field_type_property=UUIDFieldProperty(
                    field_display_name="Participant Contact ID",
                ),
                field_display_name="Participant Contact IDs",
                is_ui_displayable=False,
                is_ui_editable=False,
            ),
        ),
        Field(default_factory=list),
    ]

    attendee_user_id_list: Annotated[
        list[UUID],
        FieldMetadata(
            type_property=ListFieldProperty(
                element_field_type_property=UUIDFieldProperty(
                    field_display_name="Attendee User ID",
                ),
                field_display_name="Attendee User IDs",
                is_ui_displayable=False,
                is_ui_editable=False,
            ),
        ),
        Field(default_factory=list),
    ]

    attendee_contact_id_list: Annotated[
        list[UUID],
        FieldMetadata(
            type_property=ListFieldProperty(
                element_field_type_property=UUIDFieldProperty(
                    field_display_name="Attendee Contact ID",
                ),
                field_display_name="Attendee Contact IDs",
                is_ui_displayable=False,
                is_ui_editable=False,
            ),
        ),
        Field(default_factory=list),
    ]

    created_by_user_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Created User Id",
                is_ui_displayable=False,
                is_ui_editable=False,
            )
        ),
    ] = None

    reference_id: Annotated[
        UUID,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Reference ID",
                is_ui_displayable=False,
                is_ui_editable=False,
                index_config=UniqueIndexableConfig(is_indexed=True, is_unique=False),
            ),
        ),
    ]

    reference_type: Annotated[
        ConversationReferenceType,
        FieldMetadata(
            type_property=DefaultEnumFieldProperty(
                field_display_name="Reference Type",
                enum_class=ConversationReferenceType,
                is_ui_displayable=False,
                is_ui_editable=False,
            )
        ),
    ]

    pipeline_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Opportunity ID",
                is_ui_displayable=False,
                is_ui_editable=True,
            )
        ),
    ] = None

    account_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Account ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            )
        ),
    ] = None
