"""Service layer adapters for transforming between database models and domain DTOs."""

from uuid import UUID, uuid4

from salestech_be.core.cpq.adapters.price_book_entry_adapter import (
    PriceBookEntryAdapter,
)
from salestech_be.core.cpq.dto.price_book_dto import (
    CreatePriceBookDTO,
    PriceBookDTO,
    UpdatePriceBookDTO,
)
from salestech_be.db.models.price_book import (
    PriceBook as PriceBookModel,
)
from salestech_be.db.models.price_book import (
    PriceBookEntry as PriceBookEntryModel,
)
from salestech_be.util.time import zoned_utc_now


class PriceBookAdapter:
    """Adapter for transforming between Price Book models and DTOs."""

    @staticmethod
    def price_book_to_dto(
        model: PriceBookModel, entries: list[PriceBookEntryModel] | None = None
    ) -> PriceBookDTO:
        """Transform a Price Book model to a domain DTO."""
        entry_dtos = []
        if entries:
            entry_dtos = [
                PriceBookEntryAdapter.price_book_entry_to_dto(entry)
                for entry in entries
            ]

        return PriceBookDTO(
            id=model.id,
            organization_id=model.organization_id,
            is_default=model.is_default,
            name=model.name,
            description=model.description,
            created_at=model.created_at,
            created_by_user_id=model.created_by_user_id,
            updated_at=model.updated_at,
            updated_by_user_id=model.updated_by_user_id,
            deleted_at=model.deleted_at,
            deleted_by_user_id=model.deleted_by_user_id,
            entries=entry_dtos,
        )

    @staticmethod
    def price_book_dto_to_db_model(dto: PriceBookDTO) -> PriceBookModel:
        """Transform a Price Book DTO to a database model."""
        return PriceBookModel(
            id=dto.id,
            organization_id=dto.organization_id,
            is_default=dto.is_default,
            name=dto.name,
            description=dto.description,
            created_at=dto.created_at,
            created_by_user_id=dto.created_by_user_id,
            updated_at=dto.updated_at,
            updated_by_user_id=dto.updated_by_user_id,
            deleted_at=dto.deleted_at,
            deleted_by_user_id=dto.deleted_by_user_id,
        )

    @staticmethod
    def create_price_book_dto_to_db_model(dto: CreatePriceBookDTO) -> PriceBookModel:
        """Transform a Create Price Book DTO to a database model."""
        return PriceBookModel(
            id=uuid4(),
            organization_id=dto.organization_id,
            is_default=dto.is_default,
            name=dto.name,
            description=dto.description,
            created_at=zoned_utc_now(),
            created_by_user_id=dto.created_by_user_id,
            updated_at=None,
            updated_by_user_id=None,
            deleted_at=None,
            deleted_by_user_id=None,
        )

    @staticmethod
    def update_price_book_dto_to_db_model(
        dto: UpdatePriceBookDTO, existing_model: PriceBookModel
    ) -> PriceBookModel:
        """Transform an Update Price Book DTO to a database model."""
        updates: dict[str, str | bool | UUID | object] = {}
        if dto.name is not None:
            updates["name"] = dto.name
        if dto.description is not None:
            updates["description"] = dto.description
        if dto.is_default is not None:
            updates["is_default"] = dto.is_default

        if updates:
            updates.update(
                {
                    "updated_at": zoned_utc_now(),
                    "updated_by_user_id": dto.updated_by_user_id,
                }
            )

        return existing_model.model_copy(update=updates)
