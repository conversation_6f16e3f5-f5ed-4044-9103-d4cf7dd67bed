"""Adapter for transforming between Price Book Entry models and DTOs."""

from uuid import uuid4

from salestech_be.core.cpq.dto.price_book_entry_dto import (
    CreatePriceBookEntryDTO,
    PriceBookEntryDTO,
    UpdatePriceBookEntryDTO,
)
from salestech_be.db.models.price_book import PriceBookEntry as PriceBookEntryModel
from salestech_be.util.time import zoned_utc_now


class PriceBookEntryAdapter:
    """Adapter for transforming between Price Book Entry models and DTOs."""

    @staticmethod
    def price_book_entry_to_dto(model: PriceBookEntryModel) -> PriceBookEntryDTO:
        """Transform a Price Book Entry model to a domain DTO."""
        return PriceBookEntryDTO(
            id=model.id,
            organization_id=model.organization_id,
            price_book_id=model.price_book_id,
            product_key=model.product_key,
            billing_frequency=model.billing_frequency,
            unit_amount=model.unit_amount,
            version_number=model.version_number,
            status=model.status,
            created_at=model.created_at,
            created_by_user_id=model.created_by_user_id,
            updated_at=model.updated_at,
            updated_by_user_id=model.updated_by_user_id,
            deleted_at=model.deleted_at,
            deleted_by_user_id=model.deleted_by_user_id,
        )

    @staticmethod
    def price_book_entry_dto_to_db_model(dto: PriceBookEntryDTO) -> PriceBookEntryModel:
        """Transform a Price Book Entry DTO to a database model."""
        return PriceBookEntryModel(
            id=dto.id,
            organization_id=dto.organization_id,
            price_book_id=dto.price_book_id,
            product_key=dto.product_key,
            billing_frequency=dto.billing_frequency,
            unit_amount=dto.unit_amount,
            version_number=dto.version_number,
            status=dto.status,
            created_at=dto.created_at,
            created_by_user_id=dto.created_by_user_id,
            updated_at=dto.updated_at,
            updated_by_user_id=dto.updated_by_user_id,
            deleted_at=dto.deleted_at,
            deleted_by_user_id=dto.deleted_by_user_id,
        )

    @staticmethod
    def create_price_book_entry_dto_to_db_model(
        dto: CreatePriceBookEntryDTO,
    ) -> PriceBookEntryModel:
        """Transform a Create Price Book Entry DTO to a database model."""
        return PriceBookEntryModel(
            id=uuid4(),
            organization_id=dto.organization_id,
            price_book_id=dto.price_book_id,
            product_key=dto.product_key,
            billing_frequency=dto.billing_frequency,
            unit_amount=dto.unit_amount,
            version_number=dto.version_number,
            status=dto.status,
            created_at=zoned_utc_now(),
            created_by_user_id=dto.created_by_user_id,
            updated_at=None,
            updated_by_user_id=None,
            deleted_at=None,
            deleted_by_user_id=None,
        )

    @staticmethod
    def create_new_version_model(
        update_dto: UpdatePriceBookEntryDTO,
        existing_model: PriceBookEntryModel,
        new_version_number: int,
    ) -> PriceBookEntryModel:
        """Create a new version model from an update DTO and existing model."""
        return PriceBookEntryModel(
            id=uuid4(),
            organization_id=existing_model.organization_id,
            price_book_id=existing_model.price_book_id,
            product_key=existing_model.product_key,
            billing_frequency=(
                update_dto.billing_frequency
                if update_dto.billing_frequency is not None
                else existing_model.billing_frequency
            ),
            unit_amount=(
                update_dto.unit_amount
                if update_dto.unit_amount is not None
                else existing_model.unit_amount
            ),
            version_number=new_version_number,
            status=(
                update_dto.status
                if update_dto.status is not None
                else existing_model.status
            ),
            created_at=zoned_utc_now(),
            created_by_user_id=existing_model.created_by_user_id,
            updated_at=None,
            updated_by_user_id=update_dto.updated_by_user_id,
            deleted_at=None,
            deleted_by_user_id=None,
        )
