from salestech_be.core.cpq.dto.product_dto import (
    ProductDTO,
    ProductRelationshipDTO,
)
from salestech_be.db.models.product import Product
from salestech_be.db.models.product_relationship import ProductRelationship


class ProductAdapter:
    """Converts between service DTOs and database models (Core Layer)"""

    @classmethod
    def product_dto_to_db_model(cls, dto: ProductDTO) -> Product:
        """Convert ProductDTO to Product database model"""
        return cls._product_dto_to_db_model(dto)

    @classmethod
    def relationship_dto_to_db_model(
        cls, dto: ProductRelationshipDTO
    ) -> ProductRelationship:
        """Convert ProductRelationshipDTO to ProductRelationship database model"""
        return cls._relationship_dto_to_db_model(dto)

    @classmethod
    def product_to_dto(cls, model: Product) -> ProductDTO:
        """Convert Product database model to ProductDTO"""
        return cls._product_db_model_to_dto(model)

    @classmethod
    def relationship_to_dto(cls, model: ProductRelationship) -> ProductRelationshipDTO:
        """Convert ProductRelationship database model to ProductRelationshipDTO"""
        return cls._relationship_db_model_to_dto(model)

    # Keep generic methods for backwards compatibility if needed
    @classmethod
    def to_db_model(
        cls, dto: ProductDTO | ProductRelationshipDTO
    ) -> Product | ProductRelationship:
        """Generic method to convert any product DTO to database model"""
        if isinstance(dto, ProductDTO):
            return cls._product_dto_to_db_model(dto)
        elif isinstance(dto, ProductRelationshipDTO):
            return cls._relationship_dto_to_db_model(dto)
        else:
            raise ValueError(f"Unsupported DTO type: {type(dto)}")

    @classmethod
    def to_dto(
        cls, model: Product | ProductRelationship
    ) -> ProductDTO | ProductRelationshipDTO:
        """Generic method to convert any product or relationship database model to DTO"""
        if isinstance(model, Product):
            return cls._product_db_model_to_dto(model)
        elif isinstance(model, ProductRelationship):
            return cls._relationship_db_model_to_dto(model)
        else:
            raise ValueError(f"Unsupported model type: {type(model)}")

    @classmethod
    def _product_dto_to_db_model(cls, dto: ProductDTO) -> Product:
        """Convert ProductDTO to Product database model"""
        # Ensure version_number is never None when creating database model
        version_number = dto.version_number if dto.version_number is not None else 1

        return Product(
            id=dto.id,
            organization_id=dto.organization_id,
            product_key=dto.product_key,
            name=dto.name,
            sku=dto.sku,
            status=dto.status,
            type=dto.type,
            version_number=version_number,
            properties=dto.properties,
            description=dto.description,
            created_at=dto.created_at,
            created_by_user_id=dto.created_by_user_id,
            updated_at=dto.updated_at,
            updated_by_user_id=dto.updated_by_user_id,
            deleted_at=dto.deleted_at,
            deleted_by_user_id=dto.deleted_by_user_id,
        )

    @classmethod
    def _product_db_model_to_dto(cls, model: Product) -> ProductDTO:
        """Convert Product database model to ProductDTO"""
        return ProductDTO(
            id=model.id,
            organization_id=model.organization_id,
            product_key=model.product_key,
            name=model.name,
            sku=model.sku,
            status=model.status,
            type=model.type,
            version_number=model.version_number,
            properties=model.properties,
            description=model.description,
            created_at=model.created_at,
            created_by_user_id=model.created_by_user_id,
            updated_at=model.updated_at,
            updated_by_user_id=model.updated_by_user_id,
            deleted_at=model.deleted_at,
            deleted_by_user_id=model.deleted_by_user_id,
        )

    @classmethod
    def _relationship_dto_to_db_model(
        cls, dto: ProductRelationshipDTO
    ) -> ProductRelationship:
        """Convert ProductRelationshipDTO to ProductRelationship database model"""
        return ProductRelationship(
            id=dto.id,
            organization_id=dto.organization_id,
            product_id=dto.product_id,
            related_product_id=dto.related_product_id,
            relationship_type=dto.relationship_type,
            required=dto.required,
            created_at=dto.created_at,
            created_by_user_id=dto.created_by_user_id,
            updated_at=dto.updated_at,
            updated_by_user_id=dto.updated_by_user_id,
            deleted_at=dto.deleted_at,
            deleted_by_user_id=dto.deleted_by_user_id,
        )

    @classmethod
    def _relationship_db_model_to_dto(
        cls, model: ProductRelationship
    ) -> ProductRelationshipDTO:
        """Convert ProductRelationship database model to ProductRelationshipDTO"""
        return ProductRelationshipDTO(
            id=model.id,
            organization_id=model.organization_id,
            product_id=model.product_id,
            related_product_id=model.related_product_id,
            relationship_type=model.relationship_type,
            required=model.required,
            created_at=model.created_at,
            created_by_user_id=model.created_by_user_id,
            updated_at=model.updated_at,
            updated_by_user_id=model.updated_by_user_id,
            deleted_at=model.deleted_at,
            deleted_by_user_id=model.deleted_by_user_id,
        )
