from uuid import uuid4

from salestech_be.core.cpq.dto.quote_dto import (
    QuoteDTO,
    QuoteLineItemDTO,
)
from salestech_be.db.models.quote import Quote
from salestech_be.db.models.quote_line_item import QuoteLineItem


class QuoteAdapter:
    """Converts between service DTOs and database models (Core Layer)"""

    @staticmethod
    def quote_dto_to_db_model(dto: QuoteDTO) -> Quote:
        """Convert QuoteDTO to Quote database model"""
        return QuoteAdapter._quote_dto_to_db_model(dto)

    @staticmethod
    def quote_line_item_dto_to_db_model(dto: QuoteLineItemDTO) -> QuoteLineItem:
        """Convert QuoteLineItemDTO to QuoteLineItem database model"""
        return QuoteAdapter._quote_line_item_dto_to_db_model(dto)

    @staticmethod
    def quote_to_dto(model: Quote) -> QuoteDTO:
        """Convert Quote database model to QuoteDTO"""
        return QuoteAdapter._quote_db_model_to_dto(model)

    @staticmethod
    def quote_line_item_to_dto(model: QuoteLineItem) -> QuoteLineItemDTO:
        """Convert QuoteLineItem database model to QuoteLineItemDTO"""
        return QuoteAdapter._quote_line_item_db_model_to_dto(model)

    # Keep generic methods for backwards compatibility if needed
    @staticmethod
    def to_db_model(dto: QuoteDTO | QuoteLineItemDTO) -> Quote | QuoteLineItem:
        """Generic method to convert any quote DTO to database model"""
        if isinstance(dto, QuoteDTO):
            return QuoteAdapter._quote_dto_to_db_model(dto)
        elif isinstance(dto, QuoteLineItemDTO):
            return QuoteAdapter._quote_line_item_dto_to_db_model(dto)
        else:
            raise ValueError(f"Unsupported DTO type: {type(dto)}")

    @staticmethod
    def to_dto(model: Quote | QuoteLineItem) -> QuoteDTO | QuoteLineItemDTO:
        """Generic method to convert any quote database model to DTO"""
        if isinstance(model, Quote):
            return QuoteAdapter._quote_db_model_to_dto(model)
        elif isinstance(model, QuoteLineItem):
            return QuoteAdapter._quote_line_item_db_model_to_dto(model)
        else:
            raise ValueError(f"Unsupported model type: {type(model)}")

    @staticmethod
    def _quote_dto_to_db_model(dto: QuoteDTO) -> Quote:
        """Convert QuoteDTO to Quote database model"""
        return Quote(
            id=dto.id,
            organization_id=dto.organization_id,
            name=dto.name,
            status=dto.status,
            quote_key=dto.quote_key or uuid4(),
            version_number=dto.version_number or 1,
            rescind_reason=dto.rescind_reason,
            opportunity_id=dto.opportunity_id,
            created_by_user_id=dto.created_by_user_id,
            created_at=dto.created_at,
            updated_at=dto.updated_at,
            updated_by_user_id=dto.updated_by_user_id,
            deleted_at=dto.deleted_at,
            deleted_by_user_id=dto.deleted_by_user_id,
        )

    @staticmethod
    def _quote_db_model_to_dto(model: Quote) -> QuoteDTO:
        """Convert Quote database model to QuoteDTO"""
        return QuoteDTO(
            id=model.id,
            organization_id=model.organization_id,
            name=model.name,
            status=model.status,
            quote_key=model.quote_key,
            version_number=model.version_number,
            rescind_reason=model.rescind_reason,
            opportunity_id=model.opportunity_id,
            created_by_user_id=model.created_by_user_id,
            created_at=model.created_at,
            updated_at=model.updated_at,
            updated_by_user_id=model.updated_by_user_id,
            deleted_at=model.deleted_at,
            deleted_by_user_id=model.deleted_by_user_id,
        )

    @staticmethod
    def _quote_line_item_dto_to_db_model(dto: QuoteLineItemDTO) -> QuoteLineItem:
        """Convert QuoteLineItemDTO to QuoteLineItem database model"""
        return QuoteLineItem(
            id=dto.id,
            organization_id=dto.organization_id,
            quote_id=dto.quote_id,
            price_book_entry_id=dto.price_book_entry_id,
            product_id=dto.product_id,
            parent_line_item_id=dto.parent_line_item_id,
            quantity=dto.quantity,
            final_amount_cents=dto.final_amount_cents,
            status=dto.status,
            start_date=dto.start_date,
            end_date=dto.end_date,
            created_at=dto.created_at,
            created_by_user_id=dto.created_by_user_id,
            updated_at=dto.updated_at,
            updated_by_user_id=dto.updated_by_user_id,
            deleted_at=dto.deleted_at,
            deleted_by_user_id=dto.deleted_by_user_id,
        )

    @staticmethod
    def _quote_line_item_db_model_to_dto(model: QuoteLineItem) -> QuoteLineItemDTO:
        """Convert QuoteLineItem database model to QuoteLineItemDTO"""
        return QuoteLineItemDTO(
            id=model.id,
            organization_id=model.organization_id,
            quote_id=model.quote_id,
            price_book_entry_id=model.price_book_entry_id,
            product_id=model.product_id,
            parent_line_item_id=model.parent_line_item_id,
            quantity=model.quantity,
            final_amount_cents=model.final_amount_cents,
            status=model.status,
            start_date=model.start_date,
            end_date=model.end_date,
            created_at=model.created_at,
            created_by_user_id=model.created_by_user_id,
            updated_at=model.updated_at,
            updated_by_user_id=model.updated_by_user_id,
            deleted_at=model.deleted_at,
            deleted_by_user_id=model.deleted_by_user_id,
        )
