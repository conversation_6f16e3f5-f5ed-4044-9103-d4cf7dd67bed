"""Price Book domain DTOs with business logic."""

from uuid import UUID

from pydantic import BaseModel, Field

from salestech_be.core.cpq.dto.price_book_entry_dto import (
    PriceBookEntryDTO,
)
from salestech_be.db.models.price_book import BillingFrequency, PriceBookEntryStatus
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime


class PriceBookDTO(BaseModel):
    """Domain DTO for Price Book with business logic."""

    id: UUID
    organization_id: UUID
    is_default: bool
    name: str
    description: str | None = None
    created_at: ZoneRequiredDateTime
    created_by_user_id: UUID
    updated_at: ZoneRequiredDateTime | None = None
    updated_by_user_id: UUID | None = None
    deleted_at: ZoneRequiredDateTime | None = None
    deleted_by_user_id: UUID | None = None
    entries: list[PriceBookEntryDTO] = Field(default_factory=list)

    @property
    def is_active(self) -> bool:
        """Check if the price book is active (not deleted)."""
        return self.deleted_at is None

    @property
    def total_entries(self) -> int:
        """Calculate total number of entries."""
        return len(self.entries)

    @property
    def active_entries(self) -> list[PriceBookEntryDTO]:
        """Get all active entries."""
        return [entry for entry in self.entries if entry.is_active]

    @property
    def draft_entries(self) -> list[PriceBookEntryDTO]:
        """Get all draft entries."""
        return [
            entry
            for entry in self.entries
            if entry.status == PriceBookEntryStatus.DRAFT and entry.deleted_at is None
        ]

    @property
    def inactive_entries(self) -> list[PriceBookEntryDTO]:
        """Get all inactive entries."""
        return [
            entry
            for entry in self.entries
            if entry.status == PriceBookEntryStatus.INACTIVE
            and entry.deleted_at is None
        ]

    def get_entries_by_product(self, product_key: UUID) -> list[PriceBookEntryDTO]:
        """Get all entries for a specific product."""
        return [entry for entry in self.entries if entry.product_key == product_key]

    def get_entries_by_billing_frequency(
        self, frequency: BillingFrequency
    ) -> list[PriceBookEntryDTO]:
        """Get all entries for a specific billing frequency."""
        return [entry for entry in self.entries if entry.billing_frequency == frequency]

    def get_active_entry_for_product(
        self, product_key: UUID, billing_frequency: BillingFrequency | None = None
    ) -> PriceBookEntryDTO | None:
        """Get the active entry for a product with optional billing frequency filter."""
        entries = [
            entry for entry in self.active_entries if entry.product_key == product_key
        ]

        if billing_frequency:
            entries = [
                entry
                for entry in entries
                if entry.billing_frequency == billing_frequency
            ]

        # Return the latest version (highest version number)
        if entries:
            return max(entries, key=lambda e: e.version_number)
        return None

    def get_latest_entry_for_product(
        self, product_key: UUID, billing_frequency: BillingFrequency
    ) -> PriceBookEntryDTO | None:
        """Get the latest version entry for a product and billing frequency."""
        entries = [
            entry
            for entry in self.entries
            if entry.product_key == product_key
            and entry.billing_frequency == billing_frequency
            and entry.deleted_at is None
        ]

        if entries:
            return max(entries, key=lambda e: e.version_number)
        return None

    def has_active_entries(self) -> bool:
        """Check if the price book has any active entries."""
        return len(self.active_entries) > 0

    def can_be_deleted(self) -> bool:
        """Check if the price book can be deleted (not default and no active entries)."""
        return not self.is_default and not self.has_active_entries()

    def calculate_total_value(self, billing_frequency: BillingFrequency) -> int:
        """Calculate total value of all active entries for a billing frequency."""
        relevant_entries = [
            entry
            for entry in self.active_entries
            if entry.billing_frequency == billing_frequency
        ]
        return sum(entry.unit_amount for entry in relevant_entries)


class CreatePriceBookDTO(BaseModel):
    """DTO for creating a new price book."""

    name: str
    description: str | None = None
    is_default: bool = False
    organization_id: UUID
    created_by_user_id: UUID


class UpdatePriceBookDTO(BaseModel):
    """DTO for updating a price book."""

    id: UUID
    name: str | None = None
    description: str | None = None
    is_default: bool | None = None
    organization_id: UUID
    updated_by_user_id: UUID
