"""DTOs for Price Book Entry with business logic."""

from uuid import UUID

from pydantic import BaseModel

from salestech_be.db.models.price_book import BillingFrequency, PriceBookEntryStatus
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime


class PriceBookEntryDTO(BaseModel):
    """Domain DTO for Price Book Entry with business logic."""

    id: UUID
    organization_id: UUID
    price_book_id: UUID
    product_key: UUID
    billing_frequency: BillingFrequency
    unit_amount: int
    version_number: int
    status: PriceBookEntryStatus
    created_at: ZoneRequiredDateTime
    created_by_user_id: UUID
    updated_at: ZoneRequiredDateTime | None = None
    updated_by_user_id: UUID | None = None
    deleted_at: ZoneRequiredDateTime | None = None
    deleted_by_user_id: UUID | None = None

    @property
    def is_active(self) -> bool:
        """Check if the entry is active."""
        return self.status == PriceBookEntryStatus.ACTIVE and self.deleted_at is None

    @property
    def is_latest_version(self) -> bool:
        """This will be set by the adapter when loading with context."""
        # This property would be computed by the adapter when loading entries
        # with version context from the repository
        return True  # Default assumption, overridden by adapter

    @property
    def formatted_price(self) -> str:
        """Format price as currency string."""
        return f"${self.unit_amount / 100:.2f}"

    def is_compatible_with_billing_frequency(self, frequency: BillingFrequency) -> bool:
        """Check if this entry is compatible with a given billing frequency."""
        return self.billing_frequency == frequency

    def calculate_annual_amount(self) -> int:
        """Calculate the annual amount based on billing frequency."""
        if self.billing_frequency == BillingFrequency.ANNUALLY:
            return self.unit_amount
        elif self.billing_frequency == BillingFrequency.MONTHLY:
            return self.unit_amount * 12
        elif self.billing_frequency == BillingFrequency.ONE_TIME:
            return self.unit_amount
        else:
            raise ValueError(f"Unknown billing frequency: {self.billing_frequency}")


class CreatePriceBookEntryDTO(BaseModel):
    """DTO for creating a new price book entry."""

    price_book_id: UUID
    product_key: UUID
    billing_frequency: BillingFrequency
    unit_amount: int
    version_number: int = 1
    status: PriceBookEntryStatus = PriceBookEntryStatus.DRAFT
    organization_id: UUID
    created_by_user_id: UUID


class UpdatePriceBookEntryDTO(BaseModel):
    """DTO for updating a price book entry (creates new version)."""

    id: UUID  # Original entry ID
    billing_frequency: BillingFrequency | None = None
    unit_amount: int | None = None
    status: PriceBookEntryStatus | None = None
    organization_id: UUID
    updated_by_user_id: UUID

    @property
    def has_changes(self) -> bool:
        """Check if any fields are being updated."""
        return any(
            [
                self.billing_frequency is not None,
                self.unit_amount is not None,
                self.status is not None,
            ]
        )
