from uuid import UUID

from pydantic import BaseModel

from salestech_be.db.models.product import ProductStatus, ProductType
from salestech_be.db.models.product_relationship import ProductRelationshipType
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime


class ProductDTO(BaseModel):
    id: UUID
    organization_id: UUID
    product_key: UUID
    name: str
    sku: str
    status: ProductStatus
    type: ProductType
    version_number: int | None = None
    properties: dict[str, str] | None = None
    description: str | None = None
    created_at: ZoneRequiredDateTime
    created_by_user_id: UUID
    updated_at: ZoneRequiredDateTime | None = None
    updated_by_user_id: UUID | None = None
    deleted_at: ZoneRequiredDateTime | None = None
    deleted_by_user_id: UUID | None = None


class ProductRelationshipDTO(BaseModel):
    id: UUID
    organization_id: UUID
    product_id: UUID
    related_product_id: UUID
    relationship_type: ProductRelationshipType
    required: bool
    created_at: ZoneRequiredDateTime
    created_by_user_id: UUID
    updated_at: ZoneRequiredDateTime | None = None
    updated_by_user_id: UUID | None = None
    deleted_at: ZoneRequiredDateTime | None = None
    deleted_by_user_id: UUID | None = None
