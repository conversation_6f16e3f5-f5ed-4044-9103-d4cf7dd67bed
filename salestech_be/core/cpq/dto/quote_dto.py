from uuid import UUID

from pydantic import BaseModel, field_validator

from salestech_be.db.models.quote import QuoteStatus
from salestech_be.db.models.quote_line_item import QuoteLineItemStatus
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime


class QuoteDTO(BaseModel):
    id: UUID
    organization_id: UUID
    name: str
    status: QuoteStatus
    quote_key: UUID | None = None
    version_number: int | None = None
    rescind_reason: str | None = None
    opportunity_id: UUID | None = None
    created_by_user_id: UUID
    created_at: ZoneRequiredDateTime
    updated_at: ZoneRequiredDateTime | None = None
    updated_by_user_id: UUID | None = None
    deleted_at: ZoneRequiredDateTime | None = None
    deleted_by_user_id: UUID | None = None


class QuoteLineItemDTO(BaseModel):
    id: UUID
    organization_id: UUID
    quote_id: UUID
    price_book_entry_id: UUID
    product_id: UUID
    parent_line_item_id: UUID | None = None
    quantity: int
    final_amount_cents: int
    status: QuoteLineItemStatus
    start_date: ZoneRequiredDateTime | None = None
    end_date: ZoneRequiredDateTime | None = None
    created_at: ZoneRequiredDateTime
    created_by_user_id: UUID
    updated_at: ZoneRequiredDateTime | None = None
    updated_by_user_id: UUID | None = None
    deleted_at: ZoneRequiredDateTime | None = None
    deleted_by_user_id: UUID | None = None

    @field_validator("final_amount_cents")
    @classmethod
    def validate_final_amount_cents(cls, v: int) -> int:
        if v < 0:
            raise ValueError("final_amount_cents must be greater than or equal to 0")
        return v

    @field_validator("quantity")
    @classmethod
    def validate_quantity(cls, v: int) -> int:
        if v <= 0:
            raise ValueError("quantity must be greater than 0")
        return v
