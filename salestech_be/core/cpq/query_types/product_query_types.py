from __future__ import annotations

from typing import Annotated
from uuid import UUID

from salestech_be.common.schema_manager.std_object_field_identifier import (
    OrganizationUserField,
    ProductField,
    StdObjectIdentifiers,
)
from salestech_be.common.schema_manager.std_object_relationship import (
    ProductRelationship,
)
from salestech_be.common.type.metadata.field.field_indexable_config import (
    CaseAwareUniqueIndexableConfig,
    UniqueIndexableConfig,
)
from salestech_be.common.type.metadata.field.field_type_property import (
    DefaultEnumFieldProperty,
    DictFieldProperty,
    NumericFieldProperty,
    TextFieldProperty,
    TimestampFieldProperty,
    UUIDFieldProperty,
)
from salestech_be.common.type.metadata.schema import OutboundRelationship
from salestech_be.core.common.types import DomainModel, FieldMetadata
from salestech_be.db.models.product import (
    Product as DbProduct,
)
from salestech_be.db.models.product import (
    ProductStatus,
    ProductType,
)
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime


class Product(DomainModel):
    """Product domain model for CPQ system."""

    object_id = StdObjectIdentifiers.product.identifier
    object_display_name = "Product"
    field_name_provider = ProductField

    outbound_relationships = (
        OutboundRelationship(
            relation_type=OutboundRelationship.RelationType.LOOKUP,
            id=ProductRelationship.product__to__created_by_user,
            relationship_name="Created By User",
            self_object_identifier=StdObjectIdentifiers.product.identifier,
            related_object_identifier=StdObjectIdentifiers.user.identifier,
            self_cardinality=OutboundRelationship.Cardinality.MANY,
            related_object_cardinality=OutboundRelationship.Cardinality.ONE,
            ordered_self_field_identifiers=(
                ProductField.created_by_user_id.identifier,
            ),
            ordered_related_field_identifiers=(OrganizationUserField.id.identifier,),
        ),
        OutboundRelationship(
            relation_type=OutboundRelationship.RelationType.LOOKUP,
            id=ProductRelationship.product__to__updated_by_user,
            relationship_name="Updated By User",
            self_object_identifier=StdObjectIdentifiers.product.identifier,
            related_object_identifier=StdObjectIdentifiers.user.identifier,
            self_cardinality=OutboundRelationship.Cardinality.MANY,
            related_object_cardinality=OutboundRelationship.Cardinality.ONE,
            ordered_self_field_identifiers=(
                ProductField.updated_by_user_id.identifier,
            ),
            ordered_related_field_identifiers=(OrganizationUserField.id.identifier,),
        ),
        OutboundRelationship(
            relation_type=OutboundRelationship.RelationType.LOOKUP,
            id=ProductRelationship.product__to__deleted_by_user,
            relationship_name="Deleted By User",
            self_object_identifier=StdObjectIdentifiers.product.identifier,
            related_object_identifier=StdObjectIdentifiers.user.identifier,
            self_cardinality=OutboundRelationship.Cardinality.MANY,
            related_object_cardinality=OutboundRelationship.Cardinality.ONE,
            ordered_self_field_identifiers=(
                ProductField.deleted_by_user_id.identifier,
            ),
            ordered_related_field_identifiers=(OrganizationUserField.id.identifier,),
        ),
        OutboundRelationship(
            relation_type=OutboundRelationship.RelationType.LOOKUP,
            id=ProductRelationship.product__to__product_component,
            relationship_name="Product Components",
            self_object_identifier=StdObjectIdentifiers.product.identifier,
            related_object_identifier=StdObjectIdentifiers.product.identifier,
            self_cardinality=OutboundRelationship.Cardinality.ONE,
            related_object_cardinality=OutboundRelationship.Cardinality.MANY,
            ordered_self_field_identifiers=(ProductField.id.identifier,),
            ordered_related_field_identifiers=(ProductField.id.identifier,),
        ),
    )

    id: Annotated[
        UUID,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="ID",
                is_ui_displayable=False,
                is_ui_editable=False,
                index_config=UniqueIndexableConfig(is_indexed=True, is_unique=True),
            ),
        ),
    ]

    organization_id: Annotated[
        UUID,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Organization ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            ),
        ),
    ]

    product_key: Annotated[
        UUID,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Product Key",
                is_ui_displayable=False,
                is_ui_editable=False,
                index_config=UniqueIndexableConfig(is_indexed=True, is_unique=False),
            ),
        ),
    ]

    name: Annotated[
        str,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Name",
                is_ui_displayable=True,
                is_ui_editable=True,
            ),
        ),
    ]

    sku: Annotated[
        str,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="SKU",
                is_ui_displayable=True,
                is_ui_editable=True,
                index_config=CaseAwareUniqueIndexableConfig(
                    is_indexed=True, is_unique=False
                ),
            ),
        ),
    ]

    status: Annotated[
        ProductStatus,
        FieldMetadata(
            type_property=DefaultEnumFieldProperty(
                field_display_name="Status",
                enum_class=ProductStatus,
                is_ui_displayable=True,
                is_ui_editable=True,
            ),
        ),
    ]

    type: Annotated[
        ProductType,
        FieldMetadata(
            type_property=DefaultEnumFieldProperty(
                field_display_name="Type",
                enum_class=ProductType,
                is_ui_displayable=True,
                is_ui_editable=True,
            ),
        ),
    ]

    version_number: Annotated[
        int | None,
        FieldMetadata(
            type_property=NumericFieldProperty(
                field_display_name="Version Number",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ] = None

    properties: Annotated[
        dict[str, str] | None,
        FieldMetadata(
            type_property=DictFieldProperty(
                field_display_name="Properties",
                is_ui_displayable=True,
                is_ui_editable=True,
                value_field_type_property=TextFieldProperty(
                    field_display_name="Value",
                    is_ui_displayable=True,
                    is_ui_editable=True,
                ),
            ),
        ),
    ] = None

    description: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Description",
                is_ui_displayable=True,
                is_ui_editable=True,
            ),
        ),
    ] = None

    created_at: Annotated[
        ZoneRequiredDateTime,
        FieldMetadata(
            type_property=TimestampFieldProperty(
                field_display_name="Created At",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ]

    created_by_user_id: Annotated[
        UUID,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Created By User ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            ),
        ),
    ]

    updated_at: Annotated[
        ZoneRequiredDateTime | None,
        FieldMetadata(
            type_property=TimestampFieldProperty(
                field_display_name="Updated At",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ] = None

    updated_by_user_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Updated By User ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            ),
        ),
    ] = None

    deleted_at: Annotated[
        ZoneRequiredDateTime | None,
        FieldMetadata(
            type_property=TimestampFieldProperty(
                field_display_name="Deleted At",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ] = None

    deleted_by_user_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Deleted By User ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            ),
        ),
    ] = None

    @classmethod
    def from_db_model(cls, db_product: DbProduct) -> Product:
        """Create domain model from database model."""
        return Product(
            id=db_product.id,
            organization_id=db_product.organization_id,
            product_key=db_product.product_key,
            name=db_product.name,
            sku=db_product.sku,
            status=db_product.status,
            type=db_product.type,
            version_number=db_product.version_number,
            properties=db_product.properties,
            description=db_product.description,
            created_at=db_product.created_at,
            created_by_user_id=db_product.created_by_user_id,
            updated_at=db_product.updated_at,
            updated_by_user_id=db_product.updated_by_user_id,
            deleted_at=db_product.deleted_at,
            deleted_by_user_id=db_product.deleted_by_user_id,
        )
