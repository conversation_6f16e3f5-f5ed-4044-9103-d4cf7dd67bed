from __future__ import annotations

from typing import Annotated
from uuid import UUID

from pydantic import BaseModel, computed_field

from salestech_be.common.schema_manager.std_object_field_identifier import (
    GiantQuoteField,
    GiantQuoteLineItemField,
    StdObjectIdentifiers,
)
from salestech_be.common.type.metadata.field.field_type_property import (
    BooleanCheckboxFieldProperty,
    NumericFieldProperty,
    TextFieldProperty,
    TimestampFieldProperty,
    UUIDFieldProperty,
)
from salestech_be.core.common.types import DomainModel, FieldMetadata
from salestech_be.core.cpq.dto.quote_dto import QuoteDTO, QuoteLineItemDTO
from salestech_be.db.models.quote import (
    GiantQuote as DbGiantQuote,
)
from salestech_be.db.models.quote import (
    QuoteStatus,
)
from salestech_be.db.models.quote_line_item import QuoteLineItemStatus
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime


class Quote(BaseModel):
    """
    Quote Domain Model.

    Represents a complete quote with business logic and computed properties.
    """

    id: UUID
    organization_id: UUID
    name: str
    status: QuoteStatus
    quote_key: UUID | None = None
    version_number: int | None = None
    rescind_reason: str | None = None
    opportunity_id: UUID | None = None
    created_by_user_id: UUID
    created_at: ZoneRequiredDateTime
    updated_at: ZoneRequiredDateTime | None = None
    updated_by_user_id: UUID | None = None
    deleted_at: ZoneRequiredDateTime | None = None
    deleted_by_user_id: UUID | None = None
    line_items: list[QuoteLineItem] = []

    @computed_field  # type: ignore[prop-decorator]
    @property
    def is_draft(self) -> bool:
        """Check if the quote is in draft status."""
        return self.status == QuoteStatus.DRAFT

    @computed_field  # type: ignore[prop-decorator]
    @property
    def is_presented(self) -> bool:
        """Check if the quote has been presented."""
        return self.status == QuoteStatus.PRESENTED

    @computed_field  # type: ignore[prop-decorator]
    @property
    def is_accepted(self) -> bool:
        """Check if the quote has been accepted."""
        return self.status == QuoteStatus.ACCEPTED

    @computed_field  # type: ignore[prop-decorator]
    @property
    def is_rejected(self) -> bool:
        """Check if the quote has been rejected."""
        return self.status == QuoteStatus.REJECTED

    @computed_field  # type: ignore[prop-decorator]
    @property
    def is_rescinded(self) -> bool:
        """Check if the quote has been rescinded."""
        return self.status == QuoteStatus.RESCINDED

    @computed_field  # type: ignore[prop-decorator]
    @property
    def can_be_modified(self) -> bool:
        """Check if the quote can be modified (only DRAFT quotes can be modified)."""
        return self.is_draft

    @computed_field  # type: ignore[prop-decorator]
    @property
    def total_amount_cents(self) -> int:
        """Calculate the total amount of all active line items in cents."""
        return sum(
            item.final_amount_cents for item in self.line_items if item.is_active
        )

    @computed_field  # type: ignore[prop-decorator]
    @property
    def total_amount_dollars(self) -> float:
        """Calculate the total amount of all active line items in dollars."""
        return self.total_amount_cents / 100.0

    @computed_field  # type: ignore[prop-decorator]
    @property
    def active_line_items(self) -> list[QuoteLineItem]:
        """Get all active line items."""
        return [item for item in self.line_items if item.is_active]

    @computed_field  # type: ignore[prop-decorator]
    @property
    def line_item_count(self) -> int:
        """Get the total number of line items."""
        return len(self.line_items)

    @computed_field  # type: ignore[prop-decorator]
    @property
    def active_line_item_count(self) -> int:
        """Get the number of active line items."""
        return len(self.active_line_items)

    def has_line_items(self) -> bool:
        """Check if the quote has any line items."""
        return len(self.line_items) > 0

    def has_active_line_items(self) -> bool:
        """Check if the quote has any active line items."""
        return len(self.active_line_items) > 0

    @classmethod
    def from_dto(
        cls, quote_dto: QuoteDTO, line_items: list[QuoteLineItemDTO] | None = None
    ) -> Quote:
        """Create a Quote domain model from DTOs."""
        line_item_models = []
        if line_items:
            line_item_models = [
                QuoteLineItem.from_dto(line_item) for line_item in line_items
            ]

        return cls(
            id=quote_dto.id,
            organization_id=quote_dto.organization_id,
            name=quote_dto.name,
            status=quote_dto.status,
            quote_key=quote_dto.quote_key,
            version_number=quote_dto.version_number,
            rescind_reason=quote_dto.rescind_reason,
            opportunity_id=quote_dto.opportunity_id,
            created_by_user_id=quote_dto.created_by_user_id,
            created_at=quote_dto.created_at,
            updated_at=quote_dto.updated_at,
            updated_by_user_id=quote_dto.updated_by_user_id,
            deleted_at=quote_dto.deleted_at,
            deleted_by_user_id=quote_dto.deleted_by_user_id,
            line_items=line_item_models,
        )


class QuoteLineItem(BaseModel):
    """
    Quote Line Item Domain Model.

    Represents a line item within a quote with business logic and computed properties.
    """

    id: UUID
    organization_id: UUID
    quote_id: UUID
    price_book_entry_id: UUID
    product_id: UUID
    parent_line_item_id: UUID | None = None
    quantity: int
    final_amount_cents: int
    status: QuoteLineItemStatus
    start_date: ZoneRequiredDateTime | None = None
    end_date: ZoneRequiredDateTime | None = None
    created_at: ZoneRequiredDateTime
    created_by_user_id: UUID
    updated_at: ZoneRequiredDateTime | None = None
    updated_by_user_id: UUID | None = None
    deleted_at: ZoneRequiredDateTime | None = None
    deleted_by_user_id: UUID | None = None

    @computed_field  # type: ignore[prop-decorator]
    @property
    def is_active(self) -> bool:
        """Check if the line item is active."""
        return self.status == QuoteLineItemStatus.ACTIVE

    @computed_field  # type: ignore[prop-decorator]
    @property
    def is_inactive(self) -> bool:
        """Check if the line item is inactive."""
        return self.status == QuoteLineItemStatus.INACTIVE

    @computed_field  # type: ignore[prop-decorator]
    @property
    def final_amount_dollars(self) -> float:
        """Get the final amount in dollars."""
        return self.final_amount_cents / 100.0

    @computed_field  # type: ignore[prop-decorator]
    @property
    def total_line_amount_cents(self) -> int:
        """Get the total amount for this line item (quantity * final_amount_cents)."""
        return self.quantity * self.final_amount_cents

    @computed_field  # type: ignore[prop-decorator]
    @property
    def total_line_amount_dollars(self) -> float:
        """Get the total amount for this line item in dollars."""
        return self.total_line_amount_cents / 100.0

    @computed_field  # type: ignore[prop-decorator]
    @property
    def has_parent(self) -> bool:
        """Check if this line item has a parent."""
        return self.parent_line_item_id is not None

    @computed_field  # type: ignore[prop-decorator]
    @property
    def has_date_range(self) -> bool:
        """Check if this line item has start and end dates."""
        return self.start_date is not None and self.end_date is not None

    def is_valid_quantity(self) -> bool:
        """Check if the quantity is valid (greater than 0)."""
        return self.quantity > 0

    def is_valid_amount(self) -> bool:
        """Check if the amount is valid (greater than or equal to 0)."""
        return self.final_amount_cents >= 0

    @classmethod
    def from_dto(cls, line_item_dto: QuoteLineItemDTO) -> QuoteLineItem:
        """Create a QuoteLineItem domain model from DTO."""
        return cls(
            id=line_item_dto.id,
            organization_id=line_item_dto.organization_id,
            quote_id=line_item_dto.quote_id,
            price_book_entry_id=line_item_dto.price_book_entry_id,
            product_id=line_item_dto.product_id,
            parent_line_item_id=line_item_dto.parent_line_item_id,
            quantity=line_item_dto.quantity,
            final_amount_cents=line_item_dto.final_amount_cents,
            status=line_item_dto.status,
            start_date=line_item_dto.start_date,
            end_date=line_item_dto.end_date,
            created_at=line_item_dto.created_at,
            created_by_user_id=line_item_dto.created_by_user_id,
            updated_at=line_item_dto.updated_at,
            updated_by_user_id=line_item_dto.updated_by_user_id,
            deleted_at=line_item_dto.deleted_at,
            deleted_by_user_id=line_item_dto.deleted_by_user_id,
        )


class GiantQuoteLineItem(DomainModel):
    """
    Denormalized quote line item with embedded product and pricing data.
    """

    object_id = StdObjectIdentifiers.giant_quote_line_item.identifier
    object_display_name = "Quote Line Item"
    field_name_provider = GiantQuoteLineItemField

    inbound_relationships = ()
    outbound_relationships = ()

    # Quote line item fields
    quote_line_item_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Quote Line Item ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            )
        ),
    ] = None
    quote_line_item_organization_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Quote Line Item Organization ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            )
        ),
    ] = None
    quote_line_item_quote_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Quote Line Item Quote ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            )
        ),
    ] = None
    quote_line_item_price_book_entry_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Quote Line Item Price Book Entry ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            )
        ),
    ] = None
    quote_line_item_product_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Quote Line Item Product ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            )
        ),
    ] = None
    quote_line_item_parent_line_item_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Quote Line Item Parent Line Item ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            )
        ),
    ] = None
    quote_line_item_quantity: Annotated[
        int | None,
        FieldMetadata(
            type_property=NumericFieldProperty(
                field_display_name="Quote Line Item Quantity",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ] = None
    quote_line_item_final_amount_cents: Annotated[
        int | None,
        FieldMetadata(
            type_property=NumericFieldProperty(
                field_display_name="Quote Line Item Final Amount Cents",
                is_ui_displayable=False,
                is_ui_editable=False,
            )
        ),
    ] = None
    quote_line_item_status: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Quote Line Item Status",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ] = None
    quote_line_item_start_date: Annotated[
        ZoneRequiredDateTime | None,
        FieldMetadata(
            type_property=TimestampFieldProperty(
                field_display_name="Quote Line Item Start Date",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ] = None
    quote_line_item_end_date: Annotated[
        ZoneRequiredDateTime | None,
        FieldMetadata(
            type_property=TimestampFieldProperty(
                field_display_name="Quote Line Item End Date",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ] = None

    # Product fields
    product_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Product ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            )
        ),
    ] = None
    product_name: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Product Name",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ] = None
    product_sku: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Product SKU",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ] = None
    product_type: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Product Type",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ] = None
    product_description: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Product Description",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ] = None

    # Price book entry fields
    price_book_entry_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Price Book Entry ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            )
        ),
    ] = None
    price_book_entry_billing_frequency: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Price Book Entry Billing Frequency",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ] = None
    price_book_entry_unit_amount: Annotated[
        int | None,
        FieldMetadata(
            type_property=NumericFieldProperty(
                field_display_name="Price Book Entry Unit Amount",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ] = None
    price_book_entry_status: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Price Book Entry Status",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ] = None

    # Price book fields
    price_book_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Price Book ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            )
        ),
    ] = None
    price_book_name: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Price Book Name",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ] = None
    price_book_is_default: Annotated[
        bool | None,
        FieldMetadata(
            type_property=BooleanCheckboxFieldProperty(
                field_display_name="Price Book Is Default",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ] = None


class GiantQuote(DomainModel):
    """
    Denormalized quote with all related entities for efficient querying.

    This model provides a complete view of a quote including:
    - Quote line items with product and pricing details
    - Pipeline/opportunity information (optional)
    - User information (creator, updater)
    """

    object_id = StdObjectIdentifiers.giant_quote.identifier
    object_display_name = "Quote"
    field_name_provider = GiantQuoteField

    # Core quote fields
    quote_id: Annotated[
        UUID,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Quote ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            )
        ),
    ]
    quote_organization_id: Annotated[
        UUID,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Organization ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            )
        ),
    ]
    quote_name: Annotated[
        str,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Quote Name",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ]
    quote_status: Annotated[
        str,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Quote Status",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ]
    quote_quote_key: Annotated[
        UUID,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Quote Key",
                is_ui_displayable=False,
                is_ui_editable=False,
            )
        ),
    ]
    quote_version_number: Annotated[
        int,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Version Number",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ]
    quote_rescind_reason: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Rescind Reason",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ] = None
    quote_opportunity_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Opportunity ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            )
        ),
    ] = None
    quote_created_at: Annotated[
        ZoneRequiredDateTime,
        FieldMetadata(
            type_property=TimestampFieldProperty(
                field_display_name="Created At",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ]
    quote_created_by_user_id: Annotated[
        UUID,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Created By User ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            )
        ),
    ]
    quote_updated_at: Annotated[
        ZoneRequiredDateTime | None,
        FieldMetadata(
            type_property=TimestampFieldProperty(
                field_display_name="Updated At",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ] = None
    quote_updated_by_user_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Updated By User ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            )
        ),
    ] = None
    quote_deleted_at: Annotated[
        ZoneRequiredDateTime | None,
        FieldMetadata(
            type_property=TimestampFieldProperty(
                field_display_name="Deleted At",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ] = None
    quote_deleted_by_user_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Deleted By User ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            )
        ),
    ] = None

    # Pipeline/Opportunity fields
    pipeline_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Pipeline ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            )
        ),
    ] = None
    pipeline_organization_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Pipeline Organization ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            )
        ),
    ] = None
    pipeline_display_name: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Pipeline Name",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ] = None
    pipeline_status: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Pipeline Status",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ] = None
    pipeline_state: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Pipeline State",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ] = None
    pipeline_amount: Annotated[
        int | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Pipeline Amount",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ] = None
    pipeline_stage_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Pipeline Stage ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            )
        ),
    ] = None
    pipeline_owner_user_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Pipeline Owner User ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            )
        ),
    ] = None

    # Created by user
    created_by_user_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Created By User ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            )
        ),
    ] = None
    created_by_user_first_name: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Created By First Name",
                is_ui_displayable=False,
                is_ui_editable=False,
            )
        ),
    ] = None
    created_by_user_last_name: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Created By Last Name",
                is_ui_displayable=False,
                is_ui_editable=False,
            )
        ),
    ] = None
    created_by_user_avatar_s3_key: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Created By Avatar",
                is_ui_displayable=False,
                is_ui_editable=False,
            )
        ),
    ] = None

    # Updated by user
    updated_by_user_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Updated By User ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            )
        ),
    ] = None
    updated_by_user_first_name: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Updated By First Name",
                is_ui_displayable=False,
                is_ui_editable=False,
            )
        ),
    ] = None
    updated_by_user_last_name: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Updated By Last Name",
                is_ui_displayable=False,
                is_ui_editable=False,
            )
        ),
    ] = None
    updated_by_user_avatar_s3_key: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Updated By Avatar",
                is_ui_displayable=False,
                is_ui_editable=False,
            )
        ),
    ] = None

    @classmethod
    def from_db_giant_quote(cls, db_giant_quote: DbGiantQuote) -> GiantQuote:
        """Transform database giant quote model to domain model."""

        return cls(
            # Quote fields
            quote_id=db_giant_quote.quote_id,
            quote_organization_id=db_giant_quote.quote_organization_id,
            quote_name=db_giant_quote.quote_name,
            quote_status=db_giant_quote.quote_status,
            quote_quote_key=db_giant_quote.quote_quote_key,
            quote_version_number=db_giant_quote.quote_version_number,
            quote_rescind_reason=db_giant_quote.quote_rescind_reason,
            quote_opportunity_id=db_giant_quote.quote_opportunity_id,
            quote_created_at=db_giant_quote.quote_created_at,
            quote_created_by_user_id=db_giant_quote.quote_created_by_user_id,
            quote_updated_at=db_giant_quote.quote_updated_at,
            quote_updated_by_user_id=db_giant_quote.quote_updated_by_user_id,
            quote_deleted_at=db_giant_quote.quote_deleted_at,
            quote_deleted_by_user_id=db_giant_quote.quote_deleted_by_user_id,
            # Pipeline fields
            pipeline_id=db_giant_quote.pipeline_id,
            pipeline_organization_id=db_giant_quote.pipeline_organization_id,
            pipeline_display_name=db_giant_quote.pipeline_display_name,
            pipeline_status=db_giant_quote.pipeline_status,
            pipeline_state=db_giant_quote.pipeline_state,
            pipeline_amount=db_giant_quote.pipeline_amount,
            pipeline_stage_id=db_giant_quote.pipeline_stage_id,
            pipeline_owner_user_id=db_giant_quote.pipeline_owner_user_id,
            # User fields
            created_by_user_id=db_giant_quote.created_by_user_id,
            created_by_user_first_name=db_giant_quote.created_by_user_first_name,
            created_by_user_last_name=db_giant_quote.created_by_user_last_name,
            created_by_user_avatar_s3_key=db_giant_quote.created_by_user_avatar_s3_key,
            updated_by_user_id=db_giant_quote.updated_by_user_id,
            updated_by_user_first_name=db_giant_quote.updated_by_user_first_name,
            updated_by_user_last_name=db_giant_quote.updated_by_user_last_name,
            updated_by_user_avatar_s3_key=db_giant_quote.updated_by_user_avatar_s3_key,
        )
