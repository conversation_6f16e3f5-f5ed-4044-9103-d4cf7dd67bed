from typing import Annotated, Literal
from uuid import UUID

from fastapi import Depends, Request

from salestech_be.common.lifespan import get_db_engine
from salestech_be.common.results import Cursor
from salestech_be.core.common.domain_service import DomainQueryService
from salestech_be.core.cpq.query_types.quote_query_types import GiantQuote
from salestech_be.db.dao.quote_repository import QuoteRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.quote import QuoteStatus
from salestech_be.ree_logging import get_logger
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime

logger = get_logger(__name__)


class GiantQuoteQueryService(DomainQueryService[GiantQuote]):
    def __init__(
        self,
        quote_repository: Annotated[QuoteRepository, Depends()],
    ):
        self.quote_repository = quote_repository

    async def list_giant_quotes(
        self,
        organization_id: UUID,
        only_include_quote_ids: set[UUID] | None = None,
        status_in: list[QuoteStatus] | None = None,
        status_ne: QuoteStatus | None = None,
        created_at_gt: ZoneRequiredDateTime | None = None,
        created_at_lt: ZoneRequiredDateTime | None = None,
        pipeline_id: UUID | None = None,
        pipeline_ids_in: list[UUID] | None = None,
        sorting_field: Literal["created_at", "updated_at", "name"] | None = None,
        sorting_direction: Literal["asc", "desc"] | None = None,
        null_first: bool = False,
        cursor: Cursor | None = None,
    ) -> tuple[list[GiantQuote], Cursor | None]:
        """
        List giant quotes with filtering, sorting, and pagination.

        Args:
            organization_id: Organization to filter by
            only_include_quote_ids: Specific quote IDs to include
            status_in: List of statuses to include
            status_ne: Status to exclude
            created_at_gt: Filter quotes created after this date
            created_at_lt: Filter quotes created before this date
            pipeline_id: Filter by specific pipeline/opportunity
            pipeline_ids_in: Filter by list of pipeline/opportunity IDs
            sorting_field: Field to sort by
            sorting_direction: Sort direction (asc/desc)
            null_first: Whether to put null values first in sorting
            cursor: Pagination cursor

        Returns:
            Tuple of (list of giant quotes, next cursor)
        """
        only_include_quote_ids_list = (
            list(only_include_quote_ids) if only_include_quote_ids else None
        )

        logger.bind(
            organization_id=organization_id,
            only_include_quote_ids_list=only_include_quote_ids_list,
            status_in=status_in,
            status_ne=status_ne,
            created_at_gt=created_at_gt,
            created_at_lt=created_at_lt,
            pipeline_id=pipeline_id,
            pipeline_ids_in=pipeline_ids_in,
            sorting_field=sorting_field,
            sorting_direction=sorting_direction,
            null_first=null_first,
        ).info("Listing giant quotes")

        db_giant_quotes = await self.quote_repository.list_giant_quotes(
            organization_id=organization_id,
            only_include_quote_ids=only_include_quote_ids_list,
            cursor=cursor,
            status_in=status_in,
            status_ne=status_ne,
            created_at_gt=created_at_gt,
            created_at_lt=created_at_lt,
            pipeline_id=pipeline_id,
            pipeline_ids_in=pipeline_ids_in,
            sorting_field=sorting_field,
            sorting_direction=sorting_direction,
            null_first=null_first,
        )

        # Handle pagination cursor
        new_cursor = None
        if cursor:
            has_more: bool = len(db_giant_quotes) > cursor.page_size
            new_cursor = Cursor(
                page_size=cursor.page_size,
                page_index=cursor.page_index,
                has_more=has_more,
            )
            db_giant_quotes = db_giant_quotes[0 : cursor.page_size]

        # Transform to domain models
        domain_giant_quotes = [
            GiantQuote.from_db_giant_quote(db_giant_quote)
            for db_giant_quote in db_giant_quotes
        ]

        return domain_giant_quotes, new_cursor

    async def get_giant_quote_by_id(
        self,
        organization_id: UUID,
        quote_id: UUID,
    ) -> GiantQuote | None:
        """
        Get a single giant quote by ID.

        Args:
            organization_id: Organization to filter by
            quote_id: Quote ID to retrieve

        Returns:
            Giant quote if found, None otherwise
        """
        logger.bind(
            organization_id=organization_id,
            quote_id=quote_id,
        ).info("Getting giant quote by ID")

        db_giant_quotes = await self.quote_repository.list_giant_quotes(
            organization_id=organization_id,
            only_include_quote_ids=[quote_id],
            cursor=None,
        )

        if not db_giant_quotes:
            return None

        return GiantQuote.from_db_giant_quote(db_giant_quotes[0])

    async def count_giant_quotes(
        self,
        organization_id: UUID,
        status_in: list[QuoteStatus] | None = None,
        status_ne: QuoteStatus | None = None,
        created_at_gt: ZoneRequiredDateTime | None = None,
        created_at_lt: ZoneRequiredDateTime | None = None,
        pipeline_id: UUID | None = None,
        pipeline_ids_in: list[UUID] | None = None,
    ) -> int:
        """
        Count giant quotes matching the given filters.

        Args:
            organization_id: Organization to filter by
            status_in: List of statuses to include
            status_ne: Status to exclude
            created_at_gt: Filter quotes created after this date
            created_at_lt: Filter quotes created before this date
            pipeline_id: Filter by specific pipeline/opportunity
            pipeline_ids_in: Filter by list of pipeline/opportunity IDs

        Returns:
            Count of matching quotes
        """
        logger.bind(
            organization_id=organization_id,
            status_in=status_in,
            status_ne=status_ne,
            created_at_gt=created_at_gt,
            created_at_lt=created_at_lt,
            pipeline_id=pipeline_id,
            pipeline_ids_in=pipeline_ids_in,
        ).info("Counting giant quotes")

        # Get all matching quotes without pagination to count them
        db_giant_quotes = await self.quote_repository.list_giant_quotes(
            organization_id=organization_id,
            cursor=None,
            status_in=status_in,
            status_ne=status_ne,
            created_at_gt=created_at_gt,
            created_at_lt=created_at_lt,
            pipeline_id=pipeline_id,
            pipeline_ids_in=pipeline_ids_in,
        )

        return len(db_giant_quotes)


def get_giant_quote_query_service_from_engine(
    db_engine: DatabaseEngine,
) -> GiantQuoteQueryService:
    """Create a GiantQuoteQueryService instance from a database engine."""
    return GiantQuoteQueryService(
        quote_repository=QuoteRepository(engine=db_engine),
    )


def get_giant_quote_query_service(request: Request) -> GiantQuoteQueryService:
    """Dependency injection function for FastAPI to get GiantQuoteQueryService."""
    return get_giant_quote_query_service_from_engine(db_engine=get_db_engine(request))
