from typing import Annotated
from uuid import UUID, uuid4

from fastapi import Depends
from sqlalchemy.exc import IntegrityError

from salestech_be.common.exception import (
    ConflictResourceError,
    ResourceNotFoundError,
)
from salestech_be.core.cpq.adapters.price_book_entry_adapter import (
    PriceBookEntryAdapter,
)
from salestech_be.core.cpq.dto.price_book_entry_dto import (
    CreatePriceBookEntryDTO,
    PriceBookEntryDTO,
    UpdatePriceBookEntryDTO,
)
from salestech_be.db.dao.price_book_entry_repository import PriceBookEntryRepository
from salestech_be.db.dao.price_book_repository import PriceBookRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.price_book import (
    BillingFrequency,
    PriceBookEntryStatus,
)
from salestech_be.db.models.price_book import (
    PriceBookEntry as PriceBookEntryModel,
)
from salestech_be.util.time import zoned_utc_now


class PriceBookEntryService:
    def __init__(
        self,
        price_book_entry_repository: Annotated[PriceBookEntryRepository, Depends()],
        price_book_repository: Annotated[PriceBookRepository, Depends()],
    ):
        self.price_book_entry_repository = price_book_entry_repository
        self.price_book_repository = price_book_repository

    async def create_price_book_entry(
        self,
        create_dto: CreatePriceBookEntryDTO,
    ) -> PriceBookEntryDTO:
        """Create a new price book entry."""
        # Business logic: Verify the price book exists and belongs to the organization
        await self.price_book_repository.find_by_id_and_organization_id(
            create_dto.price_book_id, create_dto.organization_id
        )

        # Business logic: Check for duplicate entries (same product, price book, and billing frequency)
        existing_entry = await self.price_book_entry_repository.find_duplicate_entry(
            product_key=create_dto.product_key,
            price_book_id=create_dto.price_book_id,
            billing_frequency=create_dto.billing_frequency,
            organization_id=create_dto.organization_id,
        )

        if existing_entry:
            raise ConflictResourceError(
                f"An entry for product {create_dto.product_key} with {create_dto.billing_frequency} "
                f"billing frequency already exists in this price book"
            )

        # Convert DTO to database model
        entry_model = PriceBookEntryAdapter.create_price_book_entry_dto_to_db_model(
            create_dto
        )

        try:
            inserted_model = await self.price_book_entry_repository.insert(entry_model)
            # Convert back to domain DTO and then to service model
            return PriceBookEntryAdapter.price_book_entry_to_dto(inserted_model)
        except IntegrityError as e:
            raise ConflictResourceError(
                f"Failed to create price book entry: {e}"
            ) from e

    async def get_price_book_entry(
        self,
        entry_id: UUID,
        organization_id: UUID,
    ) -> PriceBookEntryDTO:
        """Get a price book entry by ID."""
        model = await self.price_book_entry_repository.find_by_id_and_organization_id(
            entry_id, organization_id
        )
        # Convert to domain DTO and then to service model
        return PriceBookEntryAdapter.price_book_entry_to_dto(model)

    async def update_price_book_entry(
        self,
        entry_id: UUID,
        update_dto: UpdatePriceBookEntryDTO,
    ) -> PriceBookEntryDTO:
        """Update a price book entry by creating a new version."""
        # Get the existing entry
        existing_model = (
            await self.price_book_entry_repository.find_by_id_and_organization_id(
                entry_id, update_dto.organization_id
            )
        )

        # Business logic: If no changes, return the existing model
        if not update_dto.has_changes:
            return PriceBookEntryAdapter.price_book_entry_to_dto(existing_model)

        # Determine the billing frequency for the new version
        new_billing_frequency = (
            update_dto.billing_frequency
            if update_dto.billing_frequency is not None
            else existing_model.billing_frequency
        )

        # Business logic: Check for duplicate if billing frequency is being changed
        if (
            update_dto.billing_frequency is not None
            and update_dto.billing_frequency != existing_model.billing_frequency
        ):
            duplicate_entry = (
                await self.price_book_entry_repository.find_duplicate_entry(
                    product_key=existing_model.product_key,
                    price_book_id=existing_model.price_book_id,
                    billing_frequency=update_dto.billing_frequency,
                    organization_id=update_dto.organization_id,
                    exclude_entry_id=entry_id,
                )
            )

            if duplicate_entry:
                raise ConflictResourceError(
                    f"An entry for this product with {update_dto.billing_frequency} "
                    f"billing frequency already exists in this price book"
                )

        # Find the latest version number for this product/price book/billing frequency combination
        latest_entry = (
            await self.price_book_entry_repository.find_latest_version_for_product(
                existing_model.product_key,
                existing_model.price_book_id,
                update_dto.organization_id,
                new_billing_frequency,
            )
        )

        # Calculate the new version number
        new_version_number = (latest_entry.version_number if latest_entry else 0) + 1

        # Use adapter to create new version model
        new_model = PriceBookEntryAdapter.create_new_version_model(
            update_dto, existing_model, new_version_number
        )

        try:
            inserted_model = await self.price_book_entry_repository.insert(new_model)
            # Convert back to domain DTO and then to service model
            return PriceBookEntryAdapter.price_book_entry_to_dto(inserted_model)
        except IntegrityError as e:
            # Check if this is a version conflict
            if "unique" in str(e).lower() or "duplicate" in str(e).lower():
                raise ConflictResourceError(
                    f"A concurrent update created version {new_version_number} for this entry. "
                    f"Please retry your update."
                ) from e
            raise ConflictResourceError(
                f"Failed to create new version of price book entry: {e}"
            ) from e

    async def delete_price_book_entry(
        self,
        entry_id: UUID,
        organization_id: UUID,
        deleted_by_user_id: UUID,
    ) -> None:
        """Soft delete a price book entry."""
        existing_model = (
            await self.price_book_entry_repository.find_by_id_and_organization_id(
                entry_id, organization_id
            )
        )

        # Convert to domain DTO for potential business logic (none needed currently)
        _ = PriceBookEntryAdapter.price_book_entry_to_dto(existing_model)

        # Soft delete
        deleted_model = existing_model.model_copy(
            update={
                "deleted_at": zoned_utc_now(),
                "deleted_by_user_id": deleted_by_user_id,
            }
        )

        result = await self.price_book_entry_repository.update_instance(deleted_model)
        if not result:
            raise ResourceNotFoundError(f"PriceBookEntry {entry_id} not found")

    async def list_entries_by_price_book(
        self,
        price_book_id: UUID,
        organization_id: UUID,
        exclude_deleted: bool = True,
    ) -> list[PriceBookEntryDTO]:
        """List all entries for a price book."""
        # Business logic: Verify the price book exists and belongs to the organization
        await self.price_book_repository.find_by_id_and_organization_id(
            price_book_id, organization_id
        )

        models = await self.price_book_entry_repository.find_by_price_book_id(
            price_book_id, organization_id, exclude_deleted=exclude_deleted
        )
        # Convert models to domain DTOs and then to service models
        return [
            PriceBookEntryAdapter.price_book_entry_to_dto(model) for model in models
        ]

    async def list_entries_by_product(
        self,
        product_key: UUID,
        organization_id: UUID,
        price_book_id: UUID | None = None,
        exclude_deleted: bool = True,
    ) -> list[PriceBookEntryDTO]:
        """List all entries for a product across price books."""
        models = await self.price_book_entry_repository.find_by_product_key(
            product_key, organization_id, price_book_id, exclude_deleted
        )
        # Convert models to domain DTOs and then to service models
        return [
            PriceBookEntryAdapter.price_book_entry_to_dto(model) for model in models
        ]

    async def get_active_entry_for_product(
        self,
        product_key: UUID,
        price_book_id: UUID,
        organization_id: UUID,
        billing_frequency: BillingFrequency | None = None,
    ) -> PriceBookEntryDTO | None:
        """Get the active price book entry for a product."""
        # Business logic: Verify the price book exists and belongs to the organization
        await self.price_book_repository.find_by_id_and_organization_id(
            price_book_id, organization_id
        )

        model = await self.price_book_entry_repository.find_active_entry_for_product(
            product_key, price_book_id, organization_id, billing_frequency
        )
        if model:
            return PriceBookEntryAdapter.price_book_entry_to_dto(model)
        return None

    async def list_entries_by_status(
        self,
        status: PriceBookEntryStatus,
        organization_id: UUID,
        price_book_id: UUID | None = None,
        limit: int | None = None,
    ) -> list[PriceBookEntryDTO]:
        """List entries by status."""
        models = await self.price_book_entry_repository.find_by_status(
            status, organization_id, price_book_id, limit
        )
        # Convert models to domain DTOs and then to service models
        return [
            PriceBookEntryAdapter.price_book_entry_to_dto(model) for model in models
        ]

    async def create_new_version(
        self,
        entry_id: UUID,
        unit_amount: int,
        organization_id: UUID,
        created_by_user_id: UUID,
        status: PriceBookEntryStatus = PriceBookEntryStatus.DRAFT,
    ) -> PriceBookEntryDTO:
        """Create a new version of an existing price book entry."""
        # Get the existing entry
        existing_model = (
            await self.price_book_entry_repository.find_by_id_and_organization_id(
                entry_id, organization_id
            )
        )

        # Find the latest version number for this product/price book/billing frequency combination
        latest_entry = (
            await self.price_book_entry_repository.find_latest_version_for_product(
                existing_model.product_key,
                existing_model.price_book_id,
                organization_id,
                existing_model.billing_frequency,
            )
        )

        new_version_number = (latest_entry.version_number if latest_entry else 0) + 1

        # Create the new version using explicit model construction (this is a specific use case)
        new_entry_model = PriceBookEntryModel(
            id=uuid4(),
            organization_id=organization_id,
            price_book_id=existing_model.price_book_id,
            product_key=existing_model.product_key,
            billing_frequency=existing_model.billing_frequency,
            unit_amount=unit_amount,
            version_number=new_version_number,
            status=status,
            created_at=zoned_utc_now(),
            created_by_user_id=created_by_user_id,
        )

        try:
            inserted_model = await self.price_book_entry_repository.insert(
                new_entry_model
            )
            # Convert back to domain DTO and then to service model
            return PriceBookEntryAdapter.price_book_entry_to_dto(inserted_model)
        except IntegrityError as e:
            raise ConflictResourceError(f"Failed to create new version: {e}") from e

    async def activate_entry(
        self,
        entry_id: UUID,
        organization_id: UUID,
        user_id: UUID,
    ) -> PriceBookEntryDTO:
        """Activate a price book entry."""
        update_dto = UpdatePriceBookEntryDTO(
            id=entry_id,
            status=PriceBookEntryStatus.ACTIVE,
            organization_id=organization_id,
            updated_by_user_id=user_id,
        )
        return await self.update_price_book_entry(entry_id, update_dto)

    async def deactivate_entry(
        self,
        entry_id: UUID,
        organization_id: UUID,
        user_id: UUID,
    ) -> PriceBookEntryDTO:
        """Deactivate a price book entry."""
        update_dto = UpdatePriceBookEntryDTO(
            id=entry_id,
            status=PriceBookEntryStatus.INACTIVE,
            organization_id=organization_id,
            updated_by_user_id=user_id,
        )
        return await self.update_price_book_entry(entry_id, update_dto)

    async def list_price_book_entries(
        self,
        organization_id: UUID,
        price_book_id: UUID | None = None,
        product_key: UUID | None = None,
        status: PriceBookEntryStatus | None = None,
        billing_frequency: BillingFrequency | None = None,
        limit: int = 50,
        offset: int = 0,
    ) -> list[PriceBookEntryDTO]:
        """List price book entries with optional filters."""
        # For now, implement this using existing methods
        # In a production system, you might want to create a more efficient repository method

        if price_book_id and product_key:
            # Find entries for specific product in specific price book
            models = await self.price_book_entry_repository.find_by_product_key(
                product_key,
                organization_id,
                price_book_id,
                exclude_deleted=True,
            )
        elif price_book_id:
            # Find all entries for specific price book
            models = await self.price_book_entry_repository.find_by_price_book_id(
                price_book_id, organization_id, exclude_deleted=True
            )
        elif product_key:
            # Find all entries for specific product across all price books
            models = await self.price_book_entry_repository.find_by_product_key(
                product_key, organization_id, exclude_deleted=True
            )
        elif status:
            # Find entries by status
            models = await self.price_book_entry_repository.find_by_status(
                status, organization_id, price_book_id, limit
            )
        else:
            # For now, require at least one filter to avoid returning all entries
            # In production, you might implement a more sophisticated listing method
            return []

        # Apply additional filters if specified
        filtered_models = models
        if status and not (price_book_id is None and product_key is None):
            # Status filter not already applied
            filtered_models = [m for m in filtered_models if m.status == status]

        if billing_frequency:
            filtered_models = [
                m for m in filtered_models if m.billing_frequency == billing_frequency
            ]

        # Apply pagination
        start_index = offset
        end_index = start_index + limit
        paginated_models = filtered_models[start_index:end_index]

        # Convert models to domain DTOs and then to service models
        return [
            PriceBookEntryAdapter.price_book_entry_to_dto(model)
            for model in paginated_models
        ]


def get_price_book_entry_service(db_engine: DatabaseEngine) -> PriceBookEntryService:
    """Factory function to create PriceBookEntryService with its dependencies."""
    return PriceBookEntryService(
        price_book_entry_repository=PriceBookEntryRepository(engine=db_engine),
        price_book_repository=PriceBookRepository(engine=db_engine),
    )
