from typing import Annotated
from uuid import UUID

from fastapi import Depends
from sqlalchemy.exc import IntegrityError

from salestech_be.common.exception import (
    ConflictResourceError,
    InvalidArgumentError,
    ResourceNotFoundError,
)
from salestech_be.core.cpq.adapters.price_book_adapter import (
    PriceBookAdapter,
)
from salestech_be.core.cpq.dto.price_book_dto import (
    CreatePriceBookDTO,
    PriceBookDTO,
    UpdatePriceBookDTO,
)
from salestech_be.db.dao.price_book_repository import PriceBookRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.util.time import zoned_utc_now


class PriceBookService:
    def __init__(
        self,
        price_book_repository: Annotated[PriceBookRepository, Depends()],
    ):
        self.price_book_repository = price_book_repository

    async def create_price_book(
        self,
        create_dto: CreatePriceBookDTO,
    ) -> PriceBookDTO:
        """Create a new price book."""
        # Business logic: If this is being set as default, check if there's already a default
        if create_dto.is_default:
            existing_default = (
                await self.price_book_repository.find_default_by_organization_id(
                    create_dto.organization_id
                )
            )
            if existing_default:
                raise ConflictResourceError(
                    "A default price book already exists for this organization"
                )

        # Convert DTO to database model
        price_book_model = PriceBookAdapter.create_price_book_dto_to_db_model(
            create_dto
        )

        try:
            inserted_model = await self.price_book_repository.insert(price_book_model)
            # Convert back to domain DTO and then to service model
            return PriceBookAdapter.price_book_to_dto(inserted_model)
        except IntegrityError as e:
            raise ConflictResourceError(f"Failed to create price book: {e}") from e

    async def get_price_book(
        self,
        price_book_id: UUID,
        organization_id: UUID,
    ) -> PriceBookDTO:
        """Get a price book by ID."""
        model = await self.price_book_repository.find_by_id_and_organization_id(
            price_book_id, organization_id
        )
        # Convert to domain DTO and then to service model
        return PriceBookAdapter.price_book_to_dto(model)

    async def update_price_book(
        self,
        price_book_id: UUID,
        update_dto: UpdatePriceBookDTO,
    ) -> PriceBookDTO:
        """Update a price book."""
        # Get the existing price book
        existing_model = (
            await self.price_book_repository.find_by_id_and_organization_id(
                price_book_id, update_dto.organization_id
            )
        )

        # Business logic: Check if trying to set as default when one already exists
        if (
            update_dto.is_default is not None
            and update_dto.is_default
            and not existing_model.is_default
        ):
            existing_default = (
                await self.price_book_repository.find_default_by_organization_id(
                    update_dto.organization_id
                )
            )
            if existing_default and existing_default.id != price_book_id:
                raise ConflictResourceError(
                    "A default price book already exists for this organization"
                )

        # Check if there are any changes
        has_changes = any(
            [
                update_dto.name is not None,
                update_dto.description is not None,
                update_dto.is_default is not None,
            ]
        )

        if not has_changes:
            return PriceBookAdapter.price_book_to_dto(existing_model)

        # Convert DTO to updated model
        updated_model = PriceBookAdapter.update_price_book_dto_to_db_model(
            update_dto, existing_model
        )

        try:
            # Handle default price book logic
            if update_dto.is_default is not None and update_dto.is_default:
                await self.price_book_repository.set_default_price_book(
                    price_book_id,
                    update_dto.organization_id,
                    update_dto.updated_by_user_id,
                )
                # Refresh the model to get the updated state
                updated_model = (
                    await self.price_book_repository.find_by_id_and_organization_id(
                        price_book_id, update_dto.organization_id
                    )
                )
            else:
                result = await self.price_book_repository.update_instance(updated_model)
                if not result:
                    raise ResourceNotFoundError(f"PriceBook {price_book_id} not found")
                updated_model = result

            # Convert back to domain DTO and then to service model
            return PriceBookAdapter.price_book_to_dto(updated_model)
        except IntegrityError as e:
            raise ConflictResourceError(f"Failed to update price book: {e}") from e

    async def delete_price_book(
        self,
        price_book_id: UUID,
        organization_id: UUID,
        deleted_by_user_id: UUID,
    ) -> None:
        """Soft delete a price book."""
        existing_model = (
            await self.price_book_repository.find_by_id_and_organization_id(
                price_book_id, organization_id
            )
        )

        # Convert to domain DTO for business logic
        domain_dto = PriceBookAdapter.price_book_to_dto(existing_model)

        # Business logic: Check if this price book can be deleted
        if domain_dto.is_default:
            raise InvalidArgumentError("Cannot delete the default price book")

        # Soft delete
        deleted_model = existing_model.model_copy(
            update={
                "deleted_at": zoned_utc_now(),
                "deleted_by_user_id": deleted_by_user_id,
            }
        )

        result = await self.price_book_repository.update_instance(deleted_model)
        if not result:
            raise ResourceNotFoundError(f"PriceBook {price_book_id} not found")

    async def list_price_books(
        self,
        organization_id: UUID,
        exclude_deleted: bool = True,
    ) -> list[PriceBookDTO]:
        """List all price books for an organization."""
        models = await self.price_book_repository.find_by_organization_id(
            organization_id, exclude_deleted=exclude_deleted
        )
        # Convert models to domain DTOs and then to service models
        return [PriceBookAdapter.price_book_to_dto(model) for model in models]

    async def get_default_price_book(
        self,
        organization_id: UUID,
    ) -> PriceBookDTO | None:
        """Get the default price book for an organization."""
        model = await self.price_book_repository.find_default_by_organization_id(
            organization_id
        )
        if model:
            return PriceBookAdapter.price_book_to_dto(model)
        return None

    async def set_default_price_book(
        self,
        price_book_id: UUID,
        organization_id: UUID,
        user_id: UUID,
    ) -> PriceBookDTO:
        """Set a price book as the default for an organization."""
        # Verify the price book exists and belongs to the organization
        await self.price_book_repository.find_by_id_and_organization_id(
            price_book_id, organization_id
        )

        # Set as default
        await self.price_book_repository.set_default_price_book(
            price_book_id, organization_id, user_id
        )

        # Return the updated price book
        updated_model = await self.price_book_repository.find_by_id_and_organization_id(
            price_book_id, organization_id
        )
        # Convert to domain DTO and then to service model
        return PriceBookAdapter.price_book_to_dto(updated_model)


def get_price_book_service(db_engine: DatabaseEngine) -> PriceBookService:
    """Factory function to create PriceBookService with its dependencies."""
    return PriceBookService(price_book_repository=PriceBookRepository(engine=db_engine))
