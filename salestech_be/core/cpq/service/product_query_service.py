from typing import Annotated
from uuid import UUID

from fastapi import Depends, Request

from salestech_be.common.lifespan import get_db_engine
from salestech_be.common.type.patch_request import UNSET, UnsetAware, specified
from salestech_be.core.common.domain_service import DomainQueryService
from salestech_be.core.cpq.query_types.product_query_types import Product
from salestech_be.db.dao.product_repository import ProductRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.product import Product as DbProduct


class ProductQueryService(DomainQueryService[Product]):
    def __init__(
        self,
        product_repository: Annotated[ProductRepository, Depends()],
    ):
        self.product_repository = product_repository

    async def list_products(
        self,
        organization_id: UUID,
        only_include_product_ids: UnsetAware[set[UUID]] = UNSET,
        exclude_archived: bool = True,
    ) -> list[Product]:
        # Determine filtering parameters
        product_ids = (
            list(only_include_product_ids)
            if specified(only_include_product_ids)
            else None
        )

        # Get products from repository
        if product_ids:
            db_products = await self.product_repository.find_by_ids(
                product_ids=product_ids,
                organization_id=organization_id,
            )
        else:
            db_products = await self.product_repository.find_all_by_organization(
                organization_id=organization_id,
                exclude_archived=exclude_archived,
            )

        # Convert to domain models
        return [self._db_model_to_domain(db_product) for db_product in db_products]

    async def find_product_components_by_product_ids(
        self, product_ids: list[UUID], organization_id: UUID
    ) -> tuple[dict[UUID, set[UUID]], set[UUID]]:
        (
            product_id_to_related_product_ids,
            all_related_product_ids,
        ) = await self.product_repository.find_related_product_ids_by_product_ids(
            product_ids=product_ids,
            organization_id=organization_id,
            exclude_incompatible_relationships=True,
        )
        return product_id_to_related_product_ids, all_related_product_ids

    def _db_model_to_domain(self, db_product: DbProduct) -> Product:
        """Convert DB Product model to domain Product model."""
        return Product(
            id=db_product.id,
            organization_id=db_product.organization_id,
            product_key=db_product.product_key,
            name=db_product.name,
            sku=db_product.sku,
            status=db_product.status,
            type=db_product.type,
            version_number=db_product.version_number,
            properties=db_product.properties,
            description=db_product.description,
            created_at=db_product.created_at,
            created_by_user_id=db_product.created_by_user_id,
            updated_at=db_product.updated_at,
            updated_by_user_id=db_product.updated_by_user_id,
            deleted_at=db_product.deleted_at,
            deleted_by_user_id=db_product.deleted_by_user_id,
        )


def get_product_query_service_by_db_engine(
    db_engine: DatabaseEngine,
) -> ProductQueryService:
    return ProductQueryService(
        product_repository=ProductRepository(engine=db_engine),
    )


def get_product_query_service(request: Request) -> ProductQueryService:
    return get_product_query_service_by_db_engine(db_engine=get_db_engine(request))
