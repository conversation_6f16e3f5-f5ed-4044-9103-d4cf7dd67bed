from uuid import UUID, uuid4

from fastapi import Request

from salestech_be.common.exception.exception import (
    ConflictResourceError,
    InvalidArgumentError,
    ResourceNotFoundError,
)
from salestech_be.common.lifespan import get_db_engine
from salestech_be.common.singleton import Singleton
from salestech_be.core.common.types import UserAuthContext
from salestech_be.core.cpq.adapters.product_adapter import ProductAdapter
from salestech_be.core.cpq.dto.product_dto import (
    ProductDTO,
    ProductRelationshipDTO,
)
from salestech_be.db.dao.product_repository import ProductRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.product import Product, ProductType
from salestech_be.db.models.product_relationship import (
    ProductRelationship,
    ProductRelationshipType,
)
from salestech_be.ree_logging import get_logger
from salestech_be.util.time import zoned_utc_now

logger = get_logger(__name__)


class ProductService:
    def __init__(self, engine: DatabaseEngine):
        self.product_repository = ProductRepository(engine=engine)
        self.engine = engine

    async def create_product(
        self,
        product_dto: ProductDTO,
        user_auth_context: UserAuthContext,
    ) -> ProductDTO:
        """Create a new product."""
        logger.bind(
            organization_id=user_auth_context.organization_id,
            user_id=user_auth_context.user_id,
            product_name=product_dto.name,
            product_type=product_dto.type,
            product_key=product_dto.product_key,
        ).info("Creating new product")

        # Handle product creation logic
        if product_dto.product_key:
            # Check if product with this product_key exists
            existing_product = await self.product_repository.find_latest_product_version_by_product_key(
                product_key=product_dto.product_key,
                organization_id=user_auth_context.organization_id,
            )

            if existing_product:
                # Case 1: Product exists - this should be an update, not create
                logger.bind(
                    product_key=product_dto.product_key,
                ).info("Product exists, calling update instead of create")
                return await self.update_product(
                    updated_product_dto=product_dto,
                    user_auth_context=user_auth_context,
                )
            else:
                # Case 2: Product key provided but doesn't exist - generate our own UUID
                product_dto.product_key = uuid4()
                product_dto.version_number = 1
                logger.bind(
                    product_key=product_dto.product_key,
                ).info(
                    "Product key provided but not found, creating new product with generated key"
                )
        else:
            # Case 3: No product_key provided - create new product with new key
            product_dto.product_key = uuid4()
            product_dto.version_number = 1
            logger.bind(
                product_key=product_dto.product_key,
            ).info("Creating new product with generated product_key")

        product_dao = ProductAdapter.product_dto_to_db_model(product_dto)
        inserted_product_dao: Product = await self.product_repository.insert(
            product_dao
        )
        created_dto = ProductAdapter.product_to_dto(inserted_product_dao)

        logger.bind(
            organization_id=user_auth_context.organization_id,
            product_id=created_dto.id,
            product_key=created_dto.product_key,
            version_number=created_dto.version_number,
        ).info("Product created successfully")

        return created_dto

    async def get_product(
        self,
        product_id: UUID,
        organization_id: UUID,
    ) -> ProductDTO:
        """Get a product by ID."""
        product_dao = await self.product_repository.find_by_tenanted_primary_key(
            Product,
            organization_id=organization_id,
            id=product_id,
        )

        if not product_dao:
            raise ResourceNotFoundError(f"Product {product_id} not found")

        return ProductAdapter.product_to_dto(product_dao)

    async def update_product(
        self,
        updated_product_dto: ProductDTO,
        user_auth_context: UserAuthContext,
    ) -> ProductDTO:
        """Update an existing product by creating a new version."""
        logger.bind(
            organization_id=user_auth_context.organization_id,
            product_id=updated_product_dto.id,
            user_id=user_auth_context.user_id,
        ).info("Updating product")

        logger.bind(
            product_key=updated_product_dto.product_key,
            version_number=updated_product_dto.version_number,
        ).info("Creating new version of product")

        # Check if product exists
        previous_product_version = (
            await self.product_repository.find_latest_product_version_by_product_key(
                product_key=updated_product_dto.product_key,
                organization_id=user_auth_context.organization_id,
            )
        )

        if not previous_product_version:
            raise ResourceNotFoundError(
                f"Product with product key {updated_product_dto.product_key} not found"
            )

        updated_product_dto.version_number = previous_product_version.version_number + 1

        product_dao = ProductAdapter.product_dto_to_db_model(updated_product_dto)
        inserted_product_dao: Product = await self.product_repository.insert(
            product_dao
        )
        created_dto = ProductAdapter.product_to_dto(inserted_product_dao)

        logger.bind(
            organization_id=user_auth_context.organization_id,
            new_product_id=created_dto.id,
            product_key=created_dto.product_key,
            version_number=created_dto.version_number,
        ).info("Product updated successfully with new version")

        return created_dto

    async def delete_product(
        self,
        product_id: UUID,
        user_auth_context: UserAuthContext,
    ) -> ProductDTO:
        """Soft delete a product."""
        logger.bind(
            organization_id=user_auth_context.organization_id,
            product_id=product_id,
            user_id=user_auth_context.user_id,
        ).info("Deleting product")

        product = await self.product_repository.find_by_tenanted_primary_key(
            Product,
            organization_id=user_auth_context.organization_id,
            id=product_id,
        )

        if not product:
            raise ResourceNotFoundError(f"Product {product_id} not found")

        # Transactionally soft delete the product and all related relationships
        try:
            product_dao = await self.product_repository.transactional_soft_delete_product_and_relationships(
                product_id=product_id,
                user_id=user_auth_context.user_id,
            )
        except ValueError as e:
            raise ResourceNotFoundError(str(e)) from e

        logger.bind(
            organization_id=user_auth_context.organization_id,
            product_id=product_id,
        ).info("Product deleted successfully")

        return ProductAdapter.product_to_dto(product_dao)

    async def add_product_relationship(
        self,
        relationship_dto: ProductRelationshipDTO,
        user_auth_context: UserAuthContext,
    ) -> ProductRelationshipDTO:
        """Add a relationship between products."""
        logger.bind(
            organization_id=user_auth_context.organization_id,
            product_id=relationship_dto.product_id,
            related_product_id=relationship_dto.related_product_id,
            relationship_type=relationship_dto.relationship_type,
        ).info("Adding product relationship")

        # Validate both products exist
        product = await self.product_repository.find_by_tenanted_primary_key(
            Product,
            organization_id=user_auth_context.organization_id,
            id=relationship_dto.product_id,
        )

        if not product:
            raise ResourceNotFoundError(
                f"Product {relationship_dto.product_id} not found"
            )

        related_product = await self.product_repository.find_by_tenanted_primary_key(
            Product,
            organization_id=user_auth_context.organization_id,
            id=relationship_dto.related_product_id,
        )

        if not related_product:
            raise ResourceNotFoundError(
                f"Related product {relationship_dto.related_product_id} not found"
            )

        # Validate relationship type rules
        await self._validate_relationship_rules(
            product, related_product, relationship_dto.relationship_type
        )

        # Check if relationship already exists
        existing_relationship = (
            await self.product_repository.find_existing_relationship(
                product_id=relationship_dto.product_id,
                related_product_id=relationship_dto.related_product_id,
                organization_id=user_auth_context.organization_id,
            )
        )

        if existing_relationship:
            raise ConflictResourceError(
                f"Relationship between products {relationship_dto.product_id} and {relationship_dto.related_product_id} already exists"
            )

        relationship_dao = ProductAdapter.relationship_dto_to_db_model(relationship_dto)
        inserted_relationship_dao: ProductRelationship = (
            await self.product_repository.insert(relationship_dao)
        )
        created_dto = ProductAdapter.relationship_to_dto(inserted_relationship_dao)

        logger.bind(
            organization_id=user_auth_context.organization_id,
            product_id=relationship_dto.product_id,
            related_product_id=relationship_dto.related_product_id,
        ).info("Product relationship added successfully")

        return created_dto

    async def remove_product_relationship(
        self,
        product_id: UUID,
        related_product_id: UUID,
        user_auth_context: UserAuthContext,
    ) -> None:
        """Remove a relationship between products."""
        logger.bind(
            organization_id=user_auth_context.organization_id,
            product_id=product_id,
            related_product_id=related_product_id,
        ).info("Removing product relationship")

        relationship = await self.product_repository.find_existing_relationship(
            product_id=product_id,
            related_product_id=related_product_id,
            organization_id=user_auth_context.organization_id,
        )

        if not relationship:
            raise ResourceNotFoundError(
                f"Relationship between products {product_id} and {related_product_id} not found"
            )

        await self.product_repository.update_by_primary_key(
            ProductRelationship,
            primary_key_to_value={"id": relationship.id},
            column_to_update={
                "deleted_at": zoned_utc_now(),
                "deleted_by_user_id": user_auth_context.user_id,
            },
        )

        logger.bind(
            organization_id=user_auth_context.organization_id,
            product_id=product_id,
            related_product_id=related_product_id,
        ).info("Product relationship removed successfully")

    async def get_product_relationships(
        self,
        product_id: UUID,
        organization_id: UUID,
    ) -> list[ProductRelationshipDTO]:
        """Get all relationships for a product (both as parent and related product)."""
        # Single query with database-level deduplication
        all_relationships_dao = (
            await self.product_repository.find_all_relationships_for_product(
                product_id=product_id,
                organization_id=organization_id,
            )
        )

        # Convert DAOs to DTOs for service layer
        return [
            ProductAdapter.relationship_to_dto(relationship)
            for relationship in all_relationships_dao
        ]

    async def _validate_relationship_rules(
        self,
        product: Product,
        related_product: Product,
        relationship_type: ProductRelationshipType,
    ) -> None:
        """Validate business rules for product relationships."""
        if relationship_type == ProductRelationshipType.INCLUDED_IN:
            if product.type != ProductType.BUNDLE:
                raise InvalidArgumentError(
                    "Only BUNDLE products can include components"
                )

            if related_product.type != ProductType.COMPONENT:
                raise InvalidArgumentError(
                    "Only COMPONENT products can be included in bundles"
                )

        elif relationship_type == ProductRelationshipType.ADD_ON:
            if related_product.type == ProductType.COMPONENT:
                raise InvalidArgumentError("COMPONENT products cannot be add-ons")


class SingletonProductService(Singleton, ProductService):
    pass


def get_product_service_general(db_engine: DatabaseEngine) -> ProductService:
    if SingletonProductService.has_instance():
        return SingletonProductService.get_singleton_instance()
    return SingletonProductService(engine=db_engine)


def get_product_service(request: Request) -> ProductService:
    return get_product_service_general(db_engine=get_db_engine(request))
