from uuid import UUID

from fastapi import Request

from salestech_be.common.exception.exception import (
    InvalidArgumentError,
    ResourceNotFoundError,
)
from salestech_be.common.lifespan import get_db_engine
from salestech_be.common.singleton import Singleton
from salestech_be.core.common.types import UserAuthContext
from salestech_be.core.cpq.adapters.quote_adapter import QuoteAdapter
from salestech_be.core.cpq.dto.quote_dto import (
    QuoteDTO,
    QuoteLineItemDTO,
)
from salestech_be.db.dao.quote_repository import QuoteRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.quote import Quote, QuoteStatus
from salestech_be.db.models.quote_line_item import QuoteLineItemUpdate
from salestech_be.ree_logging import get_logger
from salestech_be.util.time import zoned_utc_now

logger = get_logger(__name__)


class QuoteService:
    def __init__(self, engine: DatabaseEngine):
        self.quote_repository = QuoteRepository(engine=engine)
        self.engine = engine

    async def upsert_quote(
        self,
        quote_dto: QuoteDTO,
        user_auth_context: UserAuthContext,
    ) -> QuoteDTO:
        """Create a new quote or update existing quote by creating a new version."""
        # Handle quote creation logic
        if quote_dto.quote_key:
            # Check if quote with this quote_key exists
            existing_quote = (
                await self.quote_repository.find_latest_quote_version_by_quote_key(
                    quote_key=quote_dto.quote_key,
                    organization_id=user_auth_context.organization_id,
                )
            )

            if existing_quote:
                # Case 1: Quote exists - this should be an update, not create
                logger.bind(
                    organization_id=user_auth_context.organization_id,
                    user_id=user_auth_context.user_id,
                    quote_key=quote_dto.quote_key,
                    existing_version=existing_quote.version_number,
                ).info("Quote exists, redirecting to update")
                return await self.update_quote(
                    updated_quote_dto=quote_dto,
                    user_auth_context=user_auth_context,
                )
            else:
                # Case 2: Quote key provided but doesn't exist - clear it so new UUID is generated
                quote_dto.quote_key = None
        # Case 3: No quote_key provided - adapter will generate new key (handled automatically)

        quote_dao = QuoteAdapter.quote_dto_to_db_model(quote_dto)
        inserted_quote_dao: Quote = await self.quote_repository.insert(quote_dao)
        created_dto = QuoteAdapter.quote_to_dto(inserted_quote_dao)

        logger.bind(
            organization_id=user_auth_context.organization_id,
            user_id=user_auth_context.user_id,
            quote_id=created_dto.id,
            quote_key=created_dto.quote_key,
            quote_name=created_dto.name,
            version_number=created_dto.version_number,
        ).info("Quote created successfully")

        return created_dto

    async def get_quote(
        self,
        quote_id: UUID,
        organization_id: UUID,
    ) -> QuoteDTO:
        """Get a quote by ID."""
        quote_dao = await self.quote_repository.find_by_tenanted_primary_key(
            Quote,
            organization_id=organization_id,
            id=quote_id,
        )

        if not quote_dao:
            raise ResourceNotFoundError(
                f"Quote {quote_id} not found in organization {organization_id}"
            )

        return QuoteAdapter.quote_to_dto(quote_dao)

    async def update_quote(
        self,
        updated_quote_dto: QuoteDTO,
        user_auth_context: UserAuthContext,
    ) -> QuoteDTO:
        """Update an existing quote by creating a new version."""
        # Check if quote exists
        if not updated_quote_dto.quote_key:
            raise InvalidArgumentError("Quote key is required for update")

        previous_quote_version = (
            await self.quote_repository.find_latest_quote_version_by_quote_key(
                quote_key=updated_quote_dto.quote_key,
                organization_id=user_auth_context.organization_id,
            )
        )

        if not previous_quote_version:
            raise ResourceNotFoundError(
                f"Quote with quote key {updated_quote_dto.quote_key} not found in organization {user_auth_context.organization_id}"
            )

        # Business rule: Can only update quotes in DRAFT status
        if previous_quote_version.status != QuoteStatus.DRAFT:
            raise InvalidArgumentError(
                f"Cannot update quotes that are not in DRAFT status (quote {previous_quote_version.id} has status {previous_quote_version.status.value})"
            )

        updated_quote_dto.version_number = previous_quote_version.version_number + 1

        quote_dao = QuoteAdapter.quote_dto_to_db_model(updated_quote_dto)
        inserted_quote_dao: Quote = await self.quote_repository.insert(quote_dao)
        created_dto = QuoteAdapter.quote_to_dto(inserted_quote_dao)

        logger.bind(
            organization_id=user_auth_context.organization_id,
            user_id=user_auth_context.user_id,
            quote_key=created_dto.quote_key,
            previous_version=previous_quote_version.version_number,
            new_version=created_dto.version_number,
            new_quote_id=created_dto.id,
            quote_name=created_dto.name,
        ).info("Quote updated successfully with new version")

        return created_dto

    async def delete_quote(
        self,
        quote_id: UUID,
        user_auth_context: UserAuthContext,
    ) -> QuoteDTO:
        """Soft delete a quote and all its line items."""
        quote = await self.quote_repository.find_by_tenanted_primary_key(
            Quote,
            organization_id=user_auth_context.organization_id,
            id=quote_id,
        )

        if not quote:
            raise ResourceNotFoundError(
                f"Quote {quote_id} not found in organization {user_auth_context.organization_id}"
            )

        # Transactionally soft delete the quote and all line items
        try:
            quote_dao = await self.quote_repository.transactional_soft_delete_quote_and_line_items(
                quote_id=quote_id,
                organization_id=user_auth_context.organization_id,
                user_id=user_auth_context.user_id,
            )
        except ValueError as e:
            logger.bind(
                organization_id=user_auth_context.organization_id,
                quote_id=quote_id,
                user_id=user_auth_context.user_id,
                error_type=type(e).__name__,
                error_message=str(e),
            ).error("Failed to delete quote due to database constraint")
            raise ResourceNotFoundError(
                f"Quote {quote_id} not found or already deleted"
            ) from e

        logger.bind(
            organization_id=user_auth_context.organization_id,
            user_id=user_auth_context.user_id,
            quote_id=quote_id,
            quote_key=quote.quote_key,
            quote_name=quote.name,
            version_number=quote.version_number,
        ).info("Quote and associated line items deleted successfully")

        return QuoteAdapter.quote_to_dto(quote_dao)

    async def get_quote_line_items(
        self,
        quote_id: UUID,
        organization_id: UUID,
    ) -> list[QuoteLineItemDTO]:
        """Get all line items for a quote."""
        line_items_dao = await self.quote_repository.find_quote_line_items_by_quote_id(
            quote_id=quote_id,
            organization_id=organization_id,
        )

        return [
            QuoteAdapter.quote_line_item_to_dto(line_item)
            for line_item in line_items_dao
        ]

    async def add_quote_line_item(
        self,
        line_item_dto: QuoteLineItemDTO,
        user_auth_context: UserAuthContext,
    ) -> QuoteLineItemDTO:
        """Add a new line item to a quote."""
        # Verify the quote exists and check business rules
        quote = await self.quote_repository.find_by_tenanted_primary_key(
            Quote,
            organization_id=user_auth_context.organization_id,
            id=line_item_dto.quote_id,
        )

        if not quote:
            raise ResourceNotFoundError(
                f"Quote {line_item_dto.quote_id} not found in organization {user_auth_context.organization_id}"
            )

        # Business rule: Can only modify quotes in DRAFT status
        if quote.status != QuoteStatus.DRAFT:
            raise InvalidArgumentError(
                f"Cannot modify quotes that are not in DRAFT status (quote {quote.id} has status {quote.status.value})"
            )

        # Set audit fields
        line_item_dto.created_by_user_id = user_auth_context.user_id
        line_item_dto.created_at = zoned_utc_now()

        line_item_dao = QuoteAdapter.quote_line_item_dto_to_db_model(line_item_dto)
        inserted_line_item = await self.quote_repository.add_quote_line_item(
            line_item_dao
        )

        logger.bind(
            organization_id=user_auth_context.organization_id,
            user_id=user_auth_context.user_id,
            quote_id=line_item_dto.quote_id,
            quote_key=quote.quote_key,
            line_item_id=inserted_line_item.id,
            quantity=inserted_line_item.quantity,
            amount_cents=inserted_line_item.final_amount_cents,
        ).info("Line item added to quote successfully")

        return QuoteAdapter.quote_line_item_to_dto(inserted_line_item)

    async def update_quote_line_item(
        self,
        line_item_id: UUID,
        organization_id: UUID,
        updates: QuoteLineItemUpdate,
        user_id: UUID,
    ) -> QuoteLineItemDTO:
        """Update an existing quote line item."""
        # Get the line item to check business rules
        existing_line_item = await self.quote_repository.find_quote_line_item_by_id(
            line_item_id=line_item_id,
            organization_id=organization_id,
        )

        if not existing_line_item:
            raise ResourceNotFoundError(
                f"Quote line item {line_item_id} not found in organization {organization_id}"
            )

        # Get the quote to check business rules
        quote = await self.quote_repository.find_by_tenanted_primary_key(
            Quote,
            organization_id=organization_id,
            id=existing_line_item.quote_id,
        )

        if not quote:
            raise ResourceNotFoundError(
                f"Quote {existing_line_item.quote_id} not found in organization {organization_id}"
            )

        # Business rule: Can only modify quotes in DRAFT status
        if quote.status != QuoteStatus.DRAFT:
            raise InvalidArgumentError(
                f"Cannot modify quotes that are not in DRAFT status (quote {quote.id} has status {quote.status.value})"
            )

        # Create update with audit fields
        update_with_audit = QuoteLineItemUpdate(
            **updates.model_dump(
                exclude_unset=True, exclude={"updated_at", "updated_by_user_id"}
            ),
            updated_at=zoned_utc_now(),
            updated_by_user_id=user_id,
        )

        updated_line_item = await self.quote_repository.update_quote_line_item(
            line_item_id=line_item_id,
            organization_id=organization_id,
            update_data=update_with_audit,
        )

        if not updated_line_item:
            raise ResourceNotFoundError(
                f"Quote line item {line_item_id} not found during update operation"
            )

        logger.bind(
            organization_id=organization_id,
            user_id=user_id,
            quote_id=quote.id,
            quote_key=quote.quote_key,
            line_item_id=line_item_id,
            new_quantity=updated_line_item.quantity,
            new_amount_cents=updated_line_item.final_amount_cents,
        ).info("Quote line item updated successfully")

        return QuoteAdapter.quote_line_item_to_dto(updated_line_item)

    async def remove_quote_line_item(
        self,
        line_item_id: UUID,
        user_auth_context: UserAuthContext,
    ) -> QuoteLineItemDTO:
        """Remove (soft delete) a quote line item."""
        # Get the line item to check business rules
        existing_line_item = await self.quote_repository.find_quote_line_item_by_id(
            line_item_id=line_item_id,
            organization_id=user_auth_context.organization_id,
        )

        if not existing_line_item:
            raise ResourceNotFoundError(
                f"Quote line item {line_item_id} not found in organization {user_auth_context.organization_id}"
            )

        # Get the quote to check business rules
        quote = await self.quote_repository.find_by_tenanted_primary_key(
            Quote,
            organization_id=user_auth_context.organization_id,
            id=existing_line_item.quote_id,
        )

        if not quote:
            raise ResourceNotFoundError(
                f"Quote {existing_line_item.quote_id} not found in organization {user_auth_context.organization_id}"
            )

        # Business rule: Can only modify quotes in DRAFT status
        if quote.status != QuoteStatus.DRAFT:
            raise InvalidArgumentError(
                f"Cannot modify quotes that are not in DRAFT status (quote {quote.id} has status {quote.status.value})"
            )

        deleted_line_item = await self.quote_repository.remove_quote_line_item(
            line_item_id=line_item_id,
            organization_id=user_auth_context.organization_id,
            user_id=user_auth_context.user_id,
        )

        if not deleted_line_item:
            raise ResourceNotFoundError(
                f"Quote line item {line_item_id} not found during deletion operation"
            )

        logger.bind(
            organization_id=user_auth_context.organization_id,
            user_id=user_auth_context.user_id,
            quote_id=quote.id,
            quote_key=quote.quote_key,
            line_item_id=line_item_id,
            quantity=existing_line_item.quantity,
            amount_cents=existing_line_item.final_amount_cents,
        ).info("Quote line item removed successfully")

        return QuoteAdapter.quote_line_item_to_dto(deleted_line_item)

    async def get_quote_line_item(
        self,
        line_item_id: UUID,
        organization_id: UUID,
    ) -> QuoteLineItemDTO:
        """Get a specific quote line item by ID."""
        line_item = await self.quote_repository.find_quote_line_item_by_id(
            line_item_id=line_item_id,
            organization_id=organization_id,
        )

        if not line_item:
            raise ResourceNotFoundError(
                f"Quote line item {line_item_id} not found in organization {organization_id}"
            )

        return QuoteAdapter.quote_line_item_to_dto(line_item)


class SingletonQuoteService(Singleton, QuoteService):
    pass


def get_quote_service_general(db_engine: DatabaseEngine) -> QuoteService:
    if SingletonQuoteService.has_instance():
        return SingletonQuoteService.get_singleton_instance()
    return SingletonQuoteService(engine=db_engine)


def get_quote_service(request: Request) -> QuoteService:
    return get_quote_service_general(db_engine=get_db_engine(request))
