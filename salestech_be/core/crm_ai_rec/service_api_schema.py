from typing import Any
from uuid import UUID

from pydantic import BaseModel, Field

from salestech_be.common.type.metadata.common import StandardObjectIdentifier
from salestech_be.db.models.crm_ai_rec import CrmAIRecType, ParentRecordIds
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime


class DomainObjectPreview(BaseModel):  # type: ignore[explicit-any] # need to support dynamic payload
    object_identifier: StandardObjectIdentifier
    ai_rec_id: UUID
    citation_ids: list[UUID] = Field(default_factory=list)
    parent_record_ids: ParentRecordIds | None = None
    preview_data: dict[str, Any]  # type: ignore[explicit-any]
    created_at: ZoneRequiredDateTime


class DomainObjectIdWIthPreview(BaseModel):
    object_identifier: StandardObjectIdentifier
    record_id: UUID
    ai_rec_preview: DomainObjectPreview


class FieldLevelPatchPreview(BaseModel):  # type: ignore[explicit-any]
    field_path: tuple[str, ...]
    value: Any  # type: ignore[explicit-any]
    ai_rec_id: UUID
    rec_type: CrmAIRecType
    citation_ids: list[UUID] = Field(default_factory=list)


class DomainObjectPatchPreview(BaseModel):  # type: ignore[explicit-any]
    object_identifier: StandardObjectIdentifier
    record_id: UUID
    preview_data: dict[str, Any]  # type: ignore[explicit-any]
    field_level_preview: list[FieldLevelPatchPreview]


class ListActiveCrmAiRecsBySourceRequest(BaseModel):
    source_object_identifier: StandardObjectIdentifier
    source_record_id: UUID


class ListActiveCrmAiRecsBySourceResponse(BaseModel):
    preview_from_pending_patch_rec: list[DomainObjectPatchPreview]
    preview_from_accepted_patch_rec: list[DomainObjectPatchPreview]
    preview_from_pending_creation_rec: list[DomainObjectPreview]
    preview_from_accepted_creation_rec: list[DomainObjectIdWIthPreview]


class ListCrmObjectCreationRecsRequest(BaseModel):
    object_identifier: StandardObjectIdentifier
    parent_record_ids: list[ParentRecordIds]


class ListCrmObjectCreationRecsMultiRequest(BaseModel):
    requests: list[ListCrmObjectCreationRecsRequest]
    max_result_per_request: int = 5


class RejectCrmObjectCreationRecRequest(BaseModel):
    standard_object_identifier: StandardObjectIdentifier
    ai_rec_ids: set[UUID]
