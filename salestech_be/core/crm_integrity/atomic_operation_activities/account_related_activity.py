from temporalio import activity

from salestech_be.core.account.service.account_service import get_account_service
from salestech_be.core.crm_integrity.data_operation_tracker.atomic_operation_tracker_service import (
    get_atomic_operation_tracker_service,
)
from salestech_be.core.crm_integrity.types.activity_params import (
    ArchiveAccountParam,
)
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.crm_integrity import (
    CRMIntegrityOperation,
)


class AccountRelatedActivities:
    def __init__(self, db_engine: DatabaseEngine) -> None:
        self.atomic_operation_tracker_service = get_atomic_operation_tracker_service(
            db_engine=db_engine
        )

        self.account_service = get_account_service(
            db_engine=db_engine,
        )

    @activity.defn
    async def archive_account(
        self, param: ArchiveAccountParam
    ) -> CRMIntegrityOperation:
        await self.account_service.archive_by_id(
            account_id=param.archive_account_id,
            user_id=param.user_id,
            organization_id=param.organization_id,
            exclude_locked_by_integrity_jobs=False,
        )

        return await self.atomic_operation_tracker_service.track_account_archive(
            integrity_job_id=param.job_id,
            account_id=param.archive_account_id,
            user_id=param.user_id,
            organization_id=param.organization_id,
        )
