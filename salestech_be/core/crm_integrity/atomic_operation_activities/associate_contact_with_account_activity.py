from temporalio import activity

from salestech_be.common.exception import InvalidArgumentError
from salestech_be.core.contact.service.contact_data_integrity_service import (
    get_contact_data_integrity_service,
)
from salestech_be.core.contact.service.contact_service import (
    get_contact_service,
)
from salestech_be.core.contact.service_api_schema import (
    UpsertContactAccountRoleRequest,
)
from salestech_be.core.crm_integrity.data_operation_tracker.atomic_operation_tracker_service import (
    get_atomic_operation_tracker_service,
)
from salestech_be.core.crm_integrity.types.activity_params import (
    AssociateSingleContactWithAccountParam,
    CopyAssociationsFromAccountToAccountParam,
    CopyAssociationsFromContactToContactParam,
)
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.util.validation import one_row_only, one_row_or_none


class AssociateContactWithAccountActivity:
    def __init__(self, db_engine: DatabaseEngine) -> None:
        self.atomic_operation_tracker_service = get_atomic_operation_tracker_service(
            db_engine=db_engine
        )
        self.contact_service = get_contact_service(db_engine=db_engine)
        self.contact_data_integrity_service = get_contact_data_integrity_service(
            db_engine=db_engine
        )

    @activity.defn(name="copy_single_contact_association_from_account_to_account")
    async def copy_single_contact_association_from_account_to_account(
        self,
        param: AssociateSingleContactWithAccountParam,
    ) -> None:
        """
        Copy association from src account to dest account for a single contact.

        This requires there is no existing association between contact and dest account.
        Otherwise, raise InvalidArgumentError.
        """
        current_src_account_contact_association = (
            one_row_only(
                await self.contact_service.list_active_contact_account_associations(
                    organization_id=param.organization_id,
                    contact_id=param.contact_id,
                    account_id=param.src_account_id,
                )
            )
            if param.src_account_id
            else None
        )

        current_dest_account_contact_association = one_row_or_none(
            await self.contact_service.list_active_contact_account_associations(
                organization_id=param.organization_id,
                contact_id=param.contact_id,
                account_id=param.dest_account_id,
            )
        )

        if current_dest_account_contact_association:
            raise InvalidArgumentError(
                f"contact(id={param.contact_id}) already associated "
                f"with dest account(id={param.dest_account_id})"
            )

        await self.contact_service.upsert_contact_account_association(
            organization_id=param.organization_id,
            user_id=param.user_id,
            contact_id=param.contact_id,
            req=UpsertContactAccountRoleRequest(
                account_id=param.dest_account_id,
                is_primary=current_src_account_contact_association.is_primary
                if current_src_account_contact_association
                else False,
                title=current_src_account_contact_association.title
                if current_src_account_contact_association
                else param.title,
                department=current_src_account_contact_association.department
                if current_src_account_contact_association
                else param.department,
            ),
            exclude_locked_by_integrity_jobs=False,
        )
        await self.atomic_operation_tracker_service.track_contact_account_association(
            integrity_job_id=param.integrity_job_id,
            contact_id=param.contact_id,
            account_id=param.dest_account_id,
            user_id=param.user_id,
            organization_id=param.organization_id,
        )

    @activity.defn(name="copy_contact_associations_from_account_to_account")
    async def copy_contact_associations_from_account_to_account(
        self,
        param: CopyAssociationsFromAccountToAccountParam,
    ) -> int:
        """
        Associate all contacts under one account to another account.
        """
        preview_result = await self.contact_data_integrity_service.preview_contact_account_associations_for_merging_accounts(
            src_account_id=param.src_account_id,
            dest_account_id=param.dest_account_id,
            organization_id=param.organization_id,
        )

        for association in preview_result.to_create:
            await self.contact_service.upsert_contact_account_association(
                organization_id=param.organization_id,
                user_id=param.user_id,
                contact_id=association.contact_id,
                req=UpsertContactAccountRoleRequest(
                    account_id=param.dest_account_id,
                    is_primary=association.is_primary,
                    title=association.title,
                    department=association.department,
                ),
                exclude_locked_by_integrity_jobs=False,
            )

            await (
                self.atomic_operation_tracker_service.track_contact_account_association(
                    integrity_job_id=param.integrity_job_id,
                    contact_id=association.contact_id,
                    account_id=param.dest_account_id,
                    user_id=param.user_id,
                    organization_id=param.organization_id,
                )
            )

        return len(preview_result.to_create)

    @activity.defn(name="copy_contact_associations_from_contact_to_contact")
    async def copy_contact_associations_from_contact_to_contact(
        self,
        param: CopyAssociationsFromContactToContactParam,
    ) -> int:
        """
        Associate a contact to all accounts that another contact is connected to.
        """
        preview_result = await self.contact_data_integrity_service.preview_contact_account_associations_for_merging_contacts(
            src_contact_id=param.src_contact_id,
            dest_contact_id=param.dest_contact_id,
            organization_id=param.organization_id,
        )

        for association in preview_result.to_create:
            await self.contact_service.upsert_contact_account_association(
                organization_id=param.organization_id,
                user_id=param.user_id,
                contact_id=association.contact_id,
                req=UpsertContactAccountRoleRequest(
                    account_id=association.account_id,
                    is_primary=association.is_primary,
                    title=association.title,
                    department=association.department,
                ),
                exclude_locked_by_integrity_jobs=False,
            )
            await (
                self.atomic_operation_tracker_service.track_contact_account_association(
                    integrity_job_id=param.integrity_job_id,
                    contact_id=association.contact_id,
                    account_id=association.account_id,
                    user_id=param.user_id,
                    organization_id=param.organization_id,
                )
            )

        return len(preview_result.to_create)
