from uuid import UUID

from temporalio import activity

from salestech_be.core.crm_integrity.data_operation_tracker.associated_entity_operation_tracker_service import (
    get_associated_entity_operation_tracker_service,
)
from salestech_be.core.crm_integrity.data_operation_tracker.atomic_operation_tracker_service import (
    get_atomic_operation_tracker_service,
)
from salestech_be.core.crm_integrity.types.activity_params import (
    CopyPipelinesFromContactToContactParam,
)
from salestech_be.core.pipeline.service.pipeline_service import get_pipeline_service
from salestech_be.core.pipeline.service_api_schema import (
    ContactPipelineAssociationRequest,
    ReplaceContactPipelineAssociationRequest,
)
from salestech_be.db.dbengine.core import DatabaseEngine


class AssociateContactWithPipelineActivity:
    def __init__(self, db_engine: DatabaseEngine) -> None:
        self.associated_data_operation_tracker_service = (
            get_associated_entity_operation_tracker_service(db_engine=db_engine)
        )
        self.pipeline_service = get_pipeline_service(db_engine=db_engine)
        self.atomic_operation_tracker_service = get_atomic_operation_tracker_service(
            db_engine=db_engine
        )

    @activity.defn(name="copy_pipeline_associations_from_contact_to_contact")
    async def copy_pipelines_associations_from_contact_to_contact(
        self, param: CopyPipelinesFromContactToContactParam
    ) -> list[UUID]:
        pipeline_ids = [
            association.pipeline_id
            for association in await self.pipeline_service.list_contact_pipeline_associations_by_contact_id(
                organization_id=param.organization_id,
                contact_id=param.primary_entity_id,
            )
        ]
        if not pipeline_ids:
            return []

        for pipeline_id in pipeline_ids:
            result = await self.pipeline_service.replace_contact_pipeline_association(
                organization_id=param.organization_id,
                user_id=param.user_id,
                pipeline_id=pipeline_id,
                req=ReplaceContactPipelineAssociationRequest(
                    archiving_contact_id=param.primary_entity_id,
                    replacing_contact_association_request=ContactPipelineAssociationRequest(
                        contact_id=param.replaced_entity_id,
                    ),
                ),
            )
            await self.atomic_operation_tracker_service.track_contact_pipeline_unassociation(
                integrity_job_id=param.integrity_job_id,
                pipeline_id=pipeline_id,
                contact_id=param.primary_entity_id,
                user_id=param.user_id,
                organization_id=param.organization_id,
            )
            if result.replacement:
                await self.atomic_operation_tracker_service.track_contact_pipeline_association(
                    integrity_job_id=param.integrity_job_id,
                    pipeline_id=pipeline_id,
                    contact_id=param.replaced_entity_id,
                    user_id=param.user_id,
                    organization_id=param.organization_id,
                )

        return pipeline_ids
