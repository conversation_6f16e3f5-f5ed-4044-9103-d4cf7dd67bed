from typing import Named<PERSON><PERSON><PERSON>
from uuid import UUID

from temporalio import activity

from salestech_be.core.contact.service.contact_data_integrity_service import (
    get_contact_data_integrity_service,
)
from salestech_be.core.contact.service.contact_service import (
    get_contact_service,
)
from salestech_be.core.contact.service_api_schema import (
    UpsertContactAccountRoleRequest,
)
from salestech_be.core.crm_integrity.data_operation_tracker.atomic_operation_tracker_service import (
    get_atomic_operation_tracker_service,
)
from salestech_be.core.crm_integrity.types.activity_params import (
    MoveContactEmailsFromAccountToAccountParam,
    MoveContactEmailsFromContactToAccountParam,
    MoveContactEmailsFromContactToContactParam,
    MoveContactEmailToAccountParam,
    MoveContactEmailToContactParam,
    RemoveContactEmailFromAccountParam,
    RemoveContactEmailFromContactParam,
    RemoveContactEmailsFromAccountParam,
)
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.contact import (
    CreateDbContactEmailAccountAssociationRequest,
    CreateDbContactEmailRequest,
)
from salestech_be.db.models.contact_account_association import ContactAccountAssociation
from salestech_be.db.models.contact_email import (
    ContactEmail,
    ContactEmailAccountAssociation,
)
from salestech_be.db.models.crm_integrity import (
    CRMIntegrityOperation,
)
from salestech_be.ree_logging import get_logger
from salestech_be.util.validation import not_none, one_row_only

logger = get_logger(__name__)


class DeleteContactEmailResultExt(NamedTuple):
    deleted_contact_email: ContactEmail
    deleted_contact_email_account_associations: list[ContactEmailAccountAssociation]
    deleted_contact_account_associations: list[ContactAccountAssociation]


class ContactEmailRelatedActivity:
    def __init__(self, db_engine: DatabaseEngine) -> None:
        self.contact_service = get_contact_service(db_engine=db_engine)
        self.contact_data_integrity_service = get_contact_data_integrity_service(
            db_engine=db_engine
        )
        self.atomic_operation_tracker_service = get_atomic_operation_tracker_service(
            db_engine=db_engine
        )

    async def _move_contact_email_to_account(
        self,
        *,
        integrity_job_id: UUID,
        contact_email_id: UUID,
        src_account_id: UUID,
        dest_account_id: UUID,
        user_id: UUID,
        organization_id: UUID,
    ) -> tuple[CRMIntegrityOperation, CRMIntegrityOperation | None]:
        (
            deleted_association,
            inserted_association,
        ) = await self.contact_data_integrity_service.move_contact_email_to_account(
            contact_email_id=contact_email_id,
            src_account_id=src_account_id,
            dest_account_id=dest_account_id,
            user_id=user_id,
            organization_id=organization_id,
            exclude_locked_by_integrity_jobs=False,
        )

        return (
            await self.atomic_operation_tracker_service.track_contact_email_account_association_delete(
                integrity_job_id=integrity_job_id,
                contact_email_id=deleted_association.contact_email_id,
                account_id=deleted_association.account_id,
                user_id=user_id,
                organization_id=organization_id,
            ),
            await self.atomic_operation_tracker_service.track_contact_email_account_association_create(
                integrity_job_id=integrity_job_id,
                contact_email_id=inserted_association.contact_email_id,
                account_id=inserted_association.account_id,
                user_id=user_id,
                organization_id=organization_id,
            )
            if inserted_association
            else None,
        )

    @activity.defn(name="move_contact_email_to_account")
    async def move_contact_email_to_account(
        self,
        param: MoveContactEmailToAccountParam,
    ) -> tuple[CRMIntegrityOperation, CRMIntegrityOperation | None]:
        """
        Move a contact email from one account to another.

        Consequences:
        * The old contact + email + src_account association will be removed.
        * The new contact + email + dest_account association will be created if it doesn't exist.
        Otherwise, no op.
        """
        return await self._move_contact_email_to_account(
            integrity_job_id=param.job_id,
            contact_email_id=param.contact_email_id,
            src_account_id=param.src_account_id,
            dest_account_id=param.dest_account_id,
            user_id=param.user_id,
            organization_id=param.organization_id,
        )

    @activity.defn(name="remove_contact_email_from_account")
    async def remove_contact_email_from_account(
        self,
        param: RemoveContactEmailFromAccountParam,
    ) -> CRMIntegrityOperation:
        """
        Remove a contact email from an account.

        Consequences:
        * The contact + email + account association will be removed.
        """
        await self.contact_service.delete_contact_email_account_associations(
            organization_id=param.organization_id,
            user_id=param.user_id,
            contact_id=param.contact_id,
            account_id=param.account_id,
            contact_email_id=param.contact_email_id,
            exclude_locked_by_integrity_jobs=False,
        )

        return await self.atomic_operation_tracker_service.track_contact_email_account_association_delete(
            integrity_job_id=param.job_id,
            contact_email_id=param.contact_email_id,
            account_id=param.account_id,
            user_id=param.user_id,
            organization_id=param.organization_id,
        )

    @activity.defn(name="remove_contact_emails_from_account")
    async def remove_contact_emails_from_account(
        self,
        param: RemoveContactEmailsFromAccountParam,
    ) -> list[CRMIntegrityOperation]:
        """
        Remove all contact emails of one contact from an account.
        """

        contact_email_account_associations_to_delete = (
            await self.contact_service.list_contact_email_account_associationss(
                contact_id=param.contact_id,
                account_id=param.account_id,
                organization_id=param.organization_id,
            )
        )

        integrity_operations: list[CRMIntegrityOperation] = []

        for association in contact_email_account_associations_to_delete:
            await self.contact_service.delete_contact_email_account_associations(
                organization_id=param.organization_id,
                user_id=param.user_id,
                contact_id=param.contact_id,
                account_id=param.account_id,
                contact_email_id=association.contact_email_id,
                exclude_locked_by_integrity_jobs=False,
            )

            integrity_operations.append(
                await self.atomic_operation_tracker_service.track_contact_email_account_association_delete(
                    integrity_job_id=param.job_id,
                    contact_email_id=association.contact_email_id,
                    account_id=association.account_id,
                    user_id=param.user_id,
                    organization_id=param.organization_id,
                )
            )

        return integrity_operations

    async def _delete_contact_email(
        self,
        *,
        contact_email_id: UUID,
        contact_id: UUID,
        user_id: UUID,
        organization_id: UUID,
        remove_contact_account_association_if_last: bool,
    ) -> DeleteContactEmailResultExt:
        contact_email = not_none(
            await self.contact_service.get_contact_email_by_id(
                contact_email_id=contact_email_id,
                organization_id=organization_id,
            )
        )

        solo_associations = await self.contact_data_integrity_service.preview_solo_contact_email_account_associations(
            contact_id=contact_id,
            contact_email_id=contact_email_id,
            organization_id=organization_id,
        )

        result = await self.contact_service.delete_contact_email(
            email=contact_email.email,
            contact_id=contact_id,
            user_id=user_id,
            organization_id=organization_id,
            exclude_locked_by_integrity_jobs=False,
        )

        deleted_contact_account_associations: list[ContactAccountAssociation] = []
        if remove_contact_account_association_if_last:
            # remove the contact account association if there is no other contact emails for the account anymore
            for association in solo_associations:
                archive_result = (
                    await self.contact_service.archive_contact_account_association(
                        user_id=user_id,
                        organization_id=organization_id,
                        contact_id=association.contact_id,
                        account_id=association.account_id,
                    )
                )
                deleted_contact_account_associations.append(archive_result.archived)

        return DeleteContactEmailResultExt(
            deleted_contact_email=result.deleted,
            deleted_contact_email_account_associations=result.deleted_email_account_associations,
            deleted_contact_account_associations=deleted_contact_account_associations,
        )

    @activity.defn
    async def delete_contact_email(
        self, param: RemoveContactEmailFromContactParam
    ) -> CRMIntegrityOperation:
        delete_contact_email_result = await self._delete_contact_email(
            contact_email_id=param.contact_email_id,
            contact_id=param.contact_id,
            user_id=param.user_id,
            organization_id=param.organization_id,
            remove_contact_account_association_if_last=param.remove_contact_account_association_if_last,
        )

        for (
            association
        ) in delete_contact_email_result.deleted_contact_account_associations:
            await self.atomic_operation_tracker_service.track_contact_account_unassociation(
                integrity_job_id=param.job_id,
                contact_id=association.contact_id,
                account_id=association.account_id,
                user_id=param.user_id,
                organization_id=param.organization_id,
            )

        return await self.atomic_operation_tracker_service.track_contact_email_delete(
            integrity_job_id=param.job_id,
            contact_email_id=param.contact_email_id,
            contact_id=param.contact_id,
            user_id=param.user_id,
            organization_id=param.organization_id,
        )

    @activity.defn
    async def move_contact_email_to_contact(
        self,
        param: MoveContactEmailToContactParam,
    ) -> CRMIntegrityOperation:
        src_contact_email = not_none(
            await self.contact_service.get_contact_email_by_id(
                contact_email_id=param.contact_email_id,
                organization_id=param.organization_id,
            )
        )

        dest_contact_emails = (
            await self.contact_service.list_contact_emails_by_contact_id(
                contact_id=param.dest_contact_id,
                organization_id=param.organization_id,
            )
        )

        set_moving_contact_email_as_primary = bool(not dest_contact_emails)

        # find accounts that are associated with the src contact only
        contact_account_associations_source_only = await self.contact_data_integrity_service.preview_contact_account_associations_in_source_only(
            src_contact_id=param.src_contact_id,
            dest_contact_id=param.dest_contact_id,
            organization_id=param.organization_id,
        )

        account_id_to_association_map_for_src_contact_only = {
            association.account_id: association
            for association in contact_account_associations_source_only
        }

        # find all contact email account associations tha might need to move from src contact
        src_contact_email_account_associations = await self.contact_service.list_contact_email_account_associations_by_contact_email_id(
            contact_email_id=param.contact_email_id,
            organization_id=param.organization_id,
        )

        # iterate associations and determine which ones to move
        contact_account_associations_to_move: list[ContactEmailAccountAssociation] = []
        for association in src_contact_email_account_associations:
            if (
                association.account_id
                in account_id_to_association_map_for_src_contact_only
            ):
                if param.create_contact_account_association_if_missing:
                    # create contact account association for dest contact if it only exists for source contact
                    await self.contact_service.upsert_contact_account_association(
                        user_id=param.user_id,
                        organization_id=param.organization_id,
                        contact_id=param.dest_contact_id,
                        req=UpsertContactAccountRoleRequest(
                            account_id=association.account_id,
                            is_primary=False,
                            title=account_id_to_association_map_for_src_contact_only[
                                association.account_id
                            ].title,
                            department=account_id_to_association_map_for_src_contact_only[
                                association.account_id
                            ].department,
                        ),
                        exclude_locked_by_integrity_jobs=False,
                    )

                    await self.atomic_operation_tracker_service.track_contact_account_association(
                        integrity_job_id=param.job_id,
                        contact_id=param.dest_contact_id,
                        account_id=association.account_id,
                        user_id=param.user_id,
                        organization_id=param.organization_id,
                    )
                else:
                    continue

            contact_account_associations_to_move.append(association)

        # cleanup src contact email + account associations before upsert new ones to avoid conflicts
        delete_contact_email_result = await self._delete_contact_email(
            contact_email_id=param.contact_email_id,
            contact_id=param.src_contact_id,
            user_id=param.user_id,
            organization_id=param.organization_id,
            remove_contact_account_association_if_last=param.remove_contact_account_association_if_last,
        )

        for (
            deleted_association
        ) in delete_contact_email_result.deleted_contact_account_associations:
            await self.atomic_operation_tracker_service.track_contact_account_unassociation(
                integrity_job_id=param.job_id,
                contact_id=deleted_association.contact_id,
                account_id=deleted_association.account_id,
                user_id=param.user_id,
                organization_id=param.organization_id,
            )

        # add new
        upsert_result = one_row_only(
            await self.contact_service.batch_upsert_contact_email(
                organization_id=param.organization_id,
                user_id=param.user_id,
                contact_id=param.dest_contact_id,
                create_contact_email_requests=[
                    CreateDbContactEmailRequest(
                        email=src_contact_email.email,
                        is_contact_primary=set_moving_contact_email_as_primary,
                        labels=src_contact_email.labels,
                        email_account_associations=[
                            CreateDbContactEmailAccountAssociationRequest(
                                account_id=association.account_id,
                                is_contact_account_primary=False,
                            )
                            for association in contact_account_associations_to_move
                        ],
                    )
                ],
                exclude_locked_by_integrity_jobs=False,
            )
        )

        for (
            upserted_contact_email_account_association_result
        ) in upsert_result.upserted_contact_email_account_association_results:
            created_contact_email_account_association = upserted_contact_email_account_association_result.upserted_contact_email_account_association

            await self.atomic_operation_tracker_service.track_contact_email_account_association_create(
                integrity_job_id=param.job_id,
                contact_email_id=created_contact_email_account_association.contact_email_id,
                account_id=created_contact_email_account_association.account_id,
                user_id=param.user_id,
                organization_id=param.organization_id,
            )

        return await self.atomic_operation_tracker_service.track_contact_email_create(
            integrity_job_id=param.job_id,
            contact_email_id=upsert_result.upserted_contact_email.id,
            contact_id=upsert_result.upserted_contact_email.contact_id,
            user_id=param.user_id,
            organization_id=param.organization_id,
        )

    @activity.defn(name="update_contact_id_in_contact_email")
    async def move_contact_emails_from_contact_to_contact(
        self, param: MoveContactEmailsFromContactToContactParam
    ) -> list[CRMIntegrityOperation]:
        """
        Move contact emails from one contact to another contact, including
        contact email account associations.

        Note: this method requires the moving contacts of contact emails are already
        associated with both src and dest accounts. Which is specified used by Merge
        Contacts workflow.
        """
        contact_emails_to_move = (
            await self.contact_service.list_contact_emails_by_contact_id(
                contact_id=param.src_contact_id,
                organization_id=param.organization_id,
            )
        )

        if not contact_emails_to_move:
            return []

        integrity_operations: list[CRMIntegrityOperation] = []

        for contact_email in contact_emails_to_move:
            contact_account_associations_to_move = await self.contact_service.list_contact_email_account_associations_by_contact_email_id(
                contact_email_id=contact_email.id,
                organization_id=param.organization_id,
            )

            dest_contact_emails = (
                await self.contact_service.list_contact_emails_by_contact_id(
                    contact_id=param.dest_contact_id,
                    organization_id=param.organization_id,
                )
            )

            set_moving_contact_email_as_primary = bool(not dest_contact_emails)

            delete_contact_email_result = await self._delete_contact_email(
                contact_email_id=contact_email.id,
                contact_id=param.src_contact_id,
                user_id=param.user_id,
                organization_id=param.organization_id,
                remove_contact_account_association_if_last=False,
            )

            for (
                deleted_association
            ) in delete_contact_email_result.deleted_contact_account_associations:
                await self.atomic_operation_tracker_service.track_contact_account_unassociation(
                    integrity_job_id=param.integrity_job_id,
                    contact_id=deleted_association.contact_id,
                    account_id=deleted_association.account_id,
                    user_id=param.user_id,
                    organization_id=param.organization_id,
                )

            upsert_result = one_row_only(
                await self.contact_service.batch_upsert_contact_email(
                    organization_id=param.organization_id,
                    user_id=param.user_id,
                    contact_id=param.dest_contact_id,
                    create_contact_email_requests=[
                        CreateDbContactEmailRequest(
                            email=contact_email.email,
                            is_contact_primary=set_moving_contact_email_as_primary,
                            labels=contact_email.labels,
                            email_account_associations=[
                                CreateDbContactEmailAccountAssociationRequest(
                                    account_id=association.account_id,
                                    is_contact_account_primary=False,
                                )
                                for association in contact_account_associations_to_move
                            ],
                        )
                    ],
                    exclude_locked_by_integrity_jobs=False,
                )
            )

            for (
                upserted_contact_email_account_association_result
            ) in upsert_result.upserted_contact_email_account_association_results:
                created_contact_email_account_association = upserted_contact_email_account_association_result.upserted_contact_email_account_association

                await self.atomic_operation_tracker_service.track_contact_email_account_association_create(
                    integrity_job_id=param.integrity_job_id,
                    contact_email_id=created_contact_email_account_association.contact_email_id,
                    account_id=created_contact_email_account_association.account_id,
                    user_id=param.user_id,
                    organization_id=param.organization_id,
                )

            integrity_operations.append(
                await self.atomic_operation_tracker_service.track_contact_email_create(
                    integrity_job_id=param.integrity_job_id,
                    contact_email_id=upsert_result.upserted_contact_email.id,
                    contact_id=upsert_result.upserted_contact_email.contact_id,
                    user_id=param.user_id,
                    organization_id=param.organization_id,
                )
            )

        return integrity_operations

    @activity.defn(name="move_contact_emails_from_account_to_account")
    async def move_contact_emails_from_account_to_account(
        self, param: MoveContactEmailsFromAccountToAccountParam
    ) -> list[CRMIntegrityOperation]:
        """
        Move contact emails from one account to another account
        This will update all contact emails from contacts that are associated with
        the src account to the dest account.

        Note: this method requires the moving contact of contact emails are already
        associated with both src and dest accounts. Which is specified used by Merge
        Accounts workflow.
        """
        src_contact_email_account_associations = await self.contact_service.list_contact_email_account_associations_by_account_id(
            account_id=param.src_account_id,
            organization_id=param.organization_id,
        )

        integrity_operations: list[CRMIntegrityOperation] = []

        for association in src_contact_email_account_associations:
            (
                deleted_association,
                inserted_association,
            ) = await self._move_contact_email_to_account(
                integrity_job_id=param.integrity_job_id,
                contact_email_id=association.contact_email_id,
                src_account_id=param.src_account_id,
                dest_account_id=param.dest_account_id,
                user_id=param.user_id,
                organization_id=param.organization_id,
            )

            integrity_operations.append(deleted_association)
            if inserted_association:
                integrity_operations.append(inserted_association)

        return integrity_operations

    @activity.defn(name="move_contact_emails_from_contact_to_account")
    async def move_contact_emails_from_contact_to_account(
        self, param: MoveContactEmailsFromContactToAccountParam
    ) -> list[CRMIntegrityOperation]:
        """
        Move contact emails of contact from one account to another account
        This will update all contact emails of contact that are associated with
        the src account to the dest account.

        Note: this method requires the moving contact of contact emails are already
        associated with both src and dest accounts. Which is specified used by Move
        Contact to Account workflow.
        """

        src_contact_email_account_associations = (
            await self.contact_service.list_contact_email_account_associationss(
                contact_id=param.contact_id,
                account_id=param.src_account_id,
                organization_id=param.organization_id,
            )
        )

        integrity_operations: list[CRMIntegrityOperation] = []

        for association in src_contact_email_account_associations:
            (
                deleted_association,
                inserted_association,
            ) = await self._move_contact_email_to_account(
                integrity_job_id=param.integrity_job_id,
                contact_email_id=association.contact_email_id,
                src_account_id=param.src_account_id,
                dest_account_id=param.dest_account_id,
                user_id=param.user_id,
                organization_id=param.organization_id,
            )

            integrity_operations.append(deleted_association)
            if inserted_association:
                integrity_operations.append(inserted_association)

        return integrity_operations
