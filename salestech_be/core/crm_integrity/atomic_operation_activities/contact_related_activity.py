from temporalio import activity

from salestech_be.core.contact.service.contact_data_integrity_service import (
    get_contact_data_integrity_service,
)
from salestech_be.core.contact.service.contact_service import get_contact_service
from salestech_be.core.crm_integrity.data_operation_tracker.atomic_operation_tracker_service import (
    get_atomic_operation_tracker_service,
)
from salestech_be.core.crm_integrity.types.activity_params import (
    ArchiveContactParam,
)
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.crm_integrity import (
    CRMIntegrityOperation,
)


class ContactRelatedActivities:
    def __init__(self, db_engine: DatabaseEngine) -> None:
        self.atomic_operation_tracker_service = get_atomic_operation_tracker_service(
            db_engine=db_engine
        )

        self.contact_service = get_contact_service(
            db_engine=db_engine,
        )
        self.contact_data_integrity_service = get_contact_data_integrity_service(
            db_engine=db_engine,
        )

    @activity.defn
    async def archive_contact(
        self, param: ArchiveContactParam
    ) -> CRMIntegrityOperation:
        await self.contact_service.archive_contact(
            contact_id=param.archive_contact_id,
            archived_by_user_id=param.user_id,
            organization_id=param.organization_id,
            exclude_locked_by_integrity_jobs=False,
        )

        return await self.atomic_operation_tracker_service.track_contact_archive(
            integrity_job_id=param.job_id,
            contact_id=param.archive_contact_id,
            user_id=param.user_id,
            organization_id=param.organization_id,
        )
