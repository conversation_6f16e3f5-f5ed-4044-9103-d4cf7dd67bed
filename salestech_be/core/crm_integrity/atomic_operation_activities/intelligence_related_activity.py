import time

from temporalio import activity

from salestech_be.core.ai.event_handlers.pipeline_intel import (
    start_meeting_intel_workflow,
)
from salestech_be.core.contact.service.contact_query_service import (
    get_contact_query_service,
)
from salestech_be.core.crm_integrity.types.activity_params import (
    PersonIntelActivityJobParams,
    PipelineIntelActivityJobParams,
)
from salestech_be.core.pipeline.service.pipeline_service import get_pipeline_service
from salestech_be.core.research_agent.research_agent_trigger import (
    ResearchAgentTriggerService,
)
from salestech_be.core.research_agent.types import ResearchTime
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.integrations.temporal.client import get_temporal_client
from salestech_be.integrations.temporal.config import ResearchTaskQueue
from salestech_be.ree_logging import get_logger
from salestech_be.temporal.workflows.research_agent.schema import (
    ResearchPersonInput,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)

logger = get_logger(__name__)


class IntelligenceRelatedActivity:
    def __init__(self, db_engine: DatabaseEngine) -> None:
        self.contact_query_service = get_contact_query_service(db_engine)
        self.pipeline_service = get_pipeline_service(db_engine=db_engine)

    @activity.defn(name="person_intel_activity_job")
    async def person_intel_activity_job(
        self,
        param: PersonIntelActivityJobParams,
    ) -> None:
        for contact_id in param.contact_ids:
            contact_email = (
                await self.contact_query_service.get_primary_email_by_contact_id(
                    organization_id=param.organization_id,
                    contact_id=contact_id,
                )
            )
            contact = await self.contact_query_service.get_contact_v2(
                organization_id=param.organization_id,
                contact_id=contact_id,
            )
            temporal_client = await get_temporal_client()
            service = ResearchAgentTriggerService(client=temporal_client)
            if not contact.linkedin_url and not contact_email:
                logger.warning(
                    f"contact:{contact_id} has no linkedin_url and contact_email, skip research"
                )
                return
            await service.trigger_person_research(
                contact_id=contact_id,
                data=ResearchPersonInput(
                    intel_person_id=None,
                    linkedin_url=contact.linkedin_url,
                    business_email=contact_email,
                    organization_id=param.organization_id,
                    contact_id=contact_id,
                    research_time=ResearchTime(
                        cdc_triggered_at=time.time_ns(),
                        intel_created_at=None,
                    ),
                ),
                task_queue=ResearchTaskQueue.RESEARCH_TASK_QUEUE_LOW,
            )

    @activity.defn(name="pipeline_intel_activity_job")
    async def pipeline_intel_activity_job(
        self,
        param: PipelineIntelActivityJobParams,
    ) -> None:
        pipelines = await self.pipeline_service.list_pipelines(
            organization_id=param.organization_id,
            only_include_pipeline_ids=set(param.pipeline_ids),
        )
        for pipeline in pipelines:
            await start_meeting_intel_workflow(
                meeting_id=param.meeting_id,
                organization_id=param.organization_id,
                pipeline_id=pipeline.id,
                account_id=pipeline.account_id,
                contact_id=param.contact_id,
                global_thread_id=param.global_thread_id,
            )
