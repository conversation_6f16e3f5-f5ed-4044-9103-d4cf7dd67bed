from temporalio import activity

from salestech_be.core.crm_integrity.data_operation_tracker.atomic_operation_tracker_service import (
    get_atomic_operation_tracker_service,
)
from salestech_be.core.crm_integrity.types.activity_params import (
    MovePipelineToAlternativeContactParam,
)
from salestech_be.core.pipeline.service.pipeline_data_integrity_service import (
    get_pipeline_data_integrity_service,
)
from salestech_be.core.pipeline.types_v2 import PipelineV2
from salestech_be.db.dbengine.core import DatabaseEngine


class MovePipelineToAlternativeContactActivity:
    def __init__(self, db_engine: DatabaseEngine) -> None:
        self.pipeline_data_integrity_service = get_pipeline_data_integrity_service(
            db_engine=db_engine
        )
        self.atomic_operation_tracker_service = get_atomic_operation_tracker_service(
            db_engine=db_engine
        )

    @activity.defn(name="move_pipeline_to_alternative_contact")
    async def move_pipeline_to_alternative_contact(
        self,
        param: MovePipelineToAlternativeContactParam,
    ) -> list[PipelineV2]:
        updated_pipelines = await self.pipeline_data_integrity_service.move_pipeline_to_alternative_contact_if_exists(
            src_account_id=param.src_account_id,
            moving_contact_id=param.moving_contact_id,
            user_id=param.user_id,
            organization_id=param.organization_id,
        )

        for pipeline in updated_pipelines:
            await self.atomic_operation_tracker_service.track_contact_pipeline_association(
                integrity_job_id=param.integrity_job_id,
                pipeline_id=pipeline.id,
                contact_id=param.moving_contact_id,
                user_id=param.user_id,
                organization_id=param.organization_id,
            )

        return updated_pipelines
