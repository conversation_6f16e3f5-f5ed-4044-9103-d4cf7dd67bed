from temporalio import activity

from salestech_be.core.crm_integrity.data_operation_tracker.atomic_operation_tracker_service import (
    get_atomic_operation_tracker_service,
)
from salestech_be.core.crm_integrity.types.activity_params import (
    MovePipelinesToNewAccountParam,
)
from salestech_be.core.pipeline.service.pipeline_data_integrity_service import (
    get_pipeline_data_integrity_service,
)
from salestech_be.core.pipeline.service_api_schema import InternalPatchPipelineRequest
from salestech_be.core.pipeline.types_v2 import PipelineV2
from salestech_be.db.dbengine.core import DatabaseEngine


class MovePipelineToNewAccountActivity:
    def __init__(self, db_engine: DatabaseEngine) -> None:
        self.pipeline_data_integrity_service = get_pipeline_data_integrity_service(
            db_engine=db_engine
        )
        self.atomic_operation_tracker_service = get_atomic_operation_tracker_service(
            db_engine=db_engine
        )

    @activity.defn(name="move_pipelines_to_new_account")
    async def move_pipelines_to_new_account(
        self,
        param: MovePipelinesToNewAccountParam,
    ) -> list[PipelineV2]:
        """
        Move all pipelines associated with src_account_id to new_account_id
        """

        current_pipelines = await self.pipeline_data_integrity_service.pipeline_query_service.list_pipelines_by_account_id(
            account_id=param.src_account_id,
            organization_id=param.organization_id,
        )

        updated_pipelines: list[PipelineV2] = []
        for pipeline in current_pipelines:
            updated_pipeline = await self.pipeline_data_integrity_service.pipeline_service.patch_pipeline(
                pipeline_id=pipeline.id,
                req=InternalPatchPipelineRequest(account_id=param.new_account_id),
                user_id=param.user_id,
                organization_id=param.organization_id,
                exclude_locked_by_integrity_jobs=False,
            )

            # Track move in two steps
            await self.atomic_operation_tracker_service.track_pipeline_account_unassociation(
                integrity_job_id=param.integrity_job_id,
                pipeline_id=pipeline.id,
                account_id=pipeline.account_id,
                user_id=param.user_id,
                organization_id=param.organization_id,
            )
            await self.atomic_operation_tracker_service.track_pipeline_account_association(
                integrity_job_id=param.integrity_job_id,
                pipeline_id=pipeline.id,
                account_id=param.new_account_id,
                user_id=param.user_id,
                organization_id=param.organization_id,
            )

            updated_pipelines.append(updated_pipeline)

        return updated_pipelines
