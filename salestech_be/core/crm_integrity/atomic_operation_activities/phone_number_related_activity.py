from temporalio import activity

from salestech_be.core.contact.service.contact_data_integrity_service import (
    get_contact_data_integrity_service,
)
from salestech_be.core.contact.service.contact_query_service import (
    get_contact_query_service,
)
from salestech_be.core.contact.service.contact_service import (
    get_contact_service,
)
from salestech_be.core.crm_integrity.data_operation_tracker.atomic_operation_tracker_service import (
    get_atomic_operation_tracker_service,
)
from salestech_be.core.crm_integrity.types.activity_params import (
    MoveContactPhoneNumbersFromContactToContactParam,
)
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.contact import (
    CreateDbContactPhoneNumberRequest,
)
from salestech_be.db.models.crm_integrity import (
    CRMIntegrityOperation,
)
from salestech_be.ree_logging import get_logger
from salestech_be.util.validation import one_row_only

logger = get_logger()


class PhoneNumberRelatedActivity:
    def __init__(self, db_engine: DatabaseEngine) -> None:
        self.db_engine = db_engine
        self.contact_service = get_contact_service(db_engine=db_engine)
        self.contact_query_service = get_contact_query_service(db_engine=db_engine)
        self.contact_data_integrity_service = get_contact_data_integrity_service(
            db_engine=db_engine
        )
        self.atomic_operation_tracker_service = get_atomic_operation_tracker_service(
            db_engine=db_engine
        )

    @activity.defn
    async def move_contact_phone_numbers_from_contact_to_contact(
        self,
        param: MoveContactPhoneNumbersFromContactToContactParam,
    ) -> list[CRMIntegrityOperation]:
        """
        Move contact phone numbers from one contact to another contact.

        Note: this method requires the moving contacts of contact phone numbers are already
        associated with both src and dest accounts. Which is specified used by Merge
        Contacts workflow.

        Note: since we havn't offically supported multiple phone numbers per contact,
        currently don't support handling phone number account associations.
        """
        contact_phone_number_dtos_to_move = await self.contact_query_service.list_contact_phone_number_dtos_by_contact_id(
            contact_id=param.src_contact_id,
            organization_id=param.organization_id,
        )

        integrity_operations = []

        for dto in contact_phone_number_dtos_to_move:
            await self.contact_service.delete_contact_phone_number(
                organization_id=param.organization_id,
                user_id=param.user_id,
                contact_id=param.src_contact_id,
                phone_number=dto.contact_phone_number.phone_number,
                exclude_locked_by_integrity_jobs=False,
            )

            integrity_operations.append(
                await self.atomic_operation_tracker_service.track_contact_phone_number_delete(
                    integrity_job_id=param.integrity_job_id,
                    contact_phone_number_id=dto.contact_phone_number.id,
                    contact_id=param.src_contact_id,
                    user_id=param.user_id,
                    organization_id=param.organization_id,
                )
            )

            upsert_result = one_row_only(
                await self.contact_service.batch_upsert_contact_phone_number(
                    organization_id=param.organization_id,
                    user_id=param.user_id,
                    contact_id=param.dest_contact_id,
                    create_contact_phone_number_requests=[
                        CreateDbContactPhoneNumberRequest(
                            phone_number=dto.contact_phone_number.phone_number,
                            is_contact_primary=False,
                        )
                    ],
                    exclude_locked_by_integrity_jobs=False,
                )
            )

            integrity_operations.append(
                await self.atomic_operation_tracker_service.track_contact_phone_number_create(
                    integrity_job_id=param.integrity_job_id,
                    contact_phone_number_id=upsert_result.upserted_contact_phone_number.id,
                    contact_id=param.dest_contact_id,
                    user_id=param.user_id,
                    organization_id=param.organization_id,
                )
            )

        return integrity_operations
