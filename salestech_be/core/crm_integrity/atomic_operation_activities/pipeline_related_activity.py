from temporalio import activity

from salestech_be.core.crm_integrity.types.activity_params import (
    CreateContactPipelineAssociationParam,
    GetAccountPipelinesParams,
    GetContactPipelinesParams,
)
from salestech_be.core.pipeline.service.pipeline_query_service import (
    get_pipeline_query_service,
)
from salestech_be.core.pipeline.service.pipeline_service import (
    get_pipeline_service,
)
from salestech_be.core.pipeline.service_api_schema import (
    ContactPipelineAssociationRequest,
    UpsertContactPipelineAssociationRequests,
    UpsertContactPipelineAssociationResult,
)
from salestech_be.core.pipeline.types_v2 import PipelineV2
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.pipeline import Pipeline


class PipelineRelatedActivity:
    def __init__(self, db_engine: DatabaseEngine) -> None:
        self.pipeline_service = get_pipeline_service(db_engine=db_engine)
        self.pipeline_query_service = get_pipeline_query_service(db_engine=db_engine)

    @activity.defn(name="get_pipelines_by_contact_id")
    async def get_pipelines_by_contact_id(
        self, param: GetContactPipelinesParams
    ) -> list[PipelineV2]:
        pipeline_identifiers = await self.pipeline_service.list_contact_pipeline_associations_by_contact_id(
            organization_id=param.organization_id,
            contact_id=param.contact_id,
        )

        return await self.pipeline_service.list_pipelines(
            organization_id=param.organization_id,
            only_include_pipeline_ids={p.pipeline_id for p in pipeline_identifiers},
        )

    @activity.defn(name="list_pipelines_by_account_id")
    async def list_pipelines_by_account_id(
        self, param: GetAccountPipelinesParams
    ) -> list[Pipeline]:
        return await self.pipeline_query_service.list_pipelines_by_account_id(
            organization_id=param.organization_id,
            account_id=param.account_id,
        )

    @activity.defn(name="create_contact_pipeline_association")
    async def create_contact_pipeline_association(
        self, param: CreateContactPipelineAssociationParam
    ) -> UpsertContactPipelineAssociationResult | None:
        """
        Create contact to pipeline association

        Note: it's an attempt to create contact to pipeline association
        if the association already exists, it will be treated as a no-op
        """
        existing_association = await self.pipeline_query_service.list_contact_pipeline_associations_by_contact_id(
            organization_id=param.organization_id,
            contact_id=param.contact_id,
            pipeline_id=param.pipeline_id,
        )

        if existing_association:
            return None

        return await self.pipeline_service.upsert_contact_pipeline_association(
            organization_id=param.organization_id,
            user_id=param.user_id,
            pipeline_id=param.pipeline_id,
            req=UpsertContactPipelineAssociationRequests(
                primary=ContactPipelineAssociationRequest(
                    contact_id=param.contact_id,
                )
            ),
        )
