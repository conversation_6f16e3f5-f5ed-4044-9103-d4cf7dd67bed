from temporalio import activity

from salestech_be.core.contact.service.contact_service import (
    get_contact_service,
)
from salestech_be.core.crm_integrity.data_operation_tracker.atomic_operation_tracker_service import (
    get_atomic_operation_tracker_service,
)
from salestech_be.core.crm_integrity.types.activity_params import (
    UnassociateContactsWithSingleAccountParam,
    UnassociateSingleContactWithAllAccountsParam,
    UnassociateSingleContactWithSingleAccountParam,
)
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.contact_account_association import ContactAccountAssociation


class UnassociateContactWithAccountActivity:
    def __init__(self, db_engine: DatabaseEngine) -> None:
        self.atomic_operation_tracker_service = get_atomic_operation_tracker_service(
            db_engine=db_engine
        )
        self.contact_service = get_contact_service(db_engine=db_engine)

    async def _archive_and_track(
        self,
        associations: list[ContactAccountAssociation],
        param: UnassociateSingleContactWithAllAccountsParam
        | UnassociateContactsWithSingleAccountParam
        | UnassociateSingleContactWithSingleAccountParam,
    ) -> None:
        for association in associations:
            await self.contact_service.archive_contact_account_association(
                organization_id=param.organization_id,
                user_id=param.user_id,
                contact_id=association.contact_id,
                account_id=association.account_id,
            )

            await self.atomic_operation_tracker_service.track_contact_account_unassociation(
                integrity_job_id=param.integrity_job_id,
                contact_id=association.contact_id,
                account_id=association.account_id,
                user_id=param.user_id,
                organization_id=param.organization_id,
            )

    @activity.defn(name="unassociate_single_contact_with_single_account")
    async def unassociate_single_contact_with_single_account(
        self,
        param: UnassociateSingleContactWithSingleAccountParam,
    ) -> int:
        existing_associations = (
            await self.contact_service.list_active_contact_account_associations(
                organization_id=param.organization_id,
                contact_id=param.contact_id,
                account_id=param.account_id,
            )
        )

        await self._archive_and_track(
            associations=existing_associations,
            param=param,
        )

        return len(existing_associations)

    @activity.defn(name="unassociate_single_contact_with_all_accounts")
    async def unassociate_single_contact_with_all_accounts(
        self,
        param: UnassociateSingleContactWithAllAccountsParam,
    ) -> int:
        existing_associations = (
            await self.contact_service.list_active_contact_account_associations(
                organization_id=param.organization_id,
                contact_id=param.contact_id,
            )
        )

        await self._archive_and_track(
            associations=existing_associations,
            param=param,
        )

        return len(existing_associations)

    @activity.defn(name="unassociate_all_contacts_with_single_account")
    async def unassociate_all_contacts_with_single_account(
        self,
        param: UnassociateContactsWithSingleAccountParam,
    ) -> int:
        existing_associations = (
            await self.contact_service.list_active_contact_associations_for_account(
                organization_id=param.organization_id,
                account_id=param.account_id,
            )
        )

        await self._archive_and_track(
            associations=existing_associations,
            param=param,
        )

        return len(existing_associations)
