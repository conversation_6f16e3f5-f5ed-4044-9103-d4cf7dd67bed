from uuid import UUID, uuid4

from salestech_be.core.crm_integrity.types.activity_params import (
    CrmIdReplacementTrackParam,
)
from salestech_be.db.dao.crm_integrity_repository import (
    CRMIntegrityAssociatedEntityOperationRepository,
)
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.crm_integrity import (
    AssociatedEntityField,
    AssociatedEntityFieldType,
    AssociatedEntityOperation,
    AssociatedEntityType,
    CRMIntegrityAssociatedEntityOperation,
)
from salestech_be.util.time import zoned_utc_now


class AssociatedEntityOperationTrackerService:
    def __init__(self, db_engine: DatabaseEngine) -> None:
        self.associated_data_operation_repo = (
            CRMIntegrityAssociatedEntityOperationRepository(engine=db_engine)
        )

    async def track_associated_entity_operation(
        self,
        *,
        integrity_job_id: UUID,
        integrity_subdomain_job_id: UUID,
        entity_operation_type: AssociatedEntityOperation,
        entity_type: AssociatedEntityType,
        entity_id: UUID,
        entity_field: AssociatedEntityField | None,
        entity_field_type: AssociatedEntityFieldType | None,
        before_value: str | None,
        after_value: str | None,
        user_id: UUID,
        organization_id: UUID,
    ) -> CRMIntegrityAssociatedEntityOperation:
        return await self.associated_data_operation_repo.insert(
            CRMIntegrityAssociatedEntityOperation(
                id=uuid4(),
                integrity_job_id=integrity_job_id,
                integrity_subdomain_job_id=integrity_subdomain_job_id,
                type=entity_operation_type,
                entity_type=entity_type,
                entity_id=entity_id,
                entity_field=entity_field,
                entity_field_type=entity_field_type,
                before_value=before_value,
                after_value=after_value,
                created_at=zoned_utc_now(),
                created_by_user_id=user_id,
                organization_id=organization_id,
            )
        )

    async def track_crm_replacement_operations(
        self,
        *,
        integrity_job_id: UUID,
        integrity_subdomain_job_id: UUID,
        user_id: UUID,
        organization_id: UUID,
        track_params: list[CrmIdReplacementTrackParam],
    ) -> None:
        for track_param in track_params:
            await self.track_associated_entity_operation(
                integrity_job_id=integrity_job_id,
                integrity_subdomain_job_id=integrity_subdomain_job_id,
                entity_operation_type=track_param.entity_operation,
                entity_type=track_param.entity_type,
                entity_id=track_param.entity_id,
                entity_field=track_param.entity_field_name,
                entity_field_type=track_param.entity_field_type,
                before_value=track_param.before_value,
                after_value=track_param.after_value,
                user_id=user_id,
                organization_id=organization_id,
            )


def get_associated_entity_operation_tracker_service(
    db_engine: DatabaseEngine,
) -> AssociatedEntityOperationTrackerService:
    return AssociatedEntityOperationTrackerService(db_engine=db_engine)
