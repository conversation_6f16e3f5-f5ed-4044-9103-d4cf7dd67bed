from uuid import UUID, uuid4

from salestech_be.db.dao.crm_integrity_repository import CRMIntegrityOperationRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.crm_integrity import (
    CRMIntegrityOperation,
    EntityType,
    IntegrityOperation,
    JobStatus,
)
from salestech_be.util.time import zoned_utc_now


class AtomicOperationTrackerService:
    def __init__(self, db_engine: DatabaseEngine) -> None:
        self.atomic_operation_repo = CRMIntegrityOperationRepository(engine=db_engine)

    async def _track_operation(
        self,
        integrity_job_id: UUID,
        operation_type: IntegrityOperation,
        src_entity_type: EntityType,
        src_entity_id: UUID,
        dest_entity_type: EntityType,
        dest_entity_id: UUID,
        user_id: UUID,
        organization_id: UUID,
    ) -> CRMIntegrityOperation:
        return await self.atomic_operation_repo.insert(
            CRMIntegrityOperation(
                id=uuid4(),
                integrity_job_id=integrity_job_id,
                type=operation_type,
                src_entity_type=src_entity_type,
                src_entity_id=src_entity_id,
                dest_entity_type=dest_entity_type,
                dest_entity_id=dest_entity_id,
                status=JobStatus.SUCCESS,
                created_at=zoned_utc_now(),
                created_by_user_id=user_id,
                organization_id=organization_id,
            )
        )

    async def track_lock_operation(
        self,
        integrity_job_id: UUID,
        entity_type: EntityType,
        entity_id: UUID,
        user_id: UUID,
        organization_id: UUID,
    ) -> CRMIntegrityOperation:
        return await self._track_operation(
            integrity_job_id=integrity_job_id,
            operation_type=IntegrityOperation.MARK_LOCK,
            src_entity_type=entity_type,
            src_entity_id=entity_id,
            dest_entity_type=entity_type,
            dest_entity_id=entity_id,
            user_id=user_id,
            organization_id=organization_id,
        )

    async def track_unlock_operation(
        self,
        integrity_job_id: UUID,
        entity_type: EntityType,
        entity_id: UUID,
        user_id: UUID,
        organization_id: UUID,
    ) -> CRMIntegrityOperation:
        return await self._track_operation(
            integrity_job_id=integrity_job_id,
            operation_type=IntegrityOperation.MARK_UNLOCK,
            src_entity_type=entity_type,
            src_entity_id=entity_id,
            dest_entity_type=entity_type,
            dest_entity_id=entity_id,
            user_id=user_id,
            organization_id=organization_id,
        )

    async def track_contact_account_association(
        self,
        integrity_job_id: UUID,
        contact_id: UUID,
        account_id: UUID,
        user_id: UUID,
        organization_id: UUID,
    ) -> CRMIntegrityOperation:
        return await self._track_operation(
            integrity_job_id=integrity_job_id,
            operation_type=IntegrityOperation.ASSOCIATE,
            src_entity_type=EntityType.CONTACT,
            src_entity_id=contact_id,
            dest_entity_type=EntityType.ACCOUNT,
            dest_entity_id=account_id,
            user_id=user_id,
            organization_id=organization_id,
        )

    async def track_contact_account_unassociation(
        self,
        integrity_job_id: UUID,
        contact_id: UUID,
        account_id: UUID,
        user_id: UUID,
        organization_id: UUID,
    ) -> CRMIntegrityOperation:
        return await self._track_operation(
            integrity_job_id=integrity_job_id,
            operation_type=IntegrityOperation.UNASSOCIATE,
            src_entity_type=EntityType.CONTACT,
            src_entity_id=contact_id,
            dest_entity_type=EntityType.ACCOUNT,
            dest_entity_id=account_id,
            user_id=user_id,
            organization_id=organization_id,
        )

    async def track_pipeline_account_unassociation(
        self,
        integrity_job_id: UUID,
        pipeline_id: UUID,
        account_id: UUID,
        user_id: UUID,
        organization_id: UUID,
    ) -> CRMIntegrityOperation:
        return await self._track_operation(
            integrity_job_id=integrity_job_id,
            operation_type=IntegrityOperation.UNASSOCIATE,
            src_entity_type=EntityType.PIPELINE,
            src_entity_id=pipeline_id,
            dest_entity_type=EntityType.ACCOUNT,
            dest_entity_id=account_id,
            user_id=user_id,
            organization_id=organization_id,
        )

    async def track_pipeline_account_association(
        self,
        integrity_job_id: UUID,
        pipeline_id: UUID,
        account_id: UUID,
        user_id: UUID,
        organization_id: UUID,
    ) -> CRMIntegrityOperation:
        return await self._track_operation(
            integrity_job_id=integrity_job_id,
            operation_type=IntegrityOperation.ASSOCIATE,
            src_entity_type=EntityType.PIPELINE,
            src_entity_id=pipeline_id,
            dest_entity_type=EntityType.ACCOUNT,
            dest_entity_id=account_id,
            user_id=user_id,
            organization_id=organization_id,
        )

    async def track_contact_pipeline_association(
        self,
        integrity_job_id: UUID,
        pipeline_id: UUID,
        contact_id: UUID,
        user_id: UUID,
        organization_id: UUID,
    ) -> CRMIntegrityOperation:
        return await self._track_operation(
            integrity_job_id=integrity_job_id,
            operation_type=IntegrityOperation.ASSOCIATE,
            src_entity_type=EntityType.CONTACT,
            src_entity_id=pipeline_id,
            dest_entity_type=EntityType.PIPELINE,
            dest_entity_id=contact_id,
            user_id=user_id,
            organization_id=organization_id,
        )

    async def track_contact_pipeline_unassociation(
        self,
        integrity_job_id: UUID,
        pipeline_id: UUID,
        contact_id: UUID,
        user_id: UUID,
        organization_id: UUID,
    ) -> CRMIntegrityOperation:
        return await self._track_operation(
            integrity_job_id=integrity_job_id,
            operation_type=IntegrityOperation.UNASSOCIATE,
            src_entity_type=EntityType.CONTACT,
            src_entity_id=pipeline_id,
            dest_entity_type=EntityType.PIPELINE,
            dest_entity_id=contact_id,
            user_id=user_id,
            organization_id=organization_id,
        )

    async def track_contact_email_move(
        self,
        integrity_job_id: UUID,
        contact_email_id: UUID,
        dest_contact_id: UUID,
        user_id: UUID,
        organization_id: UUID,
    ) -> CRMIntegrityOperation:
        return await self._track_operation(
            integrity_job_id=integrity_job_id,
            operation_type=IntegrityOperation.MARK_MOVE,
            src_entity_type=EntityType.CONTACT_EMAIL,
            src_entity_id=contact_email_id,
            dest_entity_type=EntityType.CONTACT,
            dest_entity_id=dest_contact_id,
            user_id=user_id,
            organization_id=organization_id,
        )

    async def track_contact_email_account_association_create(
        self,
        integrity_job_id: UUID,
        contact_email_id: UUID,
        account_id: UUID,
        user_id: UUID,
        organization_id: UUID,
    ) -> CRMIntegrityOperation:
        return await self._track_operation(
            integrity_job_id=integrity_job_id,
            operation_type=IntegrityOperation.MARK_MOVE,
            src_entity_type=EntityType.CONTACT_EMAIL,
            src_entity_id=contact_email_id,
            dest_entity_type=EntityType.ACCOUNT,
            dest_entity_id=account_id,
            user_id=user_id,
            organization_id=organization_id,
        )

    async def track_contact_email_account_association_delete(
        self,
        integrity_job_id: UUID,
        contact_email_id: UUID,
        account_id: UUID,
        user_id: UUID,
        organization_id: UUID,
    ) -> CRMIntegrityOperation:
        return await self._track_operation(
            integrity_job_id=integrity_job_id,
            operation_type=IntegrityOperation.REMOVE,
            src_entity_type=EntityType.CONTACT_EMAIL,
            src_entity_id=contact_email_id,
            dest_entity_type=EntityType.ACCOUNT,
            dest_entity_id=account_id,
            user_id=user_id,
            organization_id=organization_id,
        )

    async def track_contact_archive(
        self,
        integrity_job_id: UUID,
        contact_id: UUID,
        user_id: UUID,
        organization_id: UUID,
    ) -> CRMIntegrityOperation:
        return await self._track_operation(
            integrity_job_id=integrity_job_id,
            operation_type=IntegrityOperation.ARCHIVE,
            src_entity_type=EntityType.CONTACT,
            src_entity_id=contact_id,
            dest_entity_type=EntityType.CONTACT,
            dest_entity_id=contact_id,
            user_id=user_id,
            organization_id=organization_id,
        )

    async def track_account_archive(
        self,
        integrity_job_id: UUID,
        account_id: UUID,
        user_id: UUID,
        organization_id: UUID,
    ) -> CRMIntegrityOperation:
        return await self._track_operation(
            integrity_job_id=integrity_job_id,
            operation_type=IntegrityOperation.ARCHIVE,
            src_entity_type=EntityType.ACCOUNT,
            src_entity_id=account_id,
            dest_entity_type=EntityType.ACCOUNT,
            dest_entity_id=account_id,
            user_id=user_id,
            organization_id=organization_id,
        )

    async def track_contact_email_delete(
        self,
        integrity_job_id: UUID,
        contact_email_id: UUID,
        contact_id: UUID,
        user_id: UUID,
        organization_id: UUID,
    ) -> CRMIntegrityOperation:
        return await self._track_operation(
            integrity_job_id=integrity_job_id,
            operation_type=IntegrityOperation.REMOVE,
            src_entity_type=EntityType.CONTACT_EMAIL,
            src_entity_id=contact_email_id,
            dest_entity_type=EntityType.CONTACT,
            dest_entity_id=contact_id,
            user_id=user_id,
            organization_id=organization_id,
        )

    async def track_contact_email_create(
        self,
        integrity_job_id: UUID,
        contact_email_id: UUID,
        contact_id: UUID,
        user_id: UUID,
        organization_id: UUID,
    ) -> CRMIntegrityOperation:
        return await self._track_operation(
            integrity_job_id=integrity_job_id,
            operation_type=IntegrityOperation.ADD,
            src_entity_type=EntityType.CONTACT_EMAIL,
            src_entity_id=contact_email_id,
            dest_entity_type=EntityType.CONTACT,
            dest_entity_id=contact_id,
            user_id=user_id,
            organization_id=organization_id,
        )

    async def track_contact_phone_number_delete(
        self,
        integrity_job_id: UUID,
        contact_phone_number_id: UUID,
        contact_id: UUID,
        user_id: UUID,
        organization_id: UUID,
    ) -> CRMIntegrityOperation:
        return await self._track_operation(
            integrity_job_id=integrity_job_id,
            operation_type=IntegrityOperation.REMOVE,
            src_entity_type=EntityType.CONTACT_PHONE_NUMBER,
            src_entity_id=contact_phone_number_id,
            dest_entity_type=EntityType.CONTACT,
            dest_entity_id=contact_id,
            user_id=user_id,
            organization_id=organization_id,
        )

    async def track_contact_phone_number_create(
        self,
        integrity_job_id: UUID,
        contact_phone_number_id: UUID,
        contact_id: UUID,
        user_id: UUID,
        organization_id: UUID,
    ) -> CRMIntegrityOperation:
        return await self._track_operation(
            integrity_job_id=integrity_job_id,
            operation_type=IntegrityOperation.ADD,
            src_entity_type=EntityType.CONTACT_PHONE_NUMBER,
            src_entity_id=contact_phone_number_id,
            dest_entity_type=EntityType.CONTACT,
            dest_entity_id=contact_id,
            user_id=user_id,
            organization_id=organization_id,
        )


def get_atomic_operation_tracker_service(
    db_engine: DatabaseEngine,
) -> AtomicOperationTrackerService:
    return AtomicOperationTrackerService(db_engine=db_engine)
