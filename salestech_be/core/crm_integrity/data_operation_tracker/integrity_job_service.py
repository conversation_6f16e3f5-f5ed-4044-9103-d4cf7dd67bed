from uuid import UUID

from salestech_be.common.exception.exception import ResourceNotFoundError
from salestech_be.common.type.patch_request import UNSET
from salestech_be.core.crm_integrity.types.activity_params import (
    UpdateIntegrityJobErrorParam,
    UpdateIntegrityJobParam,
)
from salestech_be.db.dao.crm_integrity_repository import CRMIntegrityJobRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.crm_integrity import (
    CRMIntegrityJob,
    CRMIntegrityJobContextualParam,
    CRMIntegrityJobUpdate,
    CRMIntegrityJobUpdateCondition,
    CRMIntegrityJobUserOption,
    CRMIntegritySubDomainJob,
    CRMSubDomain,
    EntityType,
    IntegrityJobType,
    JobErrorDetails,
    JobStatus,
)
from salestech_be.util.time import zoned_utc_now


class IntegrityJobService:
    def __init__(self, db_engine: DatabaseEngine) -> None:
        self.integrity_job_repo = CRMIntegrityJobRepository(engine=db_engine)

    async def create_integrity_job(
        self,
        *,
        user_id: UUID,
        organization_id: UUID,
        job_type: IntegrityJobType,
        src_entity_type: EntityType,
        src_entity_id: UUID,
        dest_entity_type: EntityType | None = None,
        dest_entity_id: UUID | None = None,
        contextual_param: CRMIntegrityJobContextualParam | None = None,
        user_choices: dict[CRMSubDomain, CRMIntegrityJobUserOption | None],
    ) -> tuple[CRMIntegrityJob, list[CRMIntegritySubDomainJob]]:
        return await self.integrity_job_repo.create_integrity_job(
            job_type=job_type,
            src_entity_type=src_entity_type,
            src_entity_id=src_entity_id,
            dest_entity_type=dest_entity_type,
            dest_entity_id=dest_entity_id,
            user_id=user_id,
            organization_id=organization_id,
            contextual_param=contextual_param,
            user_choices=user_choices,
        )

    async def update_integrity_job(
        self, param: UpdateIntegrityJobParam
    ) -> CRMIntegrityJob:
        match param.status:
            case JobStatus.RUNNING:
                columns_to_update = CRMIntegrityJobUpdate(
                    status=param.status,
                    updated_by_user_id=param.user_id,
                    started_at=zoned_utc_now(),
                    ended_at=None,
                )
            case JobStatus.FAIL | JobStatus.SUCCESS:
                columns_to_update = CRMIntegrityJobUpdate(
                    status=param.status,
                    updated_by_user_id=param.user_id,
                    ended_at=zoned_utc_now(),
                )
            case JobStatus.QUEUED:
                columns_to_update = CRMIntegrityJobUpdate(
                    status=param.status,
                    updated_by_user_id=param.user_id,
                    retry_count=param.retry_count if param.retry_count else UNSET,
                )
            case _:
                columns_to_update = CRMIntegrityJobUpdate(
                    status=param.status,
                    updated_by_user_id=param.user_id,
                )

        updated_integrity_job = (
            await self.integrity_job_repo.update_by_tenanted_primary_key(
                table_model=CRMIntegrityJob,
                organization_id=param.organization_id,
                primary_key_to_value={"id": param.job_id},
                column_to_update=columns_to_update,
            )
        )
        if updated_integrity_job is None:
            raise ResourceNotFoundError(
                f"Integrity job with id {param.job_id} not found"
            )
        return updated_integrity_job

    async def update_integrity_job_error(
        self, param: UpdateIntegrityJobErrorParam
    ) -> CRMIntegrityJob:
        updated_integrity_job = (
            await self.integrity_job_repo.update_by_tenanted_primary_key(
                table_model=CRMIntegrityJob,
                organization_id=param.organization_id,
                primary_key_to_value={"id": param.job_id},
                column_to_update=CRMIntegrityJobUpdate(
                    error_details=JobErrorDetails(
                        namespace=param.namespace,
                        message=param.message,
                        workflow_id=param.workflow_id,
                        workflow_type=param.workflow_type,
                        workflow_run_id=param.workflow_run_id,
                    ),
                    updated_by_user_id=param.user_id,
                ),
                column_condition=CRMIntegrityJobUpdateCondition(
                    status=JobStatus.FAIL,
                ),
            )
        )

        if updated_integrity_job is None:
            raise ResourceNotFoundError(
                f"Integrity job with id {param.job_id} not found"
            )
        return updated_integrity_job
