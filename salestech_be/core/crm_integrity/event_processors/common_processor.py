from uuid import UUID

from salestech_be.core.activity.service.activity_data_integrity_service import (
    ActivityDataIntegrityService,
)
from salestech_be.core.crm_integrity.types.activity_params import (
    CrmIdReplacementTrackParam,
    DataReplacementParam,
)
from salestech_be.db.models.activity import ActivitySubReference, ActivityType
from salestech_be.db.models.crm_integrity import (
    AssociatedEntityOperation,
    AssociatedEntityType,
)


class CommonEventProcessor:
    def __init__(
        self,
        activity_data_integrity_service: ActivityDataIntegrityService,
    ):
        self.activity_data_integrity_service = activity_data_integrity_service

    async def fetch_affected_activity_sub_references(
        self,
        contact_id: UUID,
        organization_id: UUID,
        activity_types: list[ActivityType],
    ) -> list[ActivitySubReference]:
        return await self.activity_data_integrity_service.find_activity_sub_references_by_contact_id_and_activity_type(
            contact_id=contact_id,
            organization_id=organization_id,
            activity_types=activity_types,
        )

    async def process_activity_sub_reference_records(
        self, param: DataReplacementParam, sub_references: list[ActivitySubReference]
    ) -> list[CrmIdReplacementTrackParam]:
        track_params: list[CrmIdReplacementTrackParam] = []
        for sub_reference in sub_references:
            updated_sub_reference = await self.activity_data_integrity_service.replace_contact_id_in_activity_sub_reference(
                activity_sub_reference_id=sub_reference.id,
                organization_id=param.organization_id,
                contact_id=param.primary_entity_id,
                replaced_contact_id=param.replaced_entity_id,
                user_id=param.user_id,
            )
            if not updated_sub_reference:
                continue

            track_params.append(
                CrmIdReplacementTrackParam(
                    entity_type=AssociatedEntityType.ACTIVITY_SUB_REFERENCE,
                    entity_id=sub_reference.id,
                    entity_field_name=None,
                    entity_field_type=None,
                    entity_operation=AssociatedEntityOperation.DELETE,
                    before_value=None,
                    after_value=None,
                )
            )

            track_params.append(
                CrmIdReplacementTrackParam(
                    entity_type=AssociatedEntityType.ACTIVITY_SUB_REFERENCE,
                    entity_id=updated_sub_reference.id,
                    entity_field_name=None,
                    entity_field_type=None,
                    entity_operation=AssociatedEntityOperation.CREATE,
                    before_value=None,
                    after_value=None,
                )
            )
        return track_params
