from temporalio import activity

from salestech_be.core.crm_integrity.data_operation_tracker.associated_entity_operation_tracker_service import (
    get_associated_entity_operation_tracker_service,
)
from salestech_be.core.crm_integrity.event_processors.processor_factory import (
    ProcessorFactory,
)
from salestech_be.core.crm_integrity.types.activity_params import (
    CrmIdReplacementTrackParam,
    DataOperationParam,
)
from salestech_be.db.dao.crm_integrity_repository import (
    CRMIntegritySubDomainJobRepository,
)
from salestech_be.db.models.crm_integrity import (
    CRMIntegrityJobUserOption,
    CRMSubDomain,
    IntegrityJobName,
    SubDomainJobStatus,
)
from salestech_be.ree_logging import get_logger
from salestech_be.temporal.database import (
    get_or_init_db_engine,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)

logger = get_logger(__name__)


@activity.defn(name="data_operation_event_processor_activity")
async def data_operation_event_processor_activity(
    job_name: IntegrityJobName,
    subdomain: CRMSubDomain,
    param: DataOperationParam,
    user_choice: CRMIntegrityJobUserOption | None = None,
) -> None:
    db_engine = await get_or_init_db_engine()
    processor = ProcessorFactory.create(job_name, subdomain, db_engine)
    associated_entity_operation_tracker_service = (
        get_associated_entity_operation_tracker_service(db_engine=db_engine)
    )

    sub_domain_job_repo = CRMIntegritySubDomainJobRepository(engine=db_engine)

    sub_domain_job = (
        await sub_domain_job_repo.find_sub_domain_job_by_integrity_job_id_and_subdomain(
            integrity_job_id=param.integrity_job_id,
            subdomain=subdomain,
            organization_id=param.organization_id,
        )
    )

    if not sub_domain_job:
        raise ValueError("sub domain job not found")

    await sub_domain_job_repo.update_sub_domain_job_status(
        sub_domain_job_id=sub_domain_job.id,
        status=SubDomainJobStatus.RUNNING,
    )

    try:
        associated_entity_operation_tracker_params = await processor.process(
            param=param,
            user_choice=user_choice,
        )

        if associated_entity_operation_tracker_params and isinstance(
            associated_entity_operation_tracker_params[0],
            CrmIdReplacementTrackParam,
        ):
            await associated_entity_operation_tracker_service.track_crm_replacement_operations(
                integrity_job_id=param.integrity_job_id,
                integrity_subdomain_job_id=sub_domain_job.id,
                user_id=param.user_id,
                organization_id=param.organization_id,
                track_params=associated_entity_operation_tracker_params,
            )

        await sub_domain_job_repo.update_sub_domain_job_status(
            sub_domain_job_id=sub_domain_job.id,
            status=SubDomainJobStatus.SUCCESS,
        )

    except Exception:
        await sub_domain_job_repo.update_sub_domain_job_status(
            sub_domain_job_id=sub_domain_job.id,
            status=SubDomainJobStatus.FAIL,
        )
        raise
