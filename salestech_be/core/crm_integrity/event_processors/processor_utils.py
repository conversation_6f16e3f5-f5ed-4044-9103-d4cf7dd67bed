import json
from typing import Any, TypeVar

from salestech_be.core.email.type.email import EmailHydratedParticipant
from salestech_be.db.models.meeting import (
    MeetingAttendee,
    MeetingInvitee,
)

T = TypeVar("T", MeetingInvitee, MeetingAttendee, EmailHydratedParticipant)


class ProcessorUtils:
    @staticmethod
    def stringify_json_list_to_string(json_list: list[Any] | None) -> str | None:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        if json_list is None:
            return None

        serialized_items = []
        for item in json_list:
            if hasattr(item, "model_dump"):
                # Pydantic models
                serialized_items.append(item.model_dump(mode="json"))
            elif hasattr(item, "dict"):
                # dataclasses or objects with dict() method
                serialized_items.append(item.dict())
            elif hasattr(item, "__dict__"):
                # plain Python objects
                serialized_items.append(item.__dict__)
            else:
                # primitive types or objects that can be JSON serialized directly
                serialized_items.append(item)

        return json.dumps(serialized_items)

    @staticmethod
    def deduplicate_participants(
        participants: list[T],
        unique_fields: list[str] | None = None,
    ) -> list[T]:
        """
        Deduplicate arbitrary list of objects based on all fields

        Returns:
            List of deduplicated objects, keeping the first occurrence of each unique combination
        """
        if not participants:
            return participants

        if unique_fields and any(
            field not in participants[0].model_fields for field in unique_fields
        ):
            raise ValueError(f"Invalid unique fields: {unique_fields}")

        unique_fields = unique_fields or list(participants[0].model_fields)

        seen = set()
        result = []

        for participant in participants:
            key_values = tuple(getattr(participant, field) for field in unique_fields)

            if key_values in seen:
                continue

            seen.add(key_values)
            result.append(participant)

        return result
