from uuid import UUID

from salestech_be.core.crm_integrity.integrity_operation_preview.add_account_to_pipeline_preview import (
    AddAccountToPipelinePreviewService,
)
from salestech_be.core.crm_integrity.integrity_operation_preview.add_contact_to_pipeline_preview import (
    AddContactToPipelinePreviewService,
)
from salestech_be.core.crm_integrity.integrity_operation_preview.archive_account_preview import (
    ArchiveAccountPreviewService,
)
from salestech_be.core.crm_integrity.integrity_operation_preview.archive_contact_preview import (
    ArchiveContactPreviewService,
)
from salestech_be.core.crm_integrity.integrity_operation_preview.merge_accounts_preview import (
    MergeAccountsPreviewService,
)
from salestech_be.core.crm_integrity.integrity_operation_preview.merge_contacts_preview import (
    MergeContactsPreviewService,
)
from salestech_be.core.crm_integrity.integrity_operation_preview.move_contact_email_to_account_preview import (
    MoveContactEmailToAccountPreviewService,
)
from salestech_be.core.crm_integrity.integrity_operation_preview.move_contact_email_to_contact_preview import (
    MoveContactEmailToContactPreviewService,
)
from salestech_be.core.crm_integrity.integrity_operation_preview.move_contact_to_account_preview import (
    MoveContactToAccountPreviewService,
)
from salestech_be.core.crm_integrity.integrity_operation_preview.remove_contact_email_from_account_preview import (
    RemoveContactEmailFromAccountPreviewService,
)
from salestech_be.core.crm_integrity.integrity_operation_preview.remove_contact_email_from_contact_preview import (
    RemoveContactEmailFromContactPreviewService,
)
from salestech_be.core.crm_integrity.integrity_operation_preview.remove_contact_from_account_preview import (
    RemoveContactFromAccountPreviewService,
)
from salestech_be.core.crm_integrity.types.request_schema import (
    IntegrityJobPreviewRequest,
    IntegrityJobPreviewResponse,
)
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.crm_integrity import (
    IntegrityJobName,
    MoveContactEmailToAccountContextualParam,
    MoveContactEmailToContactContextualParam,
    MoveContactToAccountContextualParam,
    RemoveContactEmailFromContactContextualParam,
    get_integrity_job_name,
)
from salestech_be.util.validation import cast_or_error, not_none


class IntegrityJobPreviewService:
    def __init__(self, database_engine: DatabaseEngine) -> None:
        self.merge_accounts_preview = MergeAccountsPreviewService(
            database_engine=database_engine
        )
        self.merge_contacts_preview = MergeContactsPreviewService(
            database_engine=database_engine
        )
        self.move_contact_to_account_preview = MoveContactToAccountPreviewService(
            database_engine=database_engine
        )
        self.remove_contact_from_account_preview = (
            RemoveContactFromAccountPreviewService(database_engine=database_engine)
        )
        self.archive_contact_preview = ArchiveContactPreviewService(
            database_engine=database_engine
        )
        self.archive_account_preview = ArchiveAccountPreviewService(
            database_engine=database_engine
        )
        self.remove_contact_email_from_contact_preview = (
            RemoveContactEmailFromContactPreviewService(database_engine=database_engine)
        )
        self.move_contact_email_to_account_preview = (
            MoveContactEmailToAccountPreviewService(database_engine=database_engine)
        )
        self.remove_contact_email_from_account_preview = (
            RemoveContactEmailFromAccountPreviewService(database_engine=database_engine)
        )
        self.move_contact_email_to_contact_preview = (
            MoveContactEmailToContactPreviewService(database_engine=database_engine)
        )
        self.add_account_to_pipeline_preview = AddAccountToPipelinePreviewService(
            database_engine=database_engine
        )
        self.add_contact_to_pipeline_preview = AddContactToPipelinePreviewService(
            database_engine=database_engine
        )

    async def preview_crm_integrity_job(  # noqa: C901, PLR0912
        self,
        integrity_job_preview_request: IntegrityJobPreviewRequest,
        user_id: UUID,
        organization_id: UUID,
    ) -> IntegrityJobPreviewResponse:
        integrity_job_name = get_integrity_job_name(
            integrity_job_type=integrity_job_preview_request.job_type,
            src_entity_type=integrity_job_preview_request.src_entity_type,
            dest_entity_type=integrity_job_preview_request.dest_entity_type,
        )

        preview_response: IntegrityJobPreviewResponse

        match integrity_job_name:
            case IntegrityJobName.MERGE_CONTACTS:
                preview_response = (
                    await self.merge_contacts_preview.preview_merge_contacts_job(
                        integrity_job_name=integrity_job_name,
                        src_contact_id=integrity_job_preview_request.src_entity_id,
                        dest_contact_id=not_none(
                            integrity_job_preview_request.dest_entity_id
                        ),
                        user_id=user_id,
                        organization_id=organization_id,
                    )
                )
            case IntegrityJobName.MERGE_ACCOUNTS:
                preview_response = (
                    await self.merge_accounts_preview.preview_merge_accounts_job(
                        integrity_job_name=integrity_job_name,
                        src_account_id=integrity_job_preview_request.src_entity_id,
                        dest_account_id=not_none(
                            integrity_job_preview_request.dest_entity_id
                        ),
                        user_id=user_id,
                        organization_id=organization_id,
                    )
                )
            case IntegrityJobName.MOVE_CONTACT_TO_ACCOUNT:
                preview_response = (
                    await self.move_contact_to_account_preview.preview_integrity_job(
                        integrity_job_name=integrity_job_name,
                        contact_id=integrity_job_preview_request.src_entity_id,
                        src_account_id=cast_or_error(
                            integrity_job_preview_request.contextual_param,
                            MoveContactToAccountContextualParam,
                        ).src_account_id,
                        dest_account_id=not_none(
                            integrity_job_preview_request.dest_entity_id
                        ),
                        user_id=user_id,
                        organization_id=organization_id,
                        user_choices=integrity_job_preview_request.user_choices,
                    )
                )
            case IntegrityJobName.REMOVE_CONTACT_FROM_ACCOUNT:
                preview_response = await self.remove_contact_from_account_preview.preview_integrity_job(
                    integrity_job_name=integrity_job_name,
                    contact_id=integrity_job_preview_request.src_entity_id,
                    account_id=not_none(integrity_job_preview_request.dest_entity_id),
                    user_id=user_id,
                    organization_id=organization_id,
                )
            case IntegrityJobName.ARCHIVE_CONTACT:
                preview_response = (
                    await self.archive_contact_preview.preview_integrity_job(
                        integrity_job_name=integrity_job_name,
                        archive_contact_id=integrity_job_preview_request.src_entity_id,
                        user_id=user_id,
                        organization_id=organization_id,
                    )
                )
            case IntegrityJobName.ARCHIVE_ACCOUNT:
                preview_response = (
                    await self.archive_account_preview.preview_integrity_job(
                        integrity_job_name=integrity_job_name,
                        archive_account_id=integrity_job_preview_request.src_entity_id,
                        user_id=user_id,
                        organization_id=organization_id,
                    )
                )
            case IntegrityJobName.MOVE_CONTACT_EMAIL_TO_CONTACT:
                preview_response = await self.move_contact_email_to_contact_preview.preview_integrity_job(
                    integrity_job_name=integrity_job_name,
                    move_contact_email_id=integrity_job_preview_request.src_entity_id,
                    src_contact_id=cast_or_error(
                        integrity_job_preview_request.contextual_param,
                        MoveContactEmailToContactContextualParam,
                    ).src_contact_id,
                    dest_contact_id=not_none(
                        integrity_job_preview_request.dest_entity_id
                    ),
                    remove_contact_account_association_if_last=cast_or_error(
                        integrity_job_preview_request.contextual_param,
                        MoveContactEmailToContactContextualParam,
                    ).remove_contact_account_association_if_last,
                    create_contact_account_association_if_missing=cast_or_error(
                        integrity_job_preview_request.contextual_param,
                        MoveContactEmailToContactContextualParam,
                    ).create_contact_account_association_if_missing,
                    user_id=user_id,
                    organization_id=organization_id,
                )

            case IntegrityJobName.REMOVE_CONTACT_EMAIL_FROM_CONTACT:
                preview_response = await self.remove_contact_email_from_contact_preview.preview_integrity_job(
                    integrity_job_name=integrity_job_name,
                    remove_contact_email_id=integrity_job_preview_request.src_entity_id,
                    contact_id=not_none(integrity_job_preview_request.dest_entity_id),
                    user_id=user_id,
                    organization_id=organization_id,
                    remove_contact_account_association_if_last=cast_or_error(
                        integrity_job_preview_request.contextual_param,
                        RemoveContactEmailFromContactContextualParam,
                    ).remove_contact_account_association_if_last,
                )
            case IntegrityJobName.MOVE_CONTACT_EMAIL_TO_ACCOUNT:
                preview_response = await self.move_contact_email_to_account_preview.preview_integrity_job(
                    integrity_job_name=integrity_job_name,
                    move_contact_email_id=integrity_job_preview_request.src_entity_id,
                    src_account_id=cast_or_error(
                        integrity_job_preview_request.contextual_param,
                        MoveContactEmailToAccountContextualParam,
                    ).src_account_id,
                    dest_account_id=not_none(
                        integrity_job_preview_request.dest_entity_id
                    ),
                    user_id=user_id,
                    organization_id=organization_id,
                )
            case IntegrityJobName.REMOVE_CONTACT_EMAIL_FROM_ACCOUNT:
                preview_response = await self.remove_contact_email_from_account_preview.preview_integrity_job(
                    integrity_job_name=integrity_job_name,
                    remove_contact_email_id=integrity_job_preview_request.src_entity_id,
                    account_id=not_none(integrity_job_preview_request.dest_entity_id),
                    user_id=user_id,
                    organization_id=organization_id,
                )
            case IntegrityJobName.ADD_ACCOUNT_TO_PIPELINE:
                preview_response = (
                    await self.add_account_to_pipeline_preview.preview_integrity_job(
                        integrity_job_name=integrity_job_name,
                        account_id=integrity_job_preview_request.src_entity_id,
                        pipeline_id=not_none(
                            integrity_job_preview_request.dest_entity_id
                        ),
                        user_id=user_id,
                        organization_id=organization_id,
                    )
                )
            case IntegrityJobName.ADD_CONTACT_TO_PIPELINE:
                preview_response = (
                    await self.add_contact_to_pipeline_preview.preview_integrity_job(
                        integrity_job_name=integrity_job_name,
                        contact_id=integrity_job_preview_request.src_entity_id,
                        pipeline_id=not_none(
                            integrity_job_preview_request.dest_entity_id
                        ),
                        user_id=user_id,
                        organization_id=organization_id,
                    )
                )
            case _:
                raise NotImplementedError(
                    f"Preview for {integrity_job_name} integrity job name is not implemented"
                )

        return preview_response
