import asyncio
from uuid import UUID, uuid4

from salestech_be.core.contact.service.contact_data_integrity_service import (
    get_contact_data_integrity_service,
)
from salestech_be.core.contact.service.contact_service import (
    get_contact_service,
)
from salestech_be.core.crm_integrity.event_processors.processor_factory import (
    ProcessorFactory,
)
from salestech_be.core.crm_integrity.types.activity_params import (
    DataReplacementParam,
)
from salestech_be.core.crm_integrity.types.default_user_options_subdomains import (
    get_affected_sub_domains_by_integrity_job_name,
)
from salestech_be.core.crm_integrity.types.request_schema import (
    IntegrityAssociatedDataOperationCommonPreviewIdentifier,
    IntegrityAssociatedDataOperationPreviewIdentifiers,
    IntegrityJobName,
    IntegrityJobPreviewResponse,
    IntegrityOperationBase,
)
from salestech_be.core.pipeline.service.pipeline_data_integrity_service import (
    get_pipeline_data_integrity_service,
)
from salestech_be.core.pipeline.service.pipeline_query_service import (
    get_pipeline_query_service,
)
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.crm_integrity import (
    EntityType,
)
from salestech_be.db.models.crm_integrity import (
    IntegrityOperation as IntegrityOperationType,
)
from salestech_be.util.validation import cast_or_error


class MergeContactsPreviewService:
    def __init__(self, database_engine: DatabaseEngine) -> None:
        self.database_engine = database_engine
        self.pipeline_query_service = get_pipeline_query_service(
            db_engine=database_engine
        )
        self.contact_service = get_contact_service(
            db_engine=database_engine,
        )
        self.contact_data_integrity_service = get_contact_data_integrity_service(
            db_engine=database_engine
        )
        self.pipeline_data_integrity_service = get_pipeline_data_integrity_service(
            db_engine=database_engine
        )

    async def preview_merge_contacts_job(
        self,
        *,
        integrity_job_name: IntegrityJobName,
        src_contact_id: UUID,
        dest_contact_id: UUID,
        user_id: UUID,
        organization_id: UUID,
    ) -> IntegrityJobPreviewResponse:
        contact_account_associations_preview_result = await self.contact_data_integrity_service.preview_contact_account_associations_for_merging_contacts(
            src_contact_id=src_contact_id,
            dest_contact_id=dest_contact_id,
            organization_id=organization_id,
        )

        contact_pipeline_associations_preview_result = await self.pipeline_data_integrity_service.preview_contact_pipeline_associations_for_merging_contacts(
            src_contact_id=src_contact_id,
            dest_contact_id=dest_contact_id,
            organization_id=organization_id,
        )

        preview_tasks = []
        associated_data_changes_identifiers = []

        preview_param = DataReplacementParam(
            integrity_job_id=uuid4(),
            replaced_entity_type=EntityType.CONTACT,
            primary_entity_id=src_contact_id,
            replaced_entity_id=dest_contact_id,
            user_id=user_id,
            organization_id=organization_id,
        )

        for subdomain in get_affected_sub_domains_by_integrity_job_name(
            integrity_job_name=integrity_job_name,
        ):
            processor = ProcessorFactory.create(
                job_name=integrity_job_name,
                subdomain=subdomain,
                db_engine=self.database_engine,
            )

            preview_tasks.append(processor.preview(param=preview_param))

        preview_results = await asyncio.gather(*preview_tasks)

        associated_data_changes_identifiers = [
            identifiers
            for preview_result in preview_results
            for identifiers in preview_result
            if cast_or_error(
                identifiers, IntegrityAssociatedDataOperationPreviewIdentifiers
            ).entity_ids
        ]

        return IntegrityJobPreviewResponse(
            src_entity_id=src_contact_id,
            dest_entity_id=dest_contact_id,
            associate_contact_to_account=[
                IntegrityOperationBase(
                    type=IntegrityOperationType.ASSOCIATE,
                    src_entity_type=EntityType.CONTACT,
                    src_entity_id=association.contact_id,
                    dest_entity_type=EntityType.ACCOUNT,
                    dest_entity_id=association.account_id,
                )
                for association in contact_account_associations_preview_result.to_create
            ],
            unassociate_contact_from_account=[
                IntegrityOperationBase(
                    type=IntegrityOperationType.UNASSOCIATE,
                    src_entity_type=EntityType.CONTACT,
                    src_entity_id=association.contact_id,
                    dest_entity_type=EntityType.ACCOUNT,
                    dest_entity_id=association.account_id,
                )
                for association in contact_account_associations_preview_result.to_archive
            ],
            associate_contact_to_pipeline=[
                IntegrityOperationBase(
                    type=IntegrityOperationType.ASSOCIATE,
                    src_entity_type=EntityType.CONTACT,
                    src_entity_id=association.contact_id,
                    dest_entity_type=EntityType.PIPELINE,
                    dest_entity_id=association.pipeline_id,
                )
                for association in contact_pipeline_associations_preview_result.to_create
            ],
            unassociate_contact_from_pipeline=[
                IntegrityOperationBase(
                    type=IntegrityOperationType.UNASSOCIATE,
                    src_entity_type=EntityType.CONTACT,
                    src_entity_id=association.contact_id,
                    dest_entity_type=EntityType.PIPELINE,
                    dest_entity_id=association.pipeline_id,
                )
                for association in contact_pipeline_associations_preview_result.to_archive
            ],
            associated_data_changes_identifiers=associated_data_changes_identifiers,
            affected_intel_pipeline_identifiers=[
                IntegrityAssociatedDataOperationCommonPreviewIdentifier(
                    id=association.pipeline_id,
                )
                for association in contact_pipeline_associations_preview_result.to_create
            ],
            affected_intel_contact_identifiers=[
                IntegrityAssociatedDataOperationCommonPreviewIdentifier(
                    id=dest_contact_id,
                )
            ],
        )
