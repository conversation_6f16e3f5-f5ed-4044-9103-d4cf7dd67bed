import asyncio
from uuid import UUID, uuid4

from salestech_be.core.crm_integrity.event_processors.processor_factory import (
    ProcessorFactory,
)
from salestech_be.core.crm_integrity.types.activity_params import (
    DataMovementParam,
)
from salestech_be.core.crm_integrity.types.default_user_options_subdomains import (
    get_affected_sub_domains_by_integrity_job_name,
)
from salestech_be.core.crm_integrity.types.request_schema import (
    IntegrityAssociatedDataOperationPreviewIdentifiers,
    IntegrityJobName,
    IntegrityJobPreviewResponse,
    IntegrityOperationBase,
)
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.crm_integrity import (
    EntityType,
)
from salestech_be.db.models.crm_integrity import (
    IntegrityOperation as IntegrityOperationType,
)
from salestech_be.util.validation import cast_or_error


class MoveContactEmailToAccountPreviewService:
    def __init__(self, database_engine: DatabaseEngine) -> None:
        self.database_engine = database_engine

    async def preview_integrity_job(
        self,
        *,
        integrity_job_name: IntegrityJobName,
        move_contact_email_id: UUID,
        src_account_id: UUID,
        dest_account_id: UUID,
        user_id: UUID,
        organization_id: UUID,
    ) -> IntegrityJobPreviewResponse:
        associated_data_changes_identifiers = []
        preview_tasks = []

        preview_param = DataMovementParam(
            integrity_job_id=uuid4(),
            move_entity_id=move_contact_email_id,
            src_entity_id=src_account_id,
            dest_entity_id=dest_account_id,
            user_id=user_id,
            organization_id=organization_id,
        )

        for subdomain in get_affected_sub_domains_by_integrity_job_name(
            integrity_job_name=integrity_job_name,
        ):
            processor = ProcessorFactory.create(
                job_name=integrity_job_name,
                subdomain=subdomain,
                db_engine=self.database_engine,
            )

            preview_tasks.append(processor.preview(param=preview_param))

        preview_results = await asyncio.gather(*preview_tasks)

        associated_data_changes_identifiers = [
            identifiers
            for preview_result in preview_results
            for identifiers in preview_result
            if cast_or_error(
                identifiers, IntegrityAssociatedDataOperationPreviewIdentifiers
            ).entity_ids
        ]

        return IntegrityJobPreviewResponse(
            src_entity_id=move_contact_email_id,
            dest_entity_id=dest_account_id,
            add_contact_email_to_account=[
                IntegrityOperationBase(
                    type=IntegrityOperationType.ADD,
                    src_entity_type=EntityType.CONTACT_EMAIL,
                    src_entity_id=move_contact_email_id,
                    dest_entity_type=EntityType.ACCOUNT,
                    dest_entity_id=dest_account_id,
                )
            ],
            remove_contact_email_from_account=[
                IntegrityOperationBase(
                    type=IntegrityOperationType.REMOVE,
                    src_entity_type=EntityType.CONTACT_EMAIL,
                    src_entity_id=move_contact_email_id,
                    dest_entity_type=EntityType.ACCOUNT,
                    dest_entity_id=src_account_id,
                )
            ],
            associated_data_changes_identifiers=associated_data_changes_identifiers,
        )
