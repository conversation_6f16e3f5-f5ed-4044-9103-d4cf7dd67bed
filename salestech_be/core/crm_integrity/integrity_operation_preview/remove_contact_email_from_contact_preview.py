import asyncio
from uuid import UUID, uuid4

from salestech_be.core.contact.service.contact_data_integrity_service import (
    get_contact_data_integrity_service,
)
from salestech_be.core.contact.service.contact_service import (
    get_contact_service,
)
from salestech_be.core.crm_integrity.event_processors.processor_factory import (
    ProcessorFactory,
)
from salestech_be.core.crm_integrity.types.activity_params import (
    DataRemovalParam,
)
from salestech_be.core.crm_integrity.types.default_user_options_subdomains import (
    get_affected_sub_domains_by_integrity_job_name,
)
from salestech_be.core.crm_integrity.types.request_schema import (
    IntegrityAssociatedDataOperationCommonPreviewIdentifier,
    IntegrityAssociatedDataOperationPreviewIdentifiers,
    IntegrityJobName,
    IntegrityJobPreviewResponse,
    IntegrityOperationBase,
)
from salestech_be.core.pipeline.service.pipeline_service import get_pipeline_service
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.crm_integrity import (
    EntityType,
)
from salestech_be.db.models.crm_integrity import (
    IntegrityOperation as IntegrityOperationType,
)
from salestech_be.util.validation import cast_or_error


class RemoveContactEmailFromContactPreviewService:
    def __init__(self, database_engine: DatabaseEngine) -> None:
        self.database_engine = database_engine
        self.contact_service = get_contact_service(db_engine=self.database_engine)
        self.contact_data_integrity_service = get_contact_data_integrity_service(
            db_engine=self.database_engine
        )
        self.pipeline_service = get_pipeline_service(db_engine=self.database_engine)

    async def preview_integrity_job(
        self,
        *,
        integrity_job_name: IntegrityJobName,
        remove_contact_email_id: UUID,
        contact_id: UUID,
        remove_contact_account_association_if_last: bool,
        user_id: UUID,
        organization_id: UUID,
    ) -> IntegrityJobPreviewResponse:
        preview_tasks = []
        associated_data_changes_identifiers = []

        preview_param = DataRemovalParam(
            integrity_job_id=uuid4(),
            remove_entity_id=remove_contact_email_id,
            src_entity_id=contact_id,
            user_id=user_id,
            organization_id=organization_id,
        )
        for subdomain in get_affected_sub_domains_by_integrity_job_name(
            integrity_job_name=integrity_job_name,
        ):
            processor = ProcessorFactory.create(
                job_name=integrity_job_name,
                subdomain=subdomain,
                db_engine=self.database_engine,
            )

            preview_tasks.append(processor.preview(param=preview_param))

        preview_results = await asyncio.gather(*preview_tasks)

        associated_data_changes_identifiers = [
            identifiers
            for preview_result in preview_results
            for identifiers in preview_result
            if cast_or_error(
                identifiers, IntegrityAssociatedDataOperationPreviewIdentifiers
            ).entity_ids
        ]

        unassociate_contact_from_account: list[IntegrityOperationBase] = []
        remove_contact_email_from_account: list[IntegrityOperationBase] = []

        if remove_contact_account_association_if_last:
            contact_account_associations = await self.contact_data_integrity_service.preview_solo_contact_email_account_associations(
                contact_id=contact_id,
                contact_email_id=remove_contact_email_id,
                organization_id=organization_id,
            )
            for association in contact_account_associations:
                unassociate_contact_from_account.append(
                    IntegrityOperationBase(
                        type=IntegrityOperationType.UNASSOCIATE,
                        src_entity_type=EntityType.CONTACT,
                        src_entity_id=association.contact_id,
                        dest_entity_type=EntityType.ACCOUNT,
                        dest_entity_id=association.account_id,
                    )
                )

        contact_email_account_associations = await self.contact_service.list_contact_email_account_associations_by_contact_email_id(
            contact_email_id=remove_contact_email_id,
            organization_id=organization_id,
        )

        for contact_email_account_association in contact_email_account_associations:
            remove_contact_email_from_account.append(
                IntegrityOperationBase(
                    type=IntegrityOperationType.REMOVE,
                    src_entity_type=EntityType.CONTACT_EMAIL,
                    src_entity_id=remove_contact_email_id,
                    dest_entity_type=EntityType.ACCOUNT,
                    dest_entity_id=contact_email_account_association.account_id,
                )
            )
        pipeline_identifiers = await self.pipeline_service.list_contact_pipeline_associations_by_contact_id(
            organization_id=organization_id,
            contact_id=contact_id,
        )
        return IntegrityJobPreviewResponse(
            src_entity_id=remove_contact_email_id,
            dest_entity_id=contact_id,
            unassociate_contact_from_account=unassociate_contact_from_account,
            remove_contact_email_from_account=remove_contact_email_from_account,
            remove_contact_email_from_contact=[
                IntegrityOperationBase(
                    type=IntegrityOperationType.REMOVE,
                    src_entity_type=EntityType.CONTACT_EMAIL,
                    src_entity_id=remove_contact_email_id,
                    dest_entity_type=EntityType.CONTACT,
                    dest_entity_id=contact_id,
                )
            ],
            associated_data_changes_identifiers=associated_data_changes_identifiers,
            affected_intel_pipeline_identifiers=[
                IntegrityAssociatedDataOperationCommonPreviewIdentifier(
                    id=p.pipeline_id
                )
                for p in pipeline_identifiers
            ],
            affected_intel_contact_identifiers=[
                IntegrityAssociatedDataOperationCommonPreviewIdentifier(
                    id=contact_id,
                )
            ],
        )
