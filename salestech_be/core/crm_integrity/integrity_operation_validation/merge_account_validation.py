from uuid import UUID

from salestech_be.core.account.service.account_data_integrity_service import (
    get_account_data_integrity_service,
)
from salestech_be.core.crm_integrity.types.request_schema import (
    IntegrityJobValidationResponse,
    IntegrityOperationValidation,
)
from salestech_be.db.dbengine.core import DatabaseEngine


class MergeAccountValidationService:
    def __init__(self, database_engine: DatabaseEngine) -> None:
        self.account_data_integrity_service = get_account_data_integrity_service(
            db_engine=database_engine
        )

    async def validate_integrity_job(
        self,
        src_account_id: UUID,
        dest_account_id: UUID,
        organization_id: UUID,
    ) -> IntegrityJobValidationResponse:
        validation_results: list[IntegrityOperationValidation] = []

        validation_results.extend(
            await self.account_data_integrity_service.validate_account_availability(
                account_id=src_account_id,
                organization_id=organization_id,
            )
        )

        validation_results.extend(
            await self.account_data_integrity_service.validate_account_availability(
                account_id=dest_account_id,
                organization_id=organization_id,
            )
        )

        return IntegrityJobValidationResponse(validations=validation_results)
