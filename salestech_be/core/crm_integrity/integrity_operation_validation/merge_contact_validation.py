from uuid import UUID

from salestech_be.core.contact.service.contact_data_integrity_service import (
    get_contact_data_integrity_service,
)
from salestech_be.core.crm_integrity.types.request_schema import (
    IntegrityJobValidationResponse,
    IntegrityOperationValidation,
)
from salestech_be.db.dbengine.core import DatabaseEngine


class MergeContactValidationService:
    def __init__(self, database_engine: DatabaseEngine) -> None:
        self.contact_data_integrity_service = get_contact_data_integrity_service(
            db_engine=database_engine
        )

    async def validate_integrity_job(
        self,
        src_contact_id: UUID,
        dest_contact_id: UUID,
        organization_id: UUID,
    ) -> IntegrityJobValidationResponse:
        validation_results: list[IntegrityOperationValidation] = []

        validation_results.extend(
            await self.contact_data_integrity_service.validate_contact_availability(
                contact_id=src_contact_id,
                organization_id=organization_id,
            )
        )

        validation_results.extend(
            await self.contact_data_integrity_service.validate_contact_availability(
                contact_id=dest_contact_id,
                organization_id=organization_id,
            )
        )

        return IntegrityJobValidationResponse(validations=validation_results)
