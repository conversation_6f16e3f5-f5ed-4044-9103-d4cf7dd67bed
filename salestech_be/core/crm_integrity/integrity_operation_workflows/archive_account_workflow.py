from temporalio import workflow

with workflow.unsafe.imports_passed_through():
    from salestech_be.core.crm_integrity.atomic_operation_activities.account_related_activity import (
        AccountRelatedActivities,
    )
    from salestech_be.core.crm_integrity.atomic_operation_activities.integrity_job_activity import (
        IntegrityJobActivity,
    )
    from salestech_be.core.crm_integrity.event_processors.processor_activity import (
        data_operation_event_processor_activity,
    )
    from salestech_be.core.crm_integrity.types.activity_params import (
        ArchiveAccountParam,
        DataArchivalParam,
        IntegrityJobEntityDescriptor,
        IntegrityJobTriggerNotificationParams,
        RequireOrFreeLocksForEntitiesParam,
        UpdateIntegrityJobParam,
    )
    from salestech_be.core.crm_integrity.types.default_user_options_subdomains import (
        get_affected_sub_domains_by_integrity_job_name,
    )
    from salestech_be.core.crm_integrity.types.workflow_default_config import (
        DEFAULT_ACTIVITY_TIMEOUT,
        DEFAULT_RETRY_POLICY,
    )
    from salestech_be.core.crm_integrity.utils_activities.send_notification_activity import (
        crm_data_integrity_job_send_notification_activity,
    )
    from salestech_be.db.models.crm_integrity import (
        EntityType,
        IntegrityJobName,
        IntegrityJobType,
        JobStatus,
    )


@workflow.defn
class ArchiveAccountWorkflow:
    @workflow.run
    async def run(self, param: ArchiveAccountParam) -> None:
        try:
            await workflow.execute_activity_method(
                IntegrityJobActivity.acquire_locks_for_entities,
                RequireOrFreeLocksForEntitiesParam(
                    job_id=param.job_id,
                    entity_descriptors=[
                        IntegrityJobEntityDescriptor(
                            entity_type=EntityType.ACCOUNT,
                            entity_id=param.archive_account_id,
                        ),
                    ],
                    user_id=param.user_id,
                    organization_id=param.organization_id,
                ),
                start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=DEFAULT_RETRY_POLICY,
            )

            await workflow.execute_activity_method(
                IntegrityJobActivity.update_integrity_job,
                UpdateIntegrityJobParam(
                    job_id=param.job_id,
                    status=JobStatus.RUNNING,
                    organization_id=param.organization_id,
                    user_id=param.user_id,
                ),
                start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=DEFAULT_RETRY_POLICY,
            )

            await workflow.execute_activity(
                crm_data_integrity_job_send_notification_activity,
                IntegrityJobTriggerNotificationParams(
                    integrity_job_id=param.job_id,
                    integrity_job_type=IntegrityJobType.ARCHIVE,
                    integrity_job_status=JobStatus.RUNNING,
                    src_entity_type=EntityType.ACCOUNT,
                    src_entity_id=param.archive_account_id,
                    dest_entity_type=None,
                    dest_entity_id=None,
                    user_id=param.user_id,
                    organization_id=param.organization_id,
                    idempotency_key=f"running_{param.job_id!s}_{param.retry_count!s}",
                    redirection_entity_type=EntityType.ACCOUNT,
                    redirection_entity_id=param.archive_account_id,
                ),
                start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=DEFAULT_RETRY_POLICY,
            )

            await workflow.execute_activity_method(
                AccountRelatedActivities.archive_account,
                ArchiveAccountParam(
                    job_id=param.job_id,
                    retry_count=param.retry_count,
                    archive_account_id=param.archive_account_id,
                    user_id=param.user_id,
                    organization_id=param.organization_id,
                ),
                start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=DEFAULT_RETRY_POLICY,
            )

            for subdomain in get_affected_sub_domains_by_integrity_job_name(
                integrity_job_name=IntegrityJobName.ARCHIVE_ACCOUNT
            ):
                await workflow.execute_activity_method(
                    data_operation_event_processor_activity,
                    args=(
                        IntegrityJobName.ARCHIVE_ACCOUNT,
                        subdomain,
                        DataArchivalParam(
                            integrity_job_id=param.job_id,
                            archive_entity_id=param.archive_account_id,
                            user_id=param.user_id,
                            organization_id=param.organization_id,
                        ),
                        None,
                    ),
                    start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                    retry_policy=DEFAULT_RETRY_POLICY,
                )

            await workflow.execute_activity_method(
                IntegrityJobActivity.update_integrity_job,
                UpdateIntegrityJobParam(
                    job_id=param.job_id,
                    status=JobStatus.SUCCESS,
                    organization_id=param.organization_id,
                    user_id=param.user_id,
                ),
                start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=DEFAULT_RETRY_POLICY,
            )

            await workflow.execute_activity(
                crm_data_integrity_job_send_notification_activity,
                IntegrityJobTriggerNotificationParams(
                    integrity_job_id=param.job_id,
                    integrity_job_type=IntegrityJobType.ARCHIVE,
                    integrity_job_status=JobStatus.SUCCESS,
                    src_entity_type=EntityType.ACCOUNT,
                    src_entity_id=param.archive_account_id,
                    dest_entity_type=None,
                    dest_entity_id=None,
                    user_id=param.user_id,
                    organization_id=param.organization_id,
                    idempotency_key=f"success_{param.job_id!s}_{param.retry_count!s}",
                    redirection_entity_type=EntityType.ACCOUNT,
                    redirection_entity_id=param.archive_account_id,
                ),
                start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=DEFAULT_RETRY_POLICY,
            )
        except Exception:
            await workflow.execute_activity_method(
                IntegrityJobActivity.update_integrity_job,
                UpdateIntegrityJobParam(
                    job_id=param.job_id,
                    status=JobStatus.FAIL,
                    organization_id=param.organization_id,
                    user_id=param.user_id,
                ),
                start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=DEFAULT_RETRY_POLICY,
            )

            await workflow.execute_activity(
                crm_data_integrity_job_send_notification_activity,
                IntegrityJobTriggerNotificationParams(
                    integrity_job_id=param.job_id,
                    integrity_job_type=IntegrityJobType.ARCHIVE,
                    integrity_job_status=JobStatus.FAIL,
                    src_entity_type=EntityType.ACCOUNT,
                    src_entity_id=param.archive_account_id,
                    dest_entity_type=None,
                    dest_entity_id=None,
                    user_id=param.user_id,
                    organization_id=param.organization_id,
                    idempotency_key=f"fail_{param.job_id!s}_{param.retry_count!s}",
                    redirection_entity_type=EntityType.ACCOUNT,
                    redirection_entity_id=param.archive_account_id,
                ),
                start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=DEFAULT_RETRY_POLICY,
            )

            raise
        finally:
            await workflow.execute_activity_method(
                IntegrityJobActivity.free_locks_for_entities,
                RequireOrFreeLocksForEntitiesParam(
                    job_id=param.job_id,
                    entity_descriptors=[
                        IntegrityJobEntityDescriptor(
                            entity_type=EntityType.ACCOUNT,
                            entity_id=param.archive_account_id,
                        ),
                    ],
                    user_id=param.user_id,
                    organization_id=param.organization_id,
                ),
                start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=DEFAULT_RETRY_POLICY,
            )
