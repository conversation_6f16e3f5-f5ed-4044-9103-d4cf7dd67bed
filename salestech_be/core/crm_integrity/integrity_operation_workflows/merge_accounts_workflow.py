from temporalio import workflow

with workflow.unsafe.imports_passed_through():
    from salestech_be.core.crm_integrity.atomic_operation_activities.account_related_activity import (
        AccountRelatedActivities,
    )
    from salestech_be.core.crm_integrity.atomic_operation_activities.associate_contact_with_account_activity import (
        AssociateContactWithAccountActivity,
    )
    from salestech_be.core.crm_integrity.atomic_operation_activities.contact_email_related_activity import (
        ContactEmailRelatedActivity,
    )
    from salestech_be.core.crm_integrity.atomic_operation_activities.integrity_job_activity import (
        IntegrityJobActivity,
    )
    from salestech_be.core.crm_integrity.atomic_operation_activities.intelligence_related_activity import (
        IntelligenceRelatedActivity,
    )
    from salestech_be.core.crm_integrity.atomic_operation_activities.move_pipeline_to_new_account_activity import (
        MovePipelineToNewAccountActivity,
    )
    from salestech_be.core.crm_integrity.atomic_operation_activities.unassociate_contact_with_account_activity import (
        UnassociateContactWithAccountActivity,
    )
    from salestech_be.core.crm_integrity.event_processors.processor_activity import (
        data_operation_event_processor_activity,
    )
    from salestech_be.core.crm_integrity.types.activity_params import (
        ArchiveAccountParam,
        CopyAssociationsFromAccountToAccountParam,
        DataReplacementParam,
        IntegrityJobEntityDescriptor,
        IntegrityJobTriggerNotificationParams,
        MergeAccountsParam,
        MoveContactEmailsFromAccountToAccountParam,
        MovePipelinesToNewAccountParam,
        PipelineIntelActivityJobParams,
        RequireOrFreeLocksForEntitiesParam,
        UnassociateContactsWithSingleAccountParam,
        UpdateIntegrityJobParam,
    )
    from salestech_be.core.crm_integrity.types.default_user_options_subdomains import (
        get_affected_sub_domains_by_integrity_job_name,
    )
    from salestech_be.core.crm_integrity.types.workflow_default_config import (
        DEFAULT_ACTIVITY_TIMEOUT,
        DEFAULT_RETRY_POLICY,
    )
    from salestech_be.core.crm_integrity.utils_activities.send_notification_activity import (
        crm_data_integrity_job_send_notification_activity,
    )
    from salestech_be.db.models.crm_integrity import (
        EntityType,
        IntegrityJobName,
        IntegrityJobType,
        JobStatus,
    )


@workflow.defn
class MergeAccountsWorkflow:
    @workflow.run
    async def run(self, param: MergeAccountsParam) -> None:
        """
        This workflow is used to merge two active accounts, and the steps are:
        1. copy contact account associations from src account to dest account
        2. replace pipeline associated with src account with dest account
        3. archive contact account associations between src account and its contacts
        4. mark src account as merging
        5. replace src account id in all referenced data with dest account id
        6. mark src account as merged

        IMPORTANT:
        Make sure to update MergeAccountsPreviewService.preview_merge_accounts_job
        when changing execution logic of this workflow.
        """
        try:
            await workflow.execute_activity_method(
                IntegrityJobActivity.acquire_locks_for_entities,
                RequireOrFreeLocksForEntitiesParam(
                    job_id=param.job_id,
                    entity_descriptors=[
                        IntegrityJobEntityDescriptor(
                            entity_type=EntityType.ACCOUNT,
                            entity_id=param.src_account_id,
                        ),
                        IntegrityJobEntityDescriptor(
                            entity_type=EntityType.ACCOUNT,
                            entity_id=param.dest_account_id,
                        ),
                    ],
                    user_id=param.user_id,
                    organization_id=param.organization_id,
                ),
                start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=DEFAULT_RETRY_POLICY,
            )

            # mark integrity job as running
            await workflow.execute_activity_method(
                IntegrityJobActivity.update_integrity_job,
                UpdateIntegrityJobParam(
                    job_id=param.job_id,
                    status=JobStatus.RUNNING,
                    organization_id=param.organization_id,
                    user_id=param.user_id,
                ),
                start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=DEFAULT_RETRY_POLICY,
            )

            await workflow.execute_activity(
                crm_data_integrity_job_send_notification_activity,
                IntegrityJobTriggerNotificationParams(
                    integrity_job_id=param.job_id,
                    integrity_job_type=IntegrityJobType.MERGE,
                    integrity_job_status=JobStatus.RUNNING,
                    src_entity_type=EntityType.ACCOUNT,
                    src_entity_id=param.src_account_id,
                    dest_entity_type=EntityType.ACCOUNT,
                    dest_entity_id=param.dest_account_id,
                    user_id=param.user_id,
                    organization_id=param.organization_id,
                    idempotency_key=f"running_{param.job_id!s}_{param.retry_count!s}",
                    redirection_entity_type=EntityType.ACCOUNT,
                    redirection_entity_id=param.src_account_id,
                ),
                start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=DEFAULT_RETRY_POLICY,
            )

            # add all contacts to dest account
            await workflow.execute_activity_method(
                AssociateContactWithAccountActivity.copy_contact_associations_from_account_to_account,
                CopyAssociationsFromAccountToAccountParam(
                    integrity_job_id=param.job_id,
                    organization_id=param.organization_id,
                    user_id=param.user_id,
                    src_account_id=param.src_account_id,
                    dest_account_id=param.dest_account_id,
                ),
                start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=DEFAULT_RETRY_POLICY,
            )

            await workflow.execute_activity_method(
                ContactEmailRelatedActivity.move_contact_emails_from_account_to_account,
                MoveContactEmailsFromAccountToAccountParam(
                    integrity_job_id=param.job_id,
                    organization_id=param.organization_id,
                    user_id=param.user_id,
                    src_account_id=param.src_account_id,
                    dest_account_id=param.dest_account_id,
                ),
                start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=DEFAULT_RETRY_POLICY,
            )

            # replace account_id in pipelines associated with src account with dest account_id
            updated_pipelines = await workflow.execute_activity_method(
                MovePipelineToNewAccountActivity.move_pipelines_to_new_account,
                MovePipelinesToNewAccountParam(
                    integrity_job_id=param.job_id,
                    organization_id=param.organization_id,
                    user_id=param.user_id,
                    src_account_id=param.src_account_id,
                    new_account_id=param.dest_account_id,
                ),
                start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=DEFAULT_RETRY_POLICY,
            )

            # archive contact_account_association between src account and contacts it associated with
            await workflow.execute_activity_method(
                UnassociateContactWithAccountActivity.unassociate_all_contacts_with_single_account,
                UnassociateContactsWithSingleAccountParam(
                    integrity_job_id=param.job_id,
                    user_id=param.user_id,
                    organization_id=param.organization_id,
                    account_id=param.src_account_id,
                ),
                start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=DEFAULT_RETRY_POLICY,
            )

            # replace account_id in referenced data
            for subdomain in get_affected_sub_domains_by_integrity_job_name(
                integrity_job_name=IntegrityJobName.MERGE_ACCOUNTS
            ):
                await workflow.execute_activity_method(
                    data_operation_event_processor_activity,
                    args=(
                        IntegrityJobName.MERGE_ACCOUNTS,
                        subdomain,
                        DataReplacementParam(
                            integrity_job_id=param.job_id,
                            replaced_entity_type=EntityType.ACCOUNT,
                            user_id=param.user_id,
                            organization_id=param.organization_id,
                            primary_entity_id=param.src_account_id,
                            replaced_entity_id=param.dest_account_id,
                        ),
                        None,
                    ),
                    start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                    retry_policy=DEFAULT_RETRY_POLICY,
                )

            # archive src account
            await workflow.execute_activity_method(
                AccountRelatedActivities.archive_account,
                ArchiveAccountParam(
                    job_id=param.job_id,
                    retry_count=param.retry_count,
                    archive_account_id=param.src_account_id,
                    user_id=param.user_id,
                    organization_id=param.organization_id,
                ),
                start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=DEFAULT_RETRY_POLICY,
            )

            await workflow.execute_activity_method(
                IntelligenceRelatedActivity.pipeline_intel_activity_job,
                PipelineIntelActivityJobParams(
                    organization_id=param.organization_id,
                    pipeline_ids={p.id for p in updated_pipelines},
                ),
                start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=DEFAULT_RETRY_POLICY,
            )

            await workflow.execute_activity_method(
                IntegrityJobActivity.update_integrity_job,
                UpdateIntegrityJobParam(
                    job_id=param.job_id,
                    status=JobStatus.SUCCESS,
                    organization_id=param.organization_id,
                    user_id=param.user_id,
                ),
                start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=DEFAULT_RETRY_POLICY,
            )

            await workflow.execute_activity(
                crm_data_integrity_job_send_notification_activity,
                IntegrityJobTriggerNotificationParams(
                    integrity_job_id=param.job_id,
                    integrity_job_type=IntegrityJobType.MERGE,
                    integrity_job_status=JobStatus.SUCCESS,
                    src_entity_type=EntityType.ACCOUNT,
                    src_entity_id=param.src_account_id,
                    dest_entity_type=EntityType.ACCOUNT,
                    dest_entity_id=param.dest_account_id,
                    user_id=param.user_id,
                    organization_id=param.organization_id,
                    idempotency_key=f"success_{param.job_id!s}_{param.retry_count!s}",
                    redirection_entity_type=EntityType.ACCOUNT,
                    redirection_entity_id=param.dest_account_id,
                ),
                start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=DEFAULT_RETRY_POLICY,
            )
        except Exception:
            await workflow.execute_activity_method(
                IntegrityJobActivity.update_integrity_job,
                UpdateIntegrityJobParam(
                    job_id=param.job_id,
                    status=JobStatus.FAIL,
                    organization_id=param.organization_id,
                    user_id=param.user_id,
                ),
                start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=DEFAULT_RETRY_POLICY,
            )

            await workflow.execute_activity(
                crm_data_integrity_job_send_notification_activity,
                IntegrityJobTriggerNotificationParams(
                    integrity_job_id=param.job_id,
                    integrity_job_type=IntegrityJobType.MERGE,
                    integrity_job_status=JobStatus.FAIL,
                    src_entity_type=EntityType.ACCOUNT,
                    src_entity_id=param.src_account_id,
                    dest_entity_type=EntityType.ACCOUNT,
                    dest_entity_id=param.dest_account_id,
                    user_id=param.user_id,
                    organization_id=param.organization_id,
                    idempotency_key=f"fail_{param.job_id!s}_{param.retry_count!s}",
                    redirection_entity_type=EntityType.ACCOUNT,
                    redirection_entity_id=param.src_account_id,
                ),
                start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=DEFAULT_RETRY_POLICY,
            )

            raise
        finally:
            await workflow.execute_activity_method(
                IntegrityJobActivity.free_locks_for_entities,
                RequireOrFreeLocksForEntitiesParam(
                    job_id=param.job_id,
                    entity_descriptors=[
                        IntegrityJobEntityDescriptor(
                            entity_type=EntityType.ACCOUNT,
                            entity_id=param.src_account_id,
                        ),
                        IntegrityJobEntityDescriptor(
                            entity_type=EntityType.ACCOUNT,
                            entity_id=param.dest_account_id,
                        ),
                    ],
                    user_id=param.user_id,
                    organization_id=param.organization_id,
                ),
                start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=DEFAULT_RETRY_POLICY,
            )
