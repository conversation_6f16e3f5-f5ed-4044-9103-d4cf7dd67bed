"""
This workflow is used to merge two active contacts.
"""

from temporalio import workflow

with workflow.unsafe.imports_passed_through():
    from salestech_be.core.crm_integrity.atomic_operation_activities.associate_contact_with_account_activity import (
        AssociateContactWithAccountActivity,
    )
    from salestech_be.core.crm_integrity.atomic_operation_activities.associate_contact_with_pipeline_activity import (
        AssociateContactWithPipelineActivity,
    )
    from salestech_be.core.crm_integrity.atomic_operation_activities.contact_email_related_activity import (
        ContactEmailRelatedActivity,
    )
    from salestech_be.core.crm_integrity.atomic_operation_activities.contact_related_activity import (
        ContactRelatedActivities,
    )
    from salestech_be.core.crm_integrity.atomic_operation_activities.integrity_job_activity import (
        IntegrityJobActivity,
    )
    from salestech_be.core.crm_integrity.atomic_operation_activities.intelligence_related_activity import (
        IntelligenceRelatedActivity,
    )
    from salestech_be.core.crm_integrity.atomic_operation_activities.phone_number_related_activity import (
        PhoneNumberRelatedActivity,
    )
    from salestech_be.core.crm_integrity.atomic_operation_activities.unassociate_contact_with_account_activity import (
        UnassociateContactWithAccountActivity,
    )
    from salestech_be.core.crm_integrity.event_processors.processor_activity import (
        data_operation_event_processor_activity,
    )
    from salestech_be.core.crm_integrity.types.activity_params import (
        ArchiveContactParam,
        CopyAssociationsFromContactToContactParam,
        CopyPipelinesFromContactToContactParam,
        DataReplacementParam,
        IntegrityJobEntityDescriptor,
        IntegrityJobTriggerNotificationParams,
        MergeContactsParam,
        MoveContactEmailsFromContactToContactParam,
        MoveContactPhoneNumbersFromContactToContactParam,
        PersonIntelActivityJobParams,
        PipelineIntelActivityJobParams,
        RequireOrFreeLocksForEntitiesParam,
        UnassociateSingleContactWithAllAccountsParam,
        UpdateIntegrityJobParam,
    )
    from salestech_be.core.crm_integrity.types.default_user_options_subdomains import (
        get_affected_sub_domains_by_integrity_job_name,
    )
    from salestech_be.core.crm_integrity.types.workflow_default_config import (
        DEFAULT_ACTIVITY_TIMEOUT,
        DEFAULT_RETRY_POLICY,
    )
    from salestech_be.core.crm_integrity.utils_activities.send_notification_activity import (
        crm_data_integrity_job_send_notification_activity,
    )
    from salestech_be.db.models.crm_integrity import (
        EntityType,
        IntegrityJobName,
        IntegrityJobType,
        JobStatus,
    )


@workflow.defn
class MergeContactsWorkflow:
    @workflow.run
    async def run(self, param: MergeContactsParam) -> None:
        try:
            await workflow.execute_activity_method(
                IntegrityJobActivity.acquire_locks_for_entities,
                RequireOrFreeLocksForEntitiesParam(
                    job_id=param.job_id,
                    entity_descriptors=[
                        IntegrityJobEntityDescriptor(
                            entity_type=EntityType.CONTACT,
                            entity_id=param.src_contact_id,
                        ),
                        IntegrityJobEntityDescriptor(
                            entity_type=EntityType.CONTACT,
                            entity_id=param.dest_contact_id,
                        ),
                    ],
                    user_id=param.user_id,
                    organization_id=param.organization_id,
                ),
                start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=DEFAULT_RETRY_POLICY,
            )

            # mark integrity job as running
            await workflow.execute_activity_method(
                IntegrityJobActivity.update_integrity_job,
                UpdateIntegrityJobParam(
                    job_id=param.job_id,
                    status=JobStatus.RUNNING,
                    organization_id=param.organization_id,
                    user_id=param.user_id,
                ),
                start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=DEFAULT_RETRY_POLICY,
            )
            await workflow.execute_activity(
                crm_data_integrity_job_send_notification_activity,
                IntegrityJobTriggerNotificationParams(
                    integrity_job_id=param.job_id,
                    integrity_job_type=IntegrityJobType.MERGE,
                    integrity_job_status=JobStatus.RUNNING,
                    src_entity_type=EntityType.CONTACT,
                    src_entity_id=param.src_contact_id,
                    dest_entity_type=EntityType.CONTACT,
                    dest_entity_id=param.dest_contact_id,
                    user_id=param.user_id,
                    organization_id=param.organization_id,
                    idempotency_key=f"running_{param.job_id!s}_{param.retry_count!s}",
                    redirection_entity_type=EntityType.CONTACT,
                    redirection_entity_id=param.src_contact_id,
                ),
                start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=DEFAULT_RETRY_POLICY,
            )

            # add all accounts to dest contact
            await workflow.execute_activity_method(
                AssociateContactWithAccountActivity.copy_contact_associations_from_contact_to_contact,
                CopyAssociationsFromContactToContactParam(
                    integrity_job_id=param.job_id,
                    organization_id=param.organization_id,
                    user_id=param.user_id,
                    src_contact_id=param.src_contact_id,
                    dest_contact_id=param.dest_contact_id,
                ),
                start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=DEFAULT_RETRY_POLICY,
            )

            # add all accounts to dest contact
            pipeline_ids = await workflow.execute_activity_method(
                AssociateContactWithPipelineActivity.copy_pipelines_associations_from_contact_to_contact,
                CopyPipelinesFromContactToContactParam(
                    integrity_job_id=param.job_id,
                    primary_entity_id=param.src_contact_id,
                    replaced_entity_id=param.dest_contact_id,
                    user_id=param.user_id,
                    organization_id=param.organization_id,
                ),
                start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=DEFAULT_RETRY_POLICY,
            )

            await workflow.execute_activity_method(
                ContactEmailRelatedActivity.move_contact_emails_from_contact_to_contact,
                MoveContactEmailsFromContactToContactParam(
                    integrity_job_id=param.job_id,
                    src_contact_id=param.src_contact_id,
                    dest_contact_id=param.dest_contact_id,
                    user_id=param.user_id,
                    organization_id=param.organization_id,
                ),
                start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=DEFAULT_RETRY_POLICY,
            )

            await workflow.execute_activity_method(
                PhoneNumberRelatedActivity.move_contact_phone_numbers_from_contact_to_contact,
                MoveContactPhoneNumbersFromContactToContactParam(
                    integrity_job_id=param.job_id,
                    src_contact_id=param.src_contact_id,
                    dest_contact_id=param.dest_contact_id,
                    user_id=param.user_id,
                    organization_id=param.organization_id,
                ),
                start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=DEFAULT_RETRY_POLICY,
            )

            for subdomain in get_affected_sub_domains_by_integrity_job_name(
                integrity_job_name=IntegrityJobName.MERGE_CONTACTS
            ):
                await workflow.execute_activity_method(
                    data_operation_event_processor_activity,
                    args=(
                        IntegrityJobName.MERGE_CONTACTS,
                        subdomain,
                        DataReplacementParam(
                            integrity_job_id=param.job_id,
                            replaced_entity_type=EntityType.CONTACT,
                            user_id=param.user_id,
                            organization_id=param.organization_id,
                            primary_entity_id=param.src_contact_id,
                            replaced_entity_id=param.dest_contact_id,
                        ),
                        None,
                    ),
                    start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                    retry_policy=DEFAULT_RETRY_POLICY,
                )

            # unassociate the src contact from its existing account
            await workflow.execute_activity_method(
                UnassociateContactWithAccountActivity.unassociate_single_contact_with_all_accounts,
                UnassociateSingleContactWithAllAccountsParam(
                    integrity_job_id=param.job_id,
                    contact_id=param.src_contact_id,
                    user_id=param.user_id,
                    organization_id=param.organization_id,
                ),
                start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=DEFAULT_RETRY_POLICY,
            )

            # archive src contact
            await workflow.execute_activity_method(
                ContactRelatedActivities.archive_contact,
                ArchiveContactParam(
                    job_id=param.job_id,
                    retry_count=param.retry_count,
                    archive_contact_id=param.src_contact_id,
                    user_id=param.user_id,
                    organization_id=param.organization_id,
                ),
                start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=DEFAULT_RETRY_POLICY,
            )

            # trigger the person intel
            await workflow.execute_activity_method(
                IntelligenceRelatedActivity.person_intel_activity_job,
                PersonIntelActivityJobParams(
                    contact_ids={param.dest_contact_id},
                    organization_id=param.organization_id,
                ),
                start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=DEFAULT_RETRY_POLICY,
            )

            await workflow.execute_activity_method(
                IntelligenceRelatedActivity.pipeline_intel_activity_job,
                PipelineIntelActivityJobParams(
                    organization_id=param.organization_id,
                    pipeline_ids=set(pipeline_ids),
                ),
                start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=DEFAULT_RETRY_POLICY,
            )

            # update integrity job status to success
            await workflow.execute_activity_method(
                IntegrityJobActivity.update_integrity_job,
                UpdateIntegrityJobParam(
                    job_id=param.job_id,
                    status=JobStatus.SUCCESS,
                    organization_id=param.organization_id,
                    user_id=param.user_id,
                ),
                start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=DEFAULT_RETRY_POLICY,
            )
            await workflow.execute_activity(
                crm_data_integrity_job_send_notification_activity,
                IntegrityJobTriggerNotificationParams(
                    integrity_job_id=param.job_id,
                    integrity_job_type=IntegrityJobType.MERGE,
                    integrity_job_status=JobStatus.SUCCESS,
                    src_entity_type=EntityType.CONTACT,
                    src_entity_id=param.src_contact_id,
                    dest_entity_type=EntityType.CONTACT,
                    dest_entity_id=param.dest_contact_id,
                    user_id=param.user_id,
                    organization_id=param.organization_id,
                    idempotency_key=f"success_{param.job_id!s}_{param.retry_count!s}",
                    redirection_entity_type=EntityType.CONTACT,
                    redirection_entity_id=param.dest_contact_id,
                ),
                start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=DEFAULT_RETRY_POLICY,
            )

        except Exception:
            # update integrity job status to fail
            await workflow.execute_activity_method(
                IntegrityJobActivity.update_integrity_job,
                UpdateIntegrityJobParam(
                    job_id=param.job_id,
                    status=JobStatus.FAIL,
                    organization_id=param.organization_id,
                    user_id=param.user_id,
                ),
                start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=DEFAULT_RETRY_POLICY,
            )
            await workflow.execute_activity(
                crm_data_integrity_job_send_notification_activity,
                IntegrityJobTriggerNotificationParams(
                    integrity_job_id=param.job_id,
                    integrity_job_type=IntegrityJobType.MERGE,
                    integrity_job_status=JobStatus.FAIL,
                    src_entity_type=EntityType.CONTACT,
                    src_entity_id=param.src_contact_id,
                    dest_entity_type=EntityType.CONTACT,
                    dest_entity_id=param.dest_contact_id,
                    user_id=param.user_id,
                    organization_id=param.organization_id,
                    idempotency_key=f"fail_{param.job_id!s}_{param.retry_count!s}",
                    redirection_entity_type=EntityType.CONTACT,
                    redirection_entity_id=param.src_contact_id,
                ),
                start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=DEFAULT_RETRY_POLICY,
            )
            raise
        finally:
            await workflow.execute_activity_method(
                IntegrityJobActivity.free_locks_for_entities,
                RequireOrFreeLocksForEntitiesParam(
                    job_id=param.job_id,
                    entity_descriptors=[
                        IntegrityJobEntityDescriptor(
                            entity_type=EntityType.CONTACT,
                            entity_id=param.src_contact_id,
                        ),
                        IntegrityJobEntityDescriptor(
                            entity_type=EntityType.CONTACT,
                            entity_id=param.dest_contact_id,
                        ),
                    ],
                    user_id=param.user_id,
                    organization_id=param.organization_id,
                ),
                start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=DEFAULT_RETRY_POLICY,
            )
