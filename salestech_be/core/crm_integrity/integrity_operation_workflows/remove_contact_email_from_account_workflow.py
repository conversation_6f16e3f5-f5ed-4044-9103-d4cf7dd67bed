from temporalio import workflow

with workflow.unsafe.imports_passed_through():
    from temporalio import workflow

    from salestech_be.core.crm_integrity.atomic_operation_activities.contact_email_related_activity import (
        ContactEmailRelatedActivity,
    )
    from salestech_be.core.crm_integrity.atomic_operation_activities.integrity_job_activity import (
        IntegrityJobActivity,
    )
    from salestech_be.core.crm_integrity.event_processors.processor_activity import (
        data_operation_event_processor_activity,
    )
    from salestech_be.core.crm_integrity.types.activity_params import (
        DataRemovalParam,
        IntegrityJobEntityDescriptor,
        IntegrityJobTriggerNotificationParams,
        RemoveContactEmailFromAccountParam,
        RequireOrFreeLocksForEntitiesParam,
        UpdateIntegrityJobParam,
    )
    from salestech_be.core.crm_integrity.types.default_user_options_subdomains import (
        get_affected_sub_domains_by_integrity_job_name,
    )
    from salestech_be.core.crm_integrity.types.workflow_default_config import (
        DEFAULT_ACTIVITY_TIMEOUT,
        DEFAULT_RETRY_POLICY,
    )
    from salestech_be.core.crm_integrity.utils_activities.send_notification_activity import (
        crm_data_integrity_job_send_notification_activity,
    )
    from salestech_be.db.models.crm_integrity import (
        EntityType,
        IntegrityJobName,
        IntegrityJobType,
        JobStatus,
    )


@workflow.defn
class RemoveContactEmailFromAccountWorkflow:
    """
    This workflow is used to remove a contact email that associates with an account.

    Consequences:
    * The contact + email + account association will be removed.

    """

    @workflow.run
    async def run(self, param: RemoveContactEmailFromAccountParam) -> None:
        try:
            await workflow.execute_activity_method(
                IntegrityJobActivity.acquire_locks_for_entities,
                RequireOrFreeLocksForEntitiesParam(
                    job_id=param.job_id,
                    entity_descriptors=[
                        IntegrityJobEntityDescriptor(
                            entity_type=EntityType.CONTACT,
                            entity_id=param.contact_id,
                        ),
                        IntegrityJobEntityDescriptor(
                            entity_type=EntityType.ACCOUNT,
                            entity_id=param.account_id,
                        ),
                    ],
                    user_id=param.user_id,
                    organization_id=param.organization_id,
                ),
                start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=DEFAULT_RETRY_POLICY,
            )

            await workflow.execute_activity_method(
                IntegrityJobActivity.update_integrity_job,
                UpdateIntegrityJobParam(
                    job_id=param.job_id,
                    status=JobStatus.RUNNING,
                    organization_id=param.organization_id,
                    user_id=param.user_id,
                ),
                start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=DEFAULT_RETRY_POLICY,
            )

            await workflow.execute_activity(
                crm_data_integrity_job_send_notification_activity,
                IntegrityJobTriggerNotificationParams(
                    integrity_job_id=param.job_id,
                    integrity_job_type=IntegrityJobType.REMOVE,
                    integrity_job_status=JobStatus.RUNNING,
                    src_entity_type=EntityType.CONTACT_EMAIL,
                    src_entity_id=param.contact_email_id,
                    dest_entity_type=EntityType.ACCOUNT,
                    dest_entity_id=param.account_id,
                    user_id=param.user_id,
                    organization_id=param.organization_id,
                    idempotency_key=f"running_{param.job_id!s}_{param.retry_count!s}",
                    redirection_entity_type=EntityType.CONTACT,
                    redirection_entity_id=param.contact_id,
                ),
                start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=DEFAULT_RETRY_POLICY,
            )

            await workflow.execute_activity_method(
                ContactEmailRelatedActivity.remove_contact_email_from_account,
                RemoveContactEmailFromAccountParam(
                    job_id=param.job_id,
                    retry_count=param.retry_count,
                    contact_email_id=param.contact_email_id,
                    contact_id=param.contact_id,
                    account_id=param.account_id,
                    user_id=param.user_id,
                    organization_id=param.organization_id,
                ),
                start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=DEFAULT_RETRY_POLICY,
            )

            for subdomain in get_affected_sub_domains_by_integrity_job_name(
                integrity_job_name=IntegrityJobName.REMOVE_CONTACT_EMAIL_FROM_ACCOUNT
            ):
                await workflow.execute_activity_method(
                    data_operation_event_processor_activity,
                    args=(
                        IntegrityJobName.REMOVE_CONTACT_EMAIL_FROM_ACCOUNT,
                        subdomain,
                        DataRemovalParam(
                            integrity_job_id=param.job_id,
                            remove_entity_id=param.contact_email_id,
                            src_entity_id=param.account_id,
                            user_id=param.user_id,
                            organization_id=param.organization_id,
                        ),
                        None,
                    ),
                    start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                    retry_policy=DEFAULT_RETRY_POLICY,
                )

            await workflow.execute_activity_method(
                IntegrityJobActivity.update_integrity_job,
                UpdateIntegrityJobParam(
                    job_id=param.job_id,
                    status=JobStatus.SUCCESS,
                    organization_id=param.organization_id,
                    user_id=param.user_id,
                ),
                start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=DEFAULT_RETRY_POLICY,
            )
            await workflow.execute_activity(
                crm_data_integrity_job_send_notification_activity,
                IntegrityJobTriggerNotificationParams(
                    integrity_job_id=param.job_id,
                    integrity_job_type=IntegrityJobType.REMOVE,
                    integrity_job_status=JobStatus.SUCCESS,
                    src_entity_type=EntityType.CONTACT_EMAIL,
                    src_entity_id=param.contact_email_id,
                    dest_entity_type=EntityType.ACCOUNT,
                    dest_entity_id=param.account_id,
                    user_id=param.user_id,
                    organization_id=param.organization_id,
                    idempotency_key=f"success_{param.job_id!s}_{param.retry_count!s}",
                    redirection_entity_type=EntityType.CONTACT,
                    redirection_entity_id=param.contact_id,
                ),
                start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=DEFAULT_RETRY_POLICY,
            )

        except Exception:
            await workflow.execute_activity_method(
                IntegrityJobActivity.update_integrity_job,
                UpdateIntegrityJobParam(
                    job_id=param.job_id,
                    status=JobStatus.FAIL,
                    organization_id=param.organization_id,
                    user_id=param.user_id,
                ),
                start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=DEFAULT_RETRY_POLICY,
            )
            await workflow.execute_activity(
                crm_data_integrity_job_send_notification_activity,
                IntegrityJobTriggerNotificationParams(
                    integrity_job_id=param.job_id,
                    integrity_job_type=IntegrityJobType.REMOVE,
                    integrity_job_status=JobStatus.FAIL,
                    src_entity_type=EntityType.CONTACT_EMAIL,
                    src_entity_id=param.contact_email_id,
                    dest_entity_type=EntityType.ACCOUNT,
                    dest_entity_id=param.account_id,
                    user_id=param.user_id,
                    organization_id=param.organization_id,
                    idempotency_key=f"fail_{param.job_id!s}_{param.retry_count!s}",
                    redirection_entity_type=EntityType.CONTACT,
                    redirection_entity_id=param.contact_id,
                ),
                start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=DEFAULT_RETRY_POLICY,
            )
            raise
        finally:
            await workflow.execute_activity_method(
                IntegrityJobActivity.free_locks_for_entities,
                RequireOrFreeLocksForEntitiesParam(
                    job_id=param.job_id,
                    entity_descriptors=[
                        IntegrityJobEntityDescriptor(
                            entity_type=EntityType.CONTACT,
                            entity_id=param.contact_id,
                        ),
                        IntegrityJobEntityDescriptor(
                            entity_type=EntityType.ACCOUNT,
                            entity_id=param.account_id,
                        ),
                    ],
                    user_id=param.user_id,
                    organization_id=param.organization_id,
                ),
                start_to_close_timeout=DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=DEFAULT_RETRY_POLICY,
            )
