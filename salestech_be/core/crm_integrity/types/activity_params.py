from collections.abc import Awaitable, Callable
from typing import Annotated, Any, Literal, NamedTuple
from uuid import UUID

from pydantic import BaseModel, Field

from salestech_be.db.models.crm_integrity import (
    AssociatedEntityField,
    AssociatedEntityFieldType,
    AssociatedEntityOperation,
    AssociatedEntityType,
    CRMIntegrityJobContextualParam,
    CRMIntegrityJobUserOption,
    CRMSubDomain,
    EntityType,
    IntegrityJobName,
    IntegrityJobType,
    JobStatus,
    MoveContactToAccountUserOption,
)


class BaseIntegrityJobParam(BaseModel):
    integrity_job_name: IntegrityJobName
    job_id: UUID
    retry_count: int
    src_entity_id: UUID
    dest_entity_id: UUID | None
    user_id: UUID
    organization_id: UUID
    user_choices: dict[CRMSubDomain, CRMIntegrityJobUserOption]
    contextual_param: CRMIntegrityJobContextualParam | None = None


class BaseIntegrityWorkflowParam(BaseModel):
    job_id: UUID
    retry_count: int
    user_id: UUID
    organization_id: UUID


class MergeAccountsParam(BaseIntegrityWorkflowParam):
    src_account_id: UUID
    dest_account_id: UUID


class MergeContactsParam(BaseIntegrityWorkflowParam):
    src_contact_id: UUID
    dest_contact_id: UUID


class MoveContactToAccountParam(BaseIntegrityWorkflowParam):
    contact_id: UUID
    src_account_id: UUID | None
    dest_account_id: UUID
    user_choices: dict[CRMSubDomain, MoveContactToAccountUserOption]
    title: str | None = None
    department: str | None = None


class RemoveContactFromAccountParam(BaseIntegrityWorkflowParam):
    contact_id: UUID
    account_id: UUID


class ArchiveContactParam(BaseIntegrityWorkflowParam):
    archive_contact_id: UUID


class ArchiveAccountParam(BaseIntegrityWorkflowParam):
    archive_account_id: UUID


class MoveContactEmailToContactParam(BaseIntegrityWorkflowParam):
    contact_email_id: UUID
    src_contact_id: UUID
    dest_contact_id: UUID
    remove_contact_account_association_if_last: bool
    create_contact_account_association_if_missing: bool


class RemoveContactEmailFromContactParam(BaseIntegrityWorkflowParam):
    contact_email_id: UUID
    contact_id: UUID
    remove_contact_account_association_if_last: bool


class MoveContactEmailToAccountParam(BaseIntegrityWorkflowParam):
    contact_email_id: UUID
    contact_id: UUID
    src_account_id: UUID
    dest_account_id: UUID


class RemoveContactEmailsFromAccountParam(BaseIntegrityWorkflowParam):
    contact_id: UUID
    account_id: UUID


class RemoveContactEmailFromAccountParam(RemoveContactEmailsFromAccountParam):
    contact_email_id: UUID


class AddAccountToPipelineParam(BaseIntegrityWorkflowParam):
    account_id: UUID
    pipeline_id: UUID


class AddContactToPipelineParam(BaseIntegrityWorkflowParam):
    contact_id: UUID
    pipeline_id: UUID


class CreateIntegrityJobParam(BaseModel):
    user_id: UUID
    organization_id: UUID
    job_type: IntegrityJobType
    src_entity_type: EntityType
    src_entity_id: UUID
    dest_entity_type: EntityType | None = None
    dest_entity_id: UUID | None = None
    contextual_param: CRMIntegrityJobContextualParam | None = None
    user_choices: dict[CRMSubDomain, CRMIntegrityJobUserOption | None] | None = None


class UpdateIntegrityJobParam(BaseModel):
    job_id: UUID
    status: JobStatus
    retry_count: int | None = None
    organization_id: UUID
    user_id: UUID


class UpdateIntegrityJobErrorParam(BaseModel):
    job_id: UUID
    organization_id: UUID
    user_id: UUID

    namespace: str
    message: str
    workflow_id: str
    workflow_type: str
    workflow_run_id: str


class IntegrityJobEntityDescriptor(NamedTuple):
    entity_type: Literal[EntityType.CONTACT, EntityType.ACCOUNT]
    entity_id: UUID

    # take effect when entity_type is EntityType.ACCOUNT
    is_dest_account_in_move_contact_to_account_job: bool = False


class RequireOrFreeLocksForEntitiesParam(BaseModel):
    job_id: UUID
    entity_descriptors: list[IntegrityJobEntityDescriptor]
    user_id: UUID
    organization_id: UUID


class BaseAssociateContactWithAccountParam(BaseModel):
    """Base parameters for contact-account association operations"""

    integrity_job_id: UUID
    user_id: UUID
    organization_id: UUID


class AssociateSingleContactWithAccountParam(BaseAssociateContactWithAccountParam):
    """Associate a single contact with an account"""

    contact_id: UUID
    src_account_id: UUID | None
    dest_account_id: UUID
    title: str | None = None
    department: str | None = None


class CopyAssociationsFromAccountToAccountParam(BaseAssociateContactWithAccountParam):
    """Associate all contacts under one account to another account"""

    src_account_id: UUID
    dest_account_id: UUID


class CopyAssociationsFromContactToContactParam(BaseAssociateContactWithAccountParam):
    """Associate a contact to all accounts that another contact is connected to"""

    src_contact_id: UUID
    dest_contact_id: UUID


class BaseUnassociateContactWithAccountParam(BaseModel):
    """Base parameters for contact-account unassociation operations"""

    integrity_job_id: UUID
    user_id: UUID
    organization_id: UUID


class UnassociateSingleContactWithSingleAccountParam(
    BaseUnassociateContactWithAccountParam
):
    """Unassociate a specific contact from a specific account"""

    contact_id: UUID
    account_id: UUID


class UnassociateSingleContactWithAllAccountsParam(
    BaseUnassociateContactWithAccountParam
):
    """Unassociate a contact from all its associated accounts"""

    contact_id: UUID


class UnassociateContactsWithSingleAccountParam(BaseUnassociateContactWithAccountParam):
    """Unassociate all contacts associated with a specific account"""

    account_id: UUID


class MoveContactEmailsFromContactToContactParam(BaseModel):
    integrity_job_id: UUID
    src_contact_id: UUID
    dest_contact_id: UUID
    user_id: UUID
    organization_id: UUID


class MoveContactPhoneNumbersFromContactToContactParam(BaseModel):
    integrity_job_id: UUID
    src_contact_id: UUID
    dest_contact_id: UUID
    user_id: UUID
    organization_id: UUID


class MoveContactEmailsFromAccountToAccountParam(BaseModel):
    integrity_job_id: UUID
    src_account_id: UUID
    dest_account_id: UUID
    user_id: UUID
    organization_id: UUID


class MoveContactEmailsFromContactToAccountParam(BaseModel):
    integrity_job_id: UUID
    contact_id: UUID
    src_account_id: UUID
    dest_account_id: UUID
    user_id: UUID
    organization_id: UUID


class DataReplacementParam(BaseModel):
    integrity_job_id: UUID
    replaced_entity_type: EntityType
    primary_entity_id: UUID
    replaced_entity_id: UUID
    user_id: UUID
    organization_id: UUID


class DataAdditionParam(BaseModel):
    integrity_job_id: UUID
    src_entity_id: UUID
    dest_entity_id: UUID
    user_id: UUID
    organization_id: UUID


class DataMovementParam(BaseModel):
    integrity_job_id: UUID
    move_entity_id: UUID
    src_entity_id: UUID | None
    dest_entity_id: UUID
    user_id: UUID
    organization_id: UUID


class DataArchivalParam(BaseModel):
    integrity_job_id: UUID
    archive_entity_id: UUID
    user_id: UUID
    organization_id: UUID


class DataRemovalParam(BaseModel):
    integrity_job_id: UUID
    remove_entity_id: UUID
    src_entity_id: UUID
    user_id: UUID
    organization_id: UUID


DataOperationParam = Annotated[
    DataReplacementParam
    | DataMovementParam
    | DataArchivalParam
    | DataRemovalParam
    | DataAdditionParam,
    Field(
        description="parameters for different data operation",
    ),
]


class CopyPipelinesFromContactToContactParam(BaseModel):
    integrity_job_id: UUID
    primary_entity_id: UUID
    replaced_entity_id: UUID
    user_id: UUID
    organization_id: UUID


class CrmIdReplacementTrackParam(BaseModel):
    entity_type: AssociatedEntityType
    entity_id: UUID
    entity_field_name: AssociatedEntityField | None = None
    entity_field_type: AssociatedEntityFieldType | None = None
    entity_operation: AssociatedEntityOperation
    before_value: str | None = None
    after_value: str | None = None


class MovePipelineToAlternativeContactParam(BaseModel):
    integrity_job_id: UUID
    src_account_id: UUID
    moving_contact_id: UUID
    user_id: UUID
    organization_id: UUID


class MovePipelinesToNewAccountParam(BaseModel):
    integrity_job_id: UUID
    src_account_id: UUID
    new_account_id: UUID
    user_id: UUID
    organization_id: UUID


class BaseMoveAccountToStateParam(BaseModel):
    integrity_job_id: UUID
    account_id: UUID
    user_id: UUID
    organization_id: UUID


class MoveAccountToMergedStateParam(BaseMoveAccountToStateParam):
    pass


class MoveAccountToMergingStateParam(BaseMoveAccountToStateParam):
    pass


class IntegrityJobTriggerNotificationParams(BaseModel):
    """Parameters for triggering notifications."""

    integrity_job_id: UUID
    integrity_job_type: IntegrityJobType
    integrity_job_status: JobStatus
    src_entity_type: EntityType
    src_entity_id: UUID
    dest_entity_type: EntityType | None
    dest_entity_id: UUID | None
    user_id: UUID
    organization_id: UUID
    redirection_entity_type: EntityType
    redirection_entity_id: UUID
    idempotency_key: str


class PersonIntelActivityJobParams(BaseModel):
    organization_id: UUID
    contact_ids: set[UUID]


class PipelineIntelActivityJobParams(BaseModel):
    organization_id: UUID
    contact_id: UUID | None = None
    meeting_id: UUID | None = None
    global_thread_id: UUID | None = None
    pipeline_ids: set[UUID]


class GetContactPipelinesParams(BaseModel):
    organization_id: UUID
    contact_id: UUID


class GetAccountPipelinesParams(BaseModel):
    account_id: UUID
    organization_id: UUID


class CreateContactPipelineAssociationParam(BaseModel):
    user_id: UUID
    organization_id: UUID
    contact_id: UUID
    pipeline_id: UUID


WorkflowEntrypointT = Callable[..., Awaitable[Any]]  # type: ignore[explicit-any] # TODO: fix-any-annotation
WorkflowParamT = (
    MergeAccountsParam
    | MergeContactsParam
    | MoveContactToAccountParam
    | RemoveContactFromAccountParam
    | ArchiveContactParam
    | ArchiveAccountParam
    | MoveContactEmailToAccountParam
    | RemoveContactEmailFromAccountParam
    | RemoveContactEmailFromContactParam
    | MoveContactEmailToContactParam
    | AddAccountToPipelineParam
    | AddContactToPipelineParam
)
