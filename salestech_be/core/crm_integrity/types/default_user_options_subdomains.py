import typing
from collections.abc import Mapping

from frozendict import frozendict

from salestech_be.core.crm_integrity.types.request_schema import (
    CRMIntegrityJobUserOptionSingle,
)
from salestech_be.db.models.crm_integrity import (
    CRMIntegrityJobUserOption,
    CRMSubDomain,
    IntegrityJobName,
    MoveContactToAccountUserOption,
)

DEFAULT_USER_OPTIONS_BY_INTEGRITY_JOB_MAP: typing.Final[
    Mapping[IntegrityJobName, list[CRMIntegrityJobUserOptionSingle]]
] = frozendict(
    {
        IntegrityJobName.MOVE_CONTACT_TO_ACCOUNT: [
            CRMIntegrityJobUserOptionSingle(
                option_name=MoveContactToAccountUserOption.MOVE,
                description="Move data to the destination account",
                is_default=True,
            ),
            CRMIntegrityJobUserOptionSingle(
                option_name=MoveContactToAccountUserOption.RETAIN,
                description="Retain data in the the source account",
                is_default=False,
            ),
        ]
    }
)

AFFECTED_SUB_DOMAINS_BY_INTEGRITY_JOB_MAP: typing.Final[
    Mapping[IntegrityJobName, list[CRMSubDomain]]
] = frozendict(
    {
        IntegrityJobName.MOVE_CONTACT_TO_ACCOUNT: [
            CRMSubDomain.CALENDAR,
            CRMSubDomain.MEETING,
            CRMSubDomain.TASK,
            CRMSubDomain.NOTE,
            CRMSubDomain.EMAIL,
            CRMSubDomain.VOICE_CALL,
            CRMSubDomain.SEQUENCE,
        ],
        IntegrityJobName.REMOVE_CONTACT_FROM_ACCOUNT: [
            CRMSubDomain.CALENDAR,
            CRMSubDomain.MEETING,
            CRMSubDomain.EMAIL,
            CRMSubDomain.SEQUENCE,
            CRMSubDomain.TASK,
            CRMSubDomain.VOICE_CALL,
        ],
        IntegrityJobName.MERGE_ACCOUNTS: [
            CRMSubDomain.CALENDAR,
            CRMSubDomain.INTELLIGENCE,
            CRMSubDomain.TASK,
            CRMSubDomain.NOTE,
            CRMSubDomain.VOICE_CALL,
            CRMSubDomain.EMAIL,
            CRMSubDomain.MEETING,
            CRMSubDomain.SEQUENCE,
            CRMSubDomain.DOMAIN_OBJECT_LIST,
        ],
        IntegrityJobName.MERGE_CONTACTS: [
            CRMSubDomain.CALENDAR,
            CRMSubDomain.EMAIL,
            CRMSubDomain.MEETING,
            CRMSubDomain.NOTE,
            CRMSubDomain.SEQUENCE,
            CRMSubDomain.TASK,
            CRMSubDomain.INTELLIGENCE,
            CRMSubDomain.VOICE_CALL,
            CRMSubDomain.DOMAIN_OBJECT_LIST,
        ],
        IntegrityJobName.ARCHIVE_CONTACT: [
            CRMSubDomain.EMAIL,
            CRMSubDomain.SEQUENCE,
        ],
        IntegrityJobName.ARCHIVE_ACCOUNT: [
            CRMSubDomain.TASK,
            CRMSubDomain.EMAIL,
            CRMSubDomain.MEETING,
            CRMSubDomain.SEQUENCE,
        ],
        IntegrityJobName.MOVE_CONTACT_EMAIL_TO_CONTACT: [
            CRMSubDomain.SEQUENCE,
            CRMSubDomain.EMAIL,
            CRMSubDomain.MEETING,
            CRMSubDomain.CALENDAR,
        ],
        IntegrityJobName.REMOVE_CONTACT_EMAIL_FROM_CONTACT: [
            CRMSubDomain.EMAIL,
            CRMSubDomain.SEQUENCE,
            CRMSubDomain.MEETING,
        ],
        IntegrityJobName.MOVE_CONTACT_EMAIL_TO_ACCOUNT: [
            CRMSubDomain.SEQUENCE,
            CRMSubDomain.EMAIL,
            CRMSubDomain.MEETING,
            CRMSubDomain.CALENDAR,
        ],
        IntegrityJobName.REMOVE_CONTACT_EMAIL_FROM_ACCOUNT: [
            CRMSubDomain.SEQUENCE,
            CRMSubDomain.EMAIL,
            CRMSubDomain.MEETING,
            CRMSubDomain.CALENDAR,
        ],
        IntegrityJobName.ADD_ACCOUNT_TO_PIPELINE: [
            CRMSubDomain.TASK,
            CRMSubDomain.EMAIL,
            CRMSubDomain.MEETING,
            CRMSubDomain.VOICE_CALL,
        ],
        IntegrityJobName.ADD_CONTACT_TO_PIPELINE: [
            CRMSubDomain.MEETING,
        ],
    }
)

# todo: subdomains that require user intervention must be sub list of
# AFFECTED_SUB_DOMAINS_BY_INTEGRITY_JOB_MAP for each job type
# will add validation for this later
SUB_DOMAINS_REQUIRE_USER_MANUAL_UPDATE: typing.Final[
    Mapping[IntegrityJobName, list[CRMSubDomain]]
] = frozendict(
    {
        IntegrityJobName.REMOVE_CONTACT_EMAIL_FROM_CONTACT: [CRMSubDomain.MEETING],
        IntegrityJobName.ARCHIVE_ACCOUNT: [
            CRMSubDomain.TASK,
        ],
    }
)


def get_user_options_by_integrity_job_name(
    *,
    integrity_job_name: IntegrityJobName,
) -> list[CRMIntegrityJobUserOptionSingle]:
    return DEFAULT_USER_OPTIONS_BY_INTEGRITY_JOB_MAP.get(integrity_job_name, [])


def get_user_option_names_by_integrity_job_name(
    *,
    integrity_job_name: IntegrityJobName,
) -> list[CRMIntegrityJobUserOption]:
    user_options = get_user_options_by_integrity_job_name(
        integrity_job_name=integrity_job_name
    )
    return [option.option_name for option in user_options]


def get_default_user_option_by_integrity_job_name(
    *,
    integrity_job_name: IntegrityJobName,
) -> CRMIntegrityJobUserOption:
    user_options = get_user_options_by_integrity_job_name(
        integrity_job_name=integrity_job_name
    )
    if not user_options:
        raise ValueError(
            f"No user options found for integrity job name: {integrity_job_name}"
        )

    default_user_option = next(
        (option for option in user_options if option.is_default),
        None,
    )

    if not default_user_option:
        raise ValueError(
            f"No default user option found for integrity job name: {integrity_job_name}"
        )

    return default_user_option.option_name


def get_affected_sub_domains_by_integrity_job_name(
    *,
    integrity_job_name: IntegrityJobName,
) -> list[CRMSubDomain]:
    return AFFECTED_SUB_DOMAINS_BY_INTEGRITY_JOB_MAP.get(integrity_job_name, [])


def get_user_manual_update_sub_domains_by_integrity_job_name(
    *,
    integrity_job_name: IntegrityJobName,
) -> list[CRMSubDomain]:
    return SUB_DOMAINS_REQUIRE_USER_MANUAL_UPDATE.get(integrity_job_name, [])
