from typing import Literal, Self
from uuid import UUID

from pydantic import BaseModel, model_validator

from salestech_be.common.exception import InvalidArgumentError
from salestech_be.common.type.patch_request import (
    UNSET,
    BasePatchRequest,
    UnsetAware,
)
from salestech_be.db.models.crm_integrity import (
    AssociatedEntityField,
    AssociatedEntityFieldType,
    AssociatedEntityOperation,
    AssociatedEntityType,
    CRMIntegrityJobContextualParam,
    CRMIntegrityJobUserOption,
    CRMSubDomain,
    EntityType,
    IntegrityJobName,
    IntegrityJobType,
    MoveContactEmailToAccountContextualParam,
    MoveContactEmailToContactContextualParam,
    MoveContactToAccountContextualParam,
    RemoveContactEmailFromAccountContextualParam,
    RemoveContactEmailFromContactContextualParam,
    SubDomainJobStatus,
    get_integrity_job_name,
)
from salestech_be.db.models.crm_integrity import (
    IntegrityOperation as IntegrityOperationType,
)
from salestech_be.util.enum_util import NameValueStrEnum


class IntegrityJobBase(BaseModel):
    """Base model for integrity jobs."""

    job_type: IntegrityJobType
    src_entity_type: EntityType
    src_entity_id: UUID
    dest_entity_type: EntityType | None = None
    dest_entity_id: UUID | None = None
    contextual_param: CRMIntegrityJobContextualParam | None = None
    user_choices: dict[CRMSubDomain, CRMIntegrityJobUserOption | None] | None = None

    @model_validator(mode="after")
    def validate_contextual_param(  # noqa: C901, PLR0912
        self,
    ) -> Self:
        integrity_job_name = get_integrity_job_name(
            self.job_type, self.src_entity_type, self.dest_entity_type
        )

        if not self.contextual_param:
            if integrity_job_name in {
                IntegrityJobName.MOVE_CONTACT_TO_ACCOUNT,
                IntegrityJobName.MOVE_CONTACT_EMAIL_TO_ACCOUNT,
                IntegrityJobName.REMOVE_CONTACT_EMAIL_FROM_ACCOUNT,
                IntegrityJobName.MOVE_CONTACT_EMAIL_TO_CONTACT,
                IntegrityJobName.REMOVE_CONTACT_EMAIL_FROM_CONTACT,
            }:
                raise InvalidArgumentError(
                    f"contextual_param is required for {integrity_job_name}"
                )
            return self

        match integrity_job_name:
            case IntegrityJobName.MOVE_CONTACT_TO_ACCOUNT:
                if not isinstance(
                    self.contextual_param, MoveContactToAccountContextualParam
                ):
                    raise InvalidArgumentError(
                        "contextual_param must be of type MoveContactToAccountContextualParam: "
                        f"{MoveContactToAccountContextualParam.model_fields}"
                    )
            case IntegrityJobName.MOVE_CONTACT_EMAIL_TO_ACCOUNT:
                if not isinstance(
                    self.contextual_param, MoveContactEmailToAccountContextualParam
                ):
                    raise InvalidArgumentError(
                        "contextual_param must be of type MoveContactEmailToAccountContextualParam: "
                        f"{MoveContactEmailToAccountContextualParam.model_fields}"
                    )
            case IntegrityJobName.REMOVE_CONTACT_EMAIL_FROM_ACCOUNT:
                if not isinstance(
                    self.contextual_param, RemoveContactEmailFromAccountContextualParam
                ):
                    raise InvalidArgumentError(
                        "contextual_param must be of type RemoveContactEmailFromAccountContextualParam: "
                        f"{RemoveContactEmailFromAccountContextualParam.model_fields}"
                    )
            case IntegrityJobName.MOVE_CONTACT_EMAIL_TO_CONTACT:
                if not isinstance(
                    self.contextual_param, MoveContactEmailToContactContextualParam
                ):
                    raise InvalidArgumentError(
                        "contextual_param must be of type MoveContactEmailToContactContextualParam: "
                        f"{MoveContactEmailToContactContextualParam.model_fields}"
                    )
            case IntegrityJobName.REMOVE_CONTACT_EMAIL_FROM_CONTACT:
                if not isinstance(
                    self.contextual_param, RemoveContactEmailFromContactContextualParam
                ):
                    raise InvalidArgumentError(
                        "contextual_param must be of type RemoveContactEmailFromContactContextualParam: "
                        f"{RemoveContactEmailFromContactContextualParam.model_fields}"
                    )
            case _:
                if self.contextual_param is not None:
                    raise InvalidArgumentError(
                        f"contextual_param not expected for {integrity_job_name}"
                    )

        return self


class IntegrityAssociatedDataOperationBase(BaseModel):
    """Base model for associated data operations."""

    type: AssociatedEntityOperation
    entity_type: AssociatedEntityType
    entity_id: UUID
    entity_field: AssociatedEntityField | None
    entity_field_type: AssociatedEntityFieldType | None
    before_value: str | None
    after_value: str | None


class IntegritySubDomainJobBase(BaseModel):
    subdomain: CRMSubDomain
    user_choice: CRMIntegrityJobUserOption | None
    status: SubDomainJobStatus


class IntegrityOperationBase(BaseModel):
    """Base model for integrity operations."""

    # todo(yitian): set dest_entity_id and dest_entity_type as optional and update db model accordingly

    type: IntegrityOperationType
    src_entity_type: EntityType
    src_entity_id: UUID
    dest_entity_type: EntityType
    dest_entity_id: UUID


class CreateCRMIntegrityJobRequest(IntegrityJobBase):
    start_immediately: bool = True


class CreateCRMIntegrityJobResponse(BaseModel):
    job_id: UUID
    subdomain_job_ids: list[UUID]


class StartCRMIntegrityJobResponse(BaseModel):
    job_id: UUID


class PatchCRMIntegrityJobRequest(BasePatchRequest):
    require_at_least_one_specified_field = True

    user_choices: UnsetAware[dict[CRMSubDomain, CRMIntegrityJobUserOption | None]] = (
        UNSET
    )


class RetryCRMIntegrityJobResponse(BaseModel):
    job_id: UUID


class IntegrityJobPreviewRequest(IntegrityJobBase):
    """Request model for job previews."""


class IntegrityDataOperationValidationError(NameValueStrEnum):
    pipeline_has_no_alternative_contact = "pipeline_has_no_alternative_contact"

    association_to_dest_already_exists = "association_to_dest_already_exists"
    association_to_dest_does_not_exist = "association_to_dest_does_not_exist"

    contact_has_active_pipeline_association = "contact_has_active_pipeline_association"
    contact_has_active_account_association = "contact_has_active_account_association"
    contact_email_not_associated_with_contact = (
        "contact_email_not_associated_with_contact"
    )

    account_has_active_pipeline_association = "account_has_active_pipeline_association"

    object_not_found = "object_not_found"
    object_not_in_active_state = "object_not_in_active_state"
    object_has_running_integrity_job = "object_has_running_integrity_job"


class IntegrityJobValidationRequest(IntegrityJobBase):
    pass


class IntegrityOperationValidation(IntegrityOperationBase):
    violation: IntegrityDataOperationValidationError
    error_message: str


class IntegrityJobValidationResponse(BaseModel):
    validations: list[IntegrityOperationValidation]

    @property
    def is_invalid(self) -> bool:
        return bool(self.validations)


class IntegrityAssociatedDataOperationCommonPreviewIdentifier(BaseModel):
    id: UUID


class IntegrityAssociatedDataOperationGlobalThreadAccountPreviewIdentifier(BaseModel):
    id: UUID
    account_id: UUID
    global_message_ids: list[UUID]
    subject: str | None = None


class IntegrityAssociatedDataOperationGlobalThreadContactPreviewIdentifier(BaseModel):
    id: UUID
    contact_id: UUID
    global_message_ids: list[UUID]
    subject: str | None = None


class IntegrityAssociatedDataOperationVoiceCallPreviewIdentifier(BaseModel):
    id: UUID
    meeting_id: UUID | None


class IntegrityAssociatedDataOperationSequencePreviewIdentifier(BaseModel):
    id: UUID
    enrollment_ids: list[UUID]


class IntegrityAssociatedDataOperationPreviewIdentifiers(BaseModel):
    """Identifiers for associated data operations."""

    entity_type: Literal[
        AssociatedEntityType.EMAIL,
        AssociatedEntityType.NOTE,
        AssociatedEntityType.TASK,
        AssociatedEntityType.SEQUENCE,
        AssociatedEntityType.VOICE_CALL,
        AssociatedEntityType.MEETING,
        AssociatedEntityType.GLOBAL_THREAD,
    ]
    entity_ids: (
        list[IntegrityAssociatedDataOperationCommonPreviewIdentifier]
        | list[IntegrityAssociatedDataOperationGlobalThreadAccountPreviewIdentifier]
        | list[IntegrityAssociatedDataOperationGlobalThreadContactPreviewIdentifier]
        | list[IntegrityAssociatedDataOperationVoiceCallPreviewIdentifier]
        | list[IntegrityAssociatedDataOperationSequencePreviewIdentifier]
    )


class IntegrityJobPreviewResponse(BaseModel):
    """Response model for merge job previews."""

    src_entity_id: UUID
    dest_entity_id: UUID
    user_choices: dict[CRMSubDomain, CRMIntegrityJobUserOption | None] | None = None

    # contact
    archive_contact: list[IntegrityOperationBase] = []

    # account
    archive_account: list[IntegrityOperationBase] = []

    # contact <> contact_email changes
    add_contact_email_to_contact: list[IntegrityOperationBase] = []
    remove_contact_email_from_contact: list[IntegrityOperationBase] = []

    # contact_email <> account changes
    add_contact_email_to_account: list[IntegrityOperationBase] = []
    remove_contact_email_from_account: list[IntegrityOperationBase] = []

    # contact <> account changes
    associate_contact_to_account: list[IntegrityOperationBase] = []
    unassociate_contact_from_account: list[IntegrityOperationBase] = []

    # contact <> pipeline changes
    associate_contact_to_pipeline: list[IntegrityOperationBase] = []
    unassociate_contact_from_pipeline: list[IntegrityOperationBase] = []

    # account <> pipeline changes
    associate_account_to_pipeline: list[IntegrityOperationBase] = []
    unassociate_account_from_pipeline: list[IntegrityOperationBase] = []

    # associated data changes (ids only)
    associated_data_changes_identifiers: list[
        IntegrityAssociatedDataOperationPreviewIdentifiers
    ] = []

    affected_intel_pipeline_identifiers: list[
        IntegrityAssociatedDataOperationCommonPreviewIdentifier
    ] = []
    affected_intel_contact_identifiers: list[
        IntegrityAssociatedDataOperationCommonPreviewIdentifier
    ] = []

    # todo(yitian): following two to be deprecated
    associated_data_changes_ids: list[
        IntegrityAssociatedDataOperationPreviewIdentifiers
    ] = []

    contact: list[IntegrityOperationBase] = []


class CRMIntegrityJobUserOptionSingle(BaseModel):
    option_name: CRMIntegrityJobUserOption
    description: str
    is_default: bool


class IntegrityJobUserOptionsResponse(BaseModel):
    integrity_job_name: IntegrityJobName
    user_options: list[CRMIntegrityJobUserOptionSingle]
    affected_sub_domains: list[CRMSubDomain]
    manual_update_subdomains: list[CRMSubDomain]
