from datetime import datetime, timed<PERSON>ta
from typing import Annotated, Any
from uuid import UUID

from fastapi import Depends

from salestech_be.common.error_code import ErrorCode
from salestech_be.common.exception import (
    ErrorDetails,
    IllegalStateError,
    ResourceNotFoundError,
    ServiceError,
)
from salestech_be.common.lifespan import get_db_engine
from salestech_be.core.workflow.service.workflow_trigger_service_ext import (
    get_workflow_trigger_service_ext,
)
from salestech_be.core.workflow.service.workflow_trigger_service_ext_protocol import (
    WorkflowTriggerServiceExtProtocol,
)
from salestech_be.core.workflow.triggers.form_submission_trigger_service import (
    WorkflowFormSubmissionTriggerService,
)
from salestech_be.db.dao.form_repository import FormRepository
from salestech_be.db.dao.form_submission_repository import FormSubmissionRepository
from salestech_be.db.dao.oauth_repository import OauthRepository
from salestech_be.db.dao.user_integration_repository import UserIntegrationRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.dto.user_integration_dto import UserIntegrationDto
from salestech_be.db.models.account import Account
from salestech_be.db.models.address import Address
from salestech_be.db.models.contact import Contact
from salestech_be.db.models.contact_account_association import ContactAccountAssociation
from salestech_be.db.models.contact_email import (
    ContactEmail,
    ContactEmailAccountAssociation,
)
from salestech_be.db.models.form import Form, FormSource, FormSyncStatus, FormUpdate
from salestech_be.db.models.form_submission import FormSubmission
from salestech_be.db.models.oauth import OAuthUserAuth, OAuthUserAuthUpdate
from salestech_be.db.models.pipeline import Pipeline as PipelineDb
from salestech_be.db.models.task import Task as TaskDb
from salestech_be.db.models.user_integration import (
    IntegrationProvider,
    IntegrationStatus,
    IntegrationType,
)
from salestech_be.integrations.field_origin.field_origin_map import (
    FieldOriginMap,
    field_origin_map,
)
from salestech_be.integrations.field_origin.field_origin_types import ReevoObjEnum
from salestech_be.integrations.hubspot.hubspot_client import HubSpotClient
from salestech_be.integrations.hubspot.model import (
    HUBSPOT_DEFAULT_LIST_SIZE,
    HubSpotCompanyBatchResponse,
    HubSpotContactBatchResponse,
    HubSpotDealBatchResponse,
    HubSpotEntityCreateRequest,
    HubSpotEntityCreateRequestInput,
    HubSpotEntityRequest,
    HubSpotEntityUpdateRequest,
    HubSpotEntityUpdateRequestInput,
    HubSpotForm,
    HubSpotFormSubmission,
    HubSpotListBaseRequest,
    HubSpotTaskBatchResponse,
    HubSpotToken,
)
from salestech_be.ree_logging import get_logger
from salestech_be.services.auth.clients import HUBSPOT_SCOPES
from salestech_be.services.auth.encryptions import (
    EncryptionManager,
    fernet_encryption_manager,
)
from salestech_be.util.time import zoned_utc_from_timestamp, zoned_utc_now
from salestech_be.util.validation import not_none

logger = get_logger()


class HubSpotService:
    def __init__(
        self,
        hubspot_client: HubSpotClient,
        user_integration_repository: UserIntegrationRepository,
        oauth_repository: OauthRepository,
        form_repository: FormRepository,
        form_submission_repository: FormSubmissionRepository,
        encryption_manager: EncryptionManager,
        workflow_trigger_service_ext: WorkflowTriggerServiceExtProtocol,
        workflow_form_submission_trigger_service: WorkflowFormSubmissionTriggerService,
        field_origin_map: FieldOriginMap,
    ):
        self.hubspot_client = hubspot_client
        self.user_integration_repository = user_integration_repository
        self.oauth_repository = oauth_repository
        self.form_repository = form_repository
        self.form_submission_repository = form_submission_repository
        self.encryption_manager = encryption_manager
        self.workflow_trigger_service_ext = workflow_trigger_service_ext
        self.workflow_form_submission_trigger_service = (
            workflow_form_submission_trigger_service
        )
        self.field_origin_map = field_origin_map

    async def get_hubspot_integration(
        self, organization_id: UUID
    ) -> UserIntegrationDto | None:
        hubspot_user_integration_list = await self.user_integration_repository.find_user_integration_by_integration_type_org_id(
            integration_type=IntegrationType.CRM,
            integration_provider=IntegrationProvider.HUBSPOT,
            organization_id=organization_id,
        )
        if len(hubspot_user_integration_list) > 1:
            logger.bind(organization_id=organization_id).error(
                "[get_hubspot_integration] find multiple hubspot integrations."
            )
        if any(
            not user_integration_dto.user_auth
            for user_integration_dto in hubspot_user_integration_list
        ):
            raise ServiceError(
                additional_error_details=ErrorDetails(
                    code=ErrorCode.RESOURCE_NOT_FOUND,
                    details=f"HubSpot integration is missing user auth for organization {organization_id}.",
                )
            )
        return (
            hubspot_user_integration_list[0] if hubspot_user_integration_list else None
        )

    async def get_or_refresh_hubspot_access_token(self, organization_id: UUID) -> str:
        if not (
            hubspot_user_integration_dto := await self.get_hubspot_integration(
                organization_id=organization_id
            )
        ):
            raise ResourceNotFoundError(
                additional_error_details=ErrorDetails(
                    code=ErrorCode.RESOURCE_NOT_FOUND,
                    details=f"HubSpot integration not found for organization {organization_id}.",
                )
            )

        if (
            hubspot_user_integration_dto.integration_status
            != IntegrationStatus.CONNECTED
        ):
            raise IllegalStateError(
                additional_error_details=ErrorDetails(
                    code=ErrorCode.ILLEGAL_STATE,
                    details=f"HubSpot integration is not connected for organization {organization_id}.",
                )
            )
        # Directly return decrypted token if it's not expired.
        user_auth: OAuthUserAuth = not_none(hubspot_user_integration_dto.user_auth)
        if user_auth.expires_at and user_auth.expires_at > zoned_utc_now() + timedelta(
            minutes=2
        ):
            return self.encryption_manager.decrypt(not_none(user_auth.access_token))

        hubspot_token = await self.hubspot_client.refresh_token(
            refresh_token=self.encryption_manager.decrypt(
                not_none(user_auth.refresh_token)
            )
        )
        await self.update_hubspot_user_auth(
            user_auth_id=user_auth.id,
            organization_id=organization_id,
            hubspot_token=hubspot_token,
        )
        return hubspot_token.access_token

    async def update_hubspot_user_auth(
        self, user_auth_id: UUID, organization_id: UUID, hubspot_token: HubSpotToken
    ) -> OAuthUserAuth:
        if not (
            user_auth := await self.oauth_repository.update_by_tenanted_primary_key(
                OAuthUserAuth,
                organization_id=organization_id,
                primary_key_to_value={"id": user_auth_id},
                column_to_update=OAuthUserAuthUpdate(
                    access_token=self.encryption_manager.encrypt(
                        hubspot_token.access_token
                    ),
                    refresh_token=self.encryption_manager.encrypt(
                        hubspot_token.refresh_token
                    ),
                    id_token=hubspot_token.id_token,
                    expires_at=zoned_utc_from_timestamp(
                        int(zoned_utc_now().timestamp()) + hubspot_token.expires_in
                    ),
                    scopes=HUBSPOT_SCOPES,
                ),
            )
        ):
            raise ServiceError(
                additional_error_details=ErrorDetails(
                    code=ErrorCode.DB_UPDATE_ERROR,
                    details=f"OAuthUserAuth {user_auth_id} failed to update.",
                )
            )
        return user_auth

    async def sync_hubspot_forms(self, organization_id: UUID) -> list[Form]:
        access_token = await self.get_or_refresh_hubspot_access_token(
            organization_id=organization_id
        )
        hubspot_forms: list[HubSpotForm] = []
        after: str | None = None
        # Do not expect a lot of forms on HubSpot side, so fetch and load all in memory,
        # and then persist into db.
        db_forms: list[Form] = []
        while True:
            hubspot_forms_response = await self.hubspot_client.list_forms(
                access_token=access_token,
                hubspot_list_form_request=HubSpotListBaseRequest(
                    limit=HUBSPOT_DEFAULT_LIST_SIZE, after=after
                ),
            )
            hubspot_forms.extend(hubspot_forms_response.data)
            after = hubspot_forms_response.next_cursor
            if not after:
                break
        for hubspot_form in hubspot_forms:
            db_form = await self.form_repository.upsert_form(
                organization_id=organization_id, hubspot_form=hubspot_form
            )
            db_forms.append(db_form)
        logger.bind(organization_id=organization_id, total=len(hubspot_forms)).info(
            "[sync_hubspot_forms] Finished."
        )
        return db_forms

    async def sync_hubspot_form_submissions(  # noqa: C901
        self, form_id: UUID, organization_id: UUID
    ) -> None:
        # 1. get/refresh token
        access_token = await self.get_or_refresh_hubspot_access_token(
            organization_id=organization_id
        )

        # 2. Validation
        if not (
            db_form := await self.form_repository.find_by_tenanted_primary_key(
                Form, organization_id=organization_id, id=form_id
            )
        ):
            logger.bind(organization_id=organization_id, form_id=form_id).error(
                f"[sync_hubspot_form_submissions] form {form_id} not found."
            )
            return

        if db_form.source != FormSource.HUBSPOT or db_form.ext_id is None:
            logger.bind(organization_id=organization_id, form_id=form_id).error(
                f"[sync_hubspot_form_submissions] form {form_id} is not synced from HubSpot."
            )
            return

        if db_form.sync_status != FormSyncStatus.SCHEDULED:
            logger.bind(organization_id=organization_id, form_id=form_id).warning(
                f"[sync_hubspot_form_submissions] form {form_id} is not in scheduled status."
            )
            return

        # 3. Transit sync status from SCHEDULED to SYNCING
        if not await self.form_repository.update_sync_status(
            form_id=form_id,
            organization_id=organization_id,
            original_status=FormSyncStatus.SCHEDULED,
            form_update=FormUpdate(  # only update sync_status
                sync_status=FormSyncStatus.SYNCING
            ),
        ):
            raise ServiceError(
                additional_error_details=ErrorDetails(
                    code=ErrorCode.DB_UPDATE_ERROR,
                    details=f"Failed to update form {form_id} from {FormSyncStatus.SCHEDULED} to {FormSyncStatus.SYNCING}",
                )
            )

        # 4. Find the latest form submission
        # HubSpot does not have any form submissions filter,
        # The current solution is fetch all and filter out based on existing db records
        hubspot_form_submissions: list[HubSpotFormSubmission] = []
        after: str | None = None
        while True:
            hubspot_form_submissions_response = (
                await self.hubspot_client.list_form_submissions(
                    access_token=access_token,
                    hubspot_form_id=not_none(db_form.ext_id),
                    hubspot_list_form_request=HubSpotListBaseRequest(
                        limit=HUBSPOT_DEFAULT_LIST_SIZE, after=after
                    ),
                )
            )
            hubspot_form_submissions.extend(hubspot_form_submissions_response.data)
            after = hubspot_form_submissions_response.next_cursor
            if not after:
                break

        sync_from: datetime | None = db_form.sync_from
        new_sync_from: datetime | None = sync_from
        for hubspot_form_submission in hubspot_form_submissions:
            # Skip those in older timestamp.
            if sync_from and hubspot_form_submission.submitted_at <= sync_from:
                continue
            inserted_form_submission = await self.form_submission_repository.insert(
                FormSubmission.from_hubspot_form_submission(
                    form_id=form_id,
                    organization_id=organization_id,
                    hubspot_form_submission=hubspot_form_submission,
                    form_fields=db_form.form_fields,
                )
            )
            if new_sync_from:
                new_sync_from = max(
                    new_sync_from, inserted_form_submission.submitted_at
                )
            else:
                new_sync_from = inserted_form_submission.submitted_at

            # Trigger Workflow run
            await self.workflow_trigger_service_ext.produce_form_submission_trigger_event(
                form_submission_event=inserted_form_submission.to_form_submission_trigger_event(
                    form_fields=db_form.form_fields
                )
            )

        # 5. Transit sync status from SYNCING back to SCHEDULED
        if not await self.form_repository.update_sync_status(
            form_id=form_id,
            organization_id=organization_id,
            original_status=FormSyncStatus.SYNCING,
            form_update=FormUpdate(
                sync_status=FormSyncStatus.SCHEDULED,
                sync_from=new_sync_from,  # set to the new sync_from
            ),
        ):
            raise ServiceError(
                additional_error_details=ErrorDetails(
                    code=ErrorCode.DB_UPDATE_ERROR,
                    details=f"Failed to update form {form_id} from {FormSyncStatus.SYNCING} back to {FormSyncStatus.SCHEDULED}",
                )
            )

    def from_reevo_field_to_hubspot_property(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self,
        hubspot_properties_to_update: dict[str, str],
        reevo_obj: ReevoObjEnum,
        field_name: str,
        value: Any | None,
    ) -> dict[str, str]:
        if (
            (
                provider_field := self.field_origin_map.get_first_field_origin(
                    reevo_obj=reevo_obj,
                    field_name=field_name,
                )
            )
            and provider_field.provider_obj_field is not None
            and value is not None
        ):
            # Use the provider's field name from the mapping
            hubspot_properties_to_update[provider_field.provider_obj_field] = str(value)
        return hubspot_properties_to_update

    def _get_first_or_value(self, value: str | list[str] | None) -> str | None:
        """
        Returns:
            - First item of list if value is a non-empty list of strings
            - None if value is an empty list
            - Original string if value is a string
        """
        if isinstance(value, list):
            return value[0] if value else None
        return value

    def account_to_hubspot_company_properties(
        self, account: Account | None, address: Address | None
    ) -> dict[str, str]:
        company_properties_to_return: dict[str, str] = {}
        # Map reevo account fields to hubspot company properties
        if account:
            for field_name, value in account.model_dump().items():
                if field_name in {
                    "category_list",
                    "technology_list",
                }:
                    tweaked_value = self._get_first_or_value(value)
                else:
                    tweaked_value = value
                self.from_reevo_field_to_hubspot_property(
                    company_properties_to_return,
                    ReevoObjEnum.ACCOUNT,
                    field_name,
                    tweaked_value,
                )
        # Map reevo account field address to hubspot company properties
        if address:
            for field_name, value in address.model_dump().items():
                self.from_reevo_field_to_hubspot_property(
                    company_properties_to_return,
                    ReevoObjEnum.ACCOUNT,
                    field_name,
                    value,
                )
        return company_properties_to_return

    def fields_to_update_to_hubspot_company_properties(
        self, fields_to_update: dict[str, str]
    ) -> dict[str, str]:
        company_properties_to_return: dict[str, str] = {}
        for field_name, value in fields_to_update.items():
            self.from_reevo_field_to_hubspot_property(
                company_properties_to_return, ReevoObjEnum.ACCOUNT, field_name, value
            )
        return company_properties_to_return

    async def sync_push_account_create(
        self,
        organization_id: UUID,
        account: Account,
        address: Address | None = None,
    ) -> HubSpotCompanyBatchResponse:
        """Sync account create to HubSpot as a company."""
        access_token = await self.get_or_refresh_hubspot_access_token(
            organization_id=organization_id
        )
        # Convert account to HubSpot company format
        company_properties: dict[str, str] = self.account_to_hubspot_company_properties(
            account, address
        )
        # Create/update company in HubSpot
        return await self.hubspot_client.create_companies(
            access_token=access_token,
            request_payload=HubSpotEntityCreateRequest(  # properties is HubSpotEntityRequest
                inputs=[
                    HubSpotEntityCreateRequestInput(
                        properties=HubSpotEntityRequest.from_dict(company_properties)
                    ),
                ]
            ),
        )

    async def sync_push_account_update(
        self,
        organization_id: UUID,
        external_id: str,
        fields_to_update: dict[str, str],
    ) -> HubSpotCompanyBatchResponse:
        access_token = await self.get_or_refresh_hubspot_access_token(
            organization_id=organization_id
        )
        company_properties: dict[str, str] = (
            self.fields_to_update_to_hubspot_company_properties(fields_to_update)
        )
        return await self.hubspot_client.update_companies(
            access_token=access_token,
            request_payload=HubSpotEntityUpdateRequest(
                inputs=[
                    HubSpotEntityUpdateRequestInput(
                        id=external_id,
                        properties=HubSpotEntityRequest.from_dict(company_properties),
                    ),
                ]
            ),
        )

    def contact_to_hubspot_contact_properties(
        self,
        contact: Contact | None,
        contact_address: Address | None,
        primary_contact_email: ContactEmail | None,
    ) -> dict[str, str]:
        contact_properties_to_return: dict[str, str] = {}
        # Map reevo contact fields to hubspot contact properties
        if contact:
            for field_name, value in contact.model_dump().items():
                self.from_reevo_field_to_hubspot_property(
                    contact_properties_to_return,
                    ReevoObjEnum.CONTACT,
                    field_name,
                    value,
                )
        if contact_address:
            for field_name, value in contact_address.model_dump().items():
                self.from_reevo_field_to_hubspot_property(
                    contact_properties_to_return,
                    ReevoObjEnum.CONTACT,
                    field_name,
                    value,
                )
        # Add primary email if available
        if primary_contact_email:
            contact_properties_to_return["email"] = primary_contact_email.email
        return contact_properties_to_return

    def fields_to_update_to_hubspot_contact_properties(
        self, fields_to_update: dict[str, str]
    ) -> dict[str, str]:
        contact_properties_to_return: dict[str, str] = {}
        for field_name, value in fields_to_update.items():
            self.from_reevo_field_to_hubspot_property(
                contact_properties_to_return, ReevoObjEnum.CONTACT, field_name, value
            )
        return contact_properties_to_return

    async def sync_push_contact_create(
        self,
        organization_id: UUID,
        contact: Contact,
        contact_address: Address | None,
        contact_emails: list[tuple[ContactEmail, list[ContactEmailAccountAssociation]]],
        contact_account_associations: list[ContactAccountAssociation] | None = None,
    ) -> HubSpotContactBatchResponse:
        """Sync contact create to HubSpot as a contact."""
        access_token = await self.get_or_refresh_hubspot_access_token(
            organization_id=organization_id
        )
        primary_contact_email = None
        for contact_email_tuple in contact_emails:
            if contact_email_tuple[0].is_contact_primary:
                primary_contact_email = contact_email_tuple[0]
                break
        # Convert contact to HubSpot contact format
        contact_properties: dict[str, str] = self.contact_to_hubspot_contact_properties(
            contact, contact_address, primary_contact_email
        )
        # Create contact in HubSpot
        return await self.hubspot_client.create_contacts(
            access_token=access_token,
            request_payload=HubSpotEntityCreateRequest(
                inputs=[
                    HubSpotEntityCreateRequestInput(
                        properties=HubSpotEntityRequest.from_dict(contact_properties)
                    ),
                ]
            ),
        )

    async def sync_push_contact_update(
        self,
        organization_id: UUID,
        external_id: str,
        fields_to_update: dict[str, str],
    ) -> HubSpotContactBatchResponse:
        access_token = await self.get_or_refresh_hubspot_access_token(
            organization_id=organization_id
        )
        contact_properties: dict[str, str] = (
            self.fields_to_update_to_hubspot_contact_properties(fields_to_update)
        )
        return await self.hubspot_client.update_contacts(
            access_token=access_token,
            request_payload=HubSpotEntityUpdateRequest(
                inputs=[
                    HubSpotEntityUpdateRequestInput(
                        id=external_id,
                        properties=HubSpotEntityRequest.from_dict(contact_properties),
                    ),
                ]
            ),
        )

    def task_db_to_hubspot_task_properties(
        self,
        task_db: TaskDb | None,
    ) -> dict[str, str]:
        """Convert task DB fields to HubSpot task properties."""
        task_properties_to_return: dict[str, str] = {}
        if task_db:
            for field_name, value in task_db.model_dump().items():
                self.from_reevo_field_to_hubspot_property(
                    task_properties_to_return,
                    ReevoObjEnum.TASK,
                    field_name,
                    value,
                )
        return task_properties_to_return

    def fields_to_update_to_hubspot_task_properties(
        self, fields_to_update: dict[str, str]
    ) -> dict[str, str]:
        task_properties_to_return: dict[str, str] = {}
        for field_name, value in fields_to_update.items():
            self.from_reevo_field_to_hubspot_property(
                task_properties_to_return, ReevoObjEnum.TASK, field_name, value
            )
        return task_properties_to_return

    async def sync_push_task_create(
        self,
        organization_id: UUID,
        task_db: TaskDb,
    ) -> HubSpotTaskBatchResponse:
        """Sync task create to HubSpot."""
        access_token = await self.get_or_refresh_hubspot_access_token(
            organization_id=organization_id
        )
        task_properties = self.task_db_to_hubspot_task_properties(task_db)
        return await self.hubspot_client.create_tasks(
            access_token=access_token,
            request_payload=HubSpotEntityCreateRequest(
                inputs=[
                    HubSpotEntityCreateRequestInput(
                        properties=HubSpotEntityRequest.from_dict(task_properties)
                    ),
                ]
            ),
        )

    async def sync_push_task_update(
        self,
        organization_id: UUID,
        external_id: str,
        fields_to_update: dict[str, str],
    ) -> HubSpotTaskBatchResponse:
        """Sync task update to HubSpot."""
        access_token = await self.get_or_refresh_hubspot_access_token(
            organization_id=organization_id
        )
        task_properties = self.fields_to_update_to_hubspot_task_properties(
            fields_to_update
        )
        return await self.hubspot_client.update_tasks(
            access_token=access_token,
            request_payload=HubSpotEntityUpdateRequest(
                inputs=[
                    HubSpotEntityUpdateRequestInput(
                        id=external_id,
                        properties=HubSpotEntityRequest.from_dict(task_properties),
                    ),
                ]
            ),
        )

    def pipeline_db_to_hubspot_deal_properties(
        self,
        pipeline_db: PipelineDb | None,
    ) -> dict[str, str]:
        """Convert pipeline DB fields to HubSpot deal properties."""
        deal_properties_to_return: dict[str, str] = {}
        if pipeline_db:
            for field_name, value in pipeline_db.model_dump().items():
                self.from_reevo_field_to_hubspot_property(
                    deal_properties_to_return,
                    ReevoObjEnum.PIPELINE,
                    field_name,
                    value,
                )
        return deal_properties_to_return

    def fields_to_update_to_hubspot_deal_properties(
        self, fields_to_update: dict[str, str]
    ) -> dict[str, str]:
        deal_properties_to_return: dict[str, str] = {}
        for field_name, value in fields_to_update.items():
            self.from_reevo_field_to_hubspot_property(
                deal_properties_to_return,
                ReevoObjEnum.PIPELINE,
                field_name,
                value,
            )
        return deal_properties_to_return

    async def sync_push_pipeline_create(
        self,
        organization_id: UUID,
        pipeline_db: PipelineDb,
    ) -> HubSpotDealBatchResponse:
        """Sync pipeline create to HubSpot as a deal."""
        access_token = await self.get_or_refresh_hubspot_access_token(
            organization_id=organization_id
        )
        deal_properties = self.pipeline_db_to_hubspot_deal_properties(pipeline_db)
        return await self.hubspot_client.create_deals(
            access_token=access_token,
            request_payload=HubSpotEntityCreateRequest(
                inputs=[
                    HubSpotEntityCreateRequestInput(
                        properties=HubSpotEntityRequest.from_dict(deal_properties)
                    ),
                ]
            ),
        )

    async def sync_push_pipeline_update(
        self,
        organization_id: UUID,
        external_id: str,
        fields_to_update: dict[str, str],
    ) -> HubSpotDealBatchResponse:
        """Sync pipeline update to HubSpot."""
        access_token = await self.get_or_refresh_hubspot_access_token(
            organization_id=organization_id
        )
        deal_properties = self.fields_to_update_to_hubspot_deal_properties(
            fields_to_update
        )
        return await self.hubspot_client.update_deals(
            access_token=access_token,
            request_payload=HubSpotEntityUpdateRequest(
                inputs=[
                    HubSpotEntityUpdateRequestInput(
                        id=external_id,
                        properties=HubSpotEntityRequest.from_dict(deal_properties),
                    ),
                ]
            ),
        )


def get_hubspot_service_by_db_engine(
    db_engine: Annotated[DatabaseEngine, Depends(get_db_engine)],
) -> HubSpotService:
    return HubSpotService(
        hubspot_client=HubSpotClient(),
        user_integration_repository=UserIntegrationRepository(engine=db_engine),
        oauth_repository=OauthRepository(engine=db_engine),
        form_repository=FormRepository(engine=db_engine),
        form_submission_repository=FormSubmissionRepository(engine=db_engine),
        encryption_manager=fernet_encryption_manager,
        workflow_trigger_service_ext=get_workflow_trigger_service_ext(
            db_engine=db_engine
        ),
        workflow_form_submission_trigger_service=WorkflowFormSubmissionTriggerService(
            db_engine=db_engine
        ),
        field_origin_map=field_origin_map,
    )
