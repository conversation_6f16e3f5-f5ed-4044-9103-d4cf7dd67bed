from uuid import UUID

from salestech_be.core.crm_sync.service.external_sync_service import ExternalSyncService
from salestech_be.integrations.kafka.types import CDCEvent
from salestech_be.ree_logging import get_logger

logger = get_logger(__name__)


class ExternalSyncCDCEventProcessor:
    def __init__(self, external_sync_service: ExternalSyncService):
        self.external_sync_service = external_sync_service

    async def process_cdc_event(self, event: CDCEvent) -> None:
        logger.bind(event_before=event.before, event_after=event.after).info(
            "Processing cdc event for external sync"
        )

        persisted_event = await self.external_sync_service.persist_cdc_event(
            organization_id=UUID(str(event.after.organization_id)),
            event_view_model=event.view_model,
            entity_id=UUID(str(event.after.id)),
        )
        logger.bind(persisted_event=persisted_event).info("Persisted cdc event.")
