import uuid
from typing import Annotated
from uuid import UUID

from fastapi import Depends

from salestech_be.common.exception import InvalidArgumentError
from salestech_be.db.dao.crm_sync_repository import CRMSyncRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.external_sync_cdc_event import (
    ExternalSyncCdcEvent,
    ExternalSyncCdcEventStatus,
    ExternalSyncEntity,
)
from salestech_be.ree_logging import get_logger
from salestech_be.util.time import zoned_utc_now

logger = get_logger(__name__)


class ExternalSyncService:
    def __init__(
        self,
        crm_sync_repository: Annotated[CRMSyncRepository, Depends()],
    ):
        super().__init__()
        self.crm_sync_repository = crm_sync_repository

    async def persist_cdc_event(
        self,
        organization_id: UUID,
        event_view_model: str,
        entity_id: UUID,
    ) -> ExternalSyncCdcEvent | None:
        return await self.crm_sync_repository.create_external_sync_cdc_event(
            external_sync_cdc_event=ExternalSyncCdcEvent(
                id=uuid.uuid4(),
                organization_id=organization_id,
                status=ExternalSyncCdcEventStatus.UNPROCESSED,
                entity=self.match_event_view_model_to_entity(event_view_model),
                entity_id=entity_id,
                created_at=zoned_utc_now(),
            )
        )

    def match_event_view_model_to_entity(
        self, event_view_model: str
    ) -> ExternalSyncEntity:
        match event_view_model:
            case "deal_view":
                return ExternalSyncEntity.DEAL
            case "contact_view" | "contact_view_v2":
                return ExternalSyncEntity.CONTACT
            case "account_view":
                return ExternalSyncEntity.ACCOUNT
            case "task_view":
                return ExternalSyncEntity.TASK
            case "meeting_view":
                return ExternalSyncEntity.MEETING
            case "pipeline_view":
                return ExternalSyncEntity.PIPELINE
        raise InvalidArgumentError(
            f"Invalid event view model: {event_view_model}",
        )


def get_external_sync_service(
    db_engine: DatabaseEngine,
) -> ExternalSyncService:
    return ExternalSyncService(crm_sync_repository=CRMSyncRepository(engine=db_engine))
