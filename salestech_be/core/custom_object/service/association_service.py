from enum import Enum
from typing import Annotated, Any
from uuid import UUID, uuid4

from fastapi import Depends

from salestech_be.common.exception import (
    ConflictResourceError,
    IllegalStateError,
    InvalidArgumentError,
    ResourceNotFoundError,
)
from salestech_be.common.lifespan import get_db_engine
from salestech_be.common.type.metadata.common import (
    AssociationState,
    CustomObjectIdentifier,
    ObjectIdentifier,
    RelationshipType,
    StandardObjectIdentifier,
    ValidationAction,
)
from salestech_be.db.dao.custom_object_association_repository import (
    CustomObjectAssociationAuditLogRepository,
    CustomObjectAssociationRecordRepository,
    CustomObjectAssociationRepository,
)
from salestech_be.db.dao.custom_object_repository import CustomObjectRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.custom_object_association import (
    CustomObjectAssociation,
    CustomObjectAssociationAuditLog,
    CustomObjectAssociationRecord,
    UpdateField,
    UpdateFields,
)
from salestech_be.ree_logging import get_logger
from salestech_be.util.time import zoned_utc_now

logger = get_logger(__name__)


class AssociationService:
    """Service for managing associations and audit logs between custom and core objects."""

    def __init__(
        self,
        engine: DatabaseEngine,
    ) -> None:
        self.custom_object_repository = CustomObjectRepository(engine=engine)
        self.association_repository = CustomObjectAssociationRepository(engine=engine)
        self.association_audit_log_repository = (
            CustomObjectAssociationAuditLogRepository(engine=engine)
        )
        self.association_record_repository = CustomObjectAssociationRecordRepository(
            engine=engine
        )

    """ Helper functions """

    async def build_association_audit_log(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self,
        association: CustomObjectAssociation,
        updated_metadata: dict[str, Any] | None = None,
    ) -> CustomObjectAssociationAuditLog:
        """Create an association audit log for a given association."""

        audit_log = CustomObjectAssociationAuditLog(
            id=uuid4(),
            organization_id=association.organization_id,
            association_id=association.id,
            source_object_identifier=association.source_object_identifier,
            target_object_identifier=association.target_object_identifier,
            created_at=zoned_utc_now(),
            created_by_user_id=association.created_by_user_id,
            updated_at=association.updated_at if updated_metadata else None,
            updated_fields=updated_metadata["updated_fields"]
            if updated_metadata
            else None,
            updated_by_user_id=updated_metadata["updated_by_user_id"]
            if updated_metadata
            else None,
            deleted_at=association.deleted_at,
            deleted_by_user_id=association.deleted_by_user_id,
        )

        return await self.create_association_audit_log(audit_log)

    """ End of Helper functions"""

    """ CRUD operations"""

    async def validate_constraints(
        self,
        organization_id: UUID,
        source_object_identifier: ObjectIdentifier,
        target_object_identifier: ObjectIdentifier,
        association_name: str,
        max_source_records: int = -1,
        max_target_records: int = -1,
        action: ValidationAction = ValidationAction.CREATE,
    ) -> None:
        """Validate association constraints."""

        # Check if the objects exist
        await self._validate_object_existence(
            organization_id,
            source_object_identifier,
            target_object_identifier,
        )

        # Validate target and source object identifiers
        self._validate_source_and_target_object_identifiers(
            source_object_identifier, target_object_identifier
        )

        # Check if the association already exists. Only check for create action
        if action == ValidationAction.CREATE:
            await self._check_existing_associations(
                organization_id,
                source_object_identifier,
                target_object_identifier,
                association_name,
            )

        # Validate association cardinality constraints
        await self._validate_association_cardinality_constraints(
            max_source_records,
            max_target_records,
        )

    async def create_association(
        self,
        organization_id: UUID,
        user_id: UUID,
        source_object_identifier: ObjectIdentifier,
        target_object_identifier: ObjectIdentifier,
        relationship_type: RelationshipType,
        association_name: str,
        inverse_name: str = "",
        max_source_records: int = -1,
        max_target_records: int = -1,
    ) -> CustomObjectAssociation:
        """Create a new association between objects."""

        # 1. Pre-validation
        await self.validate_constraints(
            organization_id=organization_id,
            source_object_identifier=source_object_identifier,
            target_object_identifier=target_object_identifier,
            association_name=association_name,
            max_source_records=max_source_records,
            max_target_records=max_target_records,
            action=ValidationAction.CREATE,
        )

        # 2. Execute operation
        logger.info(
            "Creating new association",
            extra={
                "organization_id": organization_id,
                "source_object_identifier": source_object_identifier,
                "target_object_identifier": target_object_identifier,
                "association_name": association_name,
                "relationship_type": relationship_type,
            },
        )

        # If inverse_name is not provided, make association_name the reverse_name
        if inverse_name == "":
            inverse_name = association_name

        # Create and save association
        association = self._build_association(
            organization_id=organization_id,
            user_id=user_id,
            source_object_identifier=source_object_identifier,
            target_object_identifier=target_object_identifier,
            association_name=association_name,
            inverse_name=inverse_name,
            relationship_type=relationship_type,
            max_source_records=max_source_records,
            max_target_records=max_target_records,
        )

        try:
            result = await self.association_repository.create_association(association)
            logger.info(
                "Successfully created association",
                extra={
                    "association_id": result.id,
                    "organization_id": organization_id,
                },
            )

            # Create association audit log
            await self.build_association_audit_log(result)

            return result
        except Exception as e:
            logger.error(
                "Failed to create association",
                extra={
                    "error": str(e),
                    "organization_id": organization_id,
                    "source_object_identifier": source_object_identifier,
                    "target_object_identifier": target_object_identifier,
                },
            )
            raise

    async def get_associations_for_organization(
        self, organization_id: UUID
    ) -> list[CustomObjectAssociation]:
        """Get all associations for an organization."""
        return await self.association_repository.get_associations_for_organization(
            organization_id=organization_id
        )

    async def _validate_object_existence(
        self,
        organization_id: UUID,
        source_object_identifier: ObjectIdentifier,
        target_object_identifier: ObjectIdentifier,
    ) -> None:
        """Validate that referenced objects exist."""
        if isinstance(source_object_identifier, CustomObjectIdentifier):
            source_object = await self.custom_object_repository.get_custom_object(
                organization_id=organization_id,
                custom_object_id_or_parent_object_name=source_object_identifier.object_id,
            )
            if not source_object:
                raise IllegalStateError("Source object does not exist")

        if isinstance(target_object_identifier, CustomObjectIdentifier):
            target_object = await self.custom_object_repository.get_custom_object(
                organization_id=organization_id,
                custom_object_id_or_parent_object_name=target_object_identifier.object_id,
            )
            if not target_object:
                raise IllegalStateError("Target object does not exist")

    def _validate_source_and_target_object_identifiers(
        self,
        source_object_identifier: ObjectIdentifier,
        target_object_identifier: ObjectIdentifier,
    ) -> None:
        """Validate object types."""
        # Check against the concrete classes instead of the Annotated union type
        if not isinstance(
            source_object_identifier, CustomObjectIdentifier | StandardObjectIdentifier
        ):
            raise InvalidArgumentError(
                f"Invalid source object identifier: {source_object_identifier}"
            )
        if not isinstance(
            target_object_identifier, CustomObjectIdentifier | StandardObjectIdentifier
        ):
            raise InvalidArgumentError(
                f"Invalid target object identifier: {target_object_identifier}"
            )
        # Add validation for same source and target identifiers
        if source_object_identifier == target_object_identifier:
            raise IllegalStateError(
                f"Source and target object identifiers cannot be the same: {source_object_identifier}"
            )

    async def _check_existing_associations(
        self,
        organization_id: UUID,
        source_object_identifier: ObjectIdentifier,
        target_object_identifier: ObjectIdentifier,
        association_name: str,
    ) -> None:
        """Check for existing associations between objects.

        Args:
            organization_id: Organization ID
            source_object_identifier: Source object identifier
            target_object_identifier: Target object identifier
            association_name: Association name

        Raises:
            ConflictResourceError: If an association already exists with the same
                organization_id, source_object_identifier, target_object_identifier, and association_name.
                Note: The same source and target objects can have multiple associations if they have different names.
        """
        # Check for existing association with same org and source object identifier
        existing = await self.association_repository.get_associations_for_object(
            organization_id=organization_id,
            object_identifier=source_object_identifier,
        )
        for association in existing:  # Match the target object identifier
            if (
                association.target_object_identifier == target_object_identifier
                and association.association_name == association_name
            ):
                if association.deleted_at is not None:
                    # If the association is deleted, we can create a new one
                    return
                if association.state == AssociationState.ACTIVE:
                    raise ConflictResourceError(
                        f"An active association already exists between source object {source_object_identifier} and target object {target_object_identifier} with an association name: {association_name}"
                    )
                raise ConflictResourceError(
                    f"A disabled association already exists between source object {source_object_identifier} and target object {target_object_identifier} with an association name: {association_name}. "
                    "The existing association must be deleted before creating a new one"
                )

    async def _validate_association_cardinality_constraints(
        self,
        max_source_records: int,
        max_target_records: int,
    ) -> None:
        """Validate association cardinality constraints."""
        if max_source_records != -1 and max_source_records < 1:
            raise InvalidArgumentError(
                "max_source_records must be -1 (infinite) or >= 1"
            )
        if max_target_records != -1 and max_target_records < 1:
            raise InvalidArgumentError(
                "max_target_records must be -1 (infinite) or >= 1"
            )

    # This works. Validated end-to-end.
    def _build_association(
        self,
        organization_id: UUID,
        user_id: UUID,
        source_object_identifier: ObjectIdentifier,
        target_object_identifier: ObjectIdentifier,
        association_name: str,
        inverse_name: str,
        relationship_type: RelationshipType,
        max_source_records: int,
        max_target_records: int,
    ) -> CustomObjectAssociation:
        """Build a new CustomObjectAssociation instance."""
        return CustomObjectAssociation(
            id=uuid4(),
            organization_id=organization_id,
            source_object_identifier=source_object_identifier,
            target_object_identifier=target_object_identifier,
            association_name=association_name,
            inverse_name=inverse_name,
            relationship_type=relationship_type,
            state=AssociationState.ACTIVE,
            max_source_records=max_source_records,
            max_target_records=max_target_records,
            created_at=zoned_utc_now(),
            created_by_user_id=user_id,
        )

    # Read
    async def get_association(
        self,
        organization_id: UUID,
        source_object_identifier: ObjectIdentifier,
        target_object_identifier: ObjectIdentifier,
        association_id: UUID,
    ) -> CustomObjectAssociation:
        """Get an association between two objects.

        Args:
            organization_id: Organization ID
            source_object_identifier: Source object identifier
            target_object_identifier: Target object identifier
            association_id: Association ID
        Returns:
            CustomObjectAssociation if found

        Raises:
            ResourceNotFoundError: If no association exists between the specified objects
        """
        association = await self.association_repository.get_association(
            organization_id=organization_id,
            source_object_identifier=source_object_identifier,
            target_object_identifier=target_object_identifier,
            association_id=association_id,
        )

        if not association:
            raise ResourceNotFoundError(
                f"Association not found between source object identifier: {source_object_identifier} "
                f"and target object identifier: {target_object_identifier} "
                f"and association id: {association_id} "
                f"for organization (id={organization_id})"
            )

        return association

    async def get_associations_for_object(
        self,
        organization_id: UUID,
        object_identifier: ObjectIdentifier,
    ) -> list[CustomObjectAssociation]:
        """Get all active, non-deleted associations for an object. Return empty if no associations exist.

        Args:
            organization_id: Organization ID
            object_identifier: Object identifier
        Returns:
            List of active, non-deleted associations where the object is either source or target

        Example:
            If Invoice (id=123) has associations with Contact and Payment:
            - Invoice <-> Contact
            - Invoice <-> Payment
            Calling get_association_for_object(org_id, invoice_id, CUSTOM)
            returns both associations
        """
        return await self.association_repository.get_associations_for_object(
            organization_id=organization_id,
            object_identifier=object_identifier,
        )

    async def update_association(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self,
        organization_id: UUID,
        user_id: UUID,
        source_object_identifier: ObjectIdentifier,
        target_object_identifier: ObjectIdentifier,
        association_id: UUID,
        update_data: dict[str, Any],
    ) -> CustomObjectAssociation:
        """Update an association between objects."""

        # First get the existing association to track changes
        existing_association = await self.get_association(
            organization_id=organization_id,
            source_object_identifier=source_object_identifier,
            target_object_identifier=target_object_identifier,
            association_id=association_id,
        )

        # Validate cardinality constraints if association is active
        if existing_association.state == AssociationState.ACTIVE:
            await self.validate_constraints(
                organization_id=organization_id,
                source_object_identifier=source_object_identifier,
                target_object_identifier=target_object_identifier,
                association_name=existing_association.association_name,
                max_source_records=update_data.get(
                    "max_source_records", existing_association.max_source_records
                ),
                max_target_records=update_data.get(
                    "max_target_records", existing_association.max_target_records
                ),
                action=ValidationAction.UPDATE,
            )

        # Get current timestamp for all updates
        current_time = zoned_utc_now()

        # Create a copy of update_data to avoid modifying the input
        update_data_with_metadata = update_data.copy()
        update_data_with_metadata["updated_at"] = current_time
        update_data_with_metadata["updated_by_user_id"] = user_id

        # Update the association
        updated_association = await self.association_repository.update_association(
            organization_id=organization_id,
            association_id=association_id,
            user_id=user_id,
            source_object_identifier=source_object_identifier,
            target_object_identifier=target_object_identifier,
            update_data=update_data_with_metadata,
        )

        # Track field changes - exclude metadata fields
        metadata_fields = {"updated_at", "updated_by_user_id"}
        update_fields: list[UpdateField] = []

        for (
            field,
            new_value,
        ) in (
            update_data.items()
        ):  # Use original update_data to track only user-specified changes
            if field in metadata_fields:
                continue

            old_value = getattr(existing_association, field)
            if old_value != new_value:
                # Convert enum values to their string representation
                old_value_str = (
                    str(old_value.value)
                    if isinstance(old_value, Enum)
                    else str(old_value)
                )
                new_value_str = (
                    str(new_value.value)
                    if isinstance(new_value, Enum)
                    else str(new_value)
                )

                update_fields.append(
                    UpdateField(
                        updated_field=field,
                        old_value=old_value_str,
                        new_value=new_value_str,
                    )
                )

        # Create metadata for the update audit log using the same timestamp
        update_metadata = {
            "updated_at": current_time,  # Use same timestamp
            "updated_fields": UpdateFields(updated_fields=update_fields),
            "updated_by_user_id": user_id,
        }

        # Create an association audit log for this update
        await self.build_association_audit_log(updated_association, update_metadata)

        return updated_association

    """ Association State Management """

    async def disable_association(
        self,
        organization_id: UUID,
        user_id: UUID,
        source_object_identifier: ObjectIdentifier,
        target_object_identifier: ObjectIdentifier,
        association_id: UUID,
    ) -> CustomObjectAssociation:
        """Disable an association between two objects."""
        association = await self.get_association(
            organization_id=organization_id,
            source_object_identifier=source_object_identifier,
            target_object_identifier=target_object_identifier,
            association_id=association_id,
        )

        if association.state == AssociationState.DISABLED:
            raise IllegalStateError("Association is already disabled")
        elif association.state == AssociationState.ACTIVATING:
            raise IllegalStateError("Association is currently being activated")
        else:  # Association is active
            return await self.update_association(
                organization_id=organization_id,
                user_id=user_id,
                source_object_identifier=source_object_identifier,
                target_object_identifier=target_object_identifier,
                association_id=association_id,
                update_data={"state": AssociationState.DISABLED},
            )

    async def activate_association(
        self,
        organization_id: UUID,
        user_id: UUID,
        source_object_identifier: ObjectIdentifier,
        target_object_identifier: ObjectIdentifier,
        association_id: UUID,
    ) -> CustomObjectAssociation:
        """Activate an association between two objects."""
        association = await self.get_association(
            organization_id=organization_id,
            source_object_identifier=source_object_identifier,
            target_object_identifier=target_object_identifier,
            association_id=association_id,
        )

        if association.state == AssociationState.ACTIVE:
            raise IllegalStateError("Association is already active")
        elif association.state == AssociationState.ACTIVATING:
            raise IllegalStateError("Association is currently being activated")
        else:  # Association is disabled
            return await self.update_association(
                organization_id=organization_id,
                user_id=user_id,
                source_object_identifier=source_object_identifier,
                target_object_identifier=target_object_identifier,
                association_id=association_id,
                update_data={"state": AssociationState.ACTIVATING},
            )

    async def process_association_activation(
        self,
        organization_id: UUID,
        user_id: UUID,
        source_object_identifier: ObjectIdentifier,
        target_object_identifier: ObjectIdentifier,
        association_id: UUID,
    ) -> CustomObjectAssociation:
        """Process the activation of an association."""
        association = await self.get_association(
            organization_id=organization_id,
            source_object_identifier=source_object_identifier,
            target_object_identifier=target_object_identifier,
            association_id=association_id,
        )

        if association.state == AssociationState.ACTIVE:
            raise IllegalStateError("Association is already active")
        elif association.state == AssociationState.DISABLED:
            raise IllegalStateError("Association is disabled")
        else:  # Association is activating
            # Validate association constraints and association record constraints, set to active if there's no error
            try:
                # Validate association constraints
                await self.validate_constraints(
                    organization_id=organization_id,
                    source_object_identifier=source_object_identifier,
                    target_object_identifier=target_object_identifier,
                    association_name=association.association_name,
                    max_source_records=association.max_source_records,
                    max_target_records=association.max_target_records,
                    action=ValidationAction.UPDATE,
                )

                logger.info(
                    "No error validating association constraints. Checking association record constraints next.",
                    extra={"association_id": association_id},
                )

                # Validate association record constraints
                # Get all association records linked to that association
                records = await self.get_records(association=association)
                for r in records:
                    await self.validate_association_record_constraints(
                        organization_id=organization_id,
                        association=association,
                        source_record_id=r.source_record_id,
                        target_record_id=r.target_record_id,
                    )

                    logger.info(
                        "No error validating association record constraints. Setting association to active.",
                        extra={"association_id": association_id},
                    )

                return await self.update_association(
                    organization_id=organization_id,
                    user_id=user_id,
                    source_object_identifier=source_object_identifier,
                    target_object_identifier=target_object_identifier,
                    association_id=association_id,
                    update_data={"state": AssociationState.ACTIVE},
                )
            except Exception:
                logger.exception(
                    "Error validating constraints. Setting association to disabled."
                )

                return await self.update_association(
                    organization_id=organization_id,
                    user_id=user_id,
                    source_object_identifier=source_object_identifier,
                    target_object_identifier=target_object_identifier,
                    association_id=association_id,
                    update_data={"state": AssociationState.DISABLED},
                )

    """ End of Association State Management """

    async def delete_association(
        self,
        organization_id: UUID,
        user_id: UUID,
        source_object_identifier: ObjectIdentifier,
        target_object_identifier: ObjectIdentifier,
        association_id: UUID,
    ) -> CustomObjectAssociation:
        """Delete an association between two objects.
        Note this is a soft delete. We set 'deleted_at' to the current time.

        Args:
            organization_id: Organization ID
            user_id: User ID
            source_object_identifier: Source object identifier
            target_object_identifier: Target object identifier
            association_id: Association ID
        Returns:
            The deleted CustomObjectAssociation

        Raises:
            ResourceNotFoundError: If no association exists between the specified objects
        """
        try:
            # First get the association to verify it exists
            await self.get_association(
                organization_id=organization_id,
                source_object_identifier=source_object_identifier,
                target_object_identifier=target_object_identifier,
                association_id=association_id,
            )
        except ResourceNotFoundError:
            logger.error(
                "Cannot delete non-existent association",
                extra={
                    "organization_id": organization_id,
                    "source_object_identifier": source_object_identifier,
                    "target_object_identifier": target_object_identifier,
                    "associaiton_id": association_id,
                },
            )
            raise

        logger.info(
            "Attempting to delete association",
            extra={
                "association_id": association_id,
                "organization_id": organization_id,
                "source_object_identifier": source_object_identifier,
                "target_object_identifier": target_object_identifier,
            },
        )

        try:
            # Get all the association records for this association
            association_records_to_delete = (
                await self.get_records_by_object_identifiers(
                    organization_id=organization_id,
                    association_id=association_id,
                    source_object_identifier=source_object_identifier,
                    target_object_identifier=target_object_identifier,
                )
            )

            # Delete all the association records for this association
            for record in association_records_to_delete:
                await self.delete_association_record(
                    organization_id=organization_id,
                    user_id=user_id,
                    association_id=association_id,
                    source_record_id=record.source_record_id,
                    target_record_id=record.target_record_id,
                )

            # Then, delete the association
            association_to_delete = (
                await self.association_repository.delete_association(
                    organization_id=organization_id,
                    association_id=association_id,
                    user_id=user_id,
                    source_object_identifier=source_object_identifier,
                    target_object_identifier=target_object_identifier,
                )
            )
            logger.info(
                "Successfully deleted association",
                extra={
                    "association_id": association_to_delete.id,
                    "deleted_at": association_to_delete.deleted_at,
                    "deleted_by_user_id": association_to_delete.deleted_by_user_id,
                },
            )
            logger.info(f"association_to_delete: {association_to_delete}")

            # Create association audit log
            await self.build_association_audit_log(association_to_delete)
            logger.info(
                "Successfully added association audit log for deleted association.",
                extra={
                    "organization_id": association_to_delete.organization_id,
                    "association_id": association_to_delete.id,
                    "source_object_identifier": association_to_delete.source_object_identifier,
                    "target_object_identifier": association_to_delete.target_object_identifier,
                    "deleted_at": association_to_delete.deleted_at,
                    "deleted_by_user_id": association_to_delete.deleted_by_user_id,
                },
            )

            return association_to_delete
        except IllegalStateError as e:
            logger.error(
                "Failed to delete association",
                extra={
                    "error": str(e),
                    "association_id": association_id,
                },
            )
            raise ResourceNotFoundError(
                f"Association {association_id} not found or already deleted"
            ) from e
        except Exception as e:
            logger.error(
                "Unexpected error deleting association",
                extra={
                    "error": str(e),
                    "association_id": association_id,
                },
            )
            raise

    """ End of CRUD operations """

    """ Association Record Service """

    async def validate_association_record_constraints(
        self,
        organization_id: UUID,
        association: CustomObjectAssociation,
        source_record_id: UUID,
        target_record_id: UUID,
    ) -> None:
        """Validate cardinality constraints for records in an association.

        Args:
            organization_id: Organization ID
            association: The association to validate
            source_record_id: The source record ID
            target_record_id: The target record ID

        For a given association:
        - max_source_records: Maximum number of source records that can be associated with the same target record
        - max_target_records: Maximum number of target records that can be associated with the same source record
        """
        if association.max_source_records != -1:
            # Count how many source records are already associated with this target record
            number_of_source_records = (
                await self.association_record_repository.count_records_by_target(
                    organization_id=organization_id,
                    association_id=association.id,
                    target_record_id=target_record_id,
                )
            )

            if number_of_source_records >= association.max_source_records:
                raise IllegalStateError(
                    f"Target record already has maximum number of source records ({association.max_source_records})"
                )

        if association.max_target_records != -1:
            # Count how many target records are already associated with this source record
            number_of_target_records = (
                await self.association_record_repository.count_records_by_source(
                    organization_id=organization_id,
                    association_id=association.id,
                    source_record_id=source_record_id,
                )
            )

            if number_of_target_records >= association.max_target_records:
                raise IllegalStateError(
                    f"Source record already has maximum number of target records ({association.max_target_records})"
                )

    async def create_association_record(
        self,
        organization_id: UUID,
        user_id: UUID,
        association: CustomObjectAssociation,
        source_record_id: UUID,
        target_record_id: UUID,
    ) -> CustomObjectAssociationRecord:
        """Create a new association record between two objects.

        Args:
            organization_id: Organization ID
            user_id: User ID performing the operation
            association: The association to create a record for

        Returns:
            The created association record

        Raises:
            ConflictResourceError: If record already exists
        """

        # 1. Validate cardinality constraints if association is active
        if association.state == AssociationState.ACTIVE:
            await self.validate_association_record_constraints(
                organization_id=organization_id,
                association=association,
                source_record_id=source_record_id,
                target_record_id=target_record_id,
            )

        # 2. Create the record
        record = CustomObjectAssociationRecord(
            id=uuid4(),
            organization_id=organization_id,
            association_id=association.id,
            # Source details
            source_object_identifier=association.source_object_identifier,
            source_record_id=source_record_id,
            # Target details
            target_object_identifier=association.target_object_identifier,
            target_record_id=target_record_id,
            # Audit fields
            created_at=zoned_utc_now(),
            created_by_user_id=user_id,
        )

        return await self.association_record_repository.create_record(record)

    async def get_association_record_by_record_ids(
        self,
        organization_id: UUID,
        association_id: UUID,
        source_record_id: UUID,
        target_record_id: UUID,
    ) -> CustomObjectAssociationRecord:
        """
        Get a specific association record by record IDs.

        Args:
            organization_id: Organization ID
            association_id: Association ID
            source_record_id: Source record ID
            target_record_id: Target record ID

        Returns:
            The matching CustomObjectAssociationRecord
        """

        record = await self.association_record_repository.get_record_by_record_ids(
            organization_id, association_id, source_record_id, target_record_id
        )
        if record.deleted_at is not None:
            raise IllegalStateError("Association record not found or deleted")
        return record

    async def get_records_by_object_identifiers(
        self,
        organization_id: UUID,
        association_id: UUID,
        source_object_identifier: ObjectIdentifier,
        target_object_identifier: ObjectIdentifier,
    ) -> list[CustomObjectAssociationRecord]:
        """
        Gets records by source and target object IDs.

        Args:
            source_object_identifier: The source object identifier
            target_object_identifier: The target object identifier

        Returns:
            The records for the source and target object IDs
        """
        return (
            await self.association_record_repository.get_records_by_object_identifiers(
                organization_id=organization_id,
                association_id=association_id,
                source_object_identifier=source_object_identifier,
                target_object_identifier=target_object_identifier,
            )
        )

    async def get_records(
        self, association: CustomObjectAssociation
    ) -> list[CustomObjectAssociationRecord]:
        """
        Gets all records for an association.

        Args:
            association: The association to get records for

        Returns:
            The records for the association
        """
        return await self.association_record_repository.get_records(association)

    async def update_association_record(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self,
        organization_id: UUID,
        user_id: UUID,
        association_id: UUID,
        source_record_id: UUID,
        target_record_id: UUID,
        association_name: str,
        update_data: dict[str, Any],
    ) -> CustomObjectAssociationRecord:
        """Update an association record's record IDs.

        Args:
            organization_id: Organization ID
            user_id: User ID performing the update
            association_id: Association ID
            source_record_id: Current source record ID
            target_record_id: Current target record ID
            association_name: Name of the association
            update_data: Dictionary containing new values for source_record_id and/or target_record_id

        Returns:
            The updated association record

        Raises:
            IllegalStateError: If record not found, already deleted, or validation fails
            ValueError: If update_data contains invalid fields
        """
        try:
            # 1. Verify record exists and is not deleted
            record = await self.get_association_record_by_record_ids(
                organization_id=organization_id,
                association_id=association_id,
                source_record_id=source_record_id,
                target_record_id=target_record_id,
            )
            if record.deleted_at is not None:
                raise IllegalStateError("Association record not found or deleted")

            # 2. Verify association exists and is active
            association = await self.get_association(
                organization_id=organization_id,
                source_object_identifier=record.source_object_identifier,
                target_object_identifier=record.target_object_identifier,
                association_id=association_id,
            )
            if association.deleted_at is not None:
                raise IllegalStateError(
                    "Cannot update record - parent association is deleted"
                )

            # 3. Execute update
            updated_record = await self.association_record_repository.update_record(
                organization_id=organization_id,
                user_id=user_id,
                association_id=association_id,
                source_record_id=source_record_id,
                target_record_id=target_record_id,
                update_data=update_data,
            )

            logger.info(
                "Successfully updated association record",
                extra={
                    "organization_id": organization_id,
                    "association_id": association_id,
                    "source_record_id": source_record_id,
                    "target_record_id": target_record_id,
                    "updated_by_user_id": user_id,
                    "record_id": updated_record.id,
                    "association_name": association_name,
                    "update_data": update_data,
                },
            )

            return updated_record

        except (ValueError, IllegalStateError) as e:
            logger.error(
                "Failed to update association record - validation error",
                extra={
                    "error": str(e),
                    "organization_id": organization_id,
                    "association_id": association_id,
                    "source_record_id": source_record_id,
                    "target_record_id": target_record_id,
                    "user_id": user_id,
                    "update_data": update_data,
                    "association_name": association_name,
                },
            )
            raise

        except Exception as e:
            logger.error(
                "Unexpected error updating association record",
                extra={
                    "error": str(e),
                    "error_type": type(e).__name__,
                    "organization_id": organization_id,
                    "association_id": association_id,
                    "source_record_id": source_record_id,
                    "target_record_id": target_record_id,
                    "user_id": user_id,
                    "update_data": update_data,
                    "association_name": association_name,
                },
            )
            raise IllegalStateError(
                f"Failed to update association record: {e!s}"
            ) from e

    async def remap_association_record(
        self,
        organization_id: UUID,
        user_id: UUID,
        association: CustomObjectAssociation,
        source_record_id: UUID,
        target_record_ids: list[UUID],
    ) -> list[CustomObjectAssociationRecord]:
        """Remap a source record to a new set of target records.

        Args:
            organization_id: Organization ID
            user_id: User ID performing the remap
            association_id: Association ID
            source_record_id: Source record ID
            target_record_ids: List of target record IDs to remap to

        Returns:
            List of updated CustomObjectAssociationRecord objects

        Raises:
            IllegalStateError: If record not found or already deleted
        """

        return await self.association_record_repository.remap_record(
            organization_id=organization_id,
            user_id=user_id,
            association=association,
            source_record_id=source_record_id,
            target_record_ids=target_record_ids,
        )

    async def delete_association_record(
        self,
        organization_id: UUID,
        user_id: UUID,
        association_id: UUID,
        source_record_id: UUID,
        target_record_id: UUID,
    ) -> CustomObjectAssociationRecord:
        """Delete (soft-delete) an association record.

        Args:
            organization_id: Organization ID
            user_id: User ID performing the deletion
            association_id: Association ID
            source_record_id: Source record ID
            target_record_id: Target record ID

        Returns:
            The deleted association record

        Raises:
            IllegalStateError: If record not found, already deleted, or validation fails
        """
        try:
            # 1. Verify record exists and is not deleted
            record = await self.get_association_record_by_record_ids(
                organization_id=organization_id,
                association_id=association_id,
                source_record_id=source_record_id,
                target_record_id=target_record_id,
            )

            # 2. Verify association exists and is active
            association = await self.get_association(
                organization_id=organization_id,
                source_object_identifier=record.source_object_identifier,
                target_object_identifier=record.target_object_identifier,
                association_id=association_id,
            )
            if association.deleted_at is not None:
                raise IllegalStateError(
                    "Cannot delete record - parent association is deleted"
                )

            # 3. Execute deletion
            deleted_record = await self.association_record_repository.delete_record(
                organization_id=organization_id,
                user_id=user_id,
                association_id=association_id,
                source_record_id=source_record_id,
                target_record_id=target_record_id,
            )

            logger.info(
                "Successfully deleted association record",
                extra={
                    "organization_id": organization_id,
                    "association_id": association_id,
                    "source_record_id": source_record_id,
                    "target_record_id": target_record_id,
                    "deleted_by_user_id": user_id,
                    "record_id": deleted_record.id,
                },
            )

            return deleted_record

        except IllegalStateError as e:
            logger.error(
                "Failed to delete association record - validation error",
                extra={
                    "error": str(e),
                    "organization_id": organization_id,
                    "association_id": association_id,
                    "source_record_id": source_record_id,
                    "target_record_id": target_record_id,
                    "user_id": user_id,
                },
            )
            raise

        except Exception as e:
            logger.error(
                "Unexpected error deleting association record",
                extra={
                    "error": str(e),
                    "error_type": type(e).__name__,
                    "organization_id": organization_id,
                    "association_id": association_id,
                    "source_record_id": source_record_id,
                    "target_record_id": target_record_id,
                    "user_id": user_id,
                },
            )
            raise IllegalStateError(
                f"Failed to delete association record: {e!s}"
            ) from e

    """ End of Association Record Service """

    """ Association Audit Log Service """

    async def create_association_audit_log(
        self, data: CustomObjectAssociationAuditLog
    ) -> CustomObjectAssociationAuditLog:
        """Create a new association audit log with validation.

        Args:
            data: The association audit log to create

        Returns:
            The created association audit log

        Raises:
            ConflictResourceError: If the audit log cannot be created
        """
        async with self.custom_object_repository.engine.begin():
            # 1. Pre-validation
            await self.validate_audit_log_constraints(data)

            # 2. Execute operation
            result = await self.insert_audit_log(data)

            # 3. Post-verification
            await self.verify_constraints(result)

            # 4. Return result (transaction commits if no errors)
            return result

    async def validate_audit_log_constraints(
        self, data: CustomObjectAssociationAuditLog
    ) -> None:
        """Validate constraints before creating association audit log.

        Args:
            data: The association audit log to validate
        """
        # TODO: Implement validation logic

    async def insert_audit_log(
        self, data: CustomObjectAssociationAuditLog
    ) -> CustomObjectAssociationAuditLog:
        """Insert a new association audit log between specific instances."""
        return await self.association_audit_log_repository.create_association_audit_log(
            data
        )

    async def verify_constraints(
        self, audit_log: CustomObjectAssociationAuditLog
    ) -> None:
        """Verify constraints after creating association audit log.

        Args:
            audit_log: The created association audit log to verify
        """
        # TODO: Implement verification logic

    # Used for testing and validating cross-table relationships from CustomObjectAssociationAuditLog and CustomObjectAssociation
    async def get_association_audit_logs(
        self,
        association: CustomObjectAssociation,
    ) -> list[CustomObjectAssociationAuditLog]:
        """Get all the association audit logs by organization ID and association ID.
        Args:
            association: CustomObjectAssociation,
        Returns:
            The association audit logs
        Raises:
            ResourceNotFoundError: If audit logs not found
        """

        audit_logs = (
            await self.association_audit_log_repository.get_association_audit_logs(
                association=association,
            )
        )

        def get_identifier_str(
            identifier: StandardObjectIdentifier | CustomObjectIdentifier,
        ) -> str:
            if isinstance(identifier, CustomObjectIdentifier):
                return f"id={identifier.object_id}"
            return f"name={identifier.object_name}"

        if not audit_logs:
            raise ResourceNotFoundError(
                f"Association audit logs not found for organization (id={association.organization_id}), "
                f"association (id={association.id}), "
                f"source object ({get_identifier_str(association.source_object_identifier)}), "
                f"target object ({get_identifier_str(association.target_object_identifier)})"
            )

        return audit_logs

    """ End of Association Audit Log Service """

    async def list_associations_by_source_ids(
        self,
        organization_id: UUID,
        association_id: UUID,
        source_ids: list[UUID],
    ) -> list[CustomObjectAssociationRecord]:
        """Get all association records for multiple source IDs.

        Args:
            organization_id: Organization ID
            association_id: The association definition ID
            source_ids: List of source record IDs to fetch associations for

        Returns:
            List of association records for the given source IDs

        Raises:
            ResourceNotFoundError: If association not found
        """
        # First verify the association exists
        association = await self.get_association_by_id(
            organization_id=organization_id,
            association_id=association_id,
        )

        # Then get all records for this association and these source IDs
        return await self.association_record_repository.list_records_by_source_ids(
            organization_id=organization_id,
            association_id=association.id,
            source_ids=source_ids,
        )

    # @log_timing(logger=logger)
    async def get_association_by_id(
        self,
        organization_id: UUID,
        association_id: UUID,
    ) -> CustomObjectAssociation:
        """Get an association by its ID.

        Args:
            organization_id: Organization ID
            association_id: The association ID to fetch

        Returns:
            The association if found

        Raises:
            ResourceNotFoundError: If no association exists with this ID
        """
        association = await self.association_repository.get_association_by_id(
            organization_id=organization_id,
            association_id=association_id,
        )

        if not association:
            raise ResourceNotFoundError(
                f"Association not found with id {association_id} "
                f"for organization {organization_id}"
            )

        return association


def get_association_service(
    db_engine: Annotated[DatabaseEngine, Depends(get_db_engine)],
) -> AssociationService:
    """Get an instance of AssociationService with proper dependency injection."""
    return AssociationService(engine=db_engine)
