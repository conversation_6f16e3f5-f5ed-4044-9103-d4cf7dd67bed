from typing import Protocol
from uuid import UUID

from salestech_be.common.type.metadata.schema import FieldReference
from salestech_be.core.custom_object.type.extendable_standard_object import (
    ExtendableStandardObject,
)
from salestech_be.core.data.util import ObjectRecordFetchConditions
from salestech_be.db.dto.custom_object_data_dto import CustomObjectDataListDto
from salestech_be.db.dto.custom_object_dto import CustomObjectDto
from salestech_be.db.models.custom_object_association import (
    CustomObjectAssociationRecord,
)


class CustomObjectQueryInterface(Protocol):
    async def get_custom_object(
        self,
        organization_id: UUID,
        custom_object_id_or_parent_object_name: UUID | ExtendableStandardObject,
    ) -> CustomObjectDto: ...

    async def list_custom_object_data(
        self,
        organization_id: UUID,
        custom_object_id_or_parent_object_name: UUID | ExtendableStandardObject,
        fetch_conditions: ObjectRecordFetchConditions
        | list[FieldReference]
        | None = None,
    ) -> CustomObjectDataListDto: ...


class AssociationQueryInterface(Protocol):
    async def list_associations_by_source_ids(
        self,
        organization_id: UUID,
        association_id: UUID,
        source_ids: list[UUID],
    ) -> list[CustomObjectAssociationRecord]: ...
