from collections import defaultdict
from typing import Annotated
from uuid import UUID
from venv import logger

from fastapi import Depends

from salestech_be.common.lifespan import get_db_engine
from salestech_be.common.query_util.filter_schema import FilterSpec
from salestech_be.common.query_util.sort_schema import SortingSpec
from salestech_be.common.singleton import Singleton
from salestech_be.common.type.metadata.common import (
    ObjectIdentifier,
)
from salestech_be.common.type.metadata.schema import FieldReference
from salestech_be.core.custom_object.service.association_service import (
    get_association_service,
)
from salestech_be.core.custom_object.type.extendable_standard_object import (
    ExtendableStandardObject,
)
from salestech_be.core.data.util import (
    ObjectRecordFetchConditions,
    extract_field,
    extract_field_references_from_field_set,
)
from salestech_be.db.dao.custom_object_repository import CustomObjectRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.dto.custom_object_dto import CustomObjectDto
from salestech_be.db.models.custom_object import (
    CustomObject,
)
from salestech_be.db.models.custom_object_data import (
    CustomObjectData,
)
from salestech_be.util.validation import not_none


class CustomObjectQueryService:
    def __init__(self, db_engine: Annotated[DatabaseEngine, Depends(get_db_engine)]):
        self.association_service = get_association_service(db_engine=db_engine)
        self.custom_object_repository = CustomObjectRepository(engine=db_engine)

    async def list_custom_object_data_by_custom_object_id(
        self,
        custom_object_id: UUID,
        organization_id: UUID,
        record_id: UUID | None = None,
    ) -> list[CustomObjectData]:
        return await self.custom_object_repository.list_custom_object_data_by_custom_object_id(
            custom_object_id=custom_object_id,
            organization_id=organization_id,
            record_id=record_id,
        )

    async def validate_field_and_fetch_specs(
        self,
        organization_id: UUID,
        object_identifier: ObjectIdentifier,
        fetch_conditions: ObjectRecordFetchConditions
        | list[FieldReference]
        | None = None,
    ) -> tuple[set[FieldReference], FilterSpec | None, SortingSpec | None]:
        field_set = extract_field(fetch_conditions=fetch_conditions)
        field_references = extract_field_references_from_field_set(field_set=field_set)

        # Add debug logging
        logger.info(
            "Validating fields",
            extra={
                "field_references": [str(ref) for ref in field_references],
                "object_identifier": str(object_identifier),
                "field_set": [str(f) for f in field_set],
            },
        )

        filter_spec = (
            fetch_conditions.filter_spec
            if isinstance(fetch_conditions, ObjectRecordFetchConditions)
            else None
        )
        sorting_spec = (
            fetch_conditions.sorting_spec
            if isinstance(fetch_conditions, ObjectRecordFetchConditions)
            else None
        )
        sorting_fields = extract_field(
            fetch_conditions=ObjectRecordFetchConditions(
                sorting_spec=sorting_spec, fields=None, filter_spec=None
            )
        )
        _unsorted_fields = field_set - sorting_fields

        # Add debug logging for filter
        if filter_spec:
            logger.info(
                "Filter spec",
                extra={
                    "filter": str(filter_spec),
                    "primary_object": str(filter_spec.primary_object_identifier),
                },
            )

        return field_references, filter_spec, sorting_spec

    def _group_field_references_by_relationship_id(
        self,
        field_references: list[FieldReference],
    ) -> dict[str, list[FieldReference]]:
        result: defaultdict[str, list[FieldReference]] = defaultdict(list)
        for ref in field_references:
            if isinstance(ref, FieldReference):
                result[str(ref.relationship_id)].append(ref)
        return dict(result)

    async def get_custom_object(
        self,
        organization_id: UUID,
        custom_object_id_or_parent_object_name: UUID | ExtendableStandardObject,
        include_fields: bool = False,
    ) -> CustomObjectDto:
        return await self.custom_object_repository.get_custom_object_dto(
            organization_id=organization_id,
            custom_object_id_or_parent_object_name=custom_object_id_or_parent_object_name,
        )

    async def get_extension_custom_object_statuses(
        self,
        organization_id: UUID,
    ) -> dict[ExtendableStandardObject, CustomObject | None]:
        existing_extension_cus_objs = await self.custom_object_repository.find_custom_object_by_organization_id_and_parent_object_names(
            organization_id=organization_id,
            parent_object_names=ExtendableStandardObject.members(),
        )

        existing_custom_objects: dict[ExtendableStandardObject, CustomObject] = {
            not_none(obj.parent_object_name): obj for obj in existing_extension_cus_objs
        }

        return {
            c: existing_custom_objects.get(c)
            for c in ExtendableStandardObject.members()
        }


class SingletonCustomObjectQueryService(Singleton, CustomObjectQueryService):
    pass


def get_custom_object_query_service(
    db_engine: Annotated[DatabaseEngine, Depends(get_db_engine)],
) -> CustomObjectQueryService:
    return SingletonCustomObjectQueryService(db_engine=db_engine)
