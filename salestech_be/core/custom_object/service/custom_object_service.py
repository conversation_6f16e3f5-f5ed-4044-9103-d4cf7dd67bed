from collections.abc import Mapping
from typing import Annotated
from uuid import UUID, uuid4

from fastapi import Depends
from frozendict import frozendict
from sqlalchemy import text
from sqlalchemy.exc import IntegrityError

from salestech_be.common.error_code import ErrorCode
from salestech_be.common.exception import (
    ErrorDetails,
    IllegalStateError,
    InvalidArgumentError,
    ResourceNotFoundError,
)
from salestech_be.common.exception.exception import (
    ConflictErrorDetails,
    ConflictResourceError,
)
from salestech_be.common.singleton import Singleton
from salestech_be.common.type.metadata.common import (
    CustomObjectIdentifier,
    ObjectIdentifier,
    ObjectKind,
    RelationshipCardinality,
    RelationshipType,
    StandardObjectIdentifier,
)
from salestech_be.common.type.metadata.field.field_type import (
    FieldType,
    is_custom_field_type,
)
from salestech_be.common.type.metadata.field.field_type_property import (
    MultiSelectFieldProperty,
    SingleSelectFieldProperty,
)
from salestech_be.common.type.metadata.field.field_type_property_create import (
    CustomFieldType<PERSON>ropertyCreate,
    MultiSelectFieldPropertyCreate,
    SingleSelectFieldPropertyCreate,
    custom_field_type_property_create_to_property,
)
from salestech_be.common.type.metadata.field.field_type_property_update import (
    CustomFieldTypePropertyUpdate,
)
from salestech_be.common.type.metadata.field.field_value import (
    FieldValue,
    FieldValueOrAny,
    MultiSelectFieldValue,
    PresentableSelectListValue,
    SingleSelectFieldValue,
    custom_field_value_from_generic_value,
    is_field_value,
)
from salestech_be.core.custom_object.service.association_service import (
    get_association_service,
)
from salestech_be.core.custom_object.service.custom_object_query_service import (
    get_custom_object_query_service,
)
from salestech_be.core.custom_object.type.extendable_standard_object import (
    ExtendableStandardObject,
)
from salestech_be.core.custom_object.type.object_status import (
    CustomObjectStatus,
)
from salestech_be.core.metadata.dto.service_api_schema import (
    SelectListCustomFieldAssociationCreateRequest,
)
from salestech_be.core.metadata.service.internal_select_list_service import (
    InternalSelectListService,
    get_select_list_service,
)
from salestech_be.db.dao.custom_object_repository import CustomObjectRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.dto.custom_object_data_dto import (
    CustomObjectDataDto,
    CustomObjectDataListDto,
    ExtensionCustomObjectDataGroupDto,
)
from salestech_be.db.dto.custom_object_dto import (
    CustomObjectDto,
)
from salestech_be.db.models.custom_field import CustomField
from salestech_be.db.models.custom_field_association import CustomFieldAssociation
from salestech_be.db.models.custom_object import (
    CustomObject,
    CustomObjectInsertRequest,
)
from salestech_be.db.models.custom_object_data import (
    CreateCustomObjectDataRequestV2,
    CustomObjectData,
)
from salestech_be.ree_logging import get_logger
from salestech_be.util.time import zoned_utc_now
from salestech_be.util.validation import count_not_none, not_none

logger = get_logger(__name__)


class CustomObjectService:
    def __init__(
        self,
        custom_object_repo: Annotated[CustomObjectRepository, Depends()],
        select_list_service: Annotated[InternalSelectListService, Depends()],
    ):
        self.custom_object_repository = custom_object_repo
        self.select_list_service = select_list_service
        self.association_service = get_association_service(
            db_engine=custom_object_repo.engine
        )
        self.query_service = get_custom_object_query_service(
            db_engine=custom_object_repo.engine
        )

    async def get_extension_custom_object_statuses(
        self,
        organization_id: UUID,
    ) -> dict[ExtendableStandardObject, CustomObject | None]:
        existing_extension_cus_objs = await self.custom_object_repository.find_custom_object_by_organization_id_and_parent_object_names(
            organization_id=organization_id,
            parent_object_names=ExtendableStandardObject.members(),
        )

        existing_custom_objects: dict[ExtendableStandardObject, CustomObject] = {
            not_none(obj.parent_object_name): obj for obj in existing_extension_cus_objs
        }

        return {
            c: existing_custom_objects.get(c)
            for c in ExtendableStandardObject.members()
        }

    async def get_organization_custom_objects_summary(
        self,
        organization_id: UUID,
        include_frozen: bool,
    ) -> list[CustomObject]:
        custom_objects = (
            await self.custom_object_repository.list_custom_object_for_organization(
                organization_id=organization_id,
            )
        )
        return (
            custom_objects
            if include_frozen
            else [
                obj for obj in custom_objects if obj.status != CustomObjectStatus.FROZEN
            ]
        )

    async def get_organization_custom_objects_details(
        self,
        organization_id: UUID,
        include_frozen: bool,
    ) -> list[CustomObjectDto]:
        custom_object_dtos = await self.list_custom_objects_for_organization(
            organization_id=organization_id,
        )
        return (
            custom_object_dtos
            if include_frozen
            else [
                obj_dto
                for obj_dto in custom_object_dtos
                if obj_dto.custom_object.status != CustomObjectStatus.FROZEN
            ]
        )

    async def archive_custom_object_data_by_id(
        self,
        user_id: UUID,
        organization_id: UUID,
        custom_object_data_id: UUID,
    ) -> CustomObjectData:
        return await self.custom_object_repository.archive_custom_object_data_by_id(
            user_id=user_id,
            organization_id=organization_id,
            custom_object_data_id=custom_object_data_id,
        )

    async def enable_extension_custom_object(
        self,
        user_id: UUID,
        organization_id: UUID,
        objects_to_enable: list[ExtendableStandardObject],
        icon: str | None = None,
        visible_on_navigation: bool = True,
    ) -> dict[ExtendableStandardObject, CustomObject | None]:
        existing_custom_objects = (
            await self.query_service.get_extension_custom_object_statuses(
                organization_id=organization_id,
            )
        )
        objects_to_create = [
            object_to_enable
            for object_to_enable in objects_to_enable
            if (
                (
                    not (
                        existing_custom_object := existing_custom_objects.get(
                            object_to_enable, None
                        )
                    )
                )
                or existing_custom_object.status != CustomObjectStatus.ACTIVE
            )
        ]
        utc_now = zoned_utc_now()
        dirty_objects_to_create = [
            CustomObjectInsertRequest(
                id=uuid4(),
                parent_object_name=object_to_create,
                object_display_name=CustomObject.create_extension_custom_object_display_name(
                    parent_object_name=object_to_create,
                ),
                object_name=CustomObject.create_extension_custom_object_name(
                    parent_object_name=object_to_create,
                ),
                created_at=utc_now,
                created_by_user_id=user_id,
                owner_user_id=user_id,
                status=CustomObjectStatus.ACTIVE,
                organization_id=organization_id,
                icon=icon,
                visible_on_navigation=visible_on_navigation,
            )
            for object_to_create in objects_to_create
        ]
        await self.custom_object_repository.create_custom_objects(
            custom_objects_requests=dirty_objects_to_create,
        )
        return await self.query_service.get_extension_custom_object_statuses(
            organization_id=organization_id,
        )

    async def create_standalone_custom_object(
        self,
        user_id: UUID,
        organization_id: UUID,
        object_display_name: str,
        icon: str | None = None,
        visible_on_navigation: bool = True,
    ) -> CustomObject:
        """
        Create a standalone custom object.

        Args:
            user_id: The user_id of the user creating the custom object.
            organization_id: The organization_id of the organization creating the custom object.
            object_display_name: The display name of the custom object.
            icon: The icon of the custom object.
            visible_on_navigation: Whether the custom object is visible on the navigation bar.
        Returns:
            The created custom object.

        Note:
            This method will raise an error if the custom object with same display name already exists.
            todo(xw): unique constraint on object_display_name may not be correct?
        """
        return await self.custom_object_repository.create_custom_object(
            CustomObjectInsertRequest(
                id=uuid4(),
                organization_id=organization_id,
                created_by_user_id=user_id,
                owner_user_id=user_id,
                object_display_name=object_display_name,
                object_name=CustomObject.create_standalone_custom_object_name(
                    object_display_name=object_display_name,
                ),
                created_at=zoned_utc_now(),
                status=CustomObjectStatus.ACTIVE,
                parent_object_name=None,
                icon=icon,
                visible_on_navigation=visible_on_navigation,
            ),
        )

    async def update_custom_object(
        self,
        user_id: UUID,
        organization_id: UUID,
        custom_object_id_or_parent_object_name: UUID | ExtendableStandardObject,
        new_display_name: str | None = None,
        new_status: CustomObjectStatus | None = None,
        new_icon: str | None = None,
        new_visible_on_navigation: bool | None = None,
    ) -> CustomObject:
        """
        Update a custom object.

        Args:
            user_id: The user_id of the user updating the custom object.
            organization_id: The organization_id of the organization updating the custom object.
            custom_object_id_or_parent_object_name: The id or parent object name of the custom object to update.
            new_display_name: The new display name of the custom object.
            new_status: The new status of the custom object.
            new_icon: The new icon of the custom object.
            new_visible_on_navigation: Whether the custom object is visible on the navigation bar.
        Returns:
            The updated custom object.

        Note:
            - When the current custom object is an extension, the display name cannot be changed.
            - Setting status to FROZEN will soft delete the object
        """
        if not count_not_none(
            new_display_name, new_status, new_icon, new_visible_on_navigation
        ):
            raise InvalidArgumentError(
                "at least one of new_display_name, new_status, new_icon, and new_visible_on_navigation need to be specified",
            )
        custom_object = await self.custom_object_repository.get_custom_object(
            organization_id=organization_id,
            custom_object_id_or_parent_object_name=custom_object_id_or_parent_object_name,
        )
        if custom_object.parent_object_name and new_display_name:
            raise InvalidArgumentError(
                "cannot change display name for extension "
                f"custom object: {custom_object.object_display_name}",
            )

        now = zoned_utc_now()
        should_soft_delete = new_status == CustomObjectStatus.FROZEN

        try:
            updated_custom_object = (
                await self.custom_object_repository.update_by_primary_key(
                    CustomObject,
                    primary_key_to_value={"id": custom_object.id},
                    column_to_update={
                        "status": new_status or custom_object.status,
                        "object_display_name": new_display_name
                        or custom_object.object_display_name,
                        "updated_at": now,
                        "updated_by_user_id": user_id,
                        "icon": new_icon or custom_object.icon,
                        "visible_on_navigation": (
                            new_visible_on_navigation
                            if new_visible_on_navigation is not None
                            else custom_object.visible_on_navigation
                        ),
                        # Add soft delete fields if status is FROZEN
                        "deleted_at": now if should_soft_delete else None,
                        "deleted_by_user_id": user_id if should_soft_delete else None,
                    },
                )
            )

            if (
                should_soft_delete
            ):  # Archive all associations AND records for the custom object
                # Archive all associations for the custom object
                if isinstance(custom_object_id_or_parent_object_name, UUID):
                    object_identifier: ObjectIdentifier = CustomObjectIdentifier(
                        object_kind=ObjectKind.CUSTOM,
                        organization_id=organization_id,
                        object_id=custom_object_id_or_parent_object_name,
                    )
                else:
                    object_identifier = StandardObjectIdentifier(
                        object_kind=ObjectKind.STANDARD,
                        object_name=custom_object_id_or_parent_object_name.value,
                    )
                logger.info(f"Object identifier: {object_identifier}")

                associations = (
                    await self.association_service.get_associations_for_object(
                        organization_id=organization_id,
                        object_identifier=object_identifier,
                    )
                )
                logger.info(f"Associations: {associations}")
                for association in associations:
                    logger.info(f"Deleting association: {association}")
                    await self.association_service.delete_association(
                        organization_id=association.organization_id,
                        user_id=association.created_by_user_id,
                        source_object_identifier=association.source_object_identifier,
                        target_object_identifier=association.target_object_identifier,
                        association_id=association.id,
                    )

                # Archive all records for the custom object
                custom_object_data_records = await self.custom_object_repository.list_custom_object_data_by_custom_object_id(
                    organization_id=organization_id,
                    custom_object_id=custom_object.id,
                )
                logger.info(f"Custom object data records: {custom_object_data_records}")

                for record in custom_object_data_records:
                    logger.info(f"Archiving record: {record}")
                    await (
                        self.custom_object_repository.archive_custom_object_data_by_id(
                            user_id=user_id,
                            organization_id=organization_id,
                            custom_object_data_id=record.id,
                        )
                    )
        except IntegrityError:
            _conflicted_custom_object = (
                await self.custom_object_repository._find_unique_by_column_values(
                    CustomObject,
                    organization_id=organization_id,
                    object_display_name=new_display_name,
                )
            )
            if _conflicted_custom_object:
                raise ConflictResourceError(
                    f"custom object with same display name {new_display_name} already exists",
                )
            raise
        return not_none(updated_custom_object)

    async def update_custom_field_v2(
        self,
        user_id: UUID,
        organization_id: UUID,
        custom_object_id_or_parent_object_name: UUID | ExtendableStandardObject,
        custom_field_id: UUID,
        custom_field_property_update: CustomFieldTypePropertyUpdate,
    ) -> CustomField:
        """
        Update a custom field.

        Args:
            user_id: The user_id of the user updating the custom field.
            organization_id: The organization_id of the organization updating the custom field.
            custom_object_id_or_parent_object_name: The id or parent object name of the custom object to update.
            custom_field_id: The id of the custom field to update.
            custom_field_property_update: The new property of the custom field.

        Returns:
            The updated custom field.

        Note:
            - When the new display name is the same as another existing custom field, ConflictResourceError will be raised.
        """

        custom_object_dto = await self.custom_object_repository.get_custom_object_dto(
            organization_id=organization_id,
            custom_object_id_or_parent_object_name=custom_object_id_or_parent_object_name,
        )
        custom_object = custom_object_dto.custom_object
        existing_field = custom_object_dto.custom_field(field_id=custom_field_id)
        if not existing_field.properties:
            # todo(xw): remove this once cleanup all v1 custom field data structures
            raise IllegalStateError("field doesn't have property")
        new_property = custom_field_property_update.validate_update(
            current_property=existing_field.properties
        )
        return await self.custom_object_repository.update_custom_field_v2(
            user_id=user_id,
            custom_object_id=custom_object.id,
            organization_id=organization_id,
            custom_field_id=custom_field_id,
            new_custom_field_property=new_property,
        )

    async def create_custom_field_v2(
        self,
        user_id: UUID,
        organization_id: UUID,
        custom_object_id: UUID,
        custom_field_type_property_create: CustomFieldTypePropertyCreate,
    ) -> CustomField:
        """
        Create a custom field.

        Args:
            user_id: The user_id of the user creating the custom field.
            organization_id: The organization_id of the organization creating the custom field.
            custom_object_id: The id of the custom object to create the custom field for.
            custom_field_type_property_create: The property of the custom field to create.

        Returns:
            The created custom field.

        Note:
            - When the new display name is the same as another existing custom field, ConflictResourceError will be raised.
        """
        # validate custom_field_type_property_create
        if isinstance(
            custom_field_type_property_create,
            SingleSelectFieldPropertyCreate | MultiSelectFieldPropertyCreate,
        ):
            await self.select_list_service.validate_select_list_reference(
                organization_id=organization_id,
                select_list_id=custom_field_type_property_create.select_list_id,
            )

            # check if select_list_id is already associated with custom_field
            select_list_custom_field_association = (
                await self.select_list_service.get_select_list_custom_field_association(
                    organization_id=organization_id,
                    select_list_id=custom_field_type_property_create.select_list_id,
                )
            )
            if select_list_custom_field_association:
                raise ConflictResourceError(
                    f"Select list: {custom_field_type_property_create.select_list_id} is already associated with custom field",
                )
        custom_field = await self.custom_object_repository.create_custom_field_v2(
            custom_object_id=custom_object_id,
            user_id=user_id,
            organization_id=organization_id,
            field_type_property=custom_field_type_property_create_to_property(
                custom_field_type_property_create
            ),
        )

        # insert select_list_custom_field_association
        if isinstance(
            custom_field_type_property_create,
            SingleSelectFieldPropertyCreate | MultiSelectFieldPropertyCreate,
        ):
            await self.select_list_service.create_select_list_custom_field_association(
                req=SelectListCustomFieldAssociationCreateRequest(
                    user_id=user_id,
                    organization_id=organization_id,
                    select_list_id=custom_field_type_property_create.select_list_id,
                    custom_field_id=custom_field.id,
                ),
            )
        return custom_field

    async def delete_custom_field(
        self,
        user_id: UUID,
        organization_id: UUID,
        custom_object_id: UUID,
        custom_field_id: UUID,
    ) -> CustomField:
        """
        Delete a custom field.

        Args:
            user_id: The user_id of the user deleting the custom field.
            organization_id: The organization_id of the organization deleting the custom field.
            custom_object_id: The id of the custom object to delete the custom field for.
            custom_field_id: The id of the custom field to delete.

        Returns:
            The deleted custom field.

        Note:
            - When the custom field is already deleted, it's a no-op.
            - When the custom field is not found, ResourceNotFoundError will be raised.
            - When the custom field is deleted, its data slot can be reused for new custom fields.
            - However, if there are any dependencies on the deleted custom field, currently we
                don't have a detection mechanism. Hence the dependent use case will fail on referencing
                the deleted custom field.
                todo(xw): we can have a mechanism to detect deleted field dependencies and
                prevent the deletion.
        """
        return await self.custom_object_repository.remove_custom_field(
            user_id=user_id,
            organization_id=organization_id,
            custom_object_id=custom_object_id,
            custom_field_id=custom_field_id,
        )

    async def list_custom_objects_for_organization(
        self,
        organization_id: UUID,
        only_include_custom_object_ids: list[UUID] | None = None,
        exclude_deleted_or_archived: bool = True,
    ) -> list[CustomObjectDto]:
        return (
            await self.custom_object_repository.list_custom_object_dto_for_organization(
                organization_id=organization_id,
                only_include_custom_object_ids=only_include_custom_object_ids,
                exclude_deleted_or_archived=exclude_deleted_or_archived,
            )
        )

    async def list_custom_object_data_dto_by_organization_id_paginated(
        self,
        organization_id: UUID,
        start_after: UUID | None = None,
        limit: int = 100,
        custom_object_data_ids: list[UUID]
        | None = None,  # Note: If this is None, we will return all custom object data for the organization
        exclude_deleted_or_archived: bool = True,
    ) -> list[CustomObjectDataDto]:
        """List custom object data DTOs for an organization with pagination.

        Args:
            organization_id: Organization ID to filter by
            start_after: ID of last item from previous page
            limit: Maximum number of items to return
            custom_object_data_ids: Optional list of specific IDs to fetch
            exclude_deleted_or_archived: Whether to exclude deleted/archived items
        """
        id_filter = (
            "and id = any(:custom_object_data_ids)" if custom_object_data_ids else ""
        )
        deleted_at_filter = (
            "and deleted_at is null" if exclude_deleted_or_archived else ""
        )
        pagination_filter = "and id > :start_after" if start_after else ""

        # Build base query with pagination
        cobject_data_stmt = text(
            f"""
            select * from cobject_data
            where organization_id = :organization_id
            {id_filter}
            {deleted_at_filter}
            {pagination_filter}
            order by id
            limit :limit
            """  # noqa: S608
        )

        # Build params dynamically
        params: dict[str, UUID | list[UUID] | int] = {
            "organization_id": organization_id,
            "limit": limit,
        }
        if custom_object_data_ids:
            params["custom_object_data_ids"] = custom_object_data_ids
        if start_after:
            params["start_after"] = start_after

        cobject_data_stmt = cobject_data_stmt.bindparams(**params)

        cobject_data_results = await self.custom_object_repository.engine.all(
            cobject_data_stmt
        )
        cobject_datas = [CustomObjectData.from_row(r) for r in cobject_data_results]

        cobject_dtos: list[
            CustomObjectDto
        ] = await self.custom_object_repository.list_custom_object_dto_for_organization(
            organization_id=organization_id,
        )
        cobject_dto_by_id = {
            cobject_dto.custom_object.id: cobject_dto for cobject_dto in cobject_dtos
        }
        custom_object_details_dtos: list[CustomObjectDataDto] = []
        for cobject_data in cobject_datas:
            custom_object_dto = cobject_dto_by_id.get(cobject_data.cobject_metadata_id)
            if not custom_object_dto:
                raise ResourceNotFoundError(
                    f"custom_object (id={cobject_data.cobject_metadata_id}) doesn't exist",
                )
            custom_object_details_dtos.append(
                CustomObjectDataDto(
                    custom_object_dto=custom_object_dto,
                    custom_object_data=cobject_data,
                )
            )
        return custom_object_details_dtos

    # TODO: See if this is a good candidate for using the new 'count_by_column_values' function
    async def list_custom_object_data_dto_by_organization_id(
        self,
        organization_id: UUID,
        custom_object_data_ids: list[UUID]
        | None = None,  # Note: If this is None, we will return all custom object data for the organization
        exclude_deleted_or_archived: bool = True,
    ) -> list[CustomObjectDataDto]:
        id_filter = (
            "and id = any(:custom_object_data_ids)" if custom_object_data_ids else ""
        )

        deleted_at_filter = (
            "and deleted_at is null" if exclude_deleted_or_archived else ""
        )

        # Build base query
        cobject_data_stmt = text(
            f"""
            select * from cobject_data
            where organization_id = :organization_id
            {id_filter}
            {deleted_at_filter}
            """  # noqa: S608
        )

        # Build params dynamically
        params: dict[str, UUID | list[UUID]] = {"organization_id": organization_id}
        if custom_object_data_ids:
            params["custom_object_data_ids"] = custom_object_data_ids

        cobject_data_stmt = cobject_data_stmt.bindparams(**params)

        cobject_data_results = await self.custom_object_repository.engine.all(
            cobject_data_stmt
        )
        cobject_datas = [CustomObjectData.from_row(r) for r in cobject_data_results]

        cobject_dtos: list[
            CustomObjectDto
        ] = await self.custom_object_repository.list_custom_object_dto_for_organization(
            organization_id=organization_id,
        )
        cobject_dto_by_id = {
            cobject_dto.custom_object.id: cobject_dto for cobject_dto in cobject_dtos
        }
        custom_object_details_dtos: list[CustomObjectDataDto] = []
        for cobject_data in cobject_datas:
            custom_object_dto = cobject_dto_by_id.get(cobject_data.cobject_metadata_id)
            if not custom_object_dto:
                raise ResourceNotFoundError(
                    f"custom_object (id={cobject_data.cobject_metadata_id}) doesn't exist",
                )
            custom_object_details_dtos.append(
                CustomObjectDataDto(
                    custom_object_dto=custom_object_dto,
                    custom_object_data=cobject_data,
                )
            )
        return custom_object_details_dtos

    async def list_custom_object_data_dto_by_data_ids_untenanted(
        self,
        custom_object_data_ids: list[UUID],
        exclude_deleted_or_archived: bool = True,
    ) -> list[CustomObjectDataDto]:
        deleted_at_filter = (
            "and deleted_at is null" if exclude_deleted_or_archived else ""
        )
        cobject_data_stmt = text(
            f"""
            select * from cobject_data
            where id = any(:custom_object_data_ids)
            {deleted_at_filter}
            """  # noqa: S608
        ).bindparams(
            custom_object_data_ids=custom_object_data_ids,
        )
        cobject_data_results = await self.custom_object_repository.engine.all(
            cobject_data_stmt
        )
        cobject_datas = [CustomObjectData.from_row(r) for r in cobject_data_results]

        cobject_dtos: list[CustomObjectDto] = []
        if not custom_object_data_ids:
            return []
        cobject_dtos = (
            await self.custom_object_repository.list_custom_object_dto_untenanted(
                only_include_custom_object_ids=custom_object_data_ids,
                exclude_deleted_or_archived=exclude_deleted_or_archived,
            )
        )
        cobject_dto_by_id = {
            cobject_dto.custom_object.id: cobject_dto for cobject_dto in cobject_dtos
        }
        custom_object_details_dtos: list[CustomObjectDataDto] = []
        for cobject_data in cobject_datas:
            custom_object_dto = cobject_dto_by_id.get(cobject_data.cobject_metadata_id)
            if not custom_object_dto:
                raise ResourceNotFoundError(
                    f"custom_object (id={cobject_data.cobject_metadata_id}) doesn't exist",
                )
            custom_object_details_dtos.append(
                CustomObjectDataDto(
                    custom_object_dto=custom_object_dto,
                    custom_object_data=cobject_data,
                )
            )
        return custom_object_details_dtos

    async def get_custom_object(
        self,
        organization_id: UUID,
        custom_object_id_or_parent_object_name: UUID | ExtendableStandardObject,
        include_fields: bool = False,
    ) -> CustomObjectDto:
        return await self.custom_object_repository.get_custom_object_dto(
            organization_id=organization_id,
            custom_object_id_or_parent_object_name=custom_object_id_or_parent_object_name,
        )

    async def map_custom_object_data_by_extension_ids_if_custom_object_exists(
        self,
        organization_id: UUID,
        parent_object_name: ExtendableStandardObject,
        extension_ids: set[UUID],
    ) -> ExtensionCustomObjectDataGroupDto | None:
        try:
            custom_object_dto = await self.get_custom_object(
                custom_object_id_or_parent_object_name=parent_object_name,
                organization_id=organization_id,
            )
        except ResourceNotFoundError:
            return None
        custom_object_data_by_extension_id = (
            await self.custom_object_repository.map_custom_object_data_by_extension_ids(
                organization_id=organization_id,
                extension_ids=extension_ids,
                parent_object_name=parent_object_name,
            )
        )
        select_list_ids = {
            custom_field.properties.select_list_id
            for custom_field in custom_object_dto.custom_fields
            if custom_field.field_type
            in (FieldType.SINGLE_SELECT, FieldType.MULTI_SELECT)
            and isinstance(
                custom_field.properties,
                SingleSelectFieldProperty | MultiSelectFieldProperty,
            )
            and custom_field.properties.select_list_id is not None
        }
        select_values: list[PresentableSelectListValue] = []

        select_list_values_by_select_list_id = (
            await self.select_list_service.map_select_list_value_by_select_list_ids(
                select_list_ids=select_list_ids,
                organization_id=organization_id,
            )
        )

        for select_list_id in select_list_ids:
            select_list_values = select_list_values_by_select_list_id.get(
                select_list_id, []
            )
            select_values.extend(
                [
                    PresentableSelectListValue(
                        id=value.id,
                        select_list_id=value.select_list_id,
                        display_value=value.display_value,
                        description=value.description,
                        rank=value.rank,
                        is_default=value.is_default,
                        status=value.status,
                        application_code_name=None,
                    )
                    for value in select_list_values
                ]
            )
        return ExtensionCustomObjectDataGroupDto(
            custom_object_dto=custom_object_dto,
            custom_object_data_by_extension_id=custom_object_data_by_extension_id,
            select_list_value_map=frozendict[UUID, PresentableSelectListValue](
                {select_value.id: select_value for select_value in select_values}
            ),
        )

    async def create_custom_object_data_v2(
        self,
        user_id: UUID,
        organization_id: UUID,
        custom_object_dto_or_id: CustomObjectDto | UUID,
        custom_field_data_by_field_id: Mapping[UUID, FieldValueOrAny],
        display_name: str,
        extension_id: UUID | None = None,
    ) -> CustomObjectDataDto:
        """Create a new custom object data."""

        # Validate display name for standalone custom objects only.
        # Extension custom objects are NOT required to have a display name.
        if (not display_name or display_name.strip() == "") and extension_id is None:
            raise InvalidArgumentError(
                "Display name cannot be empty for a custom object"
            )

        try:
            if isinstance(custom_object_dto_or_id, CustomObjectDto):
                custom_object_dto = custom_object_dto_or_id
            else:
                custom_object_dto = await self.get_custom_object(
                    custom_object_id_or_parent_object_name=custom_object_dto_or_id,
                    organization_id=organization_id,
                )

            if custom_object_dto.custom_object.status == CustomObjectStatus.FROZEN:
                raise InvalidArgumentError(
                    f"custom_object (id:{custom_object_dto.custom_object_id}) is frozen",
                    additional_error_details=ErrorDetails(
                        code=ErrorCode.CUSTOM_OBJECT_IS_READ_ONLY,
                    ),
                )

            if custom_object_dto.is_extension and not extension_id:
                raise InvalidArgumentError(
                    "must specify extension_id for extension custom object of "
                    f"{custom_object_dto.custom_object.parent_object_name}",
                )

            transformed_custom_field_data_by_field_id = (
                await self._transform_custom_field_data_by_field_id(
                    custom_object_dto=custom_object_dto,
                    custom_field_data_by_field_id=custom_field_data_by_field_id,
                )
            )
            utc_now = zoned_utc_now()

            custom_object_data_request = CreateCustomObjectDataRequestV2(
                id=uuid4(),
                organization_id=custom_object_dto.organization_id,
                extension_id=extension_id,
                cobject_metadata_id=custom_object_dto.custom_object_id,
                display_name=display_name,
                created_at=utc_now,
                created_by_user_id=user_id,
            )
            for (
                field_id,
                field_value,
            ) in transformed_custom_field_data_by_field_id.items():
                custom_field = custom_object_dto.custom_field(field_id=field_id)
                custom_object_data_request.set_value(
                    slot_number=custom_field.slot_number,
                    value=field_value,
                    custom_field_id=field_id,
                )
            created_custom_object_data = (
                await self.custom_object_repository.create_custom_object_data_v2(
                    create_request=custom_object_data_request,
                    custom_object_dto=custom_object_dto,
                )
            )
            return CustomObjectDataDto(
                custom_object_data=created_custom_object_data,
                custom_object_dto=custom_object_dto,
            )

        except IntegrityError as e:
            if "uniq_cobject_data_display_name" in str(e):
                # Get the ID from either the DTO or use the UUID directly
                custom_object_id = (
                    custom_object_dto.custom_object_id
                    if isinstance(custom_object_dto, CustomObjectDto)
                    else custom_object_dto
                )

                # Get the existing custom object data record
                existing_custom_object_data_record = (
                    await self.get_custom_object_data_by_display_name(
                        organization_id=organization_id,
                        custom_object_id=custom_object_id,
                        display_name=display_name,
                    )
                )

                raise ConflictResourceError(
                    f"A custom object data record with display name '{display_name}' already exists "
                    f"for object type '{custom_object_dto.custom_object.object_display_name}', "
                    f"custom object id '{custom_object_id}' and organization id '{organization_id}'.",
                    additional_error_details=ConflictErrorDetails(
                        code=ErrorCode.CUSTOM_OBJECT_DATA_RECORD_WITH_SAME_DISPLAY_NAME_ALREADY_EXISTS,
                        reference_id=str(
                            existing_custom_object_data_record.custom_object_data.id
                        ),
                        conflicted_existing_object=CustomObjectIdentifier(
                            organization_id=organization_id,
                            object_id=custom_object_id,
                        ),
                        conflicted_existing_object_attrs={
                            "organization_id": organization_id,
                            "object_id": custom_object_id,
                            "data_record_id": existing_custom_object_data_record.custom_object_data.id,
                            "display_name": display_name,
                            "error_description": "conflict",
                            "error_type": "CLIENT_ERROR",
                        },
                    ),
                ) from e
            raise

    async def create_custom_object_data_by_extension_id(
        self,
        *,
        user_id: UUID,
        organization_id: UUID,
        parent_object_name: ExtendableStandardObject,
        extension_id: UUID,
        custom_field_data_by_field_id: Mapping[UUID, FieldValueOrAny],
        display_name: str = "",
    ) -> CustomObjectDataDto:
        custom_object_dto = await self.get_custom_object(
            custom_object_id_or_parent_object_name=parent_object_name,
            organization_id=organization_id,
        )
        return await self.create_custom_object_data_v2(
            user_id=user_id,
            organization_id=organization_id,
            custom_object_dto_or_id=custom_object_dto,
            custom_field_data_by_field_id=custom_field_data_by_field_id,
            display_name=display_name,
            extension_id=extension_id,
        )

    async def update_custom_object_data_by_id_v2(
        self,
        user_id: UUID,
        organization_id: UUID,
        custom_object_id: UUID,
        custom_object_data_id: UUID,
        custom_field_data_by_field_id_or_any: dict[UUID, FieldValueOrAny],
        display_name: str | None = None,
    ) -> CustomObjectDataDto:
        custom_object_dto = await self.get_custom_object(
            custom_object_id_or_parent_object_name=custom_object_id,
            organization_id=organization_id,
        )
        custom_field_data_by_field_id = (
            await self._transform_custom_field_data_by_field_id(
                custom_object_dto=custom_object_dto,
                custom_field_data_by_field_id=custom_field_data_by_field_id_or_any,
            )
        )
        if custom_object_dto.custom_object.status == CustomObjectStatus.FROZEN:
            raise InvalidArgumentError(
                f"read-only custom_object {custom_object_id}",
                additional_error_details=ErrorDetails(
                    code=ErrorCode.CUSTOM_OBJECT_IS_READ_ONLY,
                ),
            )
        updated = await self.custom_object_repository.update_custom_object_data_v2(
            organization_id=organization_id,
            user_id=user_id,
            custom_object_data_id=custom_object_data_id,
            custom_object_dto=custom_object_dto,
            field_updates_by_field_id=custom_field_data_by_field_id,
            display_name=display_name,
        )
        return CustomObjectDataDto(
            custom_object_dto=custom_object_dto,
            custom_object_data=updated,
        )

    async def update_custom_object_data_by_extension_id_v2(
        self,
        user_id: UUID,
        organization_id: UUID,
        parent_object_name: ExtendableStandardObject,
        extension_id: UUID,
        custom_field_data_by_field_id: Mapping[UUID, FieldValueOrAny],
        display_name: str = "",
    ) -> CustomObjectDataDto:
        custom_object_data_dto = (
            await self.find_custom_object_data_by_extension_id_or_none(
                parent_object_name=parent_object_name,
                organization_id=organization_id,
                custom_object_data_extension_id=extension_id,
            )
        )
        if not custom_object_data_dto:
            return await self.create_custom_object_data_by_extension_id(
                user_id=user_id,
                organization_id=organization_id,
                parent_object_name=parent_object_name,
                extension_id=extension_id,
                custom_field_data_by_field_id=custom_field_data_by_field_id,
                display_name=display_name,
            )

        if (
            custom_object_data_dto.custom_object_dto.custom_object.status
            == CustomObjectStatus.FROZEN
        ):
            raise InvalidArgumentError(
                "read-only custom_object "
                f"{custom_object_data_dto.custom_object_dto.custom_object_id}",
                additional_error_details=ErrorDetails(
                    code=ErrorCode.CUSTOM_OBJECT_IS_READ_ONLY,
                ),
            )

        transformed_custom_field_data_by_field_id = (
            await self._transform_custom_field_data_by_field_id(
                custom_object_dto=custom_object_data_dto.custom_object_dto,
                custom_field_data_by_field_id=custom_field_data_by_field_id,
            )
        )

        updated = await self.custom_object_repository.update_custom_object_data_v2(
            user_id=user_id,
            custom_object_data_id=custom_object_data_dto.custom_object_data.id,
            custom_object_dto=custom_object_data_dto.custom_object_dto,
            field_updates_by_field_id=transformed_custom_field_data_by_field_id,
            organization_id=organization_id,
        )
        return CustomObjectDataDto(
            custom_object_dto=custom_object_data_dto.custom_object_dto,
            custom_object_data=updated,
        )

    async def get_custom_object_data(
        self,
        organization_id: UUID,
        custom_object_id: UUID,
        custom_object_data_id: UUID,
    ) -> CustomObjectDataDto:
        custom_object_dto = await self.get_custom_object(
            custom_object_id_or_parent_object_name=custom_object_id,
            organization_id=organization_id,
        )

        custom_object_data = (
            await self.custom_object_repository._find_unique_by_column_values(
                CustomObjectData,
                id=custom_object_data_id,
                cobject_metadata_id=custom_object_dto.custom_object_id,
                organization_id=organization_id,
            )
        )

        if not custom_object_data:
            raise ResourceNotFoundError(
                f"custom_object_data (id={custom_object_data_id}) doesn't exist",
            )
        return CustomObjectDataDto(
            custom_object_dto=custom_object_dto,
            custom_object_data=custom_object_data,
        )

    async def get_custom_object_data_by_display_name(
        self,
        organization_id: UUID,
        custom_object_id: UUID,
        display_name: str,
    ) -> CustomObjectDataDto:
        custom_object_dto = await self.get_custom_object(
            custom_object_id_or_parent_object_name=custom_object_id,
            organization_id=organization_id,
        )

        custom_object_data = (
            await self.custom_object_repository._find_unique_by_column_values(
                CustomObjectData,
                display_name=display_name,
                cobject_metadata_id=custom_object_dto.custom_object_id,
                organization_id=organization_id,
            )
        )

        if not custom_object_data:
            raise ResourceNotFoundError(
                f"custom_object_data (display_name={display_name}) doesn't exist",
            )
        return CustomObjectDataDto(
            custom_object_dto=custom_object_dto,
            custom_object_data=custom_object_data,
        )

    async def find_custom_object_data_by_extension_id_or_none(
        self,
        organization_id: UUID,
        parent_object_name: ExtendableStandardObject,
        custom_object_data_extension_id: UUID,
    ) -> CustomObjectDataDto | None:
        try:
            return await self.get_custom_object_data_by_extension_id(
                organization_id=organization_id,
                parent_object_name=parent_object_name,
                custom_object_data_extension_id=custom_object_data_extension_id,
            )
        except ResourceNotFoundError:
            return None

    async def get_custom_object_data_by_extension_id(
        self,
        organization_id: UUID,
        parent_object_name: ExtendableStandardObject,
        custom_object_data_extension_id: UUID,
    ) -> CustomObjectDataDto:
        custom_object_dto = await self.get_custom_object(
            custom_object_id_or_parent_object_name=parent_object_name,
            organization_id=organization_id,
        )

        custom_object_data = (
            await self.custom_object_repository._find_unique_by_column_values(
                CustomObjectData,
                extension_id=custom_object_data_extension_id,
                cobject_metadata_id=custom_object_dto.custom_object_id,
                organization_id=organization_id,
            )
        )

        if not custom_object_data:
            raise ResourceNotFoundError(
                "custom_object_data (extension_id="
                f"{custom_object_data_extension_id}) doesn't exist",
            )
        return CustomObjectDataDto(
            custom_object_dto=custom_object_dto,
            custom_object_data=custom_object_data,
        )

    async def find_custom_object_data_by_indexed_fields_v2(
        self,
        organization_id: UUID,
        custom_object_id: UUID,
        indexed_field_value_by_field_id: dict[UUID, FieldValue],
    ) -> CustomObjectDataListDto | None:
        return await self.custom_object_repository.find_custom_object_data_by_indexed_fields_v2(
            organization_id=organization_id,
            custom_object_id=custom_object_id,
            indexed_field_value_by_field_id=indexed_field_value_by_field_id,
        )

    async def _transform_custom_field_data_by_field_id(
        self,
        *,
        custom_object_dto: CustomObjectDto,
        custom_field_data_by_field_id: Mapping[UUID, FieldValueOrAny],
    ) -> dict[UUID, FieldValue]:
        transformed_custom_field_data_by_field_id: dict[UUID, FieldValue] = {}
        for field_id, field_value in custom_field_data_by_field_id.items():
            custom_field = custom_object_dto.custom_field(field_id=field_id)
            if not custom_field:
                raise InvalidArgumentError(
                    f"custom_field (id={field_id}) doesn't exist"
                )

            if not is_custom_field_type(custom_field.field_type):
                raise InvalidArgumentError(
                    f"custom_field (id={field_id}) is not a custom field"
                )

            if is_field_value(field_value):
                _field_value = field_value
            else:
                _field_value = custom_field_value_from_generic_value(
                    field_type=custom_field.field_type,
                    value=field_value,
                )
            if custom_field.field_type in (
                FieldType.SINGLE_SELECT,
                FieldType.MULTI_SELECT,
            ):
                await self._validate_select_field(
                    custom_field, _field_value, custom_object_dto.organization_id
                )

            transformed_custom_field_data_by_field_id[field_id] = _field_value

        return transformed_custom_field_data_by_field_id

    async def _validate_select_field(
        self,
        custom_field: CustomField,
        field_value: FieldValue,
        organization_id: UUID,
    ) -> None:
        if not isinstance(
            custom_field.properties,
            SingleSelectFieldProperty | MultiSelectFieldProperty,
        ):
            raise IllegalStateError(
                f"Unexpected field property type for field (id={custom_field.id})"
            )

        select_list_id = custom_field.properties.select_list_id

        if isinstance(field_value, SingleSelectFieldValue):
            value_ids = [field_value.value_id]

        elif isinstance(field_value, MultiSelectFieldValue):
            value_ids = list(field_value.value_ids)
        else:
            raise IllegalStateError(
                f"Unexpected field property type for field (id={custom_field.id})"
            )

        for value_id in value_ids:
            await self.select_list_service.validate_select_list_reference(
                organization_id=organization_id,
                select_list_id=select_list_id,
                select_list_value_id=value_id,
            )

    async def get_custom_field_by_id(
        self,
        organization_id: UUID,
        custom_field_id: UUID,
    ) -> CustomField:
        return await self.custom_object_repository.get_custom_field(
            organization_id=organization_id,
            custom_field_id=custom_field_id,
        )

    async def create_field_relationship(
        self,
        user_id: UUID,
        organization_id: UUID,
        source_field_id: UUID,
        target_field_id: UUID,
        relationship_type: RelationshipType,
        relationship_cardinality: RelationshipCardinality,
        is_dependent: bool = False,
    ) -> CustomFieldAssociation:
        """Create a relationship between two custom fields."""
        # Get both source and target fields to validate
        source_field = await self.custom_object_repository.get_custom_field(
            organization_id=organization_id,
            custom_field_id=source_field_id,
        )
        target_field = await self.custom_object_repository.get_custom_field(
            organization_id=organization_id,
            custom_field_id=target_field_id,
        )

        # Validate fields exist and belong to the organization
        if not source_field or not target_field:
            raise ResourceNotFoundError("Source or target field not found")

        # Create the association
        association = CustomFieldAssociation(
            id=uuid4(),
            organization_id=organization_id,
            source_field_id=source_field_id,
            target_field_id=target_field_id,
            source_object_type=ObjectKind.CUSTOM,
            target_object_type=ObjectKind.CUSTOM,
            relationship_type=RelationshipType(relationship_type),
            relationship_cardinality=RelationshipCardinality(relationship_cardinality),
            is_dependent=is_dependent,
            created_at=zoned_utc_now(),
            created_by_user_id=user_id,
        )

        # Save to database
        return await self.custom_object_repository.create_field_relationship(
            association
        )

    async def find_custom_object_data_by_display_name_or_none(
        self,
        organization_id: UUID,
        custom_object_id: UUID,
        display_name: str,
    ) -> CustomObjectDataDto | None:
        """Find custom object data by display name, return None if not found."""
        try:
            return await self.get_custom_object_data_by_display_name(
                organization_id=organization_id,
                custom_object_id=custom_object_id,
                display_name=display_name,
            )
        except ResourceNotFoundError:
            return None


def get_custom_object_service(db_engine: DatabaseEngine) -> CustomObjectService:
    if SingletonCustomObjectService.has_instance():
        return SingletonCustomObjectService.get_singleton_instance()
    return SingletonCustomObjectService(
        custom_object_repo=CustomObjectRepository(engine=db_engine),
        select_list_service=get_select_list_service(engine=db_engine),
    )


class SingletonCustomObjectService(Singleton, CustomObjectService):
    pass
