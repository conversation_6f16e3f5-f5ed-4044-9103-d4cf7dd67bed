from datetime import datetime
from enum import Str<PERSON>num
from typing import Any
from uuid import UUID

from pydantic import BaseModel

from salestech_be.common.events import (
    DomainEnrichedCDCEvent,
    EnrichedCDCEventProcessor,
)
from salestech_be.core.custom_object.service.metadata_cache import MetadataCache
from salestech_be.core.custom_object.type.custom_object_record import CustomObjectRecord
from salestech_be.db.models.custom_object_data import CustomFieldData
from salestech_be.integrations.kafka.types import (
    CDCObject,
    CustomObjectDataView,
)
from salestech_be.ree_logging import get_logger
from salestech_be.search.es.indexing.mapping.custom_object import (
    ESCustomObjectData,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.search.indexing.cdc_event_handlers import (
    index_custom_object_handler,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.settings import settings

logger = get_logger(__name__)


class CustomObjectData(BaseModel):  # type: ignore[explicit-any] # TODO: fix-any-annotation
    """Represents the raw custom object data from cobject_data table."""

    id: UUID
    organization_id: UUID
    extension_id: UUID | None = None
    cobject_metadata_id: UUID
    values: dict[int, dict[str, Any]]  # type: ignore[explicit-any] # TODO: fix-any-annotation
    created_at: datetime
    created_by_user_id: UUID
    updated_at: datetime | None = None
    updated_by_user_id: UUID | None = None
    deleted_at: datetime | None = None
    deleted_by_user_id: UUID | None = None


class CustomObjectField(StrEnum):
    id = "id"
    organization_id = "organization_id"
    cobject_metadata_id = "cobject_metadata_id"
    values = "values"
    created_at = "created_at"
    created_by_user_id = "created_by_user_id"
    updated_at = "updated_at"
    updated_by_user_id = "updated_by_user_id"
    deleted_at = "deleted_at"
    deleted_by_user_id = "deleted_by_user_id"


class CustomObjectCDCEventProcessor(
    EnrichedCDCEventProcessor[CustomObjectDataView, CustomObjectRecord]
):
    """Process CDC events for custom objects."""

    def __init__(self, metadata_cache: MetadataCache) -> None:
        self.metadata_cache = metadata_cache

    async def process_domain_enriched_event(
        self,
        domain_event: DomainEnrichedCDCEvent[CustomObjectDataView, CustomObjectRecord],
    ) -> CustomObjectRecord:
        if domain_event.after is None:
            raise ValueError("Cannot process event with no 'after' state")

        logger.bind(
            event_before=domain_event.before, event_after=domain_event.after
        ).info("Processing domain[custom_object] enriched event")

        # Handle search indexing if enabled
        if settings.enable_search_indexing:
            await index_custom_object_handler(domain_event)

        return await self.get_domain_model(domain_event.after, "after")

    async def get_domain_model(
        self, event: CDCObject, event_state: str
    ) -> CustomObjectRecord:
        if not isinstance(event, CustomObjectDataView):
            raise ValueError(f"Expected CustomObjectView, got {type(event)}")

        metadata = await self.metadata_cache.get_or_fetch(event.cobject_metadata_id)

        # Create mapping of slot numbers to field metadata
        slot_to_metadata = {field["slot_number"]: field for field in metadata["fields"]}

        # First collect values with slot numbers
        slot_values: dict[int, CustomFieldData] = {}
        for i in range(
            1, 101
        ):  # Assuming max 100 value slots.This may break in the future.
            value = getattr(event, f"value_{i}", None)
            if value is not None:
                slot_values[i] = value
                logger.debug(f"Found value for slot {i}: {value}")

        # Convert slot numbers to field names and include version
        field_values = {}
        for slot, value in slot_values.items():
            if slot in slot_to_metadata:
                field_metadata = slot_to_metadata[slot]
                field_name = field_metadata["field_name"]
                field_id = UUID(str(field_metadata["field_id"]))

                # value is now a CustomFieldData object
                field_value = value.value_by_field_id.get(field_id)
                if field_value:
                    field_values[field_name] = {
                        "data": field_value.to_generic_value(),
                        "version": field_metadata["field_version"],
                    }
                else:
                    field_values[field_name] = {
                        "data": None,
                        "version": field_metadata["field_version"],
                    }

        # Create the domain model without extension_id
        return CustomObjectRecord(
            id=event.id,
            organization_id=event.organization_id,
            cobject_metadata_id=event.cobject_metadata_id,
            values=field_values,
            display_name=event.display_name or "",
            created_at=event.created_at,
            created_by_user_id=event.created_by_user_id,
            updated_at=event.updated_at if event.updated_at else None,
            updated_by_user_id=event.updated_by_user_id
            if event.updated_by_user_id
            else None,
            deleted_at=event.deleted_at if event.deleted_at else None,
            deleted_by_user_id=event.deleted_by_user_id
            if event.deleted_by_user_id
            else None,
        )

    def get_table_model(self, event: CDCObject) -> CustomObjectDataView:
        if not isinstance(event, CustomObjectDataView):
            raise ValueError(f"Expected CustomObjectView, got {type(event)}")
        return event

    def to_es_document(
        self,
        custom_object: CustomObjectRecord,
        indexed_at: datetime,
    ) -> ESCustomObjectData:
        """Convert custom object to Elasticsearch document."""
        # This is not implemented because we don't need to index custom objects in event processor.
        raise NotImplementedError("Not implemented")
