import time
from typing import Any, ClassVar
from uuid import UUID

from salestech_be.common.exception.exception import ResourceNotFoundError
from salestech_be.db.dao.generic_repository import GenericRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.cfield_metadata import CFieldMetadata
from salestech_be.settings import settings


class MetadataRepository(GenericRepository):
    """Repository for handling CFieldMetadata operations."""

    async def list_active_fields(self, metadata_id: UUID) -> list[CFieldMetadata]:
        """List all active fields for a given metadata ID."""
        return await self._find_by_column_values(
            table_model=CFieldMetadata,
            cobject_metadata_id=metadata_id,
            exclude_deleted_or_archived=True,
        )


class MetadataCache:
    _engine: ClassVar[DatabaseEngine | None] = None

    def __init__(
        self, engine: DatabaseEngine | None = None, cache_ttl: int = 300
    ) -> None:
        """Initialize metadata cache.

        Args:
            engine: Optional database engine. If not provided, creates a new one.
            cache_ttl: Time-to-live for cache entries in seconds. Defaults to 5 minutes.
        """
        self._cache: dict[  # type: ignore[explicit-any] # TODO: fix-any-annotation
            UUID, tuple[float, dict[str, Any]]
        ] = {}  # (timestamp, data)
        self._cache_ttl = cache_ttl
        if engine is not None:
            MetadataCache._engine = engine
        elif MetadataCache._engine is None:
            MetadataCache._engine = DatabaseEngine(url=str(settings.db_url))

        # Use self.engine property to ensure non-None engine
        self._repository = MetadataRepository(engine=self.engine)

    @property
    def engine(self) -> DatabaseEngine:
        if self._engine is None:
            raise ResourceNotFoundError("Database engine not initialized")
        return self._engine

    async def get_or_fetch(self, metadata_id: UUID) -> dict[str, Any]:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        """Get metadata from cache or fetch it."""
        current_time = time.time()

        if metadata_id in self._cache:
            timestamp, data = self._cache[metadata_id]
            if current_time - timestamp <= self._cache_ttl:
                return data
            # Cache entry expired, remove it
            del self._cache[metadata_id]

        metadata = await self._fetch_metadata(metadata_id)
        self._cache[metadata_id] = (current_time, metadata)
        return metadata

    async def _fetch_metadata(self, metadata_id: UUID) -> dict[str, Any]:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        """Fetch metadata from the database."""
        fields = await self._repository.list_active_fields(metadata_id)

        if not fields:
            raise ResourceNotFoundError(
                f"No fields found for metadata_id {metadata_id}"
            )

        return {
            "fields": [
                {
                    "field_id": field.id,
                    "slot_number": field.slot_number,
                    "field_name": field.field_name,
                    "field_version": field.field_version,
                    "field_display_name": field.field_display_name,
                    "properties": field.properties,
                }
                for field in fields
            ]
        }

    @classmethod
    async def close(cls) -> None:
        """Close the database engine."""
        if cls._engine is not None:
            await cls._engine.close()
            cls._engine = None
