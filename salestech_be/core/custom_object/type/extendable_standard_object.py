from enum import StrEnum

from salestech_be.common.schema_manager.std_object_field_identifier import (
    StdObjectIdentifiers,
)


class ExtendableStandardObject(StrEnum):
    account = StdObjectIdentifiers.account
    contact = StdObjectIdentifiers.contact
    task = StdObjectIdentifiers.task
    meeting = StdObjectIdentifiers.meeting
    pipeline = StdObjectIdentifiers.pipeline
    comment = StdObjectIdentifiers.comment

    @classmethod
    def members(cls) -> list["ExtendableStandardObject"]:
        return sorted(cls)
