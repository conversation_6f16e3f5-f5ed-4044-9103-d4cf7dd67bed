from typing import Annotated
from uuid import UUID, uuid4

from fastapi import Depends

from salestech_be.common.exception.exception import ResourceNotFoundError
from salestech_be.common.singleton import Singleton
from salestech_be.core.custom_prompt.types import (
    CreateCustomPromptRequest,
    UpdateCustomPromptRequest,
)
from salestech_be.db.dao.custom_prompt_repository import CustomPromptRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.custom_prompt import CustomPrompt, CustomPromptUpdate
from salestech_be.util.time import zoned_utc_now


class CustomPromptService:
    """Service for custom prompt operations."""

    def __init__(
        self, custom_prompt_repository: Annotated[CustomPromptRepository, Depends()]
    ) -> None:
        self.custom_prompt_repository = custom_prompt_repository

    async def get_custom_prompt_by_id(
        self, organization_id: UUID, custom_prompt_id: UUID
    ) -> CustomPrompt:
        """Get a custom prompt by ID.

        Args:
            organization_id: The organization ID
            custom_prompt_id: The custom prompt ID
        """
        custom_prompt = await self.custom_prompt_repository.find_by_custom_prompt_id(
            organization_id, custom_prompt_id
        )
        if not custom_prompt:
            raise ResourceNotFoundError(
                f"Custom prompt {custom_prompt_id} not found for organization {organization_id}"
            )
        return custom_prompt

    async def create_custom_prompt(
        self, organization_id: UUID, user_id: UUID, request: CreateCustomPromptRequest
    ) -> CustomPrompt:
        """Create a new custom prompt.

        Args:
            organization_id: The organization ID
            user_id: The user ID
            custom_prompt_request: The custom prompt request to create
        """
        now = zoned_utc_now()
        custom_prompt = CustomPrompt(
            id=uuid4(),
            organization_id=organization_id,
            name=request.name,
            prompt_text=request.prompt_text,
            created_at=now,
            created_by_user_id=user_id,
            is_active=request.is_active,
            associated_fields=request.associated_fields,
            filters=request.filters,
            updated_at=now,
            updated_by_user_id=user_id,
            deleted_at=None,
            deleted_by_user_id=None,
        )
        return await self.custom_prompt_repository.create(custom_prompt)

    async def update_custom_prompt(
        self,
        organization_id: UUID,
        user_id: UUID,
        custom_prompt_id: UUID,
        request: UpdateCustomPromptRequest,
    ) -> CustomPrompt:
        """Update an existing custom prompt.

        Args:
            organization_id: The organization ID
            user_id: The user ID
            custom_prompt_id: The custom prompt ID
            custom_prompt_request: The custom prompt request to update
        """
        custom_prompt = await self.custom_prompt_repository.find_by_custom_prompt_id(
            organization_id, custom_prompt_id
        )

        if not custom_prompt:
            raise ResourceNotFoundError(
                f"Custom prompt {custom_prompt_id} not found for organization {organization_id}"
            )

        update = CustomPromptUpdate(
            name=request.name,
            prompt_text=request.prompt_text,
            is_active=request.is_active,
            associated_fields=request.associated_fields,
            filters=request.filters,
            updated_at=zoned_utc_now(),
            updated_by_user_id=user_id,
        )
        return await self.custom_prompt_repository.update(
            custom_prompt_id, organization_id, update
        )

    async def delete_custom_prompt(
        self, organization_id: UUID, user_id: UUID, custom_prompt_id: UUID
    ) -> None:
        """Delete a custom prompt.

        Args:
            organization_id: The organization ID
            user_id: The user ID
            custom_prompt_id: The custom prompt ID
        """
        custom_prompt = await self.custom_prompt_repository.find_by_custom_prompt_id(
            organization_id, custom_prompt_id
        )

        if not custom_prompt:
            raise ResourceNotFoundError(
                f"Custom prompt {custom_prompt_id} not found for organization {organization_id}"
            )

        await self.custom_prompt_repository.mark_deleted(
            organization_id, custom_prompt_id, user_id
        )

    async def list_custom_prompts_by_organization(
        self, organization_id: UUID
    ) -> list[CustomPrompt]:
        """List all custom prompts for an organization.

        Args:
            organization_id: The organization ID
        """
        return await self.custom_prompt_repository.list_by_organization(organization_id)


class SingletonCustomPromptService(Singleton, CustomPromptService):
    pass


def get_custom_prompt_service(
    db_engine: DatabaseEngine,
) -> CustomPromptService:
    if SingletonCustomPromptService.has_instance():
        return SingletonCustomPromptService.get_singleton_instance()
    return SingletonCustomPromptService(
        custom_prompt_repository=CustomPromptRepository(engine=db_engine),
    )
