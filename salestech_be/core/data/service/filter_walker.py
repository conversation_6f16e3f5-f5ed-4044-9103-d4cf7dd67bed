from datetime import datetime, timedelta
from typing import Final

from salestech_be.common.query_util.filter_schema import (
    CompositeFilter,
    ValueFilter,
)
from salestech_be.common.query_util.filter_walker import (
    CompositeFilterWalker,
    DefaultCompositeFilterWalker,
    ValueFilterWalker,
)
from salestech_be.common.query_util.operator import MatchOperator
from salestech_be.core.data.service._translator import (
    AbstractTranslator,
    AccountPagePrimaryAccountIdFilterTranslator,
    AccountPagePrimaryEmailFilterTranslator,
    ContactPagePrimaryAccountIdFilterTranslator,
    ContactPagePrimaryEmailFilterTranslator,
    PipelinePagePrimaryAccountIdTranslator,
    PipelinePagePrimaryEmailTranslator,
)


class DomainValueFilterWalker(ValueFilterWalker):
    def __call__(self, filter_: ValueFilter) -> CompositeFilter | ValueFilter:
        translators: list[type[AbstractTranslator]] = [
            ContactPagePrimaryEmailFilterTranslator,
            ContactPagePrimaryAccountIdFilterTranslator,
            AccountPagePrimaryEmailFilterTranslator,
            AccountPagePrimaryAccountIdFilterTranslator,
            PipelinePagePrimaryEmailTranslator,
            PipelinePagePrimaryAccountIdTranslator,
        ]
        for translator in translators:
            if translated_filter := translator().translate(filter_):
                return translated_filter
        return filter_


class TimestampDayEQFilterWalker(ValueFilterWalker):
    def __call__(self, filter_: ValueFilter) -> CompositeFilter | ValueFilter:  # noqa: PLR0911
        """
        This is a translator built as a workaround for the fact that we don't support
        "date" truncated filter precision in our system yet.

        This translator will convert a filter like:
        EQ case:
        - field: "created_at"
        - operator: "EQ"
        - value: "2025-06-03T23:59:59<TZ>"

        into a filter like:

        - field: "created_at"
        - operator: "GTE"
        - value: "2025-06-03T00:00:00<TZ>"
        and
        - field: "created_at"
        - operator: "LT"
        - value: "2025-06-04T00:00:00<TZ>"

        NE case:
        - field: "created_at"
        - operator: "NE"
        - value: "2025-06-03T23:59:59<TZ>"

        into a filter like:
        - field: "created_at"
        - operator: "LT"
        - value: "2025-06-03T00:00:00<TZ>"
        and
        - field: "created_at"
        - operator: "GTE"
        - value: "2025-06-04T00:00:00<TZ>"

        GT case:
        - field: "created_at"
        - operator: "GT"
        - value: "2025-06-03T23:59:59<TZ>"
        into a filter like:
        - field: "created_at"
        - operator: "GTE"
        - value: "2025-06-04T00:00:00<TZ>"

        GTE case:
        - field: "created_at"
        - operator: "GTE"
        - value: "2025-06-03T23:59:59<TZ>"
        into a filter like:
        - field: "created_at"
        - operator: "GTE"
        - value: "2025-06-03T00:00:00<TZ>"

        LT case:
        - field: "created_at"
        - operator: "LT"
        - value: "2025-06-03T23:59:59<TZ>"
        into a filter like:
        - field: "created_at"
        - operator: "LT"
        - value: "2025-06-03T00:00:00<TZ>"

        LTE case:
        - field: "created_at"
        - operator: "LTE"
        - value: "2025-06-03T23:59:59<TZ>"
        into a filter like:
        - field: "created_at"
        - operator: "LT"
        - value: "2025-06-04T00:00:00<TZ>"

        The implicit contract is that FE will always pass in the "end of day" value for the purpose of date truncation for now.
        so we can safely truncate the time part.

        TODO: remove this translator once we support "date" truncated filter predicates.
        https://linear.app/reevo/issue/REEVO-8958/long-term-properly-support-datetime-truncation-predicates
        """
        if not isinstance(filter_.value, datetime):
            return filter_

        if filter_.operator not in {
            MatchOperator.EQ,
            MatchOperator.NE,
            MatchOperator.GT,
            MatchOperator.GTE,
            MatchOperator.LT,
            MatchOperator.LTE,
        }:
            return filter_
        if (
            filter_.value.hour,
            filter_.value.minute,
            filter_.value.second,
        ) != (23, 59, 59):
            return filter_

        current_day_start = filter_.value.replace(
            hour=0, minute=0, second=0, microsecond=0
        )
        next_day_start = current_day_start + timedelta(days=1)

        match filter_.operator:
            case MatchOperator.EQ:
                # >= current_day and < next day
                return CompositeFilter(
                    all_of=[
                        ValueFilter(
                            field=filter_.field,
                            operator=MatchOperator.GTE,
                            value=current_day_start,
                        ),
                        ValueFilter(
                            field=filter_.field,
                            operator=MatchOperator.LT,
                            value=next_day_start,
                        ),
                    ]
                )
            case MatchOperator.NE:
                # < current_day_start or >= next_day_start
                return CompositeFilter(
                    any_of=[
                        ValueFilter(
                            field=filter_.field,
                            operator=MatchOperator.LT,
                            value=current_day_start,
                        ),
                        ValueFilter(
                            field=filter_.field,
                            operator=MatchOperator.GTE,
                            value=next_day_start,
                        ),
                    ]
                )
            case MatchOperator.GT:
                # after current day is
                # >= next_day_start
                return ValueFilter(
                    field=filter_.field,
                    operator=MatchOperator.GTE,
                    value=next_day_start,
                )
            case MatchOperator.GTE:
                # after or on current day is
                # >= current_day_start
                return ValueFilter(
                    field=filter_.field,
                    operator=MatchOperator.GTE,
                    value=current_day_start,
                )
            case MatchOperator.LT:
                # before current day is
                # < current_day_start
                return ValueFilter(
                    field=filter_.field,
                    operator=MatchOperator.LT,
                    value=current_day_start,
                )
            case MatchOperator.LTE:
                # before or on current day is
                # < next_day_start
                return ValueFilter(
                    field=filter_.field,
                    operator=MatchOperator.LT,
                    value=next_day_start,
                )
            case _:
                return filter_


_timestamp_value_filter_walker: Final[ValueFilterWalker] = TimestampDayEQFilterWalker()

_timestamp_composite_filter_walker: Final[CompositeFilterWalker] = (
    DefaultCompositeFilterWalker(_timestamp_value_filter_walker)
)


def walk_timestamp_filters(
    filter_: CompositeFilter | ValueFilter,
) -> CompositeFilter | ValueFilter:
    if isinstance(filter_, ValueFilter):
        return _timestamp_value_filter_walker(filter_)
    else:
        return _timestamp_composite_filter_walker(filter_)
