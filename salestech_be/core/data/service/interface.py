# from collections.abc import Mapping, Sequence
# from typing import TypeVar
# from uuid import UUID

# from salestech_be.common.type.metadata.schema import QualifiedField, Relationship
# from salestech_be.core.data.types import ModeledObjectRecord
# from salestech_be.core.data.util import ObjectRecordFetchConditions

# ModeledObjectRecordOutT = TypeVar("ModeledObjectRecordOutT", bound=ModeledObjectRecord)
# ModeledObjectRecordInT = TypeVar("ModeledObjectRecordInT", bound=ModeledObjectRecord)


# async def relationship_fetcher(
#     object_record_type: type[ModeledObjectRecordOutT],
#     organization_id: UUID,
#     primary_record_ids: Sequence[tuple[QualifiedField, UUID]],
#     relationship: Relationship,
#     fetch_conditions: ObjectRecordFetchConditions | None = None,
# ) -> Mapping[UUID, Sequence[ModeledObjectRecordOutT]]:
#     return {}


# """
# So we need to pair of "relationship" and "object_record_type" to do exhaustive fetching check.

# 1. MedataService maintain the relationship definition instead of the domain models.
# 2. Given a primary record and desired relationship, MetadataService should be able to extract out the ids used to fetch the related records.
# 3. Then QueryService get MetadataService to supply the "relationship_fetcher" function and execute it.
# """
