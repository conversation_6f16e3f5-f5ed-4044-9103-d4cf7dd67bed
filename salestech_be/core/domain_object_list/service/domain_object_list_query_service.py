from collections.abc import Sequence
from typing import Annotated
from uuid import UUID

from fastapi import Depends, Request
from frozendict import frozendict

from salestech_be.common.lifespan import get_db_engine
from salestech_be.common.schema_manager.std_object_field_identifier import (
    StdObjectIdentifiers,
)
from salestech_be.common.singleton import Singleton
from salestech_be.core.common.domain_service import DomainQueryService
from salestech_be.core.data.types import StandardRecord
from salestech_be.core.domain_object_list.types import (
    DomainObjectList,
    DomainObjectListItem,
    DomainObjectListItemType,
)
from salestech_be.core.ff.feature_flag_service import (
    FeatureFlagService,
    get_feature_flag_service,
)
from salestech_be.db.dao.domain_object_list_repository import DomainObjectListRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.domain_object_list_item import (
    DomainObjectListItemType as DomainObjectListItemItemType,
)


class DomainObjectListQueryService(DomainQueryService[DomainObjectList]):
    def __init__(
        self,
        domain_object_list_repository: Annotated[DomainObjectListRepository, Depends()],
        feature_flag_service: Annotated[
            FeatureFlagService, Depends(get_feature_flag_service)
        ],
    ) -> None:
        super().__init__(feature_flag_service=feature_flag_service)
        self.domain_object_list_repository = domain_object_list_repository

    async def list_domain_object_list_items(
        self,
        organization_id: UUID,
        list_id: UUID,
    ) -> Sequence[StandardRecord[DomainObjectListItem]]:
        db_records = (
            await self.domain_object_list_repository.list_domain_object_list_items(
                organization_id=organization_id,
                list_id=list_id,
            )
        )
        result: Sequence[StandardRecord[DomainObjectListItem]] = [
            StandardRecord[DomainObjectListItem](
                object_id=StdObjectIdentifiers.domain_object_list_item.identifier,
                data=DomainObjectListItem.from_db_record(db_record),
                requested_relationships=set(),
                related_records={},
            )
            for db_record in db_records
        ]

        return result

    async def map_domain_object_lists_by_account_id(
        self,
        organization_id: UUID,
        account_ids: set[UUID],
        include_deleted: bool = False,
    ) -> frozendict[UUID, list[DomainObjectList]]:
        return await self._map_domain_object_lists_by_reference_id(
            organization_id=organization_id,
            reference_type=DomainObjectListItemType.ACCOUNT,
            reference_ids=account_ids,
            include_deleted=include_deleted,
        )

    async def map_domain_object_lists_by_contact_id(
        self,
        organization_id: UUID,
        contact_ids: set[UUID],
        include_deleted: bool = False,
    ) -> frozendict[UUID, list[DomainObjectList]]:
        return await self._map_domain_object_lists_by_reference_id(
            organization_id=organization_id,
            reference_type=DomainObjectListItemType.CONTACT,
            reference_ids=contact_ids,
            include_deleted=include_deleted,
        )

    async def _map_domain_object_lists_by_reference_id(
        self,
        organization_id: UUID,
        reference_type: DomainObjectListItemType,
        reference_ids: set[UUID],
        include_deleted: bool = False,
    ) -> frozendict[UUID, list[DomainObjectList]]:
        db_records = await self.domain_object_list_repository.map_domain_object_lists_by_reference_id(
            organization_id=organization_id,
            reference_type=DomainObjectListItemItemType(reference_type),
            reference_ids=reference_ids,
            include_deleted=include_deleted,
        )
        return frozendict(
            {
                reference_id: [
                    DomainObjectList.from_db_record(db_record)
                    for db_record in db_records
                ]
                for reference_id, db_records in db_records.items()
            }
        )


class SingletonDomainObjectListQueryService(Singleton, DomainObjectListQueryService):
    pass


def get_domain_object_list_query_service_by_db_engine(
    db_engine: DatabaseEngine,
) -> DomainObjectListQueryService:
    if SingletonDomainObjectListQueryService.has_instance():
        return SingletonDomainObjectListQueryService.get_singleton_instance()
    return DomainObjectListQueryService(
        domain_object_list_repository=DomainObjectListRepository(engine=db_engine),
        feature_flag_service=get_feature_flag_service(),
    )


def get_domain_object_list_query_service(
    request: Request,
) -> DomainObjectListQueryService:
    return get_domain_object_list_query_service_by_db_engine(
        db_engine=get_db_engine(request)
    )
