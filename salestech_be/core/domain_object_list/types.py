from datetime import datetime
from typing import Annotated
from uuid import UUID

from salestech_be.common.schema_manager.std_object_field_identifier import (
    AccountField,
    ContactField,
    DomainObjectListField,
    DomainObjectListItemField,
    PipelineField,
    StdObjectIdentifiers,
)
from salestech_be.common.schema_manager.std_object_relationship import (
    DomainObjectListItemRelationship,
)
from salestech_be.common.type.metadata.field.field_indexable_config import (
    UniqueIndexableConfig,
)
from salestech_be.common.type.metadata.field.field_type_property import (
    DefaultEnumFieldProperty,
    TextFieldProperty,
    TimestampFieldProperty,
    UUIDFieldProperty,
)
from salestech_be.common.type.metadata.schema import (
    OutboundRelationship,
)
from salestech_be.core.common.types import DomainModel, FieldMetadata
from salestech_be.db.models.domain_object_list import (
    DomainObjectList as DbDomainObjectList,
)
from salestech_be.db.models.domain_object_list import (
    DomainObjectListItemType,
)
from salestech_be.db.models.domain_object_list_item import (
    DomainObjectListItem as DbDomainObjectListItem,
)
from salestech_be.util.enum_util import NameValueStrEnum


class DomainObjectListOrigin(NameValueStrEnum):
    USER = "USER"
    IMPORT = "IMPORT"


class DomainObjectListProcessingStatus(NameValueStrEnum):
    ACTIVE = "ACTIVE"
    INACTIVE = "INACTIVE"


class DomainObjectListItem(DomainModel):
    object_id = StdObjectIdentifiers.domain_object_list_item.identifier
    object_display_name = "List Item"
    field_name_provider = DomainObjectListItemField
    inbound_relationships = ()
    outbound_relationships = (
        OutboundRelationship(
            relation_type=OutboundRelationship.RelationType.LOOKUP,
            id=DomainObjectListItemRelationship.domain_object_list_item__to__account,
            relationship_name="Account",
            self_object_identifier=StdObjectIdentifiers.domain_object_list_item.identifier,
            related_object_identifier=StdObjectIdentifiers.account.identifier,
            self_cardinality=OutboundRelationship.Cardinality.MANY,
            related_object_cardinality=OutboundRelationship.Cardinality.ONE,
            ordered_self_field_identifiers=(
                DomainObjectListItemField.reference_id.identifier,
            ),
            ordered_related_field_identifiers=(AccountField.id.identifier,),
        ),
        OutboundRelationship(
            relation_type=OutboundRelationship.RelationType.LOOKUP,
            id=DomainObjectListItemRelationship.domain_object_list_item__to__contact,
            relationship_name="Contact",
            self_object_identifier=StdObjectIdentifiers.domain_object_list_item.identifier,
            related_object_identifier=StdObjectIdentifiers.contact.identifier,
            self_cardinality=OutboundRelationship.Cardinality.MANY,
            related_object_cardinality=OutboundRelationship.Cardinality.ONE,
            ordered_self_field_identifiers=(
                DomainObjectListItemField.reference_id.identifier,
            ),
            ordered_related_field_identifiers=(ContactField.id.identifier,),
        ),
        OutboundRelationship(
            relation_type=OutboundRelationship.RelationType.LOOKUP,
            id=DomainObjectListItemRelationship.domain_object_list_item__to__pipeline,
            relationship_name="Pipeline",
            self_object_identifier=StdObjectIdentifiers.domain_object_list_item.identifier,
            related_object_identifier=StdObjectIdentifiers.pipeline.identifier,
            self_cardinality=OutboundRelationship.Cardinality.MANY,
            related_object_cardinality=OutboundRelationship.Cardinality.ONE,
            ordered_self_field_identifiers=(
                DomainObjectListItemField.reference_id.identifier,
            ),
            ordered_related_field_identifiers=(PipelineField.id.identifier,),
        ),
    )

    domain_object_list_id: Annotated[
        UUID,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Domain Object List ID",
                is_ui_displayable=False,
                is_ui_editable=False,
                index_config=UniqueIndexableConfig(is_indexed=True, is_unique=False),
            ),
        ),
    ]

    reference_type: Annotated[
        DomainObjectListItemType,
        FieldMetadata(
            type_property=DefaultEnumFieldProperty(
                field_display_name="Reference Type",
                enum_class=DomainObjectListItemType,
                is_ui_displayable=False,
                is_ui_editable=False,
            )
        ),
    ]

    reference_id: Annotated[
        UUID,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Reference ID",
                is_ui_displayable=False,
                is_ui_editable=False,
                index_config=UniqueIndexableConfig(is_indexed=True, is_unique=False),
            ),
        ),
    ]

    organization_id: Annotated[
        UUID,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Organization ID",
                is_ui_displayable=False,
                is_ui_editable=False,
                index_config=UniqueIndexableConfig(is_indexed=True, is_unique=False),
            ),
        ),
    ]

    created_at: Annotated[
        datetime,
        FieldMetadata(
            type_property=TimestampFieldProperty(
                field_display_name="Created At",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ]

    created_by_user_id: Annotated[
        UUID,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Created By",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ]

    @staticmethod
    def from_db_record(db_record: DbDomainObjectListItem) -> "DomainObjectListItem":
        return DomainObjectListItem(
            domain_object_list_id=db_record.domain_object_list_id,
            reference_type=DomainObjectListItemType(db_record.reference_type),
            reference_id=db_record.reference_id,
            organization_id=db_record.organization_id,
            created_at=db_record.created_at,
            created_by_user_id=db_record.created_by_user_id,
        )


class DomainObjectList(DomainModel):
    object_id = StdObjectIdentifiers.domain_object_list.identifier
    object_display_name = "List"
    field_name_provider = DomainObjectListField
    inbound_relationships = ()
    outbound_relationships = ()

    id: Annotated[
        UUID,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="ID",
                is_ui_displayable=False,
                is_ui_editable=False,
                index_config=UniqueIndexableConfig(is_indexed=True, is_unique=True),
            ),
        ),
    ]

    organization_id: Annotated[
        UUID,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Organization ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            ),
        ),
    ]

    name: Annotated[
        str,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Name",
                is_ui_displayable=True,
                is_ui_editable=True,
            )
        ),
    ]

    description: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Description",
                is_ui_displayable=True,
                is_ui_editable=True,
            )
        ),
    ] = None

    processing_status: Annotated[
        DomainObjectListProcessingStatus,
        FieldMetadata(
            type_property=DefaultEnumFieldProperty(
                field_display_name="Processing Status",
                enum_class=DomainObjectListProcessingStatus,
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ]

    item_type: Annotated[
        DomainObjectListItemType,
        FieldMetadata(
            type_property=DefaultEnumFieldProperty(
                field_display_name="Item Type",
                enum_class=DomainObjectListItemType,
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ]

    owner_user_id: Annotated[
        UUID,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Owner User ID",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ]

    origin: Annotated[
        DomainObjectListOrigin,
        FieldMetadata(
            type_property=DefaultEnumFieldProperty(
                field_display_name="Origin",
                enum_class=DomainObjectListOrigin,
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ]

    created_at: Annotated[
        datetime,
        FieldMetadata(
            type_property=TimestampFieldProperty(
                field_display_name="Created At",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ]

    created_by_user_id: Annotated[
        UUID,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Created By",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ]

    updated_at: Annotated[
        datetime | None,
        FieldMetadata(
            type_property=TimestampFieldProperty(
                field_display_name="Updated At",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ] = None

    updated_by_user_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Updated By",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ] = None

    deleted_at: Annotated[
        datetime | None,
        FieldMetadata(
            type_property=TimestampFieldProperty(
                field_display_name="Deleted At",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ] = None

    deleted_by_user_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Deleted By",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ] = None

    @staticmethod
    def from_db_record(db_record: DbDomainObjectList) -> "DomainObjectList":
        return DomainObjectList(
            id=db_record.id,
            organization_id=db_record.organization_id,
            name=db_record.name,
            description=db_record.description,
            processing_status=DomainObjectListProcessingStatus(
                db_record.processing_status
            ),
            item_type=DomainObjectListItemType(db_record.item_type),
            owner_user_id=db_record.owner_user_id,
            origin=DomainObjectListOrigin(db_record.origin),
            created_at=db_record.created_at,
            created_by_user_id=db_record.created_by_user_id,
            updated_at=db_record.updated_at,
            updated_by_user_id=db_record.updated_by_user_id,
            deleted_at=db_record.deleted_at,
            deleted_by_user_id=db_record.deleted_by_user_id,
        )
