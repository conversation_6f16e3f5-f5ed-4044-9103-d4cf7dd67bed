from typing import Annotated
from uuid import UUID

from salestech_be.common.schema_manager.std_object_field_identifier import (
    AttachmentDetailsField,
    StdObjectIdentifiers,
)
from salestech_be.common.type.metadata.field.field_type_property import (
    BooleanCheckboxFieldProperty,
    NumericFieldProperty,
    TextFieldProperty,
    UUIDFieldProperty,
)
from salestech_be.core.common.types import (
    DomainModel,
    FieldMetadata,
)
from salestech_be.db.dto.email_attchment_dto import AttachmentDto
from salestech_be.util.validation import not_none


class AttachmentDetailsV2(DomainModel):
    object_id = StdObjectIdentifiers.attachment_details.identifier
    object_display_name = "Attachment Details"
    field_name_provider = AttachmentDetailsField

    id: Annotated[
        UUID,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            )
        ),
    ]

    content_type: Annotated[
        str,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Content Type",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ]

    file_name: Annotated[
        str,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="File Name",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ]

    content_id: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Content ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            )
        ),
    ] = None

    is_inline: Annotated[
        bool,
        FieldMetadata(
            type_property=BooleanCheckboxFieldProperty(
                field_display_name="Is Inline",
                is_ui_displayable=True,
                is_ui_editable=True,
            )
        ),
    ]

    size: Annotated[
        int,
        FieldMetadata(
            type_property=NumericFieldProperty(
                field_display_name="Size",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ]

    public_url: Annotated[
        str,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Public URL",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ]

    s3_key: Annotated[
        str,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="S3 Key",
                is_ui_displayable=False,
                is_ui_editable=False,
            )
        ),
    ]

    @classmethod
    def from_attachment_dto(
        cls, attachment_dto: AttachmentDto
    ) -> "AttachmentDetailsV2":
        return AttachmentDetailsV2(
            id=attachment_dto.attachment.id,
            content_type=attachment_dto.attachment.content_type,
            content_id=attachment_dto.attachment.provider_content_id,
            file_name=attachment_dto.attachment.file_name,
            is_inline=bool(attachment_dto.attachment.is_inline),
            size=attachment_dto.attachment.size,
            public_url=not_none(attachment_dto.public_url),
            s3_key=not_none(attachment_dto.attachment.s3_key),
        )
