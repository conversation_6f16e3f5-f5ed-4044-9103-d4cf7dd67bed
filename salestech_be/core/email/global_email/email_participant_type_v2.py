from typing import Annotated
from uuid import UUID

from salestech_be.common.schema_manager.std_object_field_identifier import (
    EmailParticipantField,
    StdObjectIdentifiers,
)
from salestech_be.common.type.metadata.field.field_type_property import (
    EmailFieldProperty,
    TextFieldProperty,
    UUIDFieldProperty,
)
from salestech_be.core.common.types import (
    DomainModel,
    FieldMetadata,
)
from salestech_be.core.email.type.email import EmailHydratedParticipant


class EmailParticipantV2(DomainModel):
    object_id = StdObjectIdentifiers.email_participant.identifier
    object_display_name = "Email Participant"
    field_name_provider = EmailParticipantField

    email_account_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Email Account ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            )
        ),
    ] = None

    contact_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Contact ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            )
        ),
    ] = None

    account_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Account ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            )
        ),
    ] = None

    email: Annotated[
        str,
        FieldMetadata(
            type_property=EmailFieldProperty(
                field_display_name="Email",
                is_ui_displayable=False,
                is_ui_editable=False,
            )
        ),
    ]

    name: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Name",
                is_ui_displayable=False,
                is_ui_editable=False,
            )
        ),
    ] = None

    @classmethod
    def from_hydrated_participant(
        cls, participant: EmailHydratedParticipant
    ) -> "EmailParticipantV2":
        return EmailParticipantV2(
            email_account_id=participant.email_account_id,
            contact_id=participant.contact_id,
            account_id=participant.account_id,
            email=participant.email,
            name=participant.name,
        )
