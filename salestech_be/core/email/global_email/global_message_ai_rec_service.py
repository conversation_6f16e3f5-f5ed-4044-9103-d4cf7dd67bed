from typing import Annotated, Literal
from uuid import UUID

from fastapi import Depends

from salestech_be.common.core_crm.sales_action import StandardSalesActionType
from salestech_be.common.exception.exception import ResourceNotFoundError
from salestech_be.common.singleton import Singleton
from salestech_be.common.type.patch_request import UNSET, UnsetAware, specified
from salestech_be.common.type.shape_constrained_model import ShapeConstrainedModel
from salestech_be.core.common.ai_rec_aware_domain_object_service import (
    BaseAIRecAwarePatchService,
)
from salestech_be.core.email.global_email.global_message_type import GlobalMessage
from salestech_be.db.dao.crm_ai_rec_repository import CrmAIRecRepository
from salestech_be.db.dao.thread_repository import ThreadRepository
from salestech_be.db.dbengine.core import DatabaseEngine

PatchGlobalMessageSalesActionsRequestFields = Literal["sales_action_types",]


class PatchGlobalMessageSalesActionsRequest(ShapeConstrainedModel[GlobalMessage]):
    sales_action_types: UnsetAware[list[StandardSalesActionType] | None] = UNSET


class GlobalMessageAIRecService(
    BaseAIRecAwarePatchService[GlobalMessage, PatchGlobalMessageSalesActionsRequest]
):
    def __init__(
        self,
        crm_ai_rec_repository: Annotated[CrmAIRecRepository, Depends()],
        thread_repository: Annotated[ThreadRepository, Depends()],
    ):
        super().__init__(crm_ai_rec_repository=crm_ai_rec_repository)
        self.thread_repository = thread_repository

    @classmethod
    def domain_model_type(cls) -> type[GlobalMessage]:
        return GlobalMessage

    @classmethod
    def record_id(cls, record: GlobalMessage) -> UUID:
        return record.id

    @classmethod
    def patch_request_type(cls) -> type[PatchGlobalMessageSalesActionsRequest]:
        return PatchGlobalMessageSalesActionsRequest

    async def _patch_record(
        self,
        *,
        organization_id: UUID,
        user_id: UUID,
        record_id: UUID,
        patch_request: PatchGlobalMessageSalesActionsRequest,
    ) -> None:
        if specified(patch_request.sales_action_types):
            patched_global_message = (
                await self.thread_repository.put_global_message_sales_activity_types(
                    organization_id=organization_id,
                    user_id=user_id,
                    global_message_id=record_id,
                    sales_activity_types=patch_request.sales_action_types,
                )
            )
            if not patched_global_message:
                raise ResourceNotFoundError(
                    f"GlobalMessage {record_id} not found for organization {organization_id}"
                )


class SingletonGlobalMessageSalesActionsAIRecPatchService(
    Singleton, GlobalMessageAIRecService
):
    pass


def get_global_message_ai_rec_service(
    db_engine: DatabaseEngine,
) -> GlobalMessageAIRecService:
    if SingletonGlobalMessageSalesActionsAIRecPatchService.has_instance():
        return (
            SingletonGlobalMessageSalesActionsAIRecPatchService.get_singleton_instance()
        )
    return SingletonGlobalMessageSalesActionsAIRecPatchService(
        crm_ai_rec_repository=CrmAIRecRepository(engine=db_engine),
        thread_repository=ThreadRepository(engine=db_engine),
    )
