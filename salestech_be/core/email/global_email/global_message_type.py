from typing import Annotated
from uuid import UUID

from pydantic import Field

from salestech_be.common.core_crm.sales_action import StandardSalesActionType
from salestech_be.common.schema_manager.std_object_field_identifier import (
    GlobalMessageField,
    StdObjectIdentifiers,
)
from salestech_be.common.type.metadata.field.field_type_property import (
    BooleanCheckboxFieldProperty,
    DefaultEnumFieldProperty,
    DictFieldProperty,
    ListFieldProperty,
    NestedObjectFieldProperty,
    TextFieldProperty,
    TimestampFieldProperty,
    UUIDFieldProperty,
)
from salestech_be.core.common.types import DomainModel, FieldMetadata
from salestech_be.core.email.global_email.attachment_details_type_v2 import (
    AttachmentDetailsV2,
)
from salestech_be.core.email.global_email.email_participant_type_v2 import (
    EmailParticipantV2,
)
from salestech_be.core.email.global_email.types_v2 import (
    GlobalMessageErrorInfoV2,
    GlobalMessageEventSummaryV2,
)
from salestech_be.db.models.message import MessageStatus
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime


class GlobalMessage(DomainModel):
    object_id = StdObjectIdentifiers.global_message.identifier
    object_display_name = "Global Message"
    field_name_provider = GlobalMessageField

    id: Annotated[
        UUID,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            ),
        ),
    ]

    global_thread_id: Annotated[
        UUID,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Global Thread ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            ),
        ),
    ]

    user_email_account_ids: Annotated[
        list[UUID],
        FieldMetadata(
            type_property=ListFieldProperty(
                element_field_type_property=UUIDFieldProperty(
                    field_display_name="User Email Account ID",
                ),
                field_display_name="User Email Account IDs",
                is_ui_displayable=False,
                is_ui_editable=False,
            ),
        ),
    ]

    message_owner_email_accounts_and_messages_map: Annotated[
        dict[UUID, UUID],
        FieldMetadata(
            type_property=DictFieldProperty(
                field_display_name="Message Owner Email Accounts Map",
                is_ui_displayable=False,
                is_ui_editable=False,
                value_field_type_property=UUIDFieldProperty(
                    field_display_name="Message ID",
                ),
            ),
        ),
    ]

    can_reply: Annotated[
        bool,
        FieldMetadata(
            type_property=BooleanCheckboxFieldProperty(
                field_display_name="Can Reply",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ]

    subject: Annotated[
        str,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Subject",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ]

    status: Annotated[
        MessageStatus | None,
        FieldMetadata(
            type_property=DefaultEnumFieldProperty(
                field_display_name="Status",
                is_ui_displayable=True,
                is_ui_editable=False,
                enum_class=MessageStatus,
            ),
        ),
    ] = None

    send_from: Annotated[
        list[EmailParticipantV2],
        FieldMetadata(
            type_property=NestedObjectFieldProperty(
                object_identifier=StdObjectIdentifiers.email_participant.identifier,
                field_display_name="Send From",
            )
        ),
    ]

    to: Annotated[
        list[EmailParticipantV2],
        FieldMetadata(
            type_property=NestedObjectFieldProperty(
                object_identifier=StdObjectIdentifiers.email_participant.identifier,
                field_display_name="Send To",
            )
        ),
    ]

    cc: Annotated[
        list[EmailParticipantV2] | None,
        FieldMetadata(
            type_property=NestedObjectFieldProperty(
                object_identifier=StdObjectIdentifiers.email_participant.identifier,
                field_display_name="CC",
            )
        ),
    ]

    bcc: Annotated[
        list[EmailParticipantV2] | None,
        FieldMetadata(
            type_property=NestedObjectFieldProperty(
                object_identifier=StdObjectIdentifiers.email_participant.identifier,
                field_display_name="BCC",
            )
        ),
    ]

    reply_to: Annotated[
        list[EmailParticipantV2] | None,
        FieldMetadata(
            type_property=NestedObjectFieldProperty(
                object_identifier=StdObjectIdentifiers.email_participant.identifier,
                field_display_name="Reply To",
            )
        ),
    ]

    snippet: Annotated[
        str,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Snippet",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ]

    body_text: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Body Text",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ]

    body_html: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Body HTML",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ]

    main_body_text: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Body Text",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ]

    main_body_html: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Body HTML",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ]

    send_at: Annotated[
        ZoneRequiredDateTime | None,
        FieldMetadata(
            type_property=TimestampFieldProperty(
                field_display_name="Send At",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ]

    folders: Annotated[
        list[str],
        FieldMetadata(
            type_property=ListFieldProperty(
                element_field_type_property=TextFieldProperty(
                    field_display_name="Folder",
                ),
                field_display_name="Folders",
                is_ui_displayable=False,
                is_ui_editable=False,
            ),
        ),
    ]

    attachment_details: Annotated[
        list[AttachmentDetailsV2] | None,
        FieldMetadata(
            type_property=NestedObjectFieldProperty(
                object_identifier=StdObjectIdentifiers.attachment_details.identifier,
                field_display_name="Attachment Details",
            )
        ),
    ]

    received_date: Annotated[
        ZoneRequiredDateTime | None,
        FieldMetadata(
            type_property=TimestampFieldProperty(
                field_display_name="Received Date",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ]

    reply_to_global_message_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Reply To Global Message ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            ),
        ),
    ]

    error_info: Annotated[
        GlobalMessageErrorInfoV2 | None,
        FieldMetadata(
            type_property=NestedObjectFieldProperty(
                object_identifier=StdObjectIdentifiers.global_message_error_info.identifier,
                field_display_name="Error info.",
            )
        ),
    ]

    email_events: Annotated[
        list[GlobalMessageEventSummaryV2],
        FieldMetadata(
            type_property=NestedObjectFieldProperty(
                object_identifier=StdObjectIdentifiers.global_message_event_summary.identifier,
                field_display_name="Email Events",
            )
        ),
    ] = Field(default_factory=list)

    email_account_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(field_display_name="Email Account ID"),
        ),
    ]

    sales_action_types: Annotated[
        list[StandardSalesActionType] | None,
        FieldMetadata(
            type_property=ListFieldProperty(
                element_field_type_property=DefaultEnumFieldProperty(
                    enum_class=StandardSalesActionType,
                    field_display_name="Sales Action Type",
                ),
                field_display_name="Sales Action Types",
                is_ui_displayable=True,
                is_ui_editable=True,
            ),
        ),
    ] = None
