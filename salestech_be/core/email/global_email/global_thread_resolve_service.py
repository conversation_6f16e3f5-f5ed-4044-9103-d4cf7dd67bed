from typing import Annotated
from uuid import UUID

from fastapi import Depends

from salestech_be.common.singleton import Singleton
from salestech_be.core.contact.service.contact_resolve_service import (
    ContactResolveService,
    get_contact_resolve_service,
)
from salestech_be.core.email.service.message_service import (
    MessageService,
    get_message_service_by_db_engine,
)
from salestech_be.core.email.type.email import EmailHydratedParticipant
from salestech_be.db.dao.thread_repository import ThreadRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.ree_logging import get_logger

logger = get_logger(__name__)


class GlobalThreadResolveService:
    def __init__(
        self,
        contact_resolve_service: Annotated[ContactResolveService, Depends()],
        thread_repository: Annotated[ThreadRepository, Depends()],
        message_service: Annotated[MessageService, Depends()],
    ):
        self.contact_resolve_service = contact_resolve_service
        self.thread_repository = thread_repository
        self.message_service = message_service

    async def _get_participants_from_global_thread(
        self,
        global_thread_id: UUID,
        organization_id: UUID,
    ) -> list[EmailHydratedParticipant]:
        messages = await self.thread_repository.list_messages_by_global_thread_id(
            global_thread_id=global_thread_id,
            organization_id=organization_id,
        )

        return [
            participant
            for message in messages
            for participants in [
                message.bcc,
                message.cc,
                message.send_from,
                message.send_to,
                message.reply_to,
            ]
            if participants
            for participant in participants
        ]

    async def resolve_contact_ids_in_global_thread(
        self,
        global_thread_id: UUID,
        organization_id: UUID,
    ) -> list[UUID] | None:
        participants = await self._get_participants_from_global_thread(
            global_thread_id=global_thread_id,
            organization_id=organization_id,
        )

        contact_id_set = {
            participant.contact_id
            for participant in participants
            if participant.contact_id
        }

        return list(contact_id_set) if contact_id_set else None

    async def resolve_account_ids_in_global_thread(
        self,
        global_thread_id: UUID,
        organization_id: UUID,
    ) -> list[UUID] | None:
        participants = await self._get_participants_from_global_thread(
            global_thread_id=global_thread_id,
            organization_id=organization_id,
        )
        await self.message_service.resolve_and_fill_fields_for_participant_inplace(
            organization_id=organization_id,
            participants=participants,
        )
        account_id_set = {
            participant.account_id
            for participant in participants
            if participant.account_id
        }
        return list(account_id_set) if account_id_set else None

    async def resolve_pipeline_id_in_global_thread(
        self,
        global_thread_id: UUID,
        organization_id: UUID,
    ) -> UUID | None:
        participants = await self._get_participants_from_global_thread(
            global_thread_id=global_thread_id,
            organization_id=organization_id,
        )

        contact_id_set = {
            participant.contact_id
            for participant in participants
            if participant.contact_id
        }

        return await self.contact_resolve_service.get_pipeline_ids_by_contact_ids(
            contact_ids=list(contact_id_set),
            organization_id=organization_id,
        )


class SingletonGlobalThreadResolveService(Singleton, GlobalThreadResolveService):
    pass


def get_global_thread_resolve_service(
    db_engine: DatabaseEngine,
) -> GlobalThreadResolveService:
    if SingletonGlobalThreadResolveService.has_instance():
        return SingletonGlobalThreadResolveService.get_singleton_instance()
    return SingletonGlobalThreadResolveService(
        contact_resolve_service=get_contact_resolve_service(db_engine=db_engine),
        thread_repository=ThreadRepository(engine=db_engine),
        message_service=get_message_service_by_db_engine(db_engine=db_engine),
    )
