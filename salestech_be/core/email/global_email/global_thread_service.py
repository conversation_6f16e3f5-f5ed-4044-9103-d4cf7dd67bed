from datetime import datetime
from uuid import UUID, uuid4

from sqlalchemy.exc import IntegrityError

from salestech_be.common.exception.exception import InvalidArgumentError
from salestech_be.common.singleton import Singleton
from salestech_be.common.type.patch_request import specified
from salestech_be.core.contact.service.contact_resolve_service import (
    ContactResolveService,
    get_contact_resolve_service,
)
from salestech_be.core.email.global_email.global_message_ai_rec_service import (
    GlobalMessageAIRecService,
    get_global_message_ai_rec_service,
)
from salestech_be.core.email.global_email.service_api_schema import (
    PatchGlobalThreadRequest,
)
from salestech_be.db.dao.thread_repository import ThreadRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.dto.email_dto import EmailDto, MessageDto
from salestech_be.db.models.global_thread import (
    GlobalMessage,
    GlobalMessageAssociation,
    GlobalMessageUpdate,
    GlobalThread,
    GlobalThreadUpdate,
)
from salestech_be.db.models.message import EmailProvider, Message
from salestech_be.ree_logging import get_logger
from salestech_be.util.time import zoned_utc_now
from salestech_be.util.validation import not_none

logger = get_logger()


class GlobalThreadService:
    def __init__(
        self,
        thread_repository: ThreadRepository,
        contact_resolve_service: ContactResolveService,
        global_message_ai_rec_service: GlobalMessageAIRecService,
    ):
        self.thread_repository = thread_repository
        self.contact_resolve_service = contact_resolve_service
        self.global_message_ai_rec_service = global_message_ai_rec_service

    async def list_global_thread_by_ids(
        self, global_thread_ids: list[UUID], organization_id: UUID
    ) -> list[GlobalThread]:
        return await self.thread_repository.find_global_thread_by_ids(
            global_thread_ids=global_thread_ids, organization_id=organization_id
        )

    async def get_global_thread_and_sender_email_account_by_global_message_id(
        self, global_message_id: UUID, organization_id: UUID
    ) -> tuple[UUID, UUID | None]:
        global_message = (
            await self.thread_repository.find_by_tenanted_primary_key_or_fail(
                GlobalMessage, id=global_message_id, organization_id=organization_id
            )
        )
        # find sender email account id from
        global_message_message_associations = await self.thread_repository.find_message_associations_by_global_message_ids(
            global_message_ids=[global_message_id],
            organization_id=organization_id,
        )
        if not global_message_message_associations:
            raise ValueError("Global Message missing association record")
        # find any message and fetch the sender email account id
        message_id = global_message_message_associations[0].message_id
        messages = await self.thread_repository.find_messages_by_ids(
            message_ids=[message_id], organization_id=organization_id
        )
        if not messages:
            raise ValueError(f"Message {message_id} not exists.")
        return global_message.global_thread_id, messages[0].send_from[
            0
        ].email_account_id

    async def construct_global_thread_and_messages(
        self,
        organization_id: UUID,
        email_dto: EmailDto,
        account_ids: list[UUID] | None = None,
        pipeline_id: UUID | None = None,
        sequence_id: UUID | None = None,
    ) -> tuple[GlobalThread, dict[UUID, tuple[Message, bool]]]:
        """
        Unified entrypoint for global thread construction
        1: find existing global thread
            - by thread_id
            - by all reference_ids
        2: if not found, create a new global thread
        3: update the global thread with email_dto
        4: upsert global message and association
        5: return the global thread and the new message map
        Args:
            organization_id: The organization ID
            email_dto: The email DTO
            account_ids: The account IDs if provided use it to update the global thread, otherwise resolve it from contact_ids
            pipeline_id: The pipeline ID if provided use it to update the global thread, otherwise resolve it from contact_ids
            sequence_id: The sequence ID if email is from a sequence
        """

        new_message_map: dict[UUID, tuple[Message, bool]] = {}

        # 1 handle global thread creation
        global_thread, is_new_global_thread = await self.get_or_create_global_thread(
            organization_id=organization_id,
            email_dto=email_dto,
            sequence_id=sequence_id,
        )
        logger.bind(
            global_thread_id=global_thread.id,
            is_new_global_thread=is_new_global_thread,
            thread_id=email_dto.thread.id,
        ).info("global thread creation from email_dto")

        # 2: update global thread
        # only allow to update optional account_ids and pipeline_id if it is a new global thread
        if not is_new_global_thread:
            account_ids = None
            pipeline_id = None
        updated_global_thread = await self.update_global_thread(
            organization_id,
            global_thread,
            email_dto,
            account_ids,
            pipeline_id,
        )

        # 3: upsert global message and association
        for message_dto in email_dto.message_dtos:
            if not message_dto.message.headers:
                logger.bind(
                    message_id=message_dto.message.id,
                ).warning("message has no headers")
                continue
            global_message, is_new = await self.upsert_global_message_and_association(
                organization_id, updated_global_thread, message_dto
            )
            new_message_map[global_message.id] = (message_dto.message, is_new)

        return updated_global_thread, new_message_map

    async def get_or_create_global_thread(
        self,
        organization_id: UUID,
        email_dto: EmailDto,
        sequence_id: UUID | None = None,
    ) -> tuple[GlobalThread, bool]:
        """
        Get or create a global thread for a given thread ID and message DTO.

        Args:
            organization_id: Organization ID
            email_dto: Email DTO
        1: find existing global thread
            - by thread_id
            - by all reference_ids
        2: if not found, create a new global thread
        3: return the global thread, and a boolean indicating if it is a new thread
        """
        existing_global_thread = (
            await self.thread_repository.find_global_thread_by_thread_id(
                thread_id=email_dto.thread.id, organization_id=organization_id
            )
        )
        if existing_global_thread:
            return existing_global_thread, False
        # Get all reference IDs including in-reply-to and original message ID from all message_dto.message.headers
        all_reference_ids = list(
            {
                ref_id
                for message_dto in email_dto.message_dtos
                if message_dto.message.headers
                for ref_id in (
                    (
                        [message_dto.message.headers.in_reply_to]
                        if message_dto.message.headers.in_reply_to is not None
                        else []
                    )
                    + (message_dto.message.headers.references or [])
                    + [message_dto.message.headers.message_id]
                )
                if ref_id is not None
            }
        )

        # Also collect original_message_ids from reply_to_message_id
        for message_dto in email_dto.message_dtos:
            if message_dto.message.reply_to_message_id:
                # Find the global message for this reply_to_message_id
                reply_to_global_message = await self.get_global_message_by_message_id(
                    message_dto.message.reply_to_message_id, organization_id
                )
                if (
                    reply_to_global_message
                    and reply_to_global_message.original_message_id
                ):
                    all_reference_ids.append(
                        reply_to_global_message.original_message_id
                    )

        # Remove duplicates while preserving order
        all_reference_ids = list(dict.fromkeys(all_reference_ids))

        # find global thread by all reference_ids
        global_thread = (
            await self.thread_repository.get_global_thread_by_original_message_ids(
                all_reference_ids, organization_id
            )
        )
        logger.bind(
            global_thread=global_thread, all_reference_ids=all_reference_ids
        ).info("find global thread by reference ids")
        if global_thread:
            return global_thread, False
        # create a new global thread, but left account_ids, pipeline_id, earliest_message_date, latest_message_received_date, latest_message_date empty
        # we will update them in the update_global_thread function later
        global_thread = GlobalThread(
            id=uuid4(),
            subject=email_dto.thread.subject,
            snippet=email_dto.thread.snippet,
            contact_ids=[],
            thread_ids=[email_dto.thread.id],
            account_ids=None,
            pipeline_id=None,
            organization_id=organization_id,
            earliest_message_date=None,
            latest_message_received_date=None,
            latest_message_date=None,
            sequence_id=sequence_id,
            created_at=zoned_utc_now(),
            updated_at=zoned_utc_now(),
        )
        await self.thread_repository.insert(global_thread)
        return global_thread, True

    async def patch_global_thread(
        self,
        global_thread_id: UUID,
        organization_id: UUID,
        patch_request: PatchGlobalThreadRequest,
    ) -> GlobalThread:
        global_thread = (
            await self.thread_repository.find_by_tenanted_primary_key_or_fail(
                GlobalThread, id=global_thread_id, organization_id=organization_id
            )
        )
        if specified(patch_request.pipeline_id):
            if (
                global_thread.pipeline_id
                and patch_request.pipeline_id
                and global_thread.pipeline_id != patch_request.pipeline_id
            ):
                # todo xw: figure out how is this handled in integrity migration flow if it's applicable.
                raise InvalidArgumentError(
                    "Directly updating an existing pipeline_id of global thread to another pipeline_id is not allowed"
                )
            return not_none(
                await self.thread_repository.update_by_tenanted_primary_key(
                    GlobalThread,
                    organization_id=organization_id,
                    primary_key_to_value={"id": global_thread_id},
                    column_to_update=GlobalThreadUpdate(
                        pipeline_id=patch_request.pipeline_id
                    ),
                )
            )
        return global_thread

    async def update_global_thread(  # noqa: C901
        self,
        organization_id: UUID,
        global_thread: GlobalThread,
        email_dto: EmailDto,
        account_ids: list[UUID] | None = None,
        pipeline_id: UUID | None = None,
    ) -> GlobalThread:
        """
        Update the global thread metadata with information from email_dto.

        Updates the following fields:
        1. contact_ids: Combined from two sources:
           - message_dto.message.unique_contact_ids from all messages
           - email_dto.thread.participants' contact_ids

        2. account_ids: Resolved from contact_ids using contact_resolve_service

        3. pipeline_id: Resolved from contact_ids using contact_resolve_service

        if account_ids and pipeline_id is provided, update the global thread with account_ids and pipeline_id

        4. latest_message_date: The latest of:
           - latest_message_received_date
           - latest_message_sent_date

        5. earliest_message_date: Updated if email_dto.thread.earliest_message_date is:
           - earlier than existing earliest_message_date, or
           - existing earliest_message_date is None

        6. latest_message_received_date: Updated if email_dto.thread.latest_message_received_date is:
           - later than existing latest_message_received_date, or
           - existing latest_message_received_date is None

        7. thread_ids: Appends email_dto.thread.id if not already in the list

        Args:
            organization_id: The organization ID
            global_thread: The global thread to update
            email_dto: The email DTO containing new information
            account_ids: account ids
            pipeline_id: pipeline id

        Returns:
            The updated global thread
        """
        updates = GlobalThreadUpdate()

        # get all contact_ids from all message_dto.message.unique_contact_ids and thread participants
        contact_ids = list(
            {
                contact_id
                for message_dto in email_dto.message_dtos
                if message_dto.message.headers
                and message_dto.message.unique_contact_ids is not None
                for contact_id in message_dto.message.unique_contact_ids
            }
            | {
                participant.contact_id
                for participant in email_dto.thread.participants
                if participant.contact_id is not None
            }
        )
        if not account_ids:
            # if account_ids is not provided, resolve account_ids from contact_ids
            account_id_map = (
                await self.contact_resolve_service.resolve_account_by_contacts(
                    organization_id=organization_id, contact_ids=contact_ids
                )
            )
            account_ids = list(
                {account_id for account_id in account_id_map.values() if account_id}
            )
        if not pipeline_id:
            # if pipeline_id is not provided, resolve pipeline_id from contact_ids
            pipeline_id = (
                await self.contact_resolve_service.get_pipeline_ids_by_contact_ids(
                    contact_ids=contact_ids, organization_id=organization_id
                )
            )

        # Only update contact_ids if different
        if set(contact_ids) != set(global_thread.contact_ids or []):
            updates.contact_ids = contact_ids

        # Only update account_ids if different
        if set(account_ids or []) != set(global_thread.account_ids or []):
            updates.account_ids = account_ids

        # Only update pipeline_id if different
        if pipeline_id != global_thread.pipeline_id:
            updates.pipeline_id = pipeline_id

        # Update latest_message_received_date if newer
        if email_dto.thread.latest_message_received_date and (
            not global_thread.latest_message_received_date
            or email_dto.thread.latest_message_received_date
            > global_thread.latest_message_received_date
        ):
            updates.latest_message_received_date = (
                email_dto.thread.latest_message_received_date
            )

        # Calculate latest_message_date from both received and sent dates
        latest_message_date: datetime | None = (
            max(
                filter(
                    None,
                    [
                        email_dto.thread.latest_message_received_date,
                        email_dto.thread.latest_message_sent_date,
                    ],
                )
            )
            if (
                email_dto.thread.latest_message_received_date
                or email_dto.thread.latest_message_sent_date
            )
            else None
        )

        # Update latest_message_date if newer
        if latest_message_date and (
            not global_thread.latest_message_date
            or latest_message_date > global_thread.latest_message_date
        ):
            updates.latest_message_date = latest_message_date

        # Update earliest_message_date if earlier
        if email_dto.thread.earliest_message_date and (
            not global_thread.earliest_message_date
            or (
                global_thread.earliest_message_date
                and email_dto.thread.earliest_message_date
                < global_thread.earliest_message_date
            )
        ):
            updates.earliest_message_date = email_dto.thread.earliest_message_date

        # Update thread_ids if not already included
        if email_dto.thread.id not in global_thread.thread_ids:
            updates.thread_ids = [*global_thread.thread_ids, email_dto.thread.id]

        # Only update if there are actual changes
        if updates.model_dump(exclude_unset=True):
            updates.updated_at = zoned_utc_now()
            updated_global_thread = (
                await self.thread_repository.update_by_tenanted_primary_key(
                    GlobalThread,
                    primary_key_to_value={"id": global_thread.id},
                    organization_id=organization_id,
                    column_to_update=updates,
                )
            )
            return not_none(updated_global_thread)
        return global_thread

    async def get_global_message_by_message_id(
        self, message_id: UUID, organization_id: UUID
    ) -> GlobalMessage | None:
        """
        find global message by message_id
        """
        # First try to find existing message association by message ID
        existing_association = (
            await self.thread_repository.find_global_message_association_by_message_id(
                message_id=message_id,
                organization_id=organization_id,
            )
        )
        if existing_association:
            # If association found, get the associated global message
            return await self.thread_repository.find_by_tenanted_primary_key(
                GlobalMessage,
                id=existing_association.global_message_id,
                organization_id=organization_id,
            )
        return None

    async def upsert_global_message_and_association(
        self,
        organization_id: UUID,
        global_thread: GlobalThread,
        message_dto: MessageDto,
    ) -> tuple[GlobalMessage, bool]:
        """
        Upsert a global message and association for a given message DTO.
        return True if the message is new, False if it is an update
        1:  Find the parent message ID from the in-reply-to header if it exists
        2:  Create a new global message
        3:  Create GlobalThreadMessagesAssociation for the original message ID
        4:  Create GlobalThreadMessagesAssociation for all reference IDs
        5:  Create GlobalMessageAssociation for the message
        6:  Return the message and a boolean indicating if it is new
        """
        headers = not_none(message_dto.message.headers)
        message = message_dto.message
        # Find the parent message ID from the in-reply-to header if it exists
        message_id_in_reply_to = headers.in_reply_to if headers.in_reply_to else None
        original_message_id = headers.message_id
        parent_global_message = None
        if message_id_in_reply_to:
            parent_global_message = (
                await self.thread_repository.find_by_original_message_id(
                    original_message_id=message_id_in_reply_to,
                    organization_id=organization_id,
                )
            )
        # collect all original message ids from message_dto.message.headers.references and original_message_id
        all_original_message_ids = {
            *(headers.references or []),
            original_message_id,
        }
        # find schedule global message by message_id
        schedule_global_message = await self.get_global_message_by_message_id(
            message.id, organization_id
        )
        if schedule_global_message:
            global_message = (
                await self.thread_repository.update_by_tenanted_primary_key(
                    GlobalMessage,
                    primary_key_to_value={"id": schedule_global_message.id},
                    organization_id=organization_id,
                    column_to_update=GlobalMessageUpdate(
                        original_message_id=original_message_id,
                        parent_global_message_id=parent_global_message.id
                        if parent_global_message
                        else None,
                        global_thread_id=global_thread.id,
                        message_received_at=message.received_at or zoned_utc_now(),
                    ),
                )
            )
            await self.thread_repository.upsert_global_thread_messages_association(
                organization_id=organization_id,
                global_thread_id=global_thread.id,
                original_message_ids=list(all_original_message_ids),
            )
            # return the global message and False since we have already create all associations before
            return not_none(global_message), False
        global_message_from_reply_to = GlobalMessage(
            id=uuid4(),
            original_message_id=original_message_id,
            organization_id=organization_id,
            parent_global_message_id=parent_global_message.id
            if parent_global_message
            else None,
            global_thread_id=global_thread.id,
            message_received_at=message.received_at or zoned_utc_now(),
            created_at=zoned_utc_now(),
        )
        is_new = False
        try:
            global_message = await self.thread_repository.insert(
                global_message_from_reply_to
            )
            is_new = True
        except IntegrityError as e:
            logger.bind(
                original_message_id=original_message_id,
                organization_id=organization_id,
                message_id_in_reply_to=message_id_in_reply_to,
            ).warning("found existing global message, skipping", exc_info=e)
            global_message = not_none(
                await self.thread_repository.find_by_original_message_id(
                    original_message_id=original_message_id,
                    organization_id=organization_id,
                )
            )
        await self.thread_repository.upsert_global_thread_messages_association(
            organization_id=organization_id,
            global_thread_id=global_thread.id,
            original_message_ids=list(all_original_message_ids),
        )
        # Create a global message association for the message
        await self.thread_repository.upsert_global_message_association(
            GlobalMessageAssociation(
                id=uuid4(),
                global_message_id=global_message.id,
                message_id=message.id,
                organization_id=organization_id,
                created_at=zoned_utc_now(),
            )
        )

        return global_message, is_new

    async def get_global_message_by_provider_id_and_email_account_ids(
        self,
        provider_id: str,
        email_provider: EmailProvider,
        email_account_ids: list[UUID],
    ) -> tuple[GlobalMessage, UUID] | None:
        return await self.thread_repository.get_global_message_by_provider_id_and_email_account_ids(
            provider_id=provider_id,
            email_provider=email_provider,
            email_account_ids=email_account_ids,
        )


class SingletonGlobalThreadService(Singleton, GlobalThreadService):
    pass


def get_global_thread_service_by_db_engine(
    db_engine: DatabaseEngine,
) -> GlobalThreadService:
    if SingletonGlobalThreadService.has_instance():
        return SingletonGlobalThreadService.get_singleton_instance()
    return SingletonGlobalThreadService(
        thread_repository=ThreadRepository(engine=db_engine),
        contact_resolve_service=get_contact_resolve_service(db_engine=db_engine),
        global_message_ai_rec_service=get_global_message_ai_rec_service(
            db_engine=db_engine
        ),
    )
