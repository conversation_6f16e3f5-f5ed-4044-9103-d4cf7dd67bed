import json
import uuid
from typing import Any
from uuid import UUID

from bs4 import BeautifulSoup, Doctype

from salestech_be.common.exception import ResourceNotFoundError
from salestech_be.common.ree_llm import (
    LLMTraceMetadata,
    ReeTraceMetadata,
    acompletion,
)
from salestech_be.common.singleton import Singleton
from salestech_be.core.account.service.account_query_service import (
    AccountQueryService,
    get_account_query_service,
)
from salestech_be.core.ai.event_handlers.intel import start_intel_workflow
from salestech_be.core.ai.event_handlers.pipeline_intel import pipeline_intel_handler
from salestech_be.core.ai.insights.llm_calls.generate_objection_insights_from_email_llm_call import (
    generate_objection_insights_from_email_llm_call,
)
from salestech_be.core.ai.prompt.langfuse_prompt_service import (
    get_langfuse_prompt_service,
)
from salestech_be.core.ai.prompt.schema import PromptEnum, PromptRequest
from salestech_be.core.ai.workflows.schema import IntelTriggerObjectType
from salestech_be.core.contact.service.contact_query_service import (
    ContactQueryService,
    get_contact_query_service,
)
from salestech_be.core.email.insight.events import NewEmailEvent
from salestech_be.core.email.type.email import EmailHydratedParticipant
from salestech_be.core.extraction_prompt.service.config_extraction_service import (
    ExtractionConfigService,
    extraction_config_service_factory_general,
)
from salestech_be.core.extraction_prompt.service.email_extraction_service import (
    EmailExtractionService,
    get_email_extraction_service_by_db_engine,
)
from salestech_be.core.extraction_prompt.type.shared_type import Feature
from salestech_be.core.meeting.dto.meeting_insight_dto import InsightDTO
from salestech_be.core.organization.service.organization_service import (
    OrganizationService,
    get_organization_service_general,
)
from salestech_be.core.pipeline.service.pipeline_service import get_pipeline_service
from salestech_be.core.prompt.types import PromptUseCase
from salestech_be.core.task.service.task_v2_service import (
    TaskV2Service,
    get_task_v2_service_general,
)
from salestech_be.core.transcript.llm import anthropic_sonnet
from salestech_be.core.user.service.user_service import (
    UserService,
    get_user_service_general,
)
from salestech_be.db.dao.insight_repository import InsightRepository
from salestech_be.db.dao.meeting_repository import MeetingRepository
from salestech_be.db.dao.message_repository import MessageRepository
from salestech_be.db.dao.thread_repository import ThreadRepository
from salestech_be.db.dao.user_repository import UserRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.extraction_config import ExtractionSection
from salestech_be.db.models.global_thread import GlobalThread
from salestech_be.db.models.insight import (
    Insight,
    InsightAuthorType,
    InsightReferenceIdType,
    InsightSection,
    InsightSourceType,
    InsightUpdate,
)
from salestech_be.db.models.message import Message
from salestech_be.db.models.thread import Thread
from salestech_be.db.models.user import User
from salestech_be.db.models.user_organization_association import (
    UserOrganizationAssociation,
)
from salestech_be.integrations.langchain.insights.schema import (
    EmailActionableInsight,
    EmailInsight,
    EmailInsightListFromLLM,
    EmailSentimentInsight,
    TEmailInsight,
)
from salestech_be.ree_logging import get_logger
from salestech_be.settings import settings
from salestech_be.temporal.database import (
    get_or_init_db_engine,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.util.time import zoned_utc_now
from salestech_be.util.validation import not_none
from salestech_be.web.api.email.insight.schema import (
    CreateThreadInsightRequest,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    PatchThreadInsightRequest,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.web.api.organization.schema import (
    OrganizationResponse,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)

logger = get_logger(__name__)

langfuse_prompt_service = get_langfuse_prompt_service()


class EmailInsightService:
    def __init__(
        self,
        config_extraction_service: ExtractionConfigService,
        email_extraction_service: EmailExtractionService,
        insight_repository: InsightRepository,
        message_repository: MessageRepository,
        thread_repository: ThreadRepository,
        meeting_repository: MeetingRepository,
        organization_service: OrganizationService,
        task_v2_service: TaskV2Service,
        contact_query_service: ContactQueryService,
        user_repository: UserRepository,
        user_service: UserService,
        account_query_service: AccountQueryService,
    ) -> None:
        self.config_extraction_service = config_extraction_service
        self.email_extraction_service = email_extraction_service
        self.insight_repository = insight_repository
        self.message_repository = message_repository
        self.thread_repository = thread_repository
        self.task_v2_service = task_v2_service
        self.contact_query_service = contact_query_service
        self.meeting_repository = meeting_repository
        self.task_v2_service = task_v2_service
        self.contact_query_service = contact_query_service
        self.organization_service = organization_service
        self.user_repository = user_repository
        self.user_service = user_service
        self.account_query_service = account_query_service

    async def create_insight(
        self, organization_id: UUID, user_id: UUID, request: CreateThreadInsightRequest
    ) -> InsightDTO:
        logger.bind(request=request).info("Create email insights field request")

        insight_section = await self.insight_repository.find_by_tenanted_primary_key(
            table_model=InsightSection,
            organization_id=organization_id,
            id=request.insight_section_id,
        )
        if not insight_section:
            raise ResourceNotFoundError(
                f"No insight section found for id {request.insight_section_id}, organization: {organization_id}"
            )
        time_now = zoned_utc_now()
        rank = request.rank
        if not rank:
            existing_insights = (
                await self.insight_repository.get_insights_by_section_id(
                    organization_id=organization_id,
                    insight_section_id=insight_section.id,
                )
            )
            rank = (
                0
                if not existing_insights
                else max(existing_insights, key=lambda x: x.rank).rank + 1
            )
        await self.insight_repository.insert(
            Insight(
                id=uuid.uuid4(),
                insight_section_id=insight_section.id,
                organization_id=organization_id,
                reference_type=InsightReferenceIdType.THREAD,
                reference_id=insight_section.reference_id,
                insight_name=insight_section.name,
                insight_description=insight_section.description,
                created_by_user_id=user_id,
                updated_by_user_id=user_id,
                deleted_by_user_id=None,
                source_type=InsightSourceType.EMAIL_THREAD,  # Only current use case
                tags=None,
                contact_id=None,
                brief_values=[request.insight_content],
                detailed_explanation=None,
                transcript_locations=None,
                source_locations=None,
                created_at=time_now,
                updated_at=time_now,
                deleted_at=None,
                author_type=InsightAuthorType.USER,
                user_feedback=None,
                approved_at=time_now,
                approved_by_user_id=user_id,
                metadata={},
                rank=rank,
                version=0,
            )
        )

        all_insights = sorted(
            await self.insight_repository.get_insights_by_section_id(
                organization_id=insight_section.organization_id,
                insight_section_id=insight_section.id,
            ),
            key=lambda insight: (insight.created_at, insight.insight_name),
        )
        return InsightDTO(
            insight_section=insight_section,
            insights=all_insights,
        )

    async def patch_insight(
        self,
        insight_id: UUID,
        organization_id: UUID,
        user_id: UUID,
        request: PatchThreadInsightRequest,
    ) -> InsightDTO:
        logger.bind(insight_id=insight_id, request=request).info(
            "Patch insight request"
        )
        insight = await self.insight_repository.find_by_tenanted_primary_key(
            table_model=Insight,
            organization_id=organization_id,
            id=insight_id,
        )
        if not insight:
            raise ResourceNotFoundError(
                f"No insight found for id {insight_id}, organization: {organization_id}"
            )
        updated_insight = not_none(
            await self.insight_repository.update_by_tenanted_primary_key(
                table_model=Insight,
                organization_id=organization_id,
                primary_key_to_value={
                    "id": insight_id,
                },
                column_to_update={
                    "brief_values": [request.insight_content],
                    "author_type": InsightAuthorType.USER,
                    "updated_by_user_id": user_id,
                    "updated_at": zoned_utc_now(),
                },
            )
        )
        logger.info("Insight updated", updated_insight=updated_insight)
        insight_section = not_none(
            await self.insight_repository.find_by_tenanted_primary_key(
                table_model=InsightSection,
                organization_id=organization_id,
                id=updated_insight.insight_section_id,
            )
        )

        all_insights = sorted(
            await self.insight_repository.get_insights_by_section_id(
                organization_id=insight_section.organization_id,
                insight_section_id=insight_section.id,
            ),
            key=lambda i: (i.created_at, i.insight_name),
        )
        return InsightDTO(
            insight_section=insight_section,
            insights=all_insights,
        )

    async def delete_insight(
        self, insight_id: UUID, organization_id: UUID, user_id: UUID
    ) -> InsightDTO:
        logger.bind(insight_id=insight_id).info("Remove insight request")
        time_now = zoned_utc_now()
        deleted_insight = await self.insight_repository.update_by_tenanted_primary_key(
            table_model=Insight,
            organization_id=organization_id,
            primary_key_to_value={
                "id": insight_id,
            },
            column_to_update={
                "updated_at": time_now,
                "updated_by_user_id": user_id,
                "deleted_at": time_now,
                "deleted_by_user_id": user_id,
            },
        )
        if not deleted_insight:
            raise ResourceNotFoundError(
                f"Insight {insight_id} for organization {organization_id} not found"
            )
        insight_section = not_none(
            await self.insight_repository.find_by_tenanted_primary_key(
                table_model=InsightSection,
                organization_id=organization_id,
                id=deleted_insight.insight_section_id,
            )
        )

        all_insights = sorted(
            await self.insight_repository.get_insights_by_section_id(
                organization_id=insight_section.organization_id,
                insight_section_id=insight_section.id,
            ),
            key=lambda i: (i.created_at, i.insight_name),
        )
        return InsightDTO(
            insight_section=insight_section,
            insights=all_insights,
        )

    async def get_insight_sections_by_thread_id(
        self, thread_id: UUID, organization_id: UUID
    ) -> list[InsightDTO]:
        insight_sections = await self.insight_repository.list_insight_sections_by_reference_id_and_organization_id(
            organization_id=organization_id,
            reference_type=InsightReferenceIdType.THREAD,
            reference_id=thread_id,
        )
        insights = await self.insight_repository.list_insights_by_reference_id_and_organization_id(
            organization_id=organization_id,
            reference_type=InsightReferenceIdType.THREAD,
            reference_id=thread_id,
        )
        return self._get_dto_list(insight_sections=insight_sections, insights=insights)

    async def generate_insight_by_global_thread_id(
        self, thread_id: UUID, organization_id: UUID
    ) -> list[InsightDTO]:
        thread = await self.thread_repository.find_by_tenanted_primary_key(
            GlobalThread, id=thread_id, organization_id=organization_id
        )
        if not thread:
            logger.bind(thread_id=thread_id, organization_id=organization_id).error(
                "No email thread found"
            )
            return []
        return await self.generate_insight_for_global_thread(
            global_thread=thread, organization_id=organization_id
        )

    async def generate_insight_for_global_thread(
        self,
        global_thread: GlobalThread,
        organization_id: UUID,
        skip_if_generated_before: bool = True,
        min_message_count: int = 2,
    ) -> list[InsightDTO]:
        with logger.contextualize(
            thread_id=global_thread.id, organization_id=organization_id
        ):
            logger.info(
                "[email_insight_service] Start generating insights for global email thread"
            )

            # 1. Get extraction configs
            extraction_section_list = (
                await self.config_extraction_service.list_extraction_section_by_feature(
                    organization_id=organization_id, feature=Feature.EMAIL_ANALYSIS
                )
            )
            if not extraction_section_list:
                logger.info(
                    "[email_insight_service] No extraction service found for email analysis"
                )
                return []

            # 2. Get the messages associated with the GlobalThread
            message_list = (
                await self.thread_repository.list_messages_by_global_thread_id(
                    global_thread_id=global_thread.id,
                    organization_id=organization_id,
                )
            )

            if not message_list:
                logger.error(
                    "[email_insight_service] No messages found for this global thread",
                    global_thread_id=global_thread.id,
                )
                return []

            # check min messages count
            if len(message_list) < min_message_count:
                logger.info(
                    "[email_insight_service] Not enough messages found for this email thread",
                    message_count=len(message_list),
                    min_message_count=min_message_count,
                )
                return []

            if self._is_invalid_messages(message_list):
                logger.warning(
                    "[email_insight_service] No valid messages found for this email thread"
                )
                return []

            dtos_list = []

            # 3. Process insights for each extraction section
            for extraction_section in extraction_section_list:
                logger.info(
                    "[email_insight_service] Processing extraction section",
                    extraction_section_id=extraction_section.id,
                )
                section_insights = await self._process_insights_for_section(
                    extraction_section=extraction_section,
                    global_thread=global_thread,
                    participants=await self._get_participants_from_global_thread(
                        global_thread=global_thread,
                        organization_id=organization_id,
                    ),
                    organization_id=organization_id,
                    message_list=message_list,
                    skip_if_generated_before=skip_if_generated_before,
                )

                dtos_list.extend(section_insights)

            return dtos_list

    async def generate_seller_contacts_prompt_input(
        self, organization: OrganizationResponse | None, global_thread: GlobalThread
    ) -> str:
        if not organization:
            return ""
        user_org_tuples: list[
            tuple[User, UserOrganizationAssociation]
        ] = await self.user_repository.list_org_users_by_organization_id(
            organization_id=organization.id,
            active_users_only=True,
        )
        user_ids = {user.id for user, _ in user_org_tuples}
        seller_contacts = ""
        if user_ids:
            users = await self.user_service.list_users_v2(
                organization_id=organization.id,
                only_include_user_ids=user_ids,
                active_users_only=True,
            )
            if users:
                seller_contacts = "\n".join(
                    [
                        f"""<seller_contact>
                <first_name>{c.first_name}</first_name>
                <last_name>{c.last_name}</last_name>
                <phone>{c.phone_number}</phone>
                <email>{c.email}</email>
                <id>{c.id}</id>
                </seller_contact>"""
                        for c in users
                    ]
                )
        return seller_contacts

    async def _generate_customer_contacts_prompt_input(
        self, organization_id: UUID, global_thread: GlobalThread
    ) -> str:
        # get customer contacts
        customer_contacts = ""
        if global_thread.contact_ids:
            # for emails, use attendees to get contacts first and fallback to contacts from pipeline
            contacts = await self.contact_query_service.list_contacts_v2(
                organization_id=organization_id,
                only_include_contact_ids=set(global_thread.contact_ids),
            )
            if contacts:
                customer_contacts = "\n".join(
                    [
                        f"""<customer_contact>
                <first_name>{c.first_name}</first_name>
                <last_name>{c.last_name}</last_name>
                <phone>{c.primary_phone_number}</phone>
                <email>{c.primary_email}</email>
                <id>{c.id}</id>
                </customer_contact>"""
                        for c in contacts
                    ]
                )
        return customer_contacts

    async def _process_insights_for_section(  # noqa: C901, PLR0915, PLR0912, PLR0911
        self,
        extraction_section: ExtractionSection,
        organization_id: UUID,
        global_thread: GlobalThread,
        participants: list[EmailHydratedParticipant],
        message_list: list[Message],
        skip_if_generated_before: bool,
    ) -> list[InsightDTO]:
        """
        Process insights for a given extraction section.
        """
        # Get or create insight section
        insight_section = await self.insight_repository.get_or_create_insight_section(
            organization_id=organization_id,
            reference_id=global_thread.id,
            reference_type=InsightReferenceIdType.THREAD,
            source_type=InsightSourceType.EMAIL_THREAD,
            extraction_section=extraction_section,
        )
        logger.info(
            "[email_insight_service] get or created insight section",
            extraction_section_id=extraction_section.id,
            insight_section_id=insight_section.id,
        )

        # Get previous insights
        previous_insights = (
            await self.insight_repository.list_previous_insights_by_section_id(
                insight_section_id=insight_section.id,
                organization_id=organization_id,
            )
        )

        logger.info(
            "[email_insight_service] list previous insights for section",
            extraction_section_id=extraction_section.id,
            previous_insights=insight_section.id,
        )

        latest_message_list = (
            [m for m in message_list if m.created_at > previous_insights[-1].created_at]
            if previous_insights
            else message_list
        )

        # Skip generating insights if no new messages found
        if not latest_message_list:
            logger.info(
                f"[email_insight_service] Skip generating insights for section {extraction_section.name} as no new messages found"
            )
            return []

        messages = EmailInsightService._get_message_prompt_input(latest_message_list)
        logger.info(
            "[email_insight_service] formatted messages for prompt", messages=messages
        )
        existing_insights = {
            insight.brief_values[0]
            for insight in previous_insights
            if insight.brief_values
        }
        formatted_existing_insights = "\n".join(
            [f"  - {ins}" for ins in existing_insights]
        )

        langfuse_session_id = (
            f"insights:{IntelTriggerObjectType.GLOBAL_THREAD}:{global_thread.id}"
        )

        organization = await self.organization_service.get_organization_by_id(
            organization_id
        )

        # get customer contacts
        customer_contacts = await self._generate_customer_contacts_prompt_input(
            organization_id=organization_id, global_thread=global_thread
        )

        # get seller contacts
        seller_contacts = await self.generate_seller_contacts_prompt_input(
            organization=organization, global_thread=global_thread
        )

        customer_company_names = ""
        if global_thread.account_ids:
            accounts = await self.account_query_service.list_accounts_v2(
                organization_id=organization_id,
                only_include_account_ids=set(global_thread.account_ids),
            )
            customer_company_names = ", ".join([a.display_name for a in accounts])

        # Extract insights
        extracted_insights: list[Any] = []  # type: ignore[explicit-any] # TODO: fix-any-annotation
        if extraction_section.name == "Email Sentiment":
            if settings.enable_langfuse_for_email_insights:
                prompt_variables = {
                    "messages": messages,
                    "seller_contacts": seller_contacts,
                    "seller_company_name": organization.display_name
                    if organization
                    else "",
                    "customer_contacts": customer_contacts,
                    "customer_company_name": customer_company_names,
                    "existing_insights": formatted_existing_insights,
                    "num_insights": "1",
                }

                prompt_obj = await langfuse_prompt_service.get_prompt(
                    request=PromptRequest(
                        prompt_name=PromptEnum.GENERATE_SENTIMENT_INSIGHTS_FROM_EMAIL_THREAD,
                        variables=prompt_variables,
                    )
                )

                response = await acompletion(
                    model=prompt_obj.get_model(),
                    messages=prompt_obj.messages,
                    temperature=0,
                    tools=[
                        {
                            "type": "function",
                            "function": {
                                "name": "create_sentiment_insights",
                                "description": "A tool that creates one or more email sentiment insights with descriptions.",
                                "parameters": EmailSentimentInsight.model_json_schema(),
                            },
                        }
                    ],
                    tool_choice="create_sentiment_insights",
                    metadata=LLMTraceMetadata(
                        prompt=prompt_obj.prompt_client,
                        trace_name="insights.email_sentiment",
                        session_id=langfuse_session_id,
                        custom_fields=ReeTraceMetadata(
                            organization_id=str(organization_id),
                            prompt_use_case=PromptUseCase.SENTIMENT_INSIGHTS,
                        ),
                    ),
                )

                if not response.choices:
                    return []
                tool_calls = response.tool_calls
                if not tool_calls:
                    return []

                extracted_insights = []
                for tool_call in tool_calls:
                    if tool_call.function and tool_call.function.arguments:
                        json_args = json.loads(tool_call.function.arguments)
                        try:
                            extracted_insights.append(
                                EmailSentimentInsight(
                                    insight=json_args.get("insight", ""),
                                    is_sales_email=json_args.get(
                                        "is_sales_email", False
                                    ),
                                    source_locations=json_args.get(
                                        "source_locations", []
                                    ),
                                    keyword_tags=json_args.get("keyword_tags", []),
                                    polarity=json_args.get("polarity", ""),
                                    emotion=json_args.get("emotion", []),
                                )
                            )
                        except Exception as e:
                            logger.error(
                                "[email_insight_service] Error parsing tool call arguments",
                                error=e,
                            )
            else:
                extracted_insights = (
                    await self.email_extraction_service.extract_sentiment(
                        messages=messages,
                        participants=participants,
                        organization_id=organization_id,
                        existing_sentiments=formatted_existing_insights,
                    )
                )
        elif extraction_section.name == "Summary":
            if settings.enable_langfuse_for_email_insights:
                prompt_variables = {
                    "messages": messages,
                    "seller_contacts": seller_contacts,
                    "seller_company_name": organization.display_name
                    if organization
                    else "",
                    "customer_contacts": customer_contacts,
                    "customer_company_name": customer_company_names,
                    "existing_insights": formatted_existing_insights,
                    "num_insights": "0 to 6",
                }

                prompt_obj = await langfuse_prompt_service.get_prompt(
                    request=PromptRequest(
                        prompt_name=PromptEnum.GENERATE_SUMMARY_INSIGHTS_FROM_EMAIL_THREAD,
                        variables=prompt_variables,
                    )
                )

                response = await acompletion(
                    model=prompt_obj.get_model(),
                    messages=prompt_obj.messages,
                    temperature=0,
                    tools=[
                        {
                            "type": "function",
                            "function": {
                                "name": "create_summary_insights",
                                "description": "A tool that creates one or more email summary insights with descriptions.",
                                "parameters": EmailInsightListFromLLM.model_json_schema(),
                            },
                        }
                    ],
                    tool_choice="create_summary_insights",
                    metadata=LLMTraceMetadata(
                        prompt=prompt_obj.prompt_client,
                        trace_name="insights.email_summary",
                        session_id=langfuse_session_id,
                        custom_fields=ReeTraceMetadata(
                            organization_id=str(organization_id),
                            prompt_use_case=PromptUseCase.SUMMARY_INSIGHTS,
                        ),
                    ),
                )

                if not response.choices:
                    return []
                tool_calls = response.tool_calls
                if not tool_calls:
                    return []

                extracted_insights = []
                for tool_call in tool_calls:
                    if tool_call.function and tool_call.function.arguments:
                        json_args = json.loads(tool_call.function.arguments)
                        try:
                            for insight in json_args.get("insights", []):
                                extracted_insights.append(
                                    EmailInsight(
                                        insight=insight.get("insight", ""),
                                        is_sales_email=insight.get(
                                            "is_sales_email", False
                                        ),
                                        source_locations=insight.get(
                                            "source_locations", []
                                        ),
                                        keyword_tags=insight.get("keyword_tags", []),
                                    )
                                )
                        except Exception as e:
                            logger.error(
                                "[email_insight_service] Error parsing tool call arguments",
                                error=e,
                            )
            else:
                extracted_insights = (
                    await self.email_extraction_service.extract_summary(
                        messages=messages,
                        participants=participants,
                        organization_id=organization_id,
                        existing_summaries=formatted_existing_insights,
                    )
                )
        elif extraction_section.name == "Objections":
            if settings.enable_langfuse_for_email_insights:
                extracted_insights = (
                    await generate_objection_insights_from_email_llm_call(
                        messages=messages,
                        seller_contacts=seller_contacts,
                        seller_company_name=organization.display_name
                        if organization
                        else "",
                        customer_contacts=customer_contacts,
                        customer_company_name=customer_company_names,
                        formatted_existing_insights=formatted_existing_insights,
                        organization_id=organization_id,
                        langfuse_session_id=langfuse_session_id,
                        min_num_insights=0,
                        max_num_insights=10,
                    )
                )
            else:
                extracted_insights = (
                    await self.email_extraction_service.extract_objections(
                        messages=messages,
                        participants=participants,
                        organization_id=organization_id,
                        existing_objections=formatted_existing_insights,
                    )
                )

        elif extraction_section.name == "Email Brief":
            if settings.enable_langfuse_for_email_insights:
                prompt_variables = {
                    "messages": messages,
                    "seller_contacts": seller_contacts,
                    "seller_company_name": organization.display_name
                    if organization
                    else "",
                    "customer_contacts": customer_contacts,
                    "customer_company_name": customer_company_names,
                    "existing_insights": formatted_existing_insights,
                    "num_insights": "1",
                }

                prompt_obj = await langfuse_prompt_service.get_prompt(
                    request=PromptRequest(
                        prompt_name=PromptEnum.GENERATE_BRIEF_INSIGHTS_FROM_EMAIL_THREAD,
                        variables=prompt_variables,
                    )
                )

                response = await acompletion(
                    model=prompt_obj.get_model(),
                    messages=prompt_obj.messages,
                    temperature=0,
                    tools=[
                        {
                            "type": "function",
                            "function": {
                                "name": "create_brief_insights",
                                "description": "A tool that creates one or more email brief insights with descriptions.",
                                "parameters": EmailInsightListFromLLM.model_json_schema(),
                            },
                        }
                    ],
                    tool_choice="create_brief_insights",
                    metadata=LLMTraceMetadata(
                        prompt=prompt_obj.prompt_client,
                        trace_name="insights.email_brief",
                        session_id=langfuse_session_id,
                        custom_fields=ReeTraceMetadata(
                            organization_id=str(organization_id),
                            prompt_use_case=PromptUseCase.BRIEF_INSIGHTS,
                        ),
                    ),
                )

                if not response.choices:
                    return []
                tool_calls = response.tool_calls
                if not tool_calls:
                    return []

                extracted_insights = []
                for tool_call in tool_calls:
                    if tool_call.function and tool_call.function.arguments:
                        json_args = json.loads(tool_call.function.arguments)
                        try:
                            for insight in json_args.get("insights", []):
                                extracted_insights.append(
                                    EmailInsight(
                                        insight=insight.get("insight", ""),
                                        is_sales_email=insight.get(
                                            "is_sales_email", False
                                        ),
                                        source_locations=insight.get(
                                            "source_locations", []
                                        ),
                                        keyword_tags=insight.get("keyword_tags", []),
                                    )
                                )
                        except Exception as e:
                            logger.error(
                                "[email_insight_service] Error parsing tool call arguments",
                                error=e,
                            )
            else:
                extracted_insights = await self.email_extraction_service.extract_brief(
                    messages=messages,
                    participants=participants,
                    organization_id=organization_id,
                )
        else:
            extracted_insights = []

        # Map extracted insights to DTOs
        previous_rank_offset = (
            int(previous_insights[-1].rank) + 1 if previous_insights else 0
        )
        dtos = [
            EmailInsightService.map_to_insight_dto(
                insight=insight,
                rank=rank + previous_rank_offset,
                insight_name=insight_section.name,
                insight_description=not_none(insight_section.description),
                organization_id=organization_id,
                source_type=InsightSourceType.EMAIL_THREAD,
                insight_section=insight_section,
                thread_id=global_thread.id,
                user_id=None,
            )
            for rank, insight in enumerate(extracted_insights)
        ]

        # Insert insights into repository
        for dto in dtos:
            for new_insight in dto.insights:
                if previous_insights:
                    existing_brief = previous_insights.pop()
                    insight = new_insight.copy(
                        update={
                            "id": existing_brief.id,
                            "rank": existing_brief.rank,
                        }
                    )
                else:
                    insight = new_insight

                inserted_insight = await self.insight_repository.upsert_pk(insight)

                if (
                    inserted_insight
                    and extraction_section.name == "Objections"
                    and (
                        settings.enable_generate_tasks_from_objection_insights
                        or str(organization_id)
                        in settings.enable_generate_tasks_from_objection_insights_org_ids
                    )
                ):
                    try:
                        now = zoned_utc_now()
                        workflow_id = f"intel-{inserted_insight.id}-{IntelTriggerObjectType.OBJECTION.value}-{organization_id}-{now.strftime('%Y%m%d%H%M%S')}"
                        await start_intel_workflow(
                            organization_id=organization_id,
                            object_id=inserted_insight.id,
                            object_type=IntelTriggerObjectType.OBJECTION,
                            workflow_id=workflow_id,
                            account_ids=global_thread.account_ids,
                            pipeline_id=global_thread.pipeline_id,
                        )
                    except Exception as e:
                        logger.error(
                            "[email_insight_service] Error starting intel workflow",
                            error=e,
                        )

        return dtos

    @staticmethod
    def _get_message_prompt_input(message_list: list[Message]) -> str:
        formatted_strings = []

        for message in message_list:
            senders = ", ".join(
                [
                    participant.name if participant.name else participant.email
                    for participant in message.send_from
                ]
            )
            try:
                message_body = EmailInsightService.extract_latest_message(
                    message.body_html
                )
                if message_body:
                    formatted_string = f"{senders} [{message.id}]: {message_body}"
                    formatted_strings.append(formatted_string)
                else:
                    logger.warning(
                        "No message body parsed from email message",
                        message_id=message.id,
                    )
            except ValueError as e:
                logger.error(
                    "Error extracting message body from HTML content",
                    error=str(e),
                    message_id=message.id,
                )
        return "\n".join(formatted_strings)

    @staticmethod
    def extract_latest_message(html_content: str) -> str:  # noqa: PLR0912,C901
        email_provider = "gmail"
        try:
            soup = BeautifulSoup(html_content, "html.parser")
            if soup.find("p", class_="MsoNormal") or soup.find(
                "meta",
                {
                    "http-equiv": "Content-Type",
                    "content": "text/html; charset=Windows-1252",
                },
            ):
                email_provider = "microsoft"

                # Remove quoted sections and unnecessary elements
            unwanted_selectors = [
                ("blockquote", {}),
                ("div", {"class": "gmail_quote"}),
                ("head", {}),
                ("div", {"id": "mail-editor-reference-message-container"}),
                ("div", {"id": "ms-outlook-mobile-signature"}),
            ]

            for tag, attrs in unwanted_selectors:
                for element in soup.find_all(tag, attrs):
                    element.decompose()

            # Remove all content after the first <hr/>
            hr_element = soup.find("hr")
            if hr_element:
                for sibling in hr_element.find_all_next():
                    sibling.extract()

            # Remove all content after a specific <div> with id="border"
            border_div = soup.find("div", id="border")
            if border_div:
                for sibling in border_div.find_all_next():
                    sibling.extract()
        except (AttributeError, ValueError) as e:
            logger.error("Error parsing HTML content", error=str(e))
            raise e

        message_parts = []

        def is_valid_text(text: str) -> bool:
            return text is not None and not any(
                keyword in text.lower()
                for keyword in ["wrote:", "from:", "sent:", "forwarded message"]
            )

        if email_provider == "gmail":
            for element in soup.find_all("div", recursive=False):
                text = element.get_text(strip=True)
                if is_valid_text(text):
                    message_parts.append(text)
            if not message_parts:
                for element in soup.find_all("div"):
                    text = element.get_text(strip=True)
                    if is_valid_text(text):
                        message_parts.append(text)
                        break
        else:  # microsoft
            br_tag = soup.find("br", id="lineBreakAtBeginningOfSignature")
            if br_tag and br_tag.previous_sibling:
                text = br_tag.previous_sibling.get_text(strip=True)
                if is_valid_text(text):
                    message_parts.append(text)
            else:
                for element in soup.find_all("div"):
                    text = element.get_text(strip=True)
                    if is_valid_text(text):
                        message_parts.append(text)
                        break

        result = "\n".join(message_parts).strip()
        if not result:
            # Return text content with proper spacing
            # Why this approach:
            # 1. Most unhandled cases in the past didn't use <div> tags for layout
            # 2. Instead they used <p> or <table> tags to structure the HTML
            # 3. get_text() with strip=True removes leading/trailing whitespace
            # 4. separator="\n" preserves paragraph breaks from <p> and <table> tags
            # 5. This better handles email clients' limited HTML support
            result = str(soup.get_text(strip=True, separator="\n"))
        return result

    @staticmethod
    def map_to_insight_dto(
        insight: TEmailInsight,
        rank: float,
        insight_name: str,
        insight_description: str,
        insight_section: InsightSection,
        organization_id: UUID,
        source_type: InsightSourceType,
        thread_id: UUID,
        user_id: UUID | None,
        author_type: InsightAuthorType = InsightAuthorType.SYSTEM,
    ) -> InsightDTO:
        metadata: dict[str, Any] = {}  # type: ignore[explicit-any] # TODO: fix-any-annotation
        if isinstance(insight, EmailActionableInsight):
            metadata = {
                "task_title": insight.title,
                "task_note": insight.insight,
                "task_priority": insight.priority,
                "task_type": insight.action_type,
                "task_associated_contact_ids": [
                    str(c.id) for c in insight.associated_contacts
                ],
                "task_due_date": insight.due_date,
                "is_sales_email": insight.is_sales_email,
            }
        else:
            metadata = {
                "is_sales_email": insight.is_sales_email,
            }

        if isinstance(insight, EmailSentimentInsight):
            metadata.update(
                {
                    "polarity": insight.polarity,
                    "emotion": insight.emotion,
                }
            )

        source_locations = [
            {
                "message_id": location,
            }
            for location in insight.source_locations
        ]

        zoned_time = zoned_utc_now()
        insights = [
            Insight(
                id=uuid.uuid4(),
                insight_section_id=insight_section.id,
                organization_id=organization_id,
                reference_type=InsightReferenceIdType.THREAD,
                reference_id=thread_id,
                contact_id=None,
                insight_name=insight_name,
                insight_description=insight_description,
                created_by_user_id=user_id,
                updated_by_user_id=user_id,
                deleted_by_user_id=None,
                source_type=source_type,
                tags=insight.keyword_tags,
                brief_values=[insight.insight],
                detailed_explanation="",
                source_locations=source_locations,
                created_at=zoned_time,
                updated_at=zoned_time,
                deleted_at=None,
                author_type=author_type,
                user_feedback=None,
                approved_at=None,
                approved_by_user_id=None,
                metadata=metadata,
                rank=rank,
                version=0,
            )
        ]

        return InsightDTO(
            insight_section=insight_section,
            insights=insights,
        )

    @staticmethod
    def _get_dto_list(
        insight_sections: list[InsightSection], insights: list[Insight]
    ) -> list[InsightDTO]:
        # Helper function for list functions that forms expected DTO results
        section_to_dtos: dict[UUID, InsightDTO] = {}
        for insight_section in insight_sections:
            section_to_dtos[insight_section.id] = InsightDTO(
                insight_section=insight_section,
                insights=[],
            )

        for insight in insights:
            section_to_dtos[insight.insight_section_id].insights.append(insight)

        insight_dtos = list(section_to_dtos.values())
        insight_dtos.sort(
            key=lambda insight_dto: (
                insight_dto.insight_section.created_at,
                insight_dto.insight_section.name,
            ),
        )
        for insight_dto in insight_dtos:
            insight_dto.insights.sort(
                key=lambda insight: (
                    insight.rank,
                    insight.created_at,
                    insight.insight_name,
                ),
            )
        return insight_dtos

    @staticmethod
    def _is_invalid_messages(messages: list[Message]) -> bool:
        for message in messages:
            html_content = message.body_html
            if not html_content:
                return True
            soup = BeautifulSoup(html_content, "html.parser")
            if any(isinstance(tag, Doctype) and "html" in tag for tag in soup.contents):
                return True
            if soup.find(
                "a", string=lambda text: text and "unsubscribe" in text.lower()
            ):
                return True
            if soup.find(itemtype="http://schema.org/EmailMessage") or soup.find(
                itemtype="http://schema.org/ViewAction"
            ):
                return True

        return False

    async def get_insights_by_contact_id(
        self,
        contact_id: UUID,
        organization_id: UUID,
    ) -> list[Insight]:
        return await self.insight_repository.get_insights_by_contact_id(
            contact_id=contact_id,
            organization_id=organization_id,
            reference_type=InsightReferenceIdType.THREAD,
        )

    async def update_contact_id_by_insight_id(
        self,
        insight_id: UUID,
        organization_id: UUID,
        new_contact_id: UUID,
    ) -> Insight | None:
        return await self.insight_repository.update_by_tenanted_primary_key(
            table_model=Insight,
            primary_key_to_value={"id": insight_id},
            organization_id=organization_id,
            column_to_update=InsightUpdate(contact_id=new_contact_id),
        )

    async def _get_participants_from_global_thread(
        self,
        global_thread: GlobalThread,
        organization_id: UUID,
    ) -> list[EmailHydratedParticipant]:
        """Get unique participants from all threads in a global thread.

        Args:
            global_thread: Global thread to get participants from
            organization_id: Organization ID for tenancy

        Returns:
            List of unique EmailHydratedParticipant objects
        """
        # Get all threads for this global thread
        threads = []
        for thread_id in global_thread.thread_ids:
            thread = await self.thread_repository.find_by_tenanted_primary_key(
                Thread,
                organization_id=organization_id,
                id=thread_id,
            )
            if thread:
                threads.append(thread)

        # Combine all participants from all threads
        seen_emails = set()
        unique_participants: list[EmailHydratedParticipant] = []

        for thread in threads:
            for participant in thread.participants:
                if participant.email and participant.email.lower() not in seen_emails:
                    seen_emails.add(participant.email.lower())
                    unique_participants.append(participant)

        return unique_participants

    async def trigger_activity_intelligence(
        self, pipeline_id: UUID, organization_id: UUID, global_thread_id: UUID
    ) -> None:
        # TODO: refactor
        db_engine = await get_or_init_db_engine()
        pipeline_service = get_pipeline_service(db_engine)
        pipeline = await pipeline_service.get_pipeline_by_id(
            pipeline_id=pipeline_id,
            organization_id=organization_id,
        )
        await pipeline_intel_handler(
            NewEmailEvent(
                organization_id=organization_id,
                pipeline_id=pipeline_id,
                account_id=pipeline.account_id,
                global_thread_id=global_thread_id,
            )
        )


class SingletonEmailInsightService(Singleton, EmailInsightService):
    pass


def get_email_insight_service_by_db(
    db_engine: DatabaseEngine,
) -> EmailInsightService:
    if SingletonEmailInsightService.has_instance():
        return SingletonEmailInsightService.get_singleton_instance()
    config_extraction_service = extraction_config_service_factory_general(db_engine)
    email_extraction_service = get_email_extraction_service_by_db_engine(
        engine=db_engine, llm=anthropic_sonnet()
    )
    insight_repository = InsightRepository(engine=db_engine)
    message_repository = MessageRepository(engine=db_engine)
    thread_repository = ThreadRepository(engine=db_engine)
    task_v2_service = get_task_v2_service_general(db_engine=db_engine)
    contact_query_service = get_contact_query_service(db_engine=db_engine)
    meeting_repository = MeetingRepository(engine=db_engine)
    organization_service = get_organization_service_general(db_engine=db_engine)
    user_repository = UserRepository(
        engine=db_engine,
    )
    user_service = get_user_service_general(db_engine=db_engine)
    return EmailInsightService(
        config_extraction_service=config_extraction_service,
        email_extraction_service=email_extraction_service,
        insight_repository=insight_repository,
        message_repository=message_repository,
        thread_repository=thread_repository,
        task_v2_service=task_v2_service,
        contact_query_service=contact_query_service,
        meeting_repository=meeting_repository,
        organization_service=organization_service,
        user_repository=user_repository,
        user_service=user_service,
        account_query_service=get_account_query_service(
            db_engine=db_engine,
        ),
    )
