from datetime import datetime
from enum import StrEnum
from uuid import UUID

from pydantic import BaseModel

from salestech_be.common.exception import InvalidArgumentError
from salestech_be.core.email.type.email import EmailHydratedParticipant, EmailTag
from salestech_be.db.models.message import Message
from salestech_be.db.models.sequence import SequenceSchedule
from salestech_be.db.models.thread import Thread
from salestech_be.util.validation import not_none


class EmailSendingErrorCode(StrEnum):
    # Ignoarable errors
    NO_OP = "NO_OP"
    DUPLICATE_REQUEST = "DUPLICATE_REQUEST"
    TEMPLATE_VERSION_MISMATCH = "TEMPLATE_VERSION_MISMATCH"
    MESSAGE_SENT = "MESSAGE_SENT"

    # Non-skip-able errors
    THREAD_CREATION_ERROR = "THREAD_CREATION_ERROR"
    MESSAGE_CREATION_ERROR = "MESSAGE_CREATION_ERROR"
    MESSAGE_UPDATE_ERROR = "MESSAGE_UPDATE_ERROR"
    MESSAGE_CANCEL_ERROR = "MESSAGE_CANCEL_ERROR"

    TEMPLATE_DATA_ERROR = "TEMPLATE_DATA_ERROR"
    TEMPLATE_RENDER_ERROR = "TEMPLATE_RENDER_ERROR"

    RECIPIENT_DATA_ERROR = "RECIPIENT_DATA_ERROR"
    MESSAGE_DATA_ERROR = "MESSAGE_DATA_ERROR"

    SENDER_EMAIL_ACCOUNT_NOT_FOUND = "SENDER_EMAIL_ACCOUNT_NOT_FOUND"
    DELIVERY_WINDOW_ERROR = "DELIVERY_WINDOW_ERROR"

    def is_ignorable(self) -> bool:
        return self in [
            EmailSendingErrorCode.NO_OP,
            EmailSendingErrorCode.DUPLICATE_REQUEST,
            EmailSendingErrorCode.TEMPLATE_VERSION_MISMATCH,
            EmailSendingErrorCode.MESSAGE_SENT,
            EmailSendingErrorCode.DELIVERY_WINDOW_ERROR,
        ]


class EmailOperationStatus(StrEnum):
    SUCCESS = "SUCCESS"
    ERROR = "ERROR"


class EmailRequestSkippingReason(StrEnum):
    MESSAGE_NOT_IN_SCHEDULED = "MESSAGE_NOT_IN_SCHEDULED"
    MESSAGE_SENT = "MESSAGE_SENT"
    DUPLICATE_REQUEST = "DUPLICATE_REQUEST"
    NO_UPDATE = "NO_UPDATE"
    TEMPLATE_VERSION_UPDATED = "TEMPLATE_VERSION_UPDATED"


class EmailOperationResultBase(BaseModel):
    status: EmailOperationStatus
    error_code: EmailSendingErrorCode | None = None
    error_detail: str | None = None


class MessageCreateResult(EmailOperationResultBase):
    recipient: EmailHydratedParticipant | None = None
    message: Message | None = None


class MessageUpdateResult(EmailOperationResultBase):
    message_id: UUID
    message: Message | None = None


class ThreadCreateResult(EmailOperationResultBase):
    thread: Thread | None = None


class ThreadUpdateResult(EmailOperationResultBase):
    thread_id: UUID | None = None
    thread: Thread | None = None


class MessageInfo(BaseModel):
    message_id: UUID
    reply_to_message_id: UUID | None


class RecipientInfo(BaseModel):
    recipient: EmailHydratedParticipant
    reply_to_message_id: UUID | None

    def __hash__(self) -> int:
        return hash((self.recipient.email, self.recipient.contact_id))


class EmailSendingRequest(BaseModel):
    idempotency_key: str
    sender: EmailHydratedParticipant | None
    recipients: list[RecipientInfo]
    template_id: UUID
    template_version: int
    send_after: datetime | None
    sequence_schedule: SequenceSchedule | None
    email_account_pool_id: UUID | None
    organization_id: UUID
    tags: list[EmailTag]


class EmailSendingUpdateTemplateRequest(BaseModel):
    message_ids: list[UUID]
    template_id: UUID
    template_version: int
    organization_id: UUID


class MessageReschedule(BaseModel):
    message_id: UUID
    send_after: datetime


class EmailSendingRescheduleRequest(BaseModel):
    reschedule_requests: list[MessageReschedule]
    sequence_schedule: SequenceSchedule | None
    organization_id: UUID


class EmailSendingCancelRequest(BaseModel):
    scheduled_messages: list[Message]
    organization_id: UUID


class EmailSendingErrorRequest(BaseModel):
    message_ids: list[UUID]
    organization_id: UUID


class EmailSendingRecipientLevelError(EmailOperationResultBase):
    recipient: EmailHydratedParticipant

    @staticmethod
    def from_thread_create_result(
        thread_create_error: ThreadCreateResult, recipient: EmailHydratedParticipant
    ) -> "EmailSendingRecipientLevelError":
        return EmailSendingRecipientLevelError(
            status=EmailOperationStatus.ERROR,
            error_code=thread_create_error.error_code,
            error_detail=thread_create_error.error_detail,
            recipient=recipient,
        )

    @staticmethod
    def from_message_create_result(
        message_create_error: MessageCreateResult, recipient: EmailHydratedParticipant
    ) -> "EmailSendingRecipientLevelError":
        return EmailSendingRecipientLevelError(
            status=EmailOperationStatus.ERROR,
            error_code=message_create_error.error_code,
            error_detail=message_create_error.error_detail,
            recipient=recipient,
        )

    @staticmethod
    def from_message_update_result(
        message_update_error: MessageUpdateResult, recipient: EmailHydratedParticipant
    ) -> "EmailSendingRecipientLevelError":
        return EmailSendingRecipientLevelError(
            status=EmailOperationStatus.ERROR,
            error_code=message_update_error.error_code,
            error_detail=message_update_error.error_detail,
            recipient=recipient,
        )

    @staticmethod
    def from_thread_update_result(
        thread_update_error: ThreadUpdateResult, recipient: EmailHydratedParticipant
    ) -> "EmailSendingRecipientLevelError":
        return EmailSendingRecipientLevelError(
            status=EmailOperationStatus.ERROR,
            error_code=thread_update_error.error_code,
            error_detail=thread_update_error.error_detail,
            recipient=recipient,
        )


class EmailSendingMessageLevelError(EmailOperationResultBase):
    message_id: UUID

    @staticmethod
    def from_message_update_result(
        message_update_error: MessageUpdateResult,
    ) -> "EmailSendingMessageLevelError":
        if message_update_error.status != EmailOperationStatus.ERROR:
            raise InvalidArgumentError(
                "EmailSendingMessageLevelError can only be created from error result"
            )

        return EmailSendingMessageLevelError(
            status=EmailOperationStatus.ERROR,
            error_code=message_update_error.error_code,
            error_detail=message_update_error.error_detail,
            message_id=message_update_error.message_id,
        )

    @staticmethod
    def from_thread_update_result(
        thread_update_error: ThreadUpdateResult,
        message_id: UUID,
    ) -> "EmailSendingMessageLevelError":
        if thread_update_error.status != EmailOperationStatus.ERROR:
            raise InvalidArgumentError(
                "EmailSendingMessageLevelError can only be created from error result"
            )

        return EmailSendingMessageLevelError(
            status=EmailOperationStatus.ERROR,
            error_code=thread_update_error.error_code,
            error_detail=thread_update_error.error_detail,
            message_id=message_id,
        )


class EmailSendingUpdateRequestValidationResult(EmailOperationResultBase):
    message_level_errors: list[EmailSendingMessageLevelError] | None = None


class MessageOperationSuccessData(EmailOperationResultBase):
    message: Message

    @staticmethod
    def from_message_create_result(
        message_create_result: MessageCreateResult,
    ) -> "MessageOperationSuccessData":
        if message_create_result.status != EmailOperationStatus.SUCCESS:
            raise InvalidArgumentError(
                "MessageOperationSuccessData can only be created from success result"
            )

        return MessageOperationSuccessData(
            status=EmailOperationStatus.SUCCESS,
            message=not_none(message_create_result.message),
        )

    @staticmethod
    def from_message_update_result(
        message_update_result: MessageUpdateResult,
    ) -> "MessageOperationSuccessData":
        if message_update_result.status != EmailOperationStatus.SUCCESS:
            raise InvalidArgumentError(
                "MessageOperationSuccessData can only be created from success result"
            )

        return MessageOperationSuccessData(
            status=EmailOperationStatus.SUCCESS,
            message=not_none(message_update_result.message),
        )


class EmailSendingResponse(BaseModel):
    data: list[
        EmailSendingRecipientLevelError
        | MessageOperationSuccessData
        | EmailSendingMessageLevelError
    ]
