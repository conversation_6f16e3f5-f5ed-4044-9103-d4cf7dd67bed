from enum import StrEnum
from typing import Annotated
from uuid import UUID

from pydantic import BaseModel

from salestech_be.common.schema_manager.std_object_field_identifier import (
    DomainConfigurationField,
    OrganizationUserField,
    OutboundDomainField,
    SequenceField,
    StdObjectIdentifiers,
)
from salestech_be.common.schema_manager.std_object_relationship import (
    OutboundDomainRelationship,
)
from salestech_be.common.type.metadata.field.field_indexable_config import (
    UniqueIndexableConfig,
)
from salestech_be.common.type.metadata.field.field_type_property import (
    BooleanCheckboxFieldProperty,
    DefaultEnumFieldProperty,
    NestedObjectFieldProperty,
    NumericFieldProperty,
    TextFieldProperty,
    TimestampFieldProperty,
    UUIDFieldProperty,
)
from salestech_be.common.type.metadata.schema import OutboundRelationship
from salestech_be.core.common.types import (
    DomainModel,
    FieldMetadata,
)
from salestech_be.core.email.outbound_domain.schema import BuyDomainRequest
from salestech_be.db.models.outbound import (
    DomainHealth,
    OutboundDomain,
    OutboundDomainStatus,
)
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime


class DomainPurchaseWorkflowInput(BaseModel):
    request: BuyDomainRequest
    user_id: UUID
    organization_id: UUID
    purchasable_domains: list[str]


class OutboundDomainHealth(StrEnum):
    HEALTHY = "HEALTHY"
    UNHEALTHY = "UNHEALTHY"
    PENDING = "PENDING"


class DomainConfigurationModel(DomainModel):
    object_id = StdObjectIdentifiers.domain_configuration.identifier
    field_name_provider = DomainConfigurationField
    object_display_name = "Domain Configuration"

    contains_mx_records: Annotated[
        bool,
        FieldMetadata(
            type_property=BooleanCheckboxFieldProperty(
                field_display_name="Contains MX Records",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ] = False

    contains_spf_records: Annotated[
        bool,
        FieldMetadata(
            type_property=BooleanCheckboxFieldProperty(
                field_display_name="Contains SPF Records",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ] = False

    contains_dkim_record: Annotated[
        bool,
        FieldMetadata(
            type_property=BooleanCheckboxFieldProperty(
                field_display_name="Contains DKIM Record",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ] = False

    contains_dmarc_record: Annotated[
        bool,
        FieldMetadata(
            type_property=BooleanCheckboxFieldProperty(
                field_display_name="Contains DMARC Record",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ] = False

    domain_forwarding: Annotated[
        bool,
        FieldMetadata(
            type_property=BooleanCheckboxFieldProperty(
                field_display_name="Domain Forwarding",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ] = False

    @classmethod
    def from_db_model(
        cls, domain_health: DomainHealth, domain_forwarding: bool = False
    ) -> "DomainConfigurationModel":
        return cls(
            contains_mx_records=len(domain_health.mx_records) >= 1,
            contains_spf_records=len(domain_health.spf_records) >= 1,
            contains_dkim_record=len(domain_health.dkim_record) >= 1,
            contains_dmarc_record=len(domain_health.dmarc_record) >= 1,
            domain_forwarding=domain_forwarding,
        )


class OutboundDomainV2(DomainModel):
    object_id = StdObjectIdentifiers.outbound_domain.identifier
    field_name_provider = OutboundDomainField
    object_display_name = "Outbound Domain"

    outbound_relationships = (
        OutboundRelationship(
            relation_type=OutboundRelationship.RelationType.LOOKUP,
            id=OutboundDomainRelationship.outbound_domain__to__sequence,
            relationship_name="Sequence",
            self_object_identifier=StdObjectIdentifiers.outbound_domain.identifier,
            related_object_identifier=StdObjectIdentifiers.sequence.identifier,
            self_cardinality=OutboundRelationship.Cardinality.ONE,
            related_object_cardinality=OutboundRelationship.Cardinality.MANY,
            ordered_self_field_identifiers=(OutboundDomainField.id.identifier,),
            ordered_related_field_identifiers=(SequenceField.id.identifier,),
        ),
        OutboundRelationship(
            relation_type=OutboundRelationship.RelationType.LOOKUP,
            id=OutboundDomainRelationship.outbound_domain__to__created_by_user,
            relationship_name="Created By User",
            self_object_identifier=StdObjectIdentifiers.outbound_domain.identifier,
            related_object_identifier=StdObjectIdentifiers.user.identifier,
            self_cardinality=OutboundRelationship.Cardinality.ONE,
            related_object_cardinality=OutboundRelationship.Cardinality.ONE,
            ordered_self_field_identifiers=(
                OutboundDomainField.created_by_user_id.identifier,
            ),
            ordered_related_field_identifiers=(OrganizationUserField.id.identifier,),
        ),
    )

    id: Annotated[
        UUID,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                index_config=UniqueIndexableConfig(is_indexed=True, is_unique=True),
                field_display_name="ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            ),
        ),
    ]

    organization_id: Annotated[
        UUID,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Organization ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            ),
        ),
    ]

    domain: Annotated[
        str,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Domain",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ]

    domain_health: Annotated[
        OutboundDomainHealth | None,
        FieldMetadata(
            type_property=DefaultEnumFieldProperty(
                enum_class=OutboundDomainHealth,
                field_display_name="Domain Health",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ]

    status: Annotated[
        OutboundDomainStatus,
        FieldMetadata(
            type_property=DefaultEnumFieldProperty(
                enum_class=OutboundDomainStatus,
                field_display_name="Status",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ]

    total_mailbox_count: Annotated[
        int,
        FieldMetadata(
            type_property=NumericFieldProperty(
                total_precision=10,
                decimal_precision=0,
                field_display_name="Mailbox Count",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ]

    active_mailbox_count: Annotated[
        int,
        FieldMetadata(
            type_property=NumericFieldProperty(
                total_precision=10,
                decimal_precision=0,
                field_display_name="Active Mailbox Count",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ]

    sequence_count: Annotated[
        int,
        FieldMetadata(
            type_property=NumericFieldProperty(
                total_precision=10,
                decimal_precision=0,
                field_display_name="Sequence Count",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ]

    domain_configuration: Annotated[
        DomainConfigurationModel | None,
        FieldMetadata(
            type_property=NestedObjectFieldProperty(
                object_identifier=StdObjectIdentifiers.domain_configuration.identifier,
                field_display_name="Domain Configuration",
            ),
        ),
    ] = None

    created_by_user_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Created By User ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            ),
        ),
    ]

    purchased_at: Annotated[
        ZoneRequiredDateTime,
        FieldMetadata(
            type_property=TimestampFieldProperty(
                field_display_name="Purchased At",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ]

    updated_at: Annotated[
        ZoneRequiredDateTime,
        FieldMetadata(
            type_property=TimestampFieldProperty(
                field_display_name="Updated At",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ]

    deleted_at: Annotated[
        ZoneRequiredDateTime | None,
        FieldMetadata(
            type_property=TimestampFieldProperty(
                field_display_name="Deleted At",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ] = None

    archived_at: Annotated[
        ZoneRequiredDateTime | None,
        FieldMetadata(
            type_property=TimestampFieldProperty(
                field_display_name="Archived At",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ] = None

    @classmethod
    def from_db_model(
        cls,
        domain: OutboundDomain,
        domain_health: OutboundDomainHealth | None = None,
        dns_configuration: DomainConfigurationModel | None = None,
        deleted_at: ZoneRequiredDateTime | None = None,
        total_mailbox_count: int = 0,
        active_mailbox_count: int = 0,
        sequence_count: int = 0,
    ) -> "OutboundDomainV2":
        return cls(
            id=domain.id,
            organization_id=domain.organization_id,
            domain=domain.domain,
            status=domain.status,
            purchased_at=domain.created_at,
            updated_at=domain.updated_at,
            total_mailbox_count=total_mailbox_count,
            active_mailbox_count=active_mailbox_count,
            created_by_user_id=domain.created_by_user_id,
            sequence_count=sequence_count,
            deleted_at=deleted_at,
            archived_at=domain.archived_at,
            domain_health=domain_health,
            domain_configuration=dns_configuration,
        )
