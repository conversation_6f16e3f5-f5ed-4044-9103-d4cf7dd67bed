from datetime import datetime
from enum import StrEnum, auto
from types import MappingProxyType
from typing import Any, <PERSON>ric, TypeVar
from uuid import UUID

from pydantic import BaseModel, Field, parse_obj_as

from salestech_be.common.type.patch_request import UNSET, BasePatchRequest, UnsetAware
from salestech_be.core.meeting.constants import UNKNOWN_EMAIL
from salestech_be.db.models.meeting import (
    MeetingAttendee,
    MeetingBotStatus,
    MeetingInvitee,
    MeetingStatus,
)
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime


# Ensuring compatibility with Pydantic v2 features
class StatusChange(BaseModel):
    # Code is a str and not RecallStatusCode, in the event the provider uses additional
    # values we don't map.
    code: str
    message: str | None = None
    created_at: datetime
    sub_code: str | None = None


class StatusHistory(BaseModel):
    status_changes: list[StatusChange] = Field(default_factory=list)

    def get_latest_status(self) -> StatusChange | None:
        return (
            max(self.status_changes, key=lambda sc: sc.created_at)
            if self.status_changes
            else None
        )

    def add_status_change(self, new_status: StatusChange) -> bool:
        if new_status not in self.status_changes:
            self.status_changes.append(new_status)
            return True
        return False

    @classmethod
    def parse_raw_status_changes(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        cls, raw_status_changes: list[dict[str, Any]]
    ) -> "StatusHistory":
        status_changes = parse_obj_as(list[StatusChange], raw_status_changes)
        return cls(status_changes=status_changes)

    def serialize_status_changes(self) -> list[dict[str, Any]]:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        return [status_change.dict() for status_change in self.status_changes]


class RecallStatusCode(StrEnum):
    READY = auto()
    IN_WAITING_ROOM = auto()
    JOINING_CALL = auto()
    IN_CALL_NOT_RECORDING = auto()
    RECORDING_PERMISSION_ALLOWED = auto()
    RECORDING_PERMISSION_DENIED = auto()
    IN_CALL_RECORDING = auto()
    CALL_ENDED = auto()
    RECORDING_DONE = auto()
    DONE = auto()
    ANALYSIS_FAILED = auto()
    ANALYSIS_DONE = auto()


class MeetingAttendance(StrEnum):
    UNKNOWN = auto()
    NONE_ATTENDED = auto()
    PARTIALLY_ATTENDED = auto()
    FULLY_ATTENDED = auto()

    @classmethod
    def from_invitee_and_attendee_lists(
        cls,
        invitees: list[MeetingInvitee] | None,
        attendees: list[MeetingAttendee] | None,
    ) -> "MeetingAttendance":
        """
        Return MeetingAttendance instance designated how meeting was attended.  Cases:

        Future meeting: don't know yet
        Live meeting: don't know yet
        Meeting completed, but we haven't filled out attendees yet: don't know yet
        Meeting completed, and nobody attended:
        Meeting completed, attendees < participants: partial
        Meeting completed, attendees >= participants: fully

        """
        if attendees is not None and invitees is not None:
            if not attendees:
                return MeetingAttendance.NONE_ATTENDED
            elif len(attendees) >= len(invitees):
                return MeetingAttendance.FULLY_ATTENDED
            else:
                return MeetingAttendance.PARTIALLY_ATTENDED
        else:
            return MeetingAttendance.UNKNOWN


class ContactSummary(BaseModel):
    """
    Internal class used by meeting service to represent info from a contact.  Minimal
    fields from contact needed for servicing api facing fields or internal service
    requirements like activity generation.
    """

    id: UUID
    # contact's primary email
    email: str | None = None
    # contact's display_name, or "{first_name} {last_name}" if display_name doesn't set.
    name: str = ""
    # indicates if primary_account_id is empty, has_account is true if not empty
    has_account: bool = False
    primary_account_id: UUID | None = None

    @property
    def email_or_default(self) -> str:
        return self.email or UNKNOWN_EMAIL

    @property
    def email_domain(self) -> str | None:
        return self.email and self.email.split("@")[-1] if self.email else None


class MeetingMetrics:
    STATE_CHANGE_METRIC_NAME = "meeting_status_change"
    SOURCE_STATUS_TAG = "source_status"
    RESULT_STATUS_TAG = "result_status"
    REFERENCE_TYPE_TAG = "reference_type"

    COMPLETED_TIMING_METRIC_NAME = "meeting_completed_timing"


class MeetingBotMetrics:
    ENDED_WITHOUT_ACCESS = "bot_ended_no_access"


class MeetingEventFieldUpdateRequest(BasePatchRequest):
    """
    Helper class that allows for updating meeting properties based on external (e.g.
    Bot) events.
    """

    started_at: UnsetAware[ZoneRequiredDateTime] = UNSET


T = TypeVar("T")


class FSM(Generic[T]):
    def __init__(self, transitions: dict[T, list[T]]) -> None:
        # Though we allow values of list[T] as convenience for call site, store as
        # frozenset to ensure immutability

        self.transitions = MappingProxyType(
            {k: frozenset(v) for k, v in transitions.items()}
        )

    def get_reachable_states(self, current_state: T) -> frozenset[T]:
        return self.transitions[current_state]

    def is_valid_progression(self, current_state: T, end_state: T) -> bool:
        reachable_states = set(self.get_reachable_states(current_state))
        seen_states: set[T] = set()
        while reachable_states:
            if end_state in reachable_states:
                return True

            last_checked_states = reachable_states.copy()
            seen_states.update(last_checked_states)
            reachable_states.clear()

            for checked_state in last_checked_states:
                candidates = self.get_reachable_states(checked_state)
                reachable_states.update(
                    [
                        candidate
                        for candidate in candidates
                        if candidate not in seen_states
                    ]
                )

        return False


class MeetingFSM(FSM[MeetingStatus]):
    def __init__(self) -> None:
        super().__init__(
            transitions={
                MeetingStatus.SCHEDULED: [MeetingStatus.CANCELED, MeetingStatus.ACTIVE],
                MeetingStatus.ACTIVE: [MeetingStatus.ENDED],
                MeetingStatus.ENDED: [
                    MeetingStatus.CANCELED,
                    MeetingStatus.SCHEDULED,
                    MeetingStatus.ANALYZING,
                ],
                MeetingStatus.ANALYZING: [MeetingStatus.COMPLETED],
                MeetingStatus.COMPLETED: [MeetingStatus.ANALYZING],
                MeetingStatus.CANCELED: [],
            }
        )


class MeetingBotFSM(FSM[MeetingBotStatus]):
    def __init__(self) -> None:
        super().__init__(
            transitions={
                MeetingBotStatus.SCHEDULED: [
                    MeetingBotStatus.JOINING,
                    MeetingBotStatus.CANCELED,
                ],
                MeetingBotStatus.JOINING: [MeetingBotStatus.WAITING],
                MeetingBotStatus.WAITING: [
                    MeetingBotStatus.EXITED,
                    MeetingBotStatus.IN_CALL_RECORDING,
                    MeetingBotStatus.IN_CALL_RECORDING_DENIED,
                    MeetingBotStatus.IN_CALL_NOT_RECORDING,
                ],
                MeetingBotStatus.IN_CALL_RECORDING: [
                    MeetingBotStatus.IN_CALL_PAUSED,
                    MeetingBotStatus.EXITED,
                ],
                MeetingBotStatus.IN_CALL_PAUSED: [
                    MeetingBotStatus.IN_CALL_RECORDING,
                    MeetingBotStatus.EXITED,
                ],
                MeetingBotStatus.IN_CALL_RECORDING_DENIED: [MeetingBotStatus.EXITED],
                MeetingBotStatus.IN_CALL_NOT_RECORDING: [
                    MeetingBotStatus.IN_CALL_RECORDING,
                    MeetingBotStatus.EXITED,
                ],
                MeetingBotStatus.EXITED: [],
                MeetingBotStatus.CANCELED: [],
            }
        )
