from typing import Any

from pydantic import BaseModel

from salestech_be.core.prompt.types import PromptUseCase


class MeetingPromptResponseBase(BaseModel):
    use_case: PromptUseCase
    rendered_prompt: str


class MeetingAssistantAgendaResponse(MeetingPromptResponseBase):
    suggested_agenda: str


class MeetingAssistantKeyPointsResponse(MeetingPromptResponseBase):
    key_talking_points: str


class MeetingAgendaContext(BaseModel):
    """Meeting context data for prompt generation."""

    meeting_title: str
    meeting_description: str
    meeting_start_time: str
    meeting_end_time: str
    meeting_sellers: str
    meeting_customers: str

    def to_prompt_variables(self) -> dict[str, Any]:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        """Convert context to prompt variables."""
        return {
            "meeting_title": self.meeting_title,
            "meeting_description": self.meeting_description,
            "meeting_start_time": self.meeting_start_time,
            "meeting_end_time": self.meeting_end_time,
            "meeting_sellers": self.meeting_sellers,
            "meeting_customers": self.meeting_customers,
        }


class MeetingKeyPointsContext(BaseModel):
    """Meeting context data for pre-meeting key points generation."""

    meeting_title: str
    meeting_description: str
    meeting_start_time: str
    meeting_end_time: str
    meeting_sellers: str
    meeting_customers: str
    pipeline_stage: str = ""
    pipeline_source: str = ""
    pipeline_type: str = ""
    pipeline_amount: str = ""
    pipeline_expected_close_date: str = ""

    def to_prompt_variables(self) -> dict[str, Any]:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        """Convert context to prompt variables."""
        base_vars = {
            "meeting_title": self.meeting_title,
            "meeting_description": self.meeting_description,
            "meeting_start_time": self.meeting_start_time,
            "meeting_end_time": self.meeting_end_time,
            "meeting_sellers": self.meeting_sellers,
            "meeting_customers": self.meeting_customers,
        }

        # Add pipeline context if available
        pipeline_vars = {}
        if any(
            [
                self.pipeline_stage,
                self.pipeline_source,
                self.pipeline_type,
                self.pipeline_amount,
                self.pipeline_expected_close_date,
            ]
        ):
            pipeline_vars = {
                "pipeline_stage": self.pipeline_stage,
                "pipeline_source": self.pipeline_source,
                "pipeline_type": self.pipeline_type,
                "pipeline_amount": self.pipeline_amount,
                "pipeline_expected_close_date": self.pipeline_expected_close_date,
            }

        return {**base_vars, **pipeline_vars}
