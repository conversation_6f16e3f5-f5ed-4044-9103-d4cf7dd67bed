from typing import Annotated

from salestech_be.common.schema_manager.std_object_field_identifier import (
    MeetingBotStatusEventField,
    StdObjectIdentifiers,
)
from salestech_be.common.type.metadata.field.field_type_property import (
    TextFieldProperty,
    TimestampFieldProperty,
)
from salestech_be.core.common.types import (
    DomainModel,
    FieldMetadata,
)
from salestech_be.db.models.meeting import BotStatusEvent
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime


class MeetingBotStatusEventV2(DomainModel):
    object_id = StdObjectIdentifiers.meeting_bot_status_event.identifier
    object_display_name = "MeetingBotStatusEvent"
    field_name_provider = MeetingBotStatusEventField

    status: Annotated[
        str,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Status",
                is_ui_displayable=True,
                is_ui_editable=True,
            )
        ),
    ]

    status_at: Annotated[
        ZoneRequiredDateTime,
        FieldMetadata(
            type_property=TimestampFieldProperty(
                field_display_name="Status At",
                is_ui_displayable=True,
                is_ui_editable=True,
            )
        ),
    ]

    sub_code: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Status Sub Code",
                is_ui_displayable=True,
                is_ui_editable=True,
            )
        ),
    ] = None

    @classmethod
    def from_bot_status_event(
        cls, bot_status_event: BotStatusEvent
    ) -> "MeetingBotStatusEventV2":
        return MeetingBotStatusEventV2(
            status=bot_status_event.status,
            status_at=bot_status_event.status_at,
            sub_code=bot_status_event.sub_code,
        )
