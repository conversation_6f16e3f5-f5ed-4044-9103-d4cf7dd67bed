from typing import Annotated
from uuid import UUID

from salestech_be.common.schema_manager.std_object_field_identifier import (
    MeetingParticipantFieldV2,
    StdObjectIdentifiers,
)
from salestech_be.common.type.metadata.field.field_type_property import (
    BooleanCheckboxFieldProperty,
    EmailFieldProperty,
    TextFieldProperty,
    UUIDFieldProperty,
)
from salestech_be.core.common.types import (
    DomainModel,
    FieldMetadata,
)
from salestech_be.core.meeting.types.meeting_reference_type import (
    MeetingReferenceParticipant,
)
from salestech_be.db.models.user_calendar_event import (
    CalendarEventParticipantStatus,
)


class MeetingParticipantV2(DomainModel):
    object_id = StdObjectIdentifiers.meeting_participant.identifier
    object_display_name = "Meeting Participant"
    field_name_provider = MeetingParticipantFieldV2

    name: Annotated[
        str,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Participant Name",
                is_ui_displayable=True,
                is_ui_editable=True,
            )
        ),
    ]

    is_host: Annotated[
        bool | None,
        FieldMetadata(
            type_property=BooleanCheckboxFieldProperty(
                field_display_name="Is Host",
                is_ui_displayable=True,
                is_ui_editable=True,
            )
        ),
    ]

    platform: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Platform",
                is_ui_displayable=True,
                is_ui_editable=True,
            )
        ),
    ]

    rsvp_status: Annotated[
        CalendarEventParticipantStatus | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="RSVPStatus",
                is_ui_displayable=True,
                is_ui_editable=True,
            )
        ),
    ]

    user_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="User ID",
                is_ui_displayable=True,
                is_ui_editable=True,
            )
        ),
    ]

    contact_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Contact ID",
                is_ui_displayable=True,
                is_ui_editable=True,
            )
        ),
    ]

    email: Annotated[
        str | None,
        FieldMetadata(
            type_property=EmailFieldProperty(
                field_display_name="Email",
                is_ui_displayable=True,
                is_ui_editable=True,
            )
        ),
    ]

    @classmethod
    def from_meeting_reference_strategy_type(
        cls, meeting_reference: MeetingReferenceParticipant
    ) -> "MeetingParticipantV2":
        return MeetingParticipantV2(
            name=meeting_reference.name or "",
            is_host=meeting_reference.is_organizer,
            platform=None,
            rsvp_status=meeting_reference.status,
            user_id=meeting_reference.user_id,
            contact_id=meeting_reference.contact_id,
            email=meeting_reference.email,
        )
