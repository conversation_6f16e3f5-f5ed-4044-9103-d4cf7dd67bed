from uuid import UUID

from pydantic import BaseModel

from salestech_be.db.models.meeting import MeetingReferenceIdType
from salestech_be.db.models.user_calendar_event import CalendarEventParticipantStatus
from salestech_be.web.api.meeting.schema import (
    MeetingReferenceId,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)


class MeetingReferenceParticipant(BaseModel):
    user_id: UUID | None
    contact_id: UUID | None
    status: CalendarEventParticipantStatus
    email: str | None
    name: str | None
    is_organizer: bool | None
    is_creator: bool | None


class MeetingReference(BaseModel):
    reference_id_type: MeetingReferenceIdType
    reference_id: MeetingReferenceId
    owner_user_id: UUID | None
    user_id: UUID | None
    is_owned_by_organizer: bool
    meeting_reference_participants: list[MeetingReferenceParticipant]
    # Optional fields, may exist based on type
    media_s3_key: str | None = None
    media_vtt_s3_key: str | None = None
    media_sprite_s3_key: str | None = None
