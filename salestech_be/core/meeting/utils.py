from datetime import timedelta
from typing import assert_never

from salestech_be.core.meeting.bot_error_mappings import BotErrorMappings
from salestech_be.core.meeting.constants import (
    BOT_JOIN_AT_OFFSET_MINUTES,
    DEFAULT_BOT_NAME,
)
from salestech_be.db.models.meeting import (
    Meeting,
    MeetingBot,
    MeetingReferenceIdType,
    MeetingStatus,
)
from salestech_be.db.models.user_organization_association import UserOrganizationProfile
from salestech_be.ree_logging import get_logger
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime
from salestech_be.util.time import zoned_utc_now

logger = get_logger(__name__)


def get_bot_error_from_status_event(
    status: str | None, sub_code: str | None
) -> tuple[str | None, str | None]:
    """
    Get bot error information based on status and sub_code
    Only returns a match if both status and sub_code match exactly.

    Args:
        status: The status string from the status event
        sub_code: The sub_code from the status event, can be None

    Returns:
        tuple: (error_code, error_desc) or (None, None) if no mapping found
    """
    return BotErrorMappings().get_error_info(status, sub_code)


def calculate_bot_join_time(
    meeting_starts_at: ZoneRequiredDateTime,
) -> ZoneRequiredDateTime:
    """
    Calculate the default bot join time based on meeting start time.

    Calculates join time as meeting_starts_at minus the configured offset.

    Args:
        meeting_starts_at: The time when the meeting starts

    Returns:
        ZoneRequiredDateTime: The time when the bot should join the meeting
    """
    return meeting_starts_at - timedelta(minutes=BOT_JOIN_AT_OFFSET_MINUTES)


def is_meeting_recorded(meeting: Meeting, meeting_bot: MeetingBot | None) -> bool:
    """
    Determine if a meeting is recorded based on meeting and bot status.

    Args:
        meeting: The meeting to check
        meeting_bot: The meeting bot, if any

    Returns:
        bool: True if the meeting is recorded or being recorded
    """
    match meeting.reference_id_type:
        case MeetingReferenceIdType.EXTERNAL_RECORDING | MeetingReferenceIdType.VOICE:
            return True
        case MeetingReferenceIdType.VOICE_V2:
            voice_v2 = (meeting.metadata or {}).get("voice_v2", {})
            return bool(voice_v2.get("is_recorded", False))
        case MeetingReferenceIdType.USER_CALENDAR_EVENT:
            if not meeting_bot:
                return False

            if meeting_bot.status_history and any(
                status_event.status == "in_call_recording"
                for status_event in meeting_bot.status_history.status_history
            ):
                # Bot history indicates we got to recording
                return True

            if meeting.status in [MeetingStatus.SCHEDULED, MeetingStatus.ACTIVE]:
                # Cases:
                # 1. Future meeting with bot - considered True
                # 2. Bot is waiting to be admitted, but not yet recording - user hasn't taken action yet.
                # 3. Meetings beyond start time but stuck in scheduled.  This means bot issues, so assume we missed recording.
                now = zoned_utc_now()
                return meeting.starts_at > now - timedelta(minutes=10)

            return False
        case _ as never:
            assert_never(never)


def get_meeting_bot_name(
    user_first_name: str | None, user_profile: UserOrganizationProfile | None
) -> str:
    if user_profile and user_profile.meeting_bot_name:
        return user_profile.meeting_bot_name

    if not user_first_name:
        return DEFAULT_BOT_NAME

    suffix = "'" if user_first_name.endswith("s") else "'s"
    return f"{user_first_name}{suffix} chief of staff"


def format_duration_display(duration_seconds: int) -> str:
    """
    Format duration in seconds to a human readable string with proper plural forms.
    Omits zero components and handles hours, minutes, and seconds.

    Args:
        duration_seconds: Duration in seconds

    Returns:
        str: Formatted duration string like "1 hour", "1 minute", "2 hours 30 minutes", etc.
    """
    hours = duration_seconds // 3600
    minutes = (duration_seconds % 3600) // 60
    seconds = duration_seconds % 60

    components = []

    if hours > 0:
        hour_text = "hour" if hours == 1 else "hours"
        components.append(f"{hours} {hour_text}")

    if minutes > 0 or (hours == 0 and seconds == 0):
        minute_text = "minute" if minutes == 1 else "minutes"
        components.append(f"{minutes} {minute_text}")

    if seconds > 0 and hours == 0:
        second_text = "second" if seconds == 1 else "seconds"
        components.append(f"{seconds} {second_text}")

    return " ".join(components)
